//
//  ParabolaView.m
//  uten
//
//  Created by yuming on 2024/8/23.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "BulletView.h"
#import "UTENFile+X.h"
#import <UIKit/UIKit.h>
#import <Masonry/Masonry.h>

@implementation BulletView

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

- (void)setupLayout {
    UIImage *image = [UIImage imageWithData:self.imageFile.data];
    UIImageView *background = [[UIImageView alloc] initWithImage:image];
    background.frame = self.bounds;
    [self addSubview:background];
    [background mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.mas_width);
        make.height.equalTo(self.mas_height);
    }];
    // label
    UILabel *label = [[UILabel alloc] init];
    label.text = self.text;
    label.textColor = [UIColor blackColor];
    label.font = [UIFont systemFontOfSize:24];
    [self addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX);
        make.centerY.equalTo(self.mas_centerY);
    }];    
}

// 拋物線動畫
- (void)animateParabolicMotion:(CGSize)screen withTarget:(CGFloat)x andDuration:(NSTimeInterval)duration {
    [self setupLayout];
    UIView *bullet = self;
    // 起始點和結束點
    CGPoint startPoint = bullet.center;
    // CGPoint endPoint = CGPointMake(300, 500);  // 假設終點為 (300, 500)
    CGPoint endPoint = CGPointMake(x * screen.width, 0.7 * screen.height);
    
    // 設定拋物線頂點 (這裡設定為兩點之間的中間位置)
    CGPoint controlPoint = CGPointMake((startPoint.x + endPoint.x) / 2, startPoint.y - 400);
    
    // 使用 UIBezierPath 設定拋物線路徑
    UIBezierPath *path = [UIBezierPath bezierPath];
    [path moveToPoint:startPoint];
    [path addQuadCurveToPoint:endPoint controlPoint:controlPoint];
    
    // 設定 CAKeyframeAnimation
    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:@"position"];
    animation.path = path.CGPath;
    animation.duration = duration;  // 動畫持續時間 (秒)
    animation.fillMode = kCAFillModeForwards;
    animation.removedOnCompletion = NO;
    
    // 添加動畫到視圖的圖層
    [bullet.layer addAnimation:animation forKey:@"parabolicMove"];
}

@end
