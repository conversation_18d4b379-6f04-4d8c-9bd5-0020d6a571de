//
//  BasicTableViewCell.m
//  uten
//
//  Created by pht on 2024/6/7.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "BasicTableViewCell.h"
#import <Masonry/Masonry.h>

@implementation BasicTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setup];
    }
    return self;
}

- (void)setup {
    _titleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    _titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:_titleLabel];
    [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(10);
        make.left.equalTo(self.contentView.mas_left).offset(15);
        make.right.equalTo(self.contentView.mas_right).offset(-15);
    }];
    // 加大字體
    _titleLabel.font = [UIFont boldSystemFontOfSize:20];
    // 置中
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    // 深灰色
    _titleLabel.textColor = [UIColor darkGrayColor];

    _subtitleLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    _subtitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.contentView addSubview:_subtitleLabel];
    [_subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_titleLabel.mas_bottom).offset(10);
        make.left.equalTo(self.contentView.mas_left).offset(15);
        make.right.equalTo(self.contentView.mas_right).offset(-15);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-10);
    }];
    // 縮小字體
    _subtitleLabel.font = [UIFont systemFontOfSize:14];
    // 置中
    _subtitleLabel.textAlignment = NSTextAlignmentCenter;
    // 深灰色
    _subtitleLabel.textColor = [UIColor darkGrayColor];
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
