#import "UTENFile.h"
#import <Foundation/Foundation.h>
#import "UTENFile.h"
#import "UTENEnum.h"

NS_ASSUME_NONNULL_BEGIN

@interface UTENFile (X)

// Add your category methods here
// read-only imageType property
@property (nonatomic, assign, readonly) ImageType imageType;
// - (ImageType)imageType;
// absolutePath property
@property (nonatomic, copy, readonly) NSString *absolutePath;
// nsdata property
@property (nonatomic, strong, readonly) NSData *data;
// url property
@property (nonatomic, strong, readonly) NSURL *url;

@end

NS_ASSUME_NONNULL_END
