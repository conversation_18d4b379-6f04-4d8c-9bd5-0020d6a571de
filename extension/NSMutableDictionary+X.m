//
//  NSMutableDictionary+NullRemoval.m
//  uten
//
//  Created by pht on 2024/12/24.
//  Copyright 2024 bekubee. All rights reserved.
//

#import "NSMutableDictionary+X.h"

@implementation NSMutableDictionary (X)

- (void)removeNullValues {
    NSArray *keysToRemove = [self allKeys].copy;
    for (NSString *key in keysToRemove) {
        if ([self[key] isKindOfClass:[NSNull class]]) {
            [self removeObjectForKey:key];
        }
    }
}

@end
