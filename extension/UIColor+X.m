#import "UIColor+X.h"

@implementation UIColor (X)

+ (UIColor *)rightColor {
    return [UIColor colorWithRed:217.0/255.0 green:228.0/255.0 blue:131.0/255.0 alpha:1.0];
}

+ (UIColor *)wrongColor {
    return [UIColor colorWithRed:244.0/255.0 green:181.0/255.0 blue:208.0/255.0 alpha:1.0];
}

+ (UIColor *)comboPinkColor {
    return [UIColor colorWithRed:241.0/255.0 green:159.0/255.0 blue:194.0/255.0 alpha:1.0];
}

+ (UIColor *)comboBlueColor {
    return [UIColor colorWithRed:117.0/255.0 green:143.0/255.0 blue:200.0/255.0 alpha:1.0];
}

+ (UIColor *)comboGreenColor {
    return [UIColor colorWithRed:172.0/255.0 green:206.0/255.0 blue:34.0/255.0 alpha:1.0];
}

+ (UIColor *)comboRedColor {
    return [UIColor colorWithRed:230.0/255.0 green:33.0/255.0 blue:41.0/255.0 alpha:1.0];
}

@end