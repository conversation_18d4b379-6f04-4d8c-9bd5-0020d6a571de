#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColor (X)

// 綠色
@property(class, nonatomic, readonly) UIColor *rightColor;
// 紅色
@property(class, nonatomic, readonly) UIColor *wrongColor;
// combo pink
@property(class, nonatomic, readonly) UIColor *comboPinkColor;
// combo blue
@property(class, nonatomic, readonly) UIColor *comboBlueColor;
// combo green
@property(class, nonatomic, readonly) UIColor *comboGreenColor;
// combo red
@property(class, nonatomic, readonly) UIColor *comboRedColor;

@end

NS_ASSUME_NONNULL_END
