//
//  UTENSoundCheckModel+X.m
//  uten
//
//  Created by pht on 2024/6/14.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENSoundCheckModel+X.h"
#import "UIColor+X.h"
#import "NSString+X.h"

@implementation UTENSoundCheckModel (X)

// parse string to model, example: eat_dinner-E0AN1-05-s031-s056r-tr.mp3
- (void)parse:(const NSString *)string {
    self.origin = string;
    self.pathExtension = [string pathExtension];
    const NSString *stringWithoutPathExtension = [string stringByDeletingPathExtension];
    NSArray *array = [stringWithoutPathExtension componentsSeparatedByString:@"-"];
    if (array.count > 0) {
        self.sound = array[0];
    }
    if (array.count > 1) {
        self.className = array[1];
    }
    if (array.count > 2) {
        self.lesson = array[2];
    }
    if (array.count > 3) {
        self.speaker = array[3];
    }
    if (array.count > 4) {
        self.checker = array[4];
    }
    if (array.count > 5) {
        self.teacher = array[5];
    }
}

+ (instancetype)modelWithString:(const NSString *)string {
    UTENSoundCheckModel *model = [UTENSoundCheckModel new];
    [model parse:string];
    return model;
}

- (ConfirmResult)teacherConfirmResult {
    const NSUInteger length = [self.teacher length];
    if (length == 0) {
        return ConfirmResultUnknown;
    }
    NSUInteger numberOfMatches = [[UTENSoundCheckModel rightRegex] numberOfMatchesInString:self.teacher options:0 range:NSMakeRange(0, length)];
    if (numberOfMatches > 0) {
        return ConfirmResultRight;
    }
    numberOfMatches = [[UTENSoundCheckModel wrongRegex] numberOfMatchesInString:self.teacher options:0 range:NSMakeRange(0, length)];
    if (numberOfMatches > 0) {
        return ConfirmResultWrong;
    }
    return ConfirmResultUnknown;
}

- (void)setTeacherConfirmResult:(ConfirmResult)teacherConfirmResult {
    if (teacherConfirmResult == ConfirmResultRight) {
        self.teacher = @"tr";
        // self.teacher = [self.pureTeacher stringByAppendingString:@"r"];
    } else if (teacherConfirmResult == ConfirmResultWrong) {
        self.teacher = @"tw";
        // self.teacher = [self.pureTeacher stringByAppendingString:@"w"];
    }
}

- (NSString *)pureTeacher {
    // remove last r or w
    if ([self.teacher hasSuffix:@"r"]) {
        return [self.teacher substringToIndex:self.teacher.length - 1];
    }
    if ([self.teacher hasSuffix:@"w"]) {
        return [self.teacher substringToIndex:self.teacher.length - 1];
    }
    // return self.teacher ? self.teacher : @"";
    return self.teacher ?: @"";
}

- (ConfirmResult)checkerConfirmResult {
    const NSUInteger length = [self.checker length];
    if (length == 0) {
        return ConfirmResultUnknown;
    }
    NSUInteger numberOfMatches = [[UTENSoundCheckModel rightRegex] numberOfMatchesInString:self.checker options:0 range:NSMakeRange(0, length)];
    if (numberOfMatches > 0) {
        return ConfirmResultRight;
    }
    numberOfMatches = [[UTENSoundCheckModel wrongRegex] numberOfMatchesInString:self.checker options:0 range:NSMakeRange(0, length)];
    if (numberOfMatches > 0) {
        return ConfirmResultWrong;
    }
    return ConfirmResultUnknown;
}

- (void)setCheckerConfirmResult:(ConfirmResult)checkerConfirmResult {
    if (checkerConfirmResult == ConfirmResultRight) {
        self.checker = [self.pureChecker stringByAppendingString:@"r"];
    } else if (checkerConfirmResult == ConfirmResultWrong) {
        self.checker = [self.pureChecker stringByAppendingString:@"w"];
    }
}

// pure check
- (NSString *)pureChecker {
    // remove last r or w
    if ([self.checker hasSuffix:@"r"]) {
        return [self.checker substringToIndex:self.checker.length - 1];
    }
    if ([self.checker hasSuffix:@"w"]) {
        return [self.checker substringToIndex:self.checker.length - 1];
    }
    return self.checker ?: @"";
}


+ (NSRegularExpression *)rightRegex {
    static NSRegularExpression *regex = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        regex = [NSRegularExpression regularExpressionWithPattern:@"r$" options:NSRegularExpressionCaseInsensitive error:nil];
    });
    return regex;
}

// end with w regexp
+ (NSRegularExpression *)wrongRegex {
    static NSRegularExpression *regex = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        regex = [NSRegularExpression regularExpressionWithPattern:@"w$" options:NSRegularExpressionCaseInsensitive error:nil];
    });
    return regex;
}

- (NSString *)fullname {
    // string list
    NSMutableArray<NSString *> *list = @[].mutableCopy;
    [list addObject:self.sound ?: @""];
    [list addObject:self.className ?: @""];
    [list addObject:self.lesson ?: @""];
    [list addObject:self.speaker ?: @""];
    if ([NSString isNotEmpty:self.teacher]) {
        // 必須要加上 checker
        [list addObject:self.checker ?: @""];
        [list addObject:self.teacher ?: @""];
    } else {
        if ([NSString isNotEmpty:self.checker]) {
            [list addObject:self.checker ?: @""];
        }
    }
    return [NSString stringWithFormat:@"%@.%@", [list componentsJoinedByString:@"-"], self.pathExtension];
}

// prefix of sound, class, lesson, speaker
- (NSString *)prefix {
    NSMutableArray<NSString *> *list = @[].mutableCopy;
    if (self.sound) {
        [list addObject:self.sound];
    }
    if (self.className) {
        [list addObject:self.className];
    }
    if (self.lesson) {
        [list addObject:self.lesson];
    }
    if (self.speaker) {
        [list addObject:self.speaker];
    }
    return [list componentsJoinedByString:@"-"];
}

// get background color by teacher and checker confirm result
- (UIColor *)backgroundColor {
    const ConfirmResult teacherConfirmResult = self.teacherConfirmResult;
    if (teacherConfirmResult == ConfirmResultRight) {
        return UIColor.rightColor;
    }
    if (teacherConfirmResult == ConfirmResultWrong) {
        return UIColor.wrongColor;
    }
    const ConfirmResult checkerConfirmResult = self.checkerConfirmResult;
    if (checkerConfirmResult == ConfirmResultRight) {
        return UIColor.rightColor;
    }
    if (checkerConfirmResult == ConfirmResultWrong) {
        return UIColor.wrongColor;
    }
    return UIColor.whiteColor;
}

// is modified
// fullname != origin
- (BOOL)isModified {
    return ![self.fullname isEqualToString:self.origin];
}

@end
