#ifndef UTENAnimation_X_h
#define UTENAnimation_X_h

#import "UTENAnimation.h"

@class UIImage;

NS_ASSUME_NONNULL_BEGIN

@interface UTENAnimation (X)

// Add your custom animation methods here
// animation duration getter property
@property (nonatomic, assign, readonly) NSTimeInterval duration;
// uiimage list getter property
@property (nonatomic, strong, readonly) NSArray<UIImage *> *images;

@end

@interface UTENFrame (X)

// absolute path getter property
@property (nonatomic, copy, readonly) NSString *absolutePath;

@end

NS_ASSUME_NONNULL_END

#endif /* UTENAnimation_X_h */
