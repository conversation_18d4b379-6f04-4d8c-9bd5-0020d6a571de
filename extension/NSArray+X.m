#import "NSArray+X.h"

@implementation NSArray (X)

- (id)randomObject {
    if (self.count == 0) {
        return nil;
    }
    NSUInteger index = arc4random_uniform((u_int32_t)self.count);
    return self[index];
}

- (NSArray *)shuffle {
    NSMutableArray *copy = [self mutableCopy];
    for (NSUInteger i = copy.count; i > 1; i--) {
        [copy exchangeObjectAtIndex:i - 1 withObjectAtIndex:arc4random_uniform((u_int32_t)i)];
    }
    return [copy copy];
}

- (NSArray *)map:(id (^)(id obj))block {
    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
    for (id obj in self) {
        [result addObject:block(obj)];
    }
    return [result copy];
}

//- (NSArray *)map:(id (^)(id))block {
//    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
//    for (id obj in self) {
//        [result addObject:block(obj)];
//    }
//    return result;
//}
//
//- (NSArray *)map:(id (^)(id obj))block {
//    NSMutableArray *result = [[NSMutableArray alloc] initWithCapacity:self.count];
//    for (id obj in self) {
//        id mapped = block(obj);
//        if (mapped) {
//            [result addObject:mapped];
//        }
//    }
//    return result;
//}
//
//- (NSArray *)map:(id (^)(id))block {
//    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
//    for (id obj in self) {
//        [result addObject:block(obj)];
//    }
//    return result;
//}

- (NSArray *)filter:(BOOL (^)(id obj))block {
    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
    for (id obj in self) {
        if (block(obj)) {
            [result addObject:obj];
        }
    }
    return [result copy];
}

//- (NSArray *)filter:(BOOL (^)(id))block {
//    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
//    for (id obj in self) {
//        if (block(obj)) {
//            [result addObject:obj];
//        }
//    }
//    return result;
//}
//
//- (NSArray *)filter:(BOOL (^)(id obj))block {
//    NSMutableArray *result = [[NSMutableArray alloc] initWithCapacity:self.count];
//    for (id obj in self) {
//        if (block(obj)) {
//            [result addObject:obj];
//        }
//    }
//    return result;
//}
//
//- (NSArray *)filter:(BOOL (^)(id))block {
//    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
//    for (id obj in self) {
//        if (block(obj)) {
//            [result addObject:obj];
//        }
//    }
//    return result;
//}

- (id)reduce:(id)initial combine:(id (^)(id accumulator, id obj))block {
    id accumulator = initial;
    for (id obj in self) {
        accumulator = block(accumulator, obj);
    }
    return accumulator;
}

//- (id)reduce:(id)initial combine:(id (^)(id result, id obj))block {
//    id result = initial;
//    for (id obj in self) {
//        result = block(result, obj);
//    }
//    return result;
//}
//
//- (id)reduce:(id)initial combine:(id (^)(id, id))combine {
//    id result = initial;
//    for (id obj in self) {
//        result = combine(result, obj);
//    }
//    return result;
//}
//
//- (id)reduce:(id)initial block:(id (^)(id, id))block {
//    id result = initial;
//    for (id obj in self) {
//        result = block(result, obj);
//    }
//    return result;
//}

- (NSArray *)take:(NSUInteger)count {
    if (count >= self.count) {
        return self;
    }
    return [self subarrayWithRange:NSMakeRange(0, count)];
}

- (NSArray *)drop:(NSUInteger)count {
    if (count >= self.count) {
        return @[];
    }
    return [self subarrayWithRange:NSMakeRange(count, self.count - count)];
}

- (NSArray *)compact {
    return [self filter:^BOOL(id obj) {
        return obj != nil;
    }];
}

- (NSArray *)flatten {
    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
    for (id obj in self) {
        if ([obj isKindOfClass:[NSArray class]]) {
            [result addObjectsFromArray:[obj flatten]];
        } else {
            [result addObject:obj];
        }
    }
    return result;
}

- (NSArray *)flatMap:(NSArray *(^)(id))block {
    NSMutableArray *result = [NSMutableArray array];
    for (id obj in self) {
        [result addObjectsFromArray:block(obj)];
    }
    return result;
}

//- (NSArray *)flatMap:(NSArray *(^)(id obj))block {
//    NSMutableArray *result = [[NSMutableArray alloc] initWithCapacity:self.count];
//    for (id obj in self) {
//        NSArray *mapped = block(obj);
//        if (mapped) {
//            [result addObjectsFromArray:mapped];
//        }
//    }
//    return result;
//}

- (NSArray *)distinct {
    NSMutableArray *result = [NSMutableArray array];
    for (id obj in self) {
        if (![result containsObject:obj]) {
            [result addObject:obj];
        }
    }
    return result;
}

//- (NSArray *)distinct {
//    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
//    for (id obj in self) {
//        if (![result containsObject:obj]) {
//            [result addObject:obj];
//        }
//    }
//    return result;
//}

- (NSArray *)sorted:(NSComparator)block {
    return [self sortedArrayUsingComparator:block];
}

- (NSArray *)sortedBy:(id (^)(id))block {
    return [self sorted:^NSComparisonResult(id obj1, id obj2) {
        id val1 = block(obj1);
        id val2 = block(obj2);
        return [val1 compare:val2];
    }];
}

- (NSDictionary *)groupBy:(id<NSCopying> (^)(id obj))block {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];
    for (id obj in self) {
        id<NSCopying> key = block(obj);
        NSMutableArray *group = result[key];
        if (group == nil) {
            group = [NSMutableArray array];
            result[key] = group;
        }
        [group addObject:obj];
    }
    return [result copy];
}

//- (NSDictionary *)groupBy:(id (^)(id))block {
//    NSMutableDictionary *result = [NSMutableDictionary dictionary];
//    for (id obj in self) {
//        id key = block(obj);
//        NSMutableArray *group = result[key];
//        if (group == nil) {
//            group = [NSMutableArray array];
//            result[key] = group;
//        }
//        [group addObject:obj];
//    }
//    return result;
//}

- (NSArray *)groupByArray:(id (^)(id))block {
    return [[self groupBy:block] allValues];
}

- (NSArray *)groupByDictionary:(id (^)(id))block {
    return [[self groupBy:block] allKeys];
}

- (NSArray *)combine:(NSArray *)array {
    NSMutableArray *result = [NSMutableArray arrayWithArray:self];
    [result addObjectsFromArray:array];
    return result;
}

- (NSString *)join:(NSString *)separator {
    return [self componentsJoinedByString:separator];
}

- (BOOL)all:(BOOL (^)(id obj))block {
    for (id obj in self) {
        if (!block(obj)) {
            return NO;
        }
    }
    return YES;
}

- (BOOL)any:(BOOL (^)(id obj))block {
    for (id obj in self) {
        if (block(obj)) {
            return YES;
        }
    }
    return NO;
}

- (id)first:(BOOL (^)(id obj))block {
    for (id obj in self) {
        if (block(obj)) {
            return obj;
        }
    }
    return nil;
}

- (NSArray *)takeWhile:(BOOL (^)(id))block {
    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
    for (id obj in self) {
        if (block(obj)) {
            [result addObject:obj];
        } else {
            break;
        }
    }
    return result;
}

- (NSArray *)dropWhile:(BOOL (^)(id))block {
    NSMutableArray *result = [NSMutableArray arrayWithCapacity:self.count];
    BOOL taking = NO;
    for (id obj in self) {
        if (!taking && block(obj)) {
            continue;
        }
        taking = YES;
        [result addObject:obj];
    }
    return result;
}

@end
