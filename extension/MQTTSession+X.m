//
//  MQTTSession+X.m
//  uten
//
//  Created by yuming on 2024/10/13.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "MQTTSession+X.h"

@implementation MQTTSession (X)

- (RACSignal *)rac_connect {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [self connectWithConnectHandler:^(NSError *error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:@YES];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (RACSignal *)rac_disconnect {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [self closeWithDisconnectHandler:^(NSError *error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:@YES];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (RACSignal *)rac_subscribeToTopic:(NSString *)topic atLevel:(MQTTQosLevel)qosLevel {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [self subscribeToTopic:topic atLevel:qosLevel subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:@YES];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (RACSignal *)rac_unsubscribeTopic:(NSString *)topic {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [self unsubscribeTopic:topic unsubscribeHandler:^(NSError *error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:@YES];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (RACSignal *)rac_publishData:(NSData *)data onTopic:(NSString *)topic retain:(BOOL)retainFlag qos:(MQTTQosLevel)qos {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        [self publishData:data onTopic:topic retain:retainFlag qos:qos publishHandler:^(NSError *error) {
            if (error) {
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:@YES];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

// connection handler
- (RACSignal<NSNumber *> *)rac_connectionHandler {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        self.connectionHandler = ^(MQTTSessionEvent event) {
            [subscriber sendNext:@(event)];
        };
        return nil;
    }];
}

// message handler
- (RACSignal<RACTuple *> *)rac_messageHandler {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        self.messageHandler = ^(NSData *data, NSString *topic) {
            [subscriber sendNext:RACTuplePack(data, topic)];
        };
        return nil;
    }];
}

@end
