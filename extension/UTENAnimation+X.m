#import "UTENAnimation+X.h"
#import "NSArray+X.h"
#import <UIKit/UIKit.h>

@implementation UTENAnimation (X)

// Add your category methods here
- (NSTimeInterval)duration {
    NSNumber *duration = [self.frames reduce:@(0) combine:^id(NSNumber *accumulator, UTENFrame *frame) {
        return @(accumulator.doubleValue + frame.duration.doubleValue);
    }];
    return [duration doubleValue];
}

- (NSArray<UIImage *> *)images {
    return [self.frames reduce:@[] combine:^id(NSArray<UIImage *> *accumulator, UTENFrame *frame) {
        UIImage *image = [UIImage imageNamed:frame.absolutePath];
        return [accumulator arrayByAddingObject:image];
    }];
}

@end


@implementation UTENFrame (X)

- (NSString *)absolutePath {
    return [[NSBundle mainBundle] pathForResource:self.name ofType:nil];
}

@end
