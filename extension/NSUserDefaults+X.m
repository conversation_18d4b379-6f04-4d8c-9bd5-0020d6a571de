//
//  NSUserDefaults+X.m
//  uten
//
//  Created by pht on 2024/6/16.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "NSUserDefaults+X.h"
#import "UTENConstants.h"

@implementation NSUserDefaults (X)

// identifier getter
- (NSNumber *)identifier {
    // NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    // return [defaults objectForKey:KEY_ID];
    return [self objectForKey:KEY_ID];
}

// identifier setter
- (void)setIdentifier:(NSString *)identifier {
    // NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    // [defaults setObject:identifier forKey:KEY_ID];
    [self setObject:identifier forKey:KEY_ID];
}

// type getter
- (NSString *)type {
    return [self objectForKey:KEY_TYPE];
}

// type setter
- (void)setType:(NSString *)type {
    [self setObject:type forKey:KEY_TYPE];
}

// cname getter
- (NSString *)cname {
    return [self objectForKey:KEY_CNAME];
}

// cname setter
- (void)setCname:(NSString *)cname {
    [self setObject:cname forKey:KEY_CNAME];
}

// ename getter
- (NSString *)ename {
    return [self objectForKey:KEY_ENAME];
}

// ename setter
- (void)setEname:(NSString *)ename {
    [self setObject:ename forKey:KEY_ENAME];
}

// Role enum property read-only
- (Role)role {
    return [[self type] integerValue];
}

// is student
- (BOOL)isStudent {
    return [self role] == RoleStudent;
}

// is master
- (BOOL)isMaster {
    NSArray *roles = @[
        @(RoleProgrammer), 
        @(RoleManager), 
        @(RoleTeacher),
    ];
    return [roles containsObject:@([self role])];
}

// image path
- (NSString *)imagePath {
    if ([self isStudent]) {
        return @"SoundCheck 05.png";
    }
    return @"SoundCheck 06.png";
}

@end
