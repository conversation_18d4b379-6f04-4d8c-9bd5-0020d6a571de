//
//  MQTTSession+X.h
//  uten
//
//  Created by yuming on 2024/10/13.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <ReactiveObjC/ReactiveObjC.h>
#import <MQTTClient.h>

NS_ASSUME_NONNULL_BEGIN

@interface MQTTSession (X)

- (RACSignal *)rac_connect;
- (RACSignal *)rac_disconnect;
- (RACSignal *)rac_subscribeToTopic:(NSString *)topic atLevel:(MQTTQosLevel)qos;
- (RACSignal *)rac_unsubscribeTopic:(NSString *)topic;
- (RACSignal *)rac_publishData:(NSData *)data onTopic:(NSString *)topic retain:(BOOL)retainFlag qos:(MQTTQosLevel)qos;
// connection handler
- (RACSignal<NSNumber *> *)rac_connectionHandler;
// message handler
- (RACSignal<RACTuple *> *)rac_messageHandler;
@end

NS_ASSUME_NONNULL_END
