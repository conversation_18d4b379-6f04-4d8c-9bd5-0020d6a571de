//
//  UTENBasicModel+X.m
//  uten
//
//  Created by pht on 2024/6/3.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENBasicModel+X.h"

@implementation UTENBasicModel (X)

// color
- (UIColor *)color {
    if ([self.result isEqualToNumber:@0]) {
        return UIColor.whiteColor;
    }
    if ([self.result isEqualToNumber:@1]) {
        // R: 217, G: 228, B: 131
        return [UIColor colorWithRed:217.0/255.0 green:228.0/255.0 blue:131.0/255.0 alpha:1.0];
    }
    // if result == 2 return R:244, G181, B:208
    if ([self.result isEqualToNumber:@2]) {
        // R: 244, G: 181, B: 208
        return [UIColor colorWithRed:244.0/255.0 green:181.0/255.0 blue:208.0/255.0 alpha:1.0];
    }
    return UIColor.whiteColor;
}

@end
