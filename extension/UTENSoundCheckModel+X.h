//
//  UTENSoundCheckModel+X.h
//  uten
//
//  Created by pht on 2024/6/14.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENSoundCheckModel.h"
#import "UTENEnum.h"

@class UIColor;

NS_ASSUME_NONNULL_BEGIN

@interface UTENSoundCheckModel (X)

@property (nonatomic, assign) ConfirmResult teacherConfirmResult;
@property (nonatomic, assign) ConfirmResult checkerConfirmResult;
// read-only fullname property
@property (nonatomic, copy, readonly) NSString *fullname;
// read-only prefix property
@property (nonatomic, copy, readonly) NSString *prefix;
// read-only backgroudColor property
@property (nonatomic, copy, readonly) UIColor *backgroundColor;
// is modified
@property (nonatomic, assign, readonly) BOOL isModified;

+ (instancetype)modelWithString:(const NSString *)string;
- (void)parse:(const NSString *)string;

@end

NS_ASSUME_NONNULL_END
