#import "UTENRect+X.h"
#import "UTENConstants.h"
#import "UTENRect.h"
#import <CoreGraphics/CoreGraphics.h>

@implementation UTENRect (X)

// 判斷點是否在矩形內
- (BOOL)containsPoint:(CGPoint)point {
    CGRect rect = CGRectMake(self.x.floatValue, self.y.floatValue, self.width.floatValue, self.height.floatValue);
    return CGRectContainsPoint(rect, point);
}

- (BOOL)containsX:(CGFloat)x {
    return self.x.floatValue <= x && x <= self.x.floatValue + self.width.floatValue;
}

- (BOOL)containsY:(CGFloat)y {
    return self.y.floatValue <= y && y <= self.y.floatValue + self.height.floatValue;
}

@end
