#import "UTENFile+X.h"

@implementation UTENFile (X)

// Add your category methods here
- (ImageType)imageType {
    if (self.name != nil) {
        // 轉換成小寫並取得副檔名
        const NSString *pathExtension = self.name.lowercaseString.pathExtension;
        if ([pathExtension isEqualToString:@"png"]) {
            return ImageTypePNG;
        }
        if ([pathExtension isEqualToString:@"jpg"]) {
            return ImageTypeJPG;
        }
        // gif
        if ([pathExtension isEqualToString:@"gif"]) {
            return ImageTypeGIF;
        }
        // mp4
        if ([pathExtension isEqualToString:@"mp4"]) {
            return ImageTypeMP4;
        }
    }
    return ImageTypeUnknown;
}

- (NSString *)absolutePath {
    return [[NSBundle mainBundle] pathForResource:self.name ofType:nil];
}

- (NSData *)data {
    return [NSData dataWithContentsOfFile:self.absolutePath];
}

- (NSURL *)url {
    return [NSURL fileURLWithPath:self.absolutePath];
}

@end