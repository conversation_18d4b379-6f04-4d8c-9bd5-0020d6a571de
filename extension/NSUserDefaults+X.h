//
//  NSUserDefaults+X.h
//  uten
//
//  Created by pht on 2024/6/16.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UTENEnum.h"

NS_ASSUME_NONNULL_BEGIN

@interface NSUserDefaults (X)
// identifier string property 1 from qrcode table
@property (nonatomic, copy) NSNumber *identifier;
// type string property 0 from qrcode table
@property (nonatomic, copy) NSString *type;
// cname string property 簡先生程式人員 from qrcode table
@property (nonatomic, copy) NSString *cname;
// ename string property 簡先生程式人員 from qrcode table
@property (nonatomic, copy) NSString *ename;
// Role enum property read-only
@property (nonatomic, assign, readonly) Role role;
// is student property read-only
@property (nonatomic, assign, readonly) BOOL isStudent;
// is master property read-only
@property (nonatomic, assign, readonly) BOOL isMaster;
// image file path property
@property (nonatomic, copy) NSString *imagePath;
@end

NS_ASSUME_NONNULL_END
