//
//  UTENCommand+X.h
//  uten
//
//  Created by yuming on 2024/10/13.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UTENCommand.h"

NS_ASSUME_NONNULL_BEGIN

@interface UTENCommand (X)
// + (instancetype)cmdWithUserDefaults:(NSUserDefaults *)defaults;
// is bye readonly property
@property (nonatomic, readonly) BOOL isLogin;
@property (nonatomic, readonly) BOOL isLogout;
@property (nonatomic, readonly) BOOL isStart;
@property (nonatomic, readonly) BOOL isBye;
@property (nonatomic, readonly) BOOL isRollCall;
@property (nonatomic, readonly) BOOL isClassStart;
@property (nonatomic, readonly) BOOL isClassStop;
@end

NS_ASSUME_NONNULL_END
