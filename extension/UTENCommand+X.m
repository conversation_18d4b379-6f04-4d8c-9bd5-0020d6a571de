//
//  UTENCommand+X.m
//  uten
//
//  Created by yuming on 2024/10/13.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENCommand+X.h"
#import "UTENEnum.h"
// #import "NSUserDefaults+X.h"

@implementation UTENCommand (X)

// + (instancetype)cmdWithUserDefaults:(NSUserDefaults *)defaults {
//     UTENCommand *cmd = [UTENCommand new];
//     cmd.identifier = [defaults identifier];
//     cmd.type = [defaults type];
//     cmd.cname = [defaults cname];
//     cmd.ename = [defaults ename];
//     return cmd;
// }

// is login
- (BOOL)isLogin {
    return [self.name isEqualToString:CmdLogin];
}

// is logout
- (BOOL)isLogout {
    return [self.name isEqualToString:CmdLogout];
}

// is start
- (BOOL)isStart {
    return [self.name isEqualToString:CmdStart];
}

// is stop
- (BOOL)isStop {
    return [self.name isEqualToString:CmdStop];
}

// is bye
- (BOOL)isBye {
    return [self.name isEqualToString:CmdBye];
}

// is roll call
- (BOOL)isRollCall {
    return [self.name isEqualToString:CmdRollCall];
}

// is class start
- (BOOL)isClassStart {
    return [self.name isEqualToString:CmdClassStart];
}

// is class stop
- (BOOL)isClassStop {
    return [self.name isEqualToString:CmdClassStop];
}

@end
