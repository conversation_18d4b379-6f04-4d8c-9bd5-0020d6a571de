//
//  UTENConfig.m
//  uten
//
//  Created by pht on 2025/2/19.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import "UTENConfig.h"

@interface UTENConfig()
@property (nonatomic, strong) NSDictionary *configDictionary;
@end

@implementation UTENConfig

+ (instancetype)sharedInstance {
    static UTENConfig *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[UTENConfig alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        NSString *path = [[NSBundle mainBundle] pathForResource:@"assets/config" ofType:@"plist"];
        self.configDictionary = [NSDictionary dictionaryWithContentsOfFile:path];
    }
    return self;
}

- (NSString *)valueForKey:(NSString *)key {
    return self.configDictionary[key];
}

@end
