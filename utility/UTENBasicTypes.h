// ref: http://fuckingblocksyntax.com/
// ref: https://stackoverflow.com/a/9201774
// ref: https://gist.github.com/swizzlr/6268955

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^VoidCallback)(void);
typedef void (^ValueChanged)(id);
typedef void (^ValueSetter)(id);
typedef id _Nullable (^ValueGetter)(void);
//typedef id _Nonnull (^ValueGetter)(void);
// predicate
typedef BOOL (^Predicate)(id);
typedef void (^Completion)(BOOL, NSError * _Nullable);

NS_ASSUME_NONNULL_END
