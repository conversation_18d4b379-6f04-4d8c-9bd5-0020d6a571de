//
//  UTENPlayerGroup.m
//  uten
//
//  Created by yuming on 2024/8/17.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENPlayerGroup.h"
#import "UTENFile.h"
#import "UTENFile+X.h"
#import <AVFoundation/AVFoundation.h>
#import <UIKit/UIKit.h>
#import <FLAnimatedImage/FLAnimatedImage.h>
#import <FLAnimatedImage/FLAnimatedImageView.h>
#import <ReactiveObjC/ReactiveObjC.h>

@interface UTENPlayerGroup()
// player layer property
@property (nonatomic, strong) AVPlayerLayer *playerLayer;
// gif image view property
@property (nonatomic, strong) FLAnimatedImageView *gifImageView;
@end

@implementation UTENPlayerGroup

// init
- (instancetype)init {
    self = [super init];
    if (self) {
        _list = [NSMutableArray array];
        _realIndex = NSNotFound; // -1
        _realIndex = 0;
    }
    return self;
}

- (NSInteger)currentIndex {
    if (self.list.count > 0) {
        return self.realIndex % self.list.count;
    }
    return NSNotFound;
}

- (void)play {
    if (self.currentIndex != NSNotFound) {
        // 取得目前的文件
        UTENFile *file = self.list[self.currentIndex];
        NSArray *basicImageTypes = @[
            @(ImageTypeJPG), 
            @(ImageTypePNG),
        ];
        if ([basicImageTypes containsObject:@(file.imageType)]) {
            [self playFile:file];
        } else if (file.imageType == ImageTypeGIF) {
            [self playGif:file];
        } else if (file.imageType == ImageTypeMP4) {
            // 播放视频
            [self playVideo:file];
        }
    }
}

- (void)playFile:(UTENFile *)file {
    // 播放图片
    UIImage *image = [UIImage imageWithData:file.data];
    UIImageView *backgroundImageView = [[UIImageView alloc] initWithImage:image];
    backgroundImageView.frame = self.view.bounds;
    // aspect fill
    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    // insert 到最下層
    [self.view insertSubview:backgroundImageView atIndex:0];
}

- (void)playGif:(UTENFile *)file {
    // 播放 GIF
    FLAnimatedImage *image = [FLAnimatedImage animatedImageWithGIFData:file.data];
    FLAnimatedImageView *backgroundImageView = [[FLAnimatedImageView alloc] init];
    backgroundImageView.animatedImage = image;
    backgroundImageView.frame = self.view.bounds;
    self.gifImageView = backgroundImageView;
    [self.view insertSubview:backgroundImageView atIndex:0];
    // 設置播放次数
    // image.loopCount = 1; // readonly
    // 設置播放完成後的 block
    @weakify(self);
    self.gifImageView.loopCompletionBlock = ^(NSUInteger loopCountRemaining) {
        @strongify(self);
        [self playNext];
    };
}

- (void)playVideo:(UTENFile *)file {
    AVPlayerItem *playerItem = [AVPlayerItem playerItemWithURL:file.url];
    // 添加播放结束通知观察者
    [[NSNotificationCenter defaultCenter] addObserver:self
                                            selector:@selector(playerItemDidReachEnd:)
                                                name:AVPlayerItemDidPlayToEndTimeNotification
                                            object:playerItem];
    // 建立播放器
    AVPlayer *player = [AVPlayer playerWithPlayerItem:playerItem];
    self.playerLayer = [AVPlayerLayer playerLayerWithPlayer:player];
    if (self.view) {
        self.playerLayer.frame = self.view.bounds;
        [self.view.layer insertSublayer:self.playerLayer atIndex:0];
    }
    // 播放
    [player play];
}

- (void)playerItemDidReachEnd:(NSNotification *)notification {
    // 移除观察者
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:notification.object];
    [self playNext];
}

- (void)playNext {
    [self beforePlay];
    // 更新当前视频索引
    _realIndex++;
    // 播放下一个视频
    [self play];
}

- (void)beforePlay {
    // 移除所有通知观察者
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    // 移除目前的播放器
    if (self.playerLayer) {
        [self.playerLayer removeFromSuperlayer];
    }
    // 移除目前的 GIF
    if (self.gifImageView) {
        [self.gifImageView removeFromSuperview];
    }
}

@end
