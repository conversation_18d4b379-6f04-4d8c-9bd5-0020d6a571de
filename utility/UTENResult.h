//
//  UTENResult.h
//  uten
//
//  Created by pht on 2024/7/2.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, ResultType) {
    ResultTypeLeft,
    ResultTypeRight
};

@interface UTENResult<__covariant Left, __covariant Right> : NSObject

@property (nonatomic, readonly, assign) ResultType type;
@property (nonatomic, readonly, nullable) Left left;
@property (nonatomic, readonly, nullable) Right right;

+ (instancetype)left:(Left)value;
+ (instancetype)right:(Right)value;

@end

// @interface UTENLeft<Left> : UTENResult<Left, NSNull *>
// + (instancetype)value:(Left)value;
// @end

// @interface UTENRight<Right> : UTENResult<NSNull *, Right>
// + (instancetype)value:(Right)value;
// @end

NS_ASSUME_NONNULL_END
