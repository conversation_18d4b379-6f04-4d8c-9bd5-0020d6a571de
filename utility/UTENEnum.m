#import "UTENEnum.h"


NSString *displayForWriteModule(WriteModule module) {
    switch (module) {
        case WriteModuleDefault:
            return @"預設";
        case WriteModuleCombo:
            return @"連擊99";
        case WriteModuleBomb:
            return @"轟炸竹東";
        case WriteModuleFourPickOne:
            return @"四選一";
        default:
            return @"未知";
    }
}

NSArray<NSNumber *> *availableWriteModules(void) {
    return @[
        @(WriteModuleDefault),
        @(WriteModuleCombo),
        @(WriteModuleBomb),
        // @(WriteModuleFourPickOne),
    ];
}


NSString * const CmdLogin = @"LOGIN";
NSString * const CmdLogout = @"LOGOUT";
NSString * const CmdStart = @"START";
NSString * const CmdStop = @"STOP";
NSString * const CmdBye = @"BYE";
NSString * const CmdRollCall = @"ROLL_CALL";
NSString * const CmdClassStart = @"CLASS_START";
NSString * const CmdClassStop = @"CLASS_STOP";
