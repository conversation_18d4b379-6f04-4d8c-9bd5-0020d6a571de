//
//  UTENResult.m
//  uten
//
//  Created by pht on 2024/7/2.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENResult.h"

@implementation UTENResult

+ (instancetype)left:(id)value {
    UTENResult *result = [[self alloc] init];
    result->_type = ResultTypeLeft;
    result->_left = value;
    return result;
}

+ (instancetype)right:(id)value {
    UTENResult *result = [[self alloc] init];
    result->_type = ResultTypeRight;
    result->_right = value;
    return result;
}

@end

// @implementation UTENRight

// + (instancetype)value:(id)value {
//     UTENRight *right = [[self alloc] init];
//     right->_right = value;
//     return right;
// }

// @end

// @implementation UTENLeft

// + (instancetype)left:(id)value {
//     UTENLeft *left = [[self alloc] init];
//     left->_left = value;
//     return left;
// }

// @end
