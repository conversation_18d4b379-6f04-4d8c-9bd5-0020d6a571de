//
//  UTENConstants.m
//  uten
//
//  Created by pht on 2024/6/4.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENConstants.h"
#import "UTENEnum.h"

NSString * const KEY_ID = @"ID";
NSString * const KEY_TYPE = @"TYPE";
NSString * const KEY_CNAME = @"CNAME";
NSString * const KEY_ENAME = @"ENAME";

@implementation UTENConstants

+ (NSString *)host {
    return @"uten.synology.me";
}

+ (NSNumber *)dbPort {
    return @3307;
}

+ (NSString *)dbName {
    return @"uten";
}

+ (NSString *)dbUser {
    return @"uten";
}

+ (NSString *)dbPassword {
    return @"1qazXSW@3edcVFR$";
}

+ (NSString *)ftpUser {
    return @"uten";
}

+ (NSString *)ftpPassword {
    return @"zZ54775178";
}

+ (NSString *)ftpHost {
    return @"uten.synology.me";
}

+ (NSString *)ftpPort {
    return @"21";
}

+ (NSString *)KEY_ID {
    return @"ID";
}

+ (NSString *)ftpUploadPath {
    return @"/uten/upload";
}

// + (NSString *)ftpRoot {
//     return @"/";
// }

// + (NSString *)ftpRootPath {
//     return @"/";
// }

// + (NSString *)ftpRootPathWithSlash {
//     return @"/";
// }

// + (NSString *)ftpRootPathWithoutSlash {
//     return @"";
// }

// + (NSString *)ftpRootPathWithSlashAndPath:(NSString *)path {
//     return [NSString stringWithFormat:@"/%@", path];
// }

+ (NSArray *)masterGroup {
    return @[
        @(RoleProgrammer),
        @(RoleManager),
        @(RoleTeacher),
    ];
}

@end
