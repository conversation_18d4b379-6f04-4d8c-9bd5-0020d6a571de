//
//  UTENUtility.h
//  uten
//
//  Created by pht on 2024/7/2.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <ReactiveObjC/ReactiveObjC.h>

@class UTENDialogArgs;

NS_ASSUME_NONNULL_BEGIN

@interface UTENUtility : NSObject

// usage:
// [[UTENUtility showAlert:args] subscribeNext:^(RACTwoTuple<NSNumber *, NSError *> *res) {
//     NSLog(@"show alert");
//     if (res.second) {
//         NSLog(@"error: %@", res.second);
//     } else {
//         NSLog(@"success: %@", res.first);
//     }
// }];
+ (RACSignal<RACTwoTuple<NSNumber *, NSError *> *> *)showAlert:(UTENDialogArgs *)args;
+ (RACSignal<RACTwoTuple<NSNumber *, NSError *> *> *)showConfirm:(UTENDialogArgs *)args;
+ (RACSignal<RACTwoTuple<NSString *, NSError *> *> *)showPrompt:(UTENDialogArgs *)args;
// show options with array of strings
+ (RACSignal<RACTwoTuple<NSNumber *, NSError *> *> *)show:(UIViewController *)vc withOptions:(NSArray<NSString *> *)options;
// 0..<1
+ (CGFloat)randomValue;
+ (CGFloat)randomValueBetween:(CGFloat)min andMax:(CGFloat)max;
+ (NSUInteger)randomIndexWithCount:(NSUInteger)count;

@end

NS_ASSUME_NONNULL_END
