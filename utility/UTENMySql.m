//
//  UTENMySql.m
//  uten
//
//  Created by pht on 2024/6/4.
//  Copyright 2024 bekubee. All rights reserved.
//

#import "UTENMySql.h"
#import "UTENConstants.h"
#import "UTENQrcodeModel.h"
#import "UTENNclassModel.h"
#import "UTENNstudentModel.h"
#import "UTENNteacherModel.h"
#import "UTENNhistoryModel.h"
#import "UTENMediaModel.h"
#import "NSMutableDictionary+X.h"
#import <OHMySQL/OHMySQL.h>

@interface UTENMySql ()
@property (nonatomic, strong) OHMySQLUser *user;
@end

@implementation UTENMySql

// shared instance
+ (instancetype)sharedInstance {
    static UTENMySql *shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[UTENMySql alloc] init];
    });
    return shared;
}

// init
- (instancetype)init {
    self = [super init];
    if (self) {
        _user = [[OHMySQLUser alloc] initWithUserName:UTENConstants.dbUser
            password:UTENConstants.dbPassword
            serverName:UTENConstants.host
            dbName:UTENConstants.dbName
            port:UTENConstants.dbPort.integerValue
            socket:@"/run/mysqld/mysqld10.sock"
        ];
    }
    return self;
}

// fetch pure data
- (RACSignal<NSArray *> *)fetchData:(NSString *)tableName {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:self.user];
        [coordinator connect];
        [coordinator setEncoding:CharsetEncodingUTF8MB4];
        OHMySQLQueryRequest *request = [OHMySQLQueryRequestFactory SELECT:tableName condition:nil];
        OHMySQLQueryContext *manager = [[OHMySQLQueryContext alloc] init];
        manager.storeCoordinator = coordinator;
        NSError *error = nil;
        NSArray *response = [manager executeQueryRequestAndFetchResult:request error:&error];
        [coordinator disconnect];
        NSLog(@"%@", response);
        if (error) {
            [subscriber sendError:error];
        } else {
            [subscriber sendNext:response];
            [subscriber sendCompleted];
        }
        return [RACDisposable disposableWithBlock:^{
            NSLog(@"clean up");
        }];
    }];
}

- (RACSignal<NSArray<UTENQrcodeModel *> *> *)fetchQrCode {
    return [[self fetchData:@"qrcode"] map:^id(NSArray *response) {
        // NSMutableArray *qrCodeArray = [NSMutableArray array];
        NSMutableArray *qrCodeArray = @[].mutableCopy;
        for (NSDictionary *item in response) {
            UTENQrcodeModel *qrCode = [UTENQrcodeModel fromJSONDictionary:item];
            [qrCodeArray addObject:qrCode];
        }
        // nsmutablearray 轉換成 nsarray
        return qrCodeArray.copy;
    }];
}

// fetch nclass data
- (RACSignal<NSArray<UTENNclassModel *> *> *)fetchNclass {
    return [[self fetchData:@"nclass"] map:^id(NSArray *response) {
        NSMutableArray *nclassArray = @[].mutableCopy;
        for (NSDictionary *item in response) {
            UTENNclassModel *nclass = [UTENNclassModel fromJSONDictionary:item];
            [nclassArray addObject:nclass];
        }
        return nclassArray.copy;
    }];
}

// fetch nstudent data
- (RACSignal<NSArray<UTENNstudentModel *> *> *)fetchNstudent {
    return [[self fetchData:@"nstudent"] map:^id(NSArray *response) {
        NSMutableArray *nstudentArray = @[].mutableCopy;
        for (NSDictionary *item in response) {
            UTENNstudentModel *nstudent = [UTENNstudentModel fromJSONDictionary:item];
            [nstudentArray addObject:nstudent];
        }
        return nstudentArray.copy;
    }];
}

// fetch nteacher data
- (RACSignal<NSArray<UTENNteacherModel *> *> *)fetchNteacher {
    return [[self fetchData:@"nteacher"] map:^id(NSArray *response) {
        NSMutableArray *nteacherArray = @[].mutableCopy;
        for (NSDictionary *item in response) {
            UTENNteacherModel *nteacher = [UTENNteacherModel fromJSONDictionary:item];
            [nteacherArray addObject:nteacher];
        }
        return nteacherArray.copy;
    }];
}

// fetch nhistory data
- (RACSignal<NSArray<UTENNhistoryModel *> *> *)fetchNhistory {
    return [[self fetchData:@"nhistory"] map:^id(NSArray *response) {
        NSMutableArray *nhistoryArray = @[].mutableCopy;
        for (NSDictionary *item in response) {
            UTENNhistoryModel *nhistory = [UTENNhistoryModel fromJSONDictionary:item];
            [nhistoryArray addObject:nhistory];
        }
        return nhistoryArray.copy;
    }];
}

// media
- (RACSignal<NSArray<UTENMediaModel *> *> *)fetchMedia {
    return [[self fetchData:@"medias"] map:^id(NSArray *value) {
        NSMutableArray<UTENMediaModel *> *result = [NSMutableArray array];
        for (NSDictionary *dict in value) {
            UTENMediaModel *model = [UTENMediaModel fromJSONDictionary:dict];
            [result addObject:model];
        }
        return result;
    }];
}

// put media data
- (RACSignal *)putMedia:(UTENMediaModel *)media {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:self.user];
            [coordinator connect];
            [coordinator setEncoding:CharsetEncodingUTF8MB4];
            
            NSError *error = nil;
            NSDictionary *jsonDict = [media JSONDictionary];
            if (error) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [subscriber sendError:error];
                });
                [coordinator disconnect];
                return;
            }
            
            NSMutableDictionary *params = jsonDict.mutableCopy;
            [params removeNullValues];  // Remove null values
            
            // Escape single quotes in string values
            [params enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
                if ([obj isKindOfClass:[NSString class]]) {
                    NSString *escapedString = [obj stringByReplacingOccurrencesOfString:@"'" withString:@"''"];
                    params[key] = escapedString;
                }
            }];
            
            OHMySQLQueryRequest *request = [OHMySQLQueryRequestFactory INSERT:@"medias" set:params];
            OHMySQLQueryContext *manager = [[OHMySQLQueryContext alloc] init];
            manager.storeCoordinator = coordinator;
            
            NSError *queryError = nil;
            [manager executeQueryRequest:request error:&queryError];
            [coordinator disconnect];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if (queryError) {
                    [subscriber sendError:queryError];
                } else {
                    [subscriber sendNext:@(YES)];
                    [subscriber sendCompleted];
                }
            });
        });
        
        return nil;
    }];
}

@end
