//
//  UTENMySql.h
//  uten
//
//  Created by pht on 2024/6/4.
//  Copyright 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <ReactiveObjC/ReactiveObjC.h>

@class UTENQrcodeModel;
@class UTENNclassModel;
@class UTENNstudentModel;
@class UTENNteacherModel;
@class UTENNhistoryModel;
@class UTENMediaModel;

NS_ASSUME_NONNULL_BEGIN

@interface UTENMySql : NSObject
+ (instancetype)sharedInstance;
- (RACSignal<NSArray<UTENQrcodeModel *> *> *)fetchQrCode;
- (RACSignal<NSArray<UTENNclassModel *> *> *)fetchNclass;
- (RACSignal<NSArray<UTENNstudentModel *> *> *)fetchNstudent;
// teacher
- (RACSignal<NSArray<UTENNteacherModel *> *> *)fetchNteacher;
// nhistory
- (RACSignal<NSArray<UTENNhistoryModel *> *> *)fetchNhistory;
// media

// usage:
// [[[UTENMySql sharedInstance] fetchMedia] subscribeNext:^(NSArray<UTENMediaModel *> *mediaArray) {
//     // Handle the media array here
//     NSLog(@"mediaArray: %@", mediaArray);
// }];
- (RACSignal<NSArray<UTENMediaModel *> *> *)fetchMedia;
// usage:
// [[[UTENMySql sharedInstance] putMedia:media] subscribeNext:^(id x) {
//     // Handle success
//     NSLog(@"Success: %@", x);
// } error:^(NSError *error) {
//     // Handle error
//     NSLog(@"Error: %@", error);
// }];
- (RACSignal *)putMedia:(UTENMediaModel *)media;
@end

NS_ASSUME_NONNULL_END
