//
//  UTENUtility.m
//  uten
//
//  Created by pht on 2024/7/2.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "UTENUtility.h"
#import "UTENDialogArgs.h"
#import "UTENEnum.h"
#import <ReactiveObjC/ReactiveObjC.h>
#import <UIKit/UIKit.h>

@implementation UTENUtility

+ (RACSignal<RACTwoTuple<NSNumber *, NSError *> *> *)showAlert:(UTENDialogArgs *)args {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:args.title ?: @""
            message:args.message ?: @"" 
            preferredStyle:UIAlertControllerStyleAlert
        ];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:args.okButton ?: @"OK"
            style:UIAlertActionStyleDefault 
            handler:^(UIAlertAction * _Nonnull action) {
                [subscriber sendNext:[RACTwoTuple pack:@(ButtonOkay) :nil]];
                [subscriber sendCompleted];
            }
        ];
        [alert addAction:okAction];
        UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
        [rootViewController presentViewController:alert animated:YES completion:nil];
        return [RACDisposable disposableWithBlock:^{
            [alert dismissViewControllerAnimated:YES completion:nil];
        }];
    }];
}

// 顯示確認對話框
+ (RACSignal<RACTwoTuple<NSNumber *, NSError *> *> *)showConfirm:(UTENDialogArgs *)args {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:args.title ?: @""
            message:args.message ?: @"" 
            preferredStyle:UIAlertControllerStyleAlert
        ];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:args.okButton ?: @"OK"
            style:UIAlertActionStyleDefault 
            handler:^(UIAlertAction * _Nonnull action) {
                [subscriber sendNext:[RACTwoTuple pack:@(ButtonOkay) :nil]];
                [subscriber sendCompleted];
            }
        ];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:args.cancelButton ?: @"Cancel"
            style:UIAlertActionStyleCancel 
            handler:^(UIAlertAction * _Nonnull action) {
                [subscriber sendNext:[RACTwoTuple pack:@(ButtonCancel) :nil]];
                [subscriber sendCompleted];
            }
        ];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
        [rootViewController presentViewController:alert animated:YES completion:nil];
        return [RACDisposable disposableWithBlock:^{
            [alert dismissViewControllerAnimated:YES completion:nil];
        }];
    }];
}

// 顯示輸入框
+ (RACSignal<RACTwoTuple<NSString *, NSError *> *> *)showPrompt:(UTENDialogArgs *)args {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:args.title ?: @""
            message:args.message ?: @"" 
            preferredStyle:UIAlertControllerStyleAlert
        ];
        [alert addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
            textField.placeholder = args.placeholder ?: @"";
        }];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:args.okButton ?: @"OK"
            style:UIAlertActionStyleDefault 
            handler:^(UIAlertAction * _Nonnull action) {
                [subscriber sendNext:[RACTwoTuple pack:alert.textFields.firstObject.text :nil]];
                [subscriber sendCompleted];
            }
        ];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:args.cancelButton ?: @"Cancel"
            style:UIAlertActionStyleCancel 
            handler:^(UIAlertAction * _Nonnull action) {
                [subscriber sendNext:[RACTwoTuple pack:@"" :nil]];
                [subscriber sendCompleted];
            }
        ];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
        [rootViewController presentViewController:alert animated:YES completion:nil];
        return [RACDisposable disposableWithBlock:^{
            [alert dismissViewControllerAnimated:YES completion:nil];
        }];
    }];
}

+ (CGFloat)randomValue {
    // 0..<1
    // return ((CGFloat)arc4random() / 0x100000000);
    return [UTENUtility randomIndexWithCount:100] / 100.0;
}

+ (CGFloat)randomValueBetween:(CGFloat)min andMax:(CGFloat)max {
    return UTENUtility.randomValue * (max - min) + min;
}

+ (NSUInteger)randomIndexWithCount:(NSUInteger)count {
    return arc4random_uniform((uint32_t)count);
}

+ (RACSignal<RACTwoTuple<NSNumber *, NSError *> *> *)show:(UIViewController *)vc withOptions:(NSArray<NSString *> *)options {
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil
            message:nil 
            preferredStyle:UIAlertControllerStyleActionSheet
        ];
        for (int i = 0; i < options.count; i++) {
            UIAlertAction *action = [UIAlertAction actionWithTitle:options[i]
                style:UIAlertActionStyleDefault 
                handler:^(UIAlertAction * _Nonnull action) {
                    [subscriber sendNext:[RACTwoTuple pack:@(i) :nil]];
                    [subscriber sendCompleted];
                }
            ];
            [alert addAction:action];
        }
        // UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"Cancel"
        //     style:UIAlertActionStyleCancel 
        //     handler:^(UIAlertAction * _Nonnull action) {
        //         [subscriber sendNext:[RACTwoTuple pack:@(options.count) :nil]];
        //         [subscriber sendCompleted];
        //     }
        // ];
        // [alert addAction:cancelAction];
        // UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
        // [rootViewController presentViewController:alert animated:YES completion:nil];
        // For iPad, you need to specify the source view and source rect for the popover presentation
        alert.popoverPresentationController.sourceView = vc.view;
        alert.popoverPresentationController.sourceRect = CGRectMake(vc.view.bounds.size.width / 2.0, vc.view.bounds.size.height / 2.0, 1.0, 1.0);
        [vc presentViewController:alert animated:YES completion:nil];
        return [RACDisposable disposableWithBlock:^{
            [alert dismissViewControllerAnimated:YES completion:nil];
        }];
    }];
}

@end
