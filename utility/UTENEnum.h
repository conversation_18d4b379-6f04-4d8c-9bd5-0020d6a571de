//
//  Enum.h
//  uten
//
//  Created by pht on 2024/6/15.
//  Copyright © 2024 bekubee. All rights reserved.
//
#import <Foundation/Foundation.h>

#ifndef Enum_h
#define Enum_h

typedef enum {
    ConfirmResultUnknown = -1,
    ConfirmResultWrong,
    ConfirmResultRight,
} ConfirmResult;

// 角色 enum
// 0: 程式人員
// 1: 管理員
// 2: 老師
// 3: 學生
// 4: 訪客
typedef enum {
    RoleUnknown = -1,
    RoleProgrammer,
    RoleManager,
    RoleTeacher,
    RoleStudent,
    RoleGuest,
    RoleMax,
} Role;

// 按鈕 enum
typedef enum {
    ButtonUnknown = -1,
    ButtonCancel,
    ButtonOkay,
} Button;

// 圖檔 type enum
typedef enum {
    ImageTypeUnknown = -1,
    ImageTypeJPG,
    ImageTypePNG,
    ImageTypeGIF,
    ImageTypeMP4,
    ImageTypeMax,
} ImageType;

// 寫字模組 enum
typedef NS_ENUM(NSInteger, WriteModule) {
    WriteModuleUnknown = -1,
    WriteModuleDefault,
    WriteModuleCombo, // 連擊99
    WriteModuleBomb, // 轟炸竹東
    WriteModuleFourPickOne, // 四選一
    WriteModuleMax,
};

// tables
typedef NS_ENUM(NSInteger, Table) {
    TableUnknown = -1,
    TableUser,
    TableClass,
    TableStudent,
    TableTeacher,
    TableSound,
    TableMax,
};

// 命令 enum
FOUNDATION_EXPORT NSString * const CmdLogin;
FOUNDATION_EXPORT NSString * const CmdLogout;
FOUNDATION_EXPORT NSString * const CmdStart;
FOUNDATION_EXPORT NSString * const CmdStop;
FOUNDATION_EXPORT NSString * const CmdBye;
FOUNDATION_EXPORT NSString * const CmdRollCall;
FOUNDATION_EXPORT NSString * const CmdClassStart;
FOUNDATION_EXPORT NSString * const CmdClassStop;

// 顯示 enum
FOUNDATION_EXPORT NSString *displayForWriteModule(WriteModule module);
FOUNDATION_EXPORT NSArray<NSNumber *> *availableWriteModules(void);

// local tables
typedef NS_ENUM(NSInteger, LocalTable) {
    LocalTableUnknown = -1,
    LocalTableMedia,
    LocalTableScore,
    LocalTableMax,
};

#endif /* Enum_h */
