//
//  UTENLogFormatter.m
//  uten
//
//  Created by pht on 2025/2/19.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import "UTENLogFormatter.h"

@implementation UTENLogFormatter

// Helper method to decode unicode escape sequences like \uXXXX
- (NSString *)stringByDecodingUnicodeSequencesInString:(NSString *)sourceString {
    // Check if the string contains Unicode escape sequences.
    if (![sourceString isKindOfClass:[NSString class]] || (![sourceString containsString:@"\\u"] && ![sourceString containsString:@"\\U"])) {
        return sourceString;
    }

    // The property list parser expects the string to be enclosed in quotes.
    // We also need to escape existing quotes inside the string.
    NSString *stringToParse = [sourceString stringByReplacingOccurrencesOfString:@"\"" withString:@"\\\""];
    stringToParse = [NSString stringWithFormat:@"\"%@\"", stringToParse];

    NSData *data = [stringToParse dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return sourceString; // Failed to encode to data
    }

    NSError *error = nil;
    id propertyList = [NSPropertyListSerialization propertyListWithData:data
                                                                options:NSPropertyListImmutable
                                                                 format:NULL
                                                                  error:&error];

    // If decoding fails or the result is not a string, return the original string.
    if (error || ![propertyList isKindOfClass:[NSString class]]) {
        return sourceString;
    }

    return (NSString *)propertyList;
}

- (NSString *)formatLogMessage:(DDLogMessage *)logMessage {
    NSString *tag = logMessage.representedObject ?: @"Default";
    NSString *message = [self stringByDecodingUnicodeSequencesInString:logMessage->_message];
    return [NSString stringWithFormat:@"[%@] %@", tag, message];
}

- (NSString *)logLevelString:(DDLogFlag)logFlag {
    switch (logFlag) {
        case DDLogFlagError:    return @"ERROR";
        case DDLogFlagWarning:  return @"WARN";
        case DDLogFlagInfo:     return @"INFO";
        case DDLogFlagDebug:    return @"DEBUG";
        case DDLogFlagVerbose:  return @"VERBOSE";
        default:                return @"UNKNOWN";
    }
}

@end
