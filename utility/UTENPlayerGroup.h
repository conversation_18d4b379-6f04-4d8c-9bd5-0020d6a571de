//
//  UTENPlayerGroup.h
//  uten
//
//  Created by yuming on 2024/8/17.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UTENFile;
@class UIView;

NS_ASSUME_NONNULL_BEGIN

@interface UTENPlayerGroup : NSObject
// utenfile array
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *list;
// readonly current index of player
@property (nonatomic, assign, readonly) NSInteger currentIndex;
// readonly real index of player
@property (nonatomic, assign, readonly) NSInteger realIndex;
// parent view property
@property (nonatomic, nullable, weak) UIView *view;
- (void)play;
@end

NS_ASSUME_NONNULL_END
