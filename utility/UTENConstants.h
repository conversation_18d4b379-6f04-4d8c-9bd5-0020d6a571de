//
//  UTENConstants.h
//  uten
//
//  Created by pht on 2024/6/4.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// static NSString * const KEY_ID = @"ID";
extern NSString * const KEY_ID;
// key type
extern NSString * const KEY_TYPE;
// key cname
extern NSString * const KEY_CNAME;
// key ename
extern NSString * const KEY_ENAME;

@interface UTENConstants : NSObject
+ (NSString *)host;
+ (NSNumber *)dbPort;
+ (NSString *)dbName;
+ (NSString *)dbUser;
+ (NSString *)dbPassword;
// ftp host
+ (NSString *)ftpHost;
// ftp port
+ (NSNumber *)ftpPort;
// ftp user
+ (NSString *)ftpUser;
// ftp password
+ (NSString *)ftpPassword;
// ftp root path
// + (NSString *)ftpRootPath;
// ftp upload path
+ (NSString *)ftpUploadPath;
// ftp download path
// ftp mp3 path
+ (NSArray *)masterGroup;
@end

NS_ASSUME_NONNULL_END
