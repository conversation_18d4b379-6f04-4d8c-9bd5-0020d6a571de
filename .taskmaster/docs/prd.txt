# 產品需求文檔 (PRD) - uten

## 1. 簡介

本文件旨在詳細說明 "uten" 應用程序的產品需求。Uten 是一款專為師生設計的移動端教育應用程序，旨在通過結合語音識別技術和遊戲化學習，提供一種創新、互動且有趣的語言學習體驗。

**產品目標:**
*   **提升學習效率:** 利用即時語音反饋幫助學生改善發音和口語能力。
*   **增強學習動機:** 通過遊戲化元素（如連擊、炸彈道具）和獎勵機制，激發學生的學習興趣。
*   **簡化教學管理:** 為教師提供創建班級、分配作業和追蹤學生進度的工具。

**目標用戶:**
*   **學生:** 希望通過互動方式學習語言的 K-12 學生。
*   **教師:** 尋求創新教學工具以輔助課堂教學和作業批改的語言教師。

## 2. 用戶畫像

### 2.1 學生 (小明)
*   **背景:** 10歲的小學生，正在學習英語。
*   **痛點:** 對傳統的背單詞和讀課文感到枯燥，缺乏練習口語的自信。
*   **期望:** 希望學習過程能像玩遊戲一樣有趣，並能即時知道自己的發音是否標準。

### 2.2 教師 (王老師)
*   **背景:** 小學英語教師，教授多個班級。
*   **痛點:** 難以一對一地糾正每個學生的發音，作業批改耗時，且難以量化學生的口語進步。
*   **期望:** 希望有一個工具能自動評估學生的口語作業，追蹤學生的學習進度，並讓課堂教學更有互動性。

## 3. 產品功能

### 3.1 核心功能

#### 3.1.1 用戶角色系統
*   **學生端:**
    *   註冊/登錄賬戶。
    *   通過 QR Code 或班級碼加入班級。
    *   查看作業和學習內容。
    *   完成語音練習並獲得即時反饋。
    *   查看個人學習歷史和成就。
*   **教師端:**
    *   註冊/登錄賬戶。
    *   創建和管理班級，生成班級 QR Code。
    *   發布學習內容和語音作業。
    *   查看班級內所有學生的學習進度和作業完成情況。
    *   監聽學生的錄音。

#### 3.1.2 語音識別與學習模塊
*   **核心技術:** 集成 `OpenEars` 離線語音識別引擎。
*   **發音練習:** 學生根據應用程序提供的單詞或句子進行跟讀。
*   **即時反饋:** 系統對學生的發音進行評分和分析，指出發音不準確的地方。
*   **語音合成 (TTS):** 提供標準發音的音頻示範。
*   **音頻檢測 (`SoundCheck`):** 在練習前檢測環境噪音和麥克風狀態，確保錄音質量。

#### 3.1.3 遊戲化學習機制
*   **連擊系統 (`ComboModel`):** 連續正確發音可觸發連擊效果，獲得額外積分。
*   **道具系統 (`BombModel`):** 學生可以使用道具（如"炸彈"）來跳過難點或獲得提示，增加遊戲趣味性。
*   **動畫與音效 (`Animation`, `MediaModel`):** 提供豐富的視覺和聽覺反饋，增強互動體驗。

#### 3.1.4 班級與歷史記錄
*   **班級管理 (`NclassModel`):** 教師可以創建、編輯和解散班級。
*   **學習歷史 (`NhistoryModel`):** 學生和教師都可以查看詳細的學習記錄，包括練習時間、得分、錯誤分析等。

### 3.2 輔助功能

*   **QR Code 功能 (`QrcodeModel`):** 支持掃描二維碼快速加入班級或訪問特定學習內容。
*   **文件管理 (`File`):** 管理應用內的媒體資源和用戶數據。
*   **對話框系統 (`DialogArgs`):** 用於應用內的各種提示和交互。

## 4. 技術規格

*   **平台:** iOS 12.1 及以上
*   **主要語言:** Objective-C
*   **核心框架:** UIKit, AVFoundation, AudioToolbox
*   **第三方庫:**
    *   `OpenEars` & `Slt`: 用於離線語音識別和語音合成。
    *   `CocoaPods`: 用於依賴管理。

## 5. 未來展望 (Roadmap)

*   **V2.0:**
    *   支持更多語種學習。
    *   引入線上對戰模式，讓學生可以進行發音比賽。
    *   開發教師端 Web 管理後台，方便在電腦上管理教學內容。
*   **V3.0:**
    *   利用 AI 技術提供更個性化的學習路徑推薦。
    *   增加家長賬號，讓家長可以監督孩子的學習情況。 