# Task ID: 4
# Title: Implement `QrcodeModel` for QR Code Operations
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Implement the `QrcodeModel` utility for QR code generation (e.g., for class codes) and scanning (e.g., for students joining classes).
# Details:
Generation: Input String, Output UIImage using `CoreImage` (`CIQRCodeGenerator`). Scanning: Utilize `AVFoundation` (`AVCaptureSession`, `AVCaptureMetadataOutputObjectsDelegate`) to decode QR codes and return the string data via a delegate or completion block. Handle camera permissions.

# Test Strategy:
Test QR code generation with sample strings and verify image output. Test QR code scanning with various QR codes and verify correct data decoding. Test camera permission handling.
