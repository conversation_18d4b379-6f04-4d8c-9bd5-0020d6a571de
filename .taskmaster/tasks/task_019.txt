# Task ID: 19
# Title: Implement `BombModel` for 'Bomb' Prop
# Status: pending
# Dependencies: 10
# Priority: medium
# Description: Implement the `BombModel` for the 'bomb' prop, allowing students to skip difficult items. Manage bomb inventory and usage.
# Details:
`BombModel` class: manage bomb inventory (e.g., give N bombs initially). UI element on `PracticeViewController` to use a bomb. Logic: Skip current item, mark as 'skipped with bomb'. Decrement bomb count. (Acquisition of bombs is V2).

# Test Strategy:
Test using a bomb to skip an item. Verify item is skipped, bomb count decrements, and UI reflects this. Test behavior when no bombs are left.
