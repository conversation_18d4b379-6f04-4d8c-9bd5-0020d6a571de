# Task ID: 7
# Title: Integrate `OpenEars` for Offline Speech Recognition
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Integrate the `OpenEars` library for offline speech recognition. Set up basic configuration, language models (English), and `OEEventsObserver` delegate.
# Details:
Ensure `OpenEars` and `Slt` are linked via CocoaPods. Initialize `OEEventsObserver`. Load English language model. Implement methods to start/stop listening. Handle microphone permissions (`AVAudioSession`). Configure paths to models: `[[OEPocketsphinxController sharedInstance] set pathToModels:[[NSBundle mainBundle] pathForResource:@"AcousticModelEnglish" ofType:@"bundle"]];` (example).

# Test Strategy:
Verify `OpenEars` initializes without errors. Test basic speech recognition with a small, predefined vocabulary to confirm audio capture and hypothesis generation. Test microphone permission requests.
