# Task ID: 21
# Title: Implement `DialogArgs` System for In-App Dialogs
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Create a centralized system or utility (`DialogArgs`) for displaying various in-app alerts, confirmations, and informational dialogs using `UIAlertController`.
# Details:
Create a helper class that wraps `UIAlertController` setup. Methods like `showInfo(title, message, viewController)`, `showConfirm(title, message, confirmAction, cancelAction, viewController)`, `showError(title, message, viewController)`. Use this for error messages, confirmations (e.g., disband class), `SoundCheck` results.

# Test Strategy:
Test displaying different types of dialogs (info, error, confirmation with actions) from various parts of the app. Verify correct titles, messages, and action handling.
