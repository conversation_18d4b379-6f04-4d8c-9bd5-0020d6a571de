# Task ID: 12
# Title: Implement `File` Module for Media and User Data Management
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Create a utility (`File` module/manager) for managing application files, such as student audio recordings, cached data, or downloaded learning content.
# Details:
Methods for saving, loading, deleting files in Documents/Caches directories using `NSFileManager`. Handle audio file storage for student recordings (e.g., `.wav` or `.m4a`). Manage paths for learning materials if any are downloaded.

# Test Strategy:
Test saving and retrieving audio files and simple data files. Verify files are stored in correct locations and can be deleted. Check error handling for file operations.
