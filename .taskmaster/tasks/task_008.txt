# Task ID: 8
# Title: Implement `SoundCheck` Module
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Develop the `SoundCheck` module to detect environment noise and microphone status before a pronunciation practice session.
# Details:
Use `AVFoundation` (`AVAudioRecorder` or `AVAudioEngine`) to monitor input audio levels (average/peak power). Check microphone availability and permissions. Provide user feedback (e.g., 'Environment too noisy', 'Microphone OK'). This should be invoked before starting `OpenEars` listening.

# Test Strategy:
Test in quiet and noisy environments. Test with microphone enabled/disabled or permissions denied. Verify appropriate feedback is given to the user.
