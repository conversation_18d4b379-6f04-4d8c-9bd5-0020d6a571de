# Task ID: 11
# Title: Implement Instant Feedback Logic for Pronunciation
# Status: pending
# Dependencies: 10
# Priority: high
# Description: Process `OpenEars` recognition results (`hypotheses`, `scores`) to provide a score and identify mispronounced parts against the target text.
# Details:
Compare recognized text from `OpenEars` with target text. Implement scoring (e.g., word accuracy, `OpenEars` confidence). Highlight mispronounced words or provide feedback. Display score and analysis on `PracticeViewController`.

# Test Strategy:
Test with correct and various incorrect pronunciations. Verify scoring logic and feedback accuracy. Check highlighting of incorrect words.
