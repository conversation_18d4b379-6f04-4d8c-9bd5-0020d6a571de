# Task ID: 6
# Title: Implement Teacher 'Create Class' UI & Logic (`NclassModel` Part 1)
# Status: pending
# Dependencies: 3, 4
# Priority: high
# Description: Allow teachers to create new classes. This includes UI for class details and generation of a unique class code and corresponding QR code. Define `NclassModel` structure.
# Details:
Create `CreateClassViewController`. Fields: Class Name, Description. Logic: Generate unique class code. Use `QrcodeModel` to generate QR image. Define `NclassModel` (e.g., `classId`, `className`, `teacherId`, `classCode`). Store class data locally, associated with the teacher.

# Test Strategy:
Test creating a new class, verify class code and QR code generation. Check `NclassModel` instance is correctly created and stored locally. Verify UI for displaying class list and QR code.
