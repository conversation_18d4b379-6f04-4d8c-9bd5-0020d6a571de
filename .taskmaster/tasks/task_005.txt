# Task ID: 5
# Title: Implement Student 'Join Class' Feature
# Status: pending
# Dependencies: 3, 4
# Priority: high
# Description: Allow students to join a class using a QR code or a manually entered class code. This involves UI and logic for both methods.
# Details:
Create `JoinClassViewController`. UI: `UITextField` for class code, button for QR scan. Logic: Use `QrcodeModel` for scanning. Stub backend call to validate class code/QR data and associate student with class (locally for V1).

# Test Strategy:
Test joining class via manual code input and QR code scanning. Verify UI feedback on success/failure. Ensure student's class association is updated locally.
