# Task ID: 2
# Title: Implement User Registration and Login UI
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Create UI screens for user registration and login for both student and teacher roles using UIKit.
# Details:
Design and implement `RegistrationViewController` and `LoginViewController`. Include fields for email/username, password, and role selection (Student/Teacher) during registration. Implement basic client-side input validation.

# Test Strategy:
Manually test UI elements, input fields, role selection, and navigation between registration and login screens. Validate input field constraints.
