# Task ID: 20
# Title: Implement `Animation` and `MediaModel` for Enhanced Feedback
# Status: pending
# Dependencies: 1
# Priority: low
# Description: Integrate animations (`Animation`) and sound effects (`MediaModel`) for feedback (correct/incorrect, combo, bomb usage) to enhance user experience.
# Details:
`MediaModel`: Utility to play short sound effects (e.g., using `AudioToolbox` for system sounds or `AVAudioPlayer` for custom clips). `Animation`: Implement simple UI animations using `UIView.animateWithDuration` for score pop-ups, combo text, etc. Integrate into `PracticeViewController` and other relevant screens.

# Test Strategy:
Trigger various game events (correct answer, wrong answer, combo, bomb use) and verify corresponding animations and sound effects play correctly and are not obtrusive.
