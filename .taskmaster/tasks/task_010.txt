# Task ID: 10
# Title: Implement Student Pronunciation Practice UI and Core Loop
# Status: pending
# Dependencies: 3, 7, 8, 9
# Priority: high
# Description: Create the UI for students to see a word/sentence from an assignment, record their pronunciation, and submit for feedback. Integrate `SoundCheck` before recording.
# Details:
Create `PracticeViewController`. Display text to speak. 'Record' button starts `OpenEars` (after `<PERSON><PERSON><PERSON><PERSON>`). 'Stop' button or auto-stop. Visual feedback during recording. Button to play standard pronunciation using TTS module. Interface with `OpenEars` for results.

# Test Strategy:
Test practice flow: display text, run `SoundCheck`, record audio, stop. Verify UI updates, TTS playback, and `OpenEars` is invoked. Test with mock assignment data.
