# Task ID: 17
# Title: Implement Teacher 'View Student Progress and Recordings' Screen
# Status: pending
# Dependencies: 3, 6, 15, 12
# Priority: medium
# Description: Allow teachers to view learning progress and assignment completion of students in their classes. Enable listening to student recordings.
# Details:
Create `ClassProgressViewController`. UI: Select class, then list students. For each student, show completed assignments, scores (from `NhistoryModel`). Implement functionality to retrieve (from `File` module via path in `NhistoryModel`) and play student audio recordings using `AVAudioPlayer`.

# Test Strategy:
Test with mock student data and recordings. Verify correct display of progress, scores, and successful playback of audio recordings. Test navigation through classes and students.
