# Task ID: 3
# Title: Implement User Registration and Login Logic (Stubbed)
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Implement client-side logic for user registration and login. Use mock data or local storage (e.g., NSUserDefaults or Keychain for sensitive data) for user accounts as backend is not specified for V1.
# Details:
Create `AuthService` class. Implement `registerUser(email, password, role)` and `loginUser(email, password)`. On successful login, store user session (including role and user ID) locally. Manage app flow based on login state.

# Test Strategy:
Test registration of new student/teacher accounts. Test login with valid/invalid credentials. Verify role and session are correctly stored and retrieved. Test logout functionality.
