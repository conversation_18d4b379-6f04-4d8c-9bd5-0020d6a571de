# Task ID: 18
# Title: Implement `ComboModel` for Combo System
# Status: pending
# Dependencies: 11
# Priority: medium
# Description: Implement the `ComboModel` for the combo system: track consecutive correct pronunciations, award bonus points, and provide UI feedback.
# Details:
`ComboModel` class: manage current combo count. Increment on successful pronunciation (based on score threshold from Task 11), reset on error. Define scoring bonus. Integrate with `PracticeViewController` to display combo count and visual effects (e.g., 'Combo x3!').

# Test Strategy:
Test consecutive correct/incorrect pronunciations. Verify combo count updates, bonus points are applied (if scoring is modified), and UI feedback is shown.
