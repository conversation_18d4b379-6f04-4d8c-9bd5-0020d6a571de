# Task ID: 13
# Title: Implement Student 'View Assignments and Learning Content' Screen
# Status: pending
# Dependencies: 3, 5
# Priority: high
# Description: Create a UI for students to view assignments posted by their teacher for the classes they are enrolled in.
# Details:
Create `AssignmentsViewController` (e.g., `UITableView`). Fetch assignment list for student's class(es) (from local storage, based on teacher's published assignments). Display assignment details (title, content preview). Navigate to `PracticeViewController` on selection.

# Test Strategy:
Populate with mock assignment data (or data from Task 14). Verify display, navigation to practice screen, and filtering by student's classes.
