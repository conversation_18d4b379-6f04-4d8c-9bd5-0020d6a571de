# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'

target 'uten' do
  # Uncomment the next line if you're using Swift or would like to use dynamic frameworks
  # use_frameworks!

  # Pods for uten
  pod "GoldRaccoon"
  pod 'SSZipArchive'
  pod 'OHMySQL'
  pod 'MQTTClient', '~> 0.15'
  pod 'JHUD'
  pod 'ReactiveObjC', '~> 3.1'
  pod 'Masonry', '~> 1.1'
  pod 'FLAnimatedImage', '~> 1.0'
  pod 'MMKV'
  pod 'ObjectBox'
  pod 'Resolver'
  pod 'CocoaLumberjack', '~> 3.8'
  pod 'JLRoutes', '~> 2.1'

  target 'utenTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'utenUITests' do
    inherit! :search_paths
    # Pods for testing
  end
end

post_install do |installer|
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
  end
	installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.2'
      end
    end
  end
end
