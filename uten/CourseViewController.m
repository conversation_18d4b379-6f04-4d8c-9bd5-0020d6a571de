//
//  CourseViewController.m
//  uten
//
//  Created by <PERSON> on 2019/10/20.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "CourseViewController.h"
#import "MQTTClient.h"
#import "OHMySQL.h"
#import "UTENEnum.h"
#import "Masonry.h"
#import "ReactiveObjC.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "NSUserDefaults+X.h"

@interface CourseViewController (){
    MQTTSession *session;
    NSArray *dyItems;
    IBOutlet UITableView *dyTableView;
    IBOutlet UIPickerView *bookda;
    IBOutlet UIPickerView *classda;
    IBOutlet UIPickerView *typeda;
    IBOutlet UIPickerView *l1da;
    IBOutlet UIPickerView *l2da;
    IBOutlet UILabel *mytitle;
    IBOutlet UILabel *mystudy;
    NSArray *bookItems;
    NSArray *classItems;
    NSArray *typeItems;
    NSArray *l1Items;
    NSArray *l2Items;
    NSMutableArray *SetArray;
    int bookptr;
    int c1[100];
    int c2[100];
    int c3[100];
    int cptr;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    int playindex;
    int courseindex;
    int hall;
    int study;
    NSString *ServerIP;
    NSString *uten_class,*r_uten_class;
    OHMySQLQueryContext *queryContext;
    int selclass;
    NSString *SCL;
}
// module picker view
@property (weak, nonatomic) IBOutlet UIPickerView *modulePickerView;
// module items
@property (strong, nonatomic) NSArray<NSString *> *moduleItems;
// selected write module
@property (assign, nonatomic) WriteModule currentWriteModule;
// delay picker view
@property (weak, nonatomic) IBOutlet UIPickerView *waitPickerView;
// delay items
@property (strong, nonatomic) NSArray<NSString *> *waitItems;
// selected delay
@property (assign, nonatomic) NSInteger waitSeconds;
@end

@implementation CourseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    selclass=0;
       courseindex=0;
       hall=0;
       study=0;
       // Do any additional setup after loading the view.
       // Cell 資料使用陣列儲存，你也可以使用其他方式提供
       //dyItems = @[@"資料第一項", @"資料第二項", @"資料第三項", @"資料第四項"];
       bookItems = [self updatebook];
       typeItems = @[@"寫.(單字)", @"寫.(字組)", @"寫.(拆字)", @"寫.(練寫)",@"說.(單字)", @"說.(字組)"]; //, @"說.(拆字)", @"說.(練寫)"];
       l1Items = @[@"第01堂", @"第02堂", @"第03堂", @"第04堂",@"第05堂", @"第06堂",
                   @"第07堂", @"第08堂", @"第09堂", @"第10堂",@"第11堂", @"第12堂",
                   @"第13堂", @"第14堂", @"第15堂", @"第16堂",@"第17堂", @"第18堂",
                   @"第19堂", @"第20堂", @"第21堂", @"第22堂",@"第23堂", @"第24堂",
                   ];
       
    l2Items = @[@"A1", @"A2", @"A3", @"A4",@"A5", @"A6",@"A7", @"A8", @"A9", @"A10",
                @"A11", @"A12", @"A13", @"A14",@"A15", @"A16",@"A17", @"A18", @"A19", @"A20",
                @"A21", @"A22", @"A23", @"A24",@"A25", @"A26",@"A27", @"A28", @"A29", @"A30",
                @"A31", @"A32", @"A33", @"A34",@"A35", @"A36",@"A37", @"A38", @"A39", @"A40",
                @"A41", @"A42", @"A43", @"A44",@"A45", @"A46",@"A47", @"A48", @"A49", @"A50",
                @"A51", @"A52", @"A53", @"A54",@"A55", @"A56",@"A57", @"A58", @"A59", @"A60",
                @"A61", @"A62", @"A63", @"A64",@"A65", @"A66",@"A67", @"A68", @"A69", @"A70",
                @"A71", @"A72", @"A73", @"A74",@"A75", @"A76",@"A77", @"A78", @"A79", @"A80",
                @"A81", @"A82", @"A83", @"A84",@"A85", @"A86",@"A87", @"A88", @"A89", @"A90",
                @"A91", @"A92", @"A93", @"A94",@"A95", @"A96",@"A97", @"A98", @"A99"];
       bookptr=0;
       classItems= [self updateclass];
       SetArray = [NSMutableArray array];
       dyItems = SetArray;
       // 初始化cell的樣式及名稱，告訴UITableView每個cell顯示的樣式，這裡指定只顯示預設(一行文字)
       UITableViewCell *tableViewCell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"DyDataCell"];
       
       // 設定Cell名稱、樣式
       [dyTableView registerClass:tableViewCell.class forCellReuseIdentifier:tableViewCell.reuseIdentifier];


       // DataSource 設定self，必需寫好對應的function才能取得正確資料
       dyTableView.dataSource = self;
       
       // Delegate 設定self，必需寫好對應的delegate function才能取得正確資訊
       dyTableView.delegate = self;
    
       bookda.dataSource = self;
       bookda.delegate = self;
       classda.dataSource = self;
       classda.delegate = self;
       typeda.dataSource = self;
       typeda.delegate = self;
       l1da.dataSource = self;
       l1da.delegate = self;
       l2da.dataSource = self;
       l2da.delegate = self;
       cptr=0;
       [self LoadCourse];
       SCL=classItems[0];
    
    /*
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
    transport.host = ServerIP;
    transport.port = 1883;
    session = [[MQTTSession alloc] init];
    session.transport = transport;
    session.delegate=self;
    [session connectWithConnectHandler:^(NSError *error) {
        // Do some work
        [session subscribeToTopic:r_uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                NSLog(@"Subscription failed %@", error.localizedDescription);
            } else {
                NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
            }
        }];
    }];
     */
    // Setup layout
    [self setupLayout];
}

// Setup layout
- (void)setupLayout {
    // module items 配置 "預設", "連擊99", "轟炸竹東"
    // availableWriteModules map to string
    NSArray<NSNumber *> *modules = availableWriteModules();
    self.moduleItems = [modules.rac_sequence map:^NSString *(NSNumber *module) {
            return displayForWriteModule(module.integerValue);
        }].array;
    // 新增 picker view, 內容有 "預設", "連擊99", "轟炸竹東"
    UIPickerView *modulePickerView = [[UIPickerView alloc] init];
    self.modulePickerView = modulePickerView;
    modulePickerView.dataSource = self;
    modulePickerView.delegate = self;
    modulePickerView.translatesAutoresizingMaskIntoConstraints = NO;
    // 加入等待時間
    self.waitItems = @[
        @"立刻開始", 
        @"等待1秒後開始", 
        @"等待2秒後開始", 
        @"等待3秒後開始",
    ];
    // picker view
    UIPickerView *waitPickerView = [[UIPickerView alloc] init];
    self.waitPickerView = waitPickerView;
    waitPickerView.dataSource = self;
    waitPickerView.delegate = self;
    waitPickerView.translatesAutoresizingMaskIntoConstraints = NO;
    // 把所有的 UIPickerView 加到橫向的 UIStackView
    NSArray<UIView *> *pickers = @[
        modulePickerView, // 模組
        waitPickerView, // 等待時間
    ];
    UIStackView *picksStackView = [[UIStackView alloc] initWithArrangedSubviews:pickers];
    picksStackView.axis = UILayoutConstraintAxisHorizontal;
    picksStackView.distribution = UIStackViewDistributionFillEqually;
    picksStackView.spacing = 10;
    picksStackView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:picksStackView];
    {
        @weakify(self);
        [picksStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            @strongify(self);
            make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(0);
            make.height.equalTo(@160);
            make.leading.equalTo(self.view.mas_leading).offset(20);
            make.trailing.equalTo(self.view.mas_trailing).offset(-250);
        }];
    }
    

    // dyTableView
    {
        @weakify(self);
        [dyTableView mas_makeConstraints:^(MASConstraintMaker *make) {
            @strongify(self);
            make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(60);
            make.bottom.equalTo(picksStackView.mas_top).offset(0);
            make.leading.equalTo(picksStackView.mas_leading).offset(0);
            make.trailing.equalTo(picksStackView.mas_trailing).offset(0);
        }];
    }
}

-(NSArray *) updatebook {
    NSArray *da=[[NSArray alloc] init];
    NSString *ss=@"";
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"class.csv"]];
    NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"class FIle:%@\n%@",DirFile,classContent);
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    for(int i=0;i<21;i++) {
            NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
            if([split1 count] > 2) {
                ss = [ss stringByAppendingString:split1[2]];
                ss = [ss stringByAppendingString:@","];
            }
    }
    ss = [ss stringByAppendingString:@"休息1分鐘"];
    ss = [ss stringByAppendingString:@","];
    ss = [ss stringByAppendingString:@"休息3分鐘"];
    ss = [ss stringByAppendingString:@","];
    ss = [ss stringByAppendingString:@"休息5分鐘"];
    da = [ss componentsSeparatedByString:@","];
    return da;
}
-(NSArray *) updateclass {
    
    OHMySQLUser *user;
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    
    //OHMySQLQueryContext *
    queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    
    NSString *strcondition = [[NSString alloc] initWithFormat:(@"cname='%@'"),CNAME];
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"nteacher" condition:strcondition];
    NSError *error = nil;
    NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
    NSArray *da=[[NSArray alloc] init];
    NSString *ss=@"";
    NSString *ca;
    if([tasks count]>0) {
        NSDictionary *dict = [tasks objectAtIndex:0];
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c01"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c02"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c03"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c04"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c05"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c06"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c07"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c08"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c09"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c10"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c11"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c12"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c13"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c14"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        ca=[[NSString alloc] initWithData:[dict objectForKey:@"c15"]  encoding:NSUTF8StringEncoding];
        if([ca length] > 1) { ss = [ss stringByAppendingString:ca]; ss = [ss stringByAppendingString:@","]; }
        
    }
    NSLog(@"DBG NAME:%@ , ca:%@",CNAME,ca);
    da = [ss componentsSeparatedByString:@","];
    uten_class = [[NSString alloc] initWithFormat:(@"uten_%@"),da[0]];
    r_uten_class = [[NSString alloc] initWithFormat:(@"r_uten_%@"),da[0]];
    NSLog(@"DBG NAME:%@ , uten_class:%@ , r_uten_class:%@",CNAME,uten_class,r_uten_class);
    return da;
}
- (IBAction)btLogout:(id)sender {
    [self dismissViewControllerAnimated:NO completion:nil];
}
- (IBAction)btPlayClass:(id)sender {
    int flag=0;
    if(selclass==0) {
        uten_class = [[NSString alloc] initWithFormat:(@"uten_%@"),SCL];
        r_uten_class = [[NSString alloc] initWithFormat:(@"r_uten_%@"),SCL];
        MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
        transport.host = ServerIP;
        transport.port = 1883;
        session = [[MQTTSession alloc] init];
        session.transport = transport;
        session.delegate=self;
        [session connectWithConnectHandler:^(NSError *error) {
            // Do some work
            [session subscribeToTopic:r_uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
                if (error) {
                    NSLog(@"DBG Subscription failed %@", error.localizedDescription);
                } else {
                    NSLog(@"DBG Subscription sucessfull! Granted Qos: %@", gQoss);
                    
                    
                }
            }];
        }];
        selclass=1;
        flag=1;
        
    }
    if(flag==1) {
        NSString *msg01=[NSString stringWithFormat:@"你選擇班級為:%@,確認後請在選擇一次播放課程,如需要更換班級,請退出再進入",SCL];
        UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:msg01 delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
        [alert show];
    } else {
        if([SetArray count] > 0) {
            NSString *cmd=[NSString stringWithFormat:@"$%@,START,%04d,%@,%04d,%@,%@,end~",uten_class,c3[playindex],[bookItems objectAtIndex:c1[playindex]],c2[playindex],SetArray[playindex],@(self.currentWriteModule)];
            // use utencommand
            UTENCommand *command = [[UTENCommand alloc] init];
            command.name = CmdStart;
            command.utenClass = uten_class;
            command.rUtenClass = r_uten_class;
            command.c1 = [bookItems objectAtIndex:c1[playindex]];
            command.c2 = [NSString stringWithFormat:@"%04d", c2[playindex]];
            command.c3 = [NSString stringWithFormat:@"%04d", c3[playindex]];
            command.writeModule = @(self.currentWriteModule);
            command.selTag = SetArray[playindex];
            command.loginID = ID;
            // NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            // command.loginID = defaults.identifier;
            // 取得現在時間
            NSDate *now = [NSDate date];
            // 設定觸發時間 timestamp 秒
            double delay = self.waitSeconds;
            NSLog(@"DBG delay:%f",delay);
            command.triggerAt = @(now.timeIntervalSince1970 - 3.0 + delay);
            NSError *error = nil;
            NSData *data = [command toData:&error];
            if (error) {
                NSLog(@"Error: %@", error);
            } else {
                // 送出課程
                // data = [[[NSString alloc] initWithFormat:(cmd),ID] dataUsingEncoding:NSUTF8StringEncoding];
                [session publishData:data onTopic:uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
                }];
                NSLog(@"DBG Play:%@",cmd);
            }
        } else {
            NSLog(@"DBG NO Content");
            UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:@"無效課表,請在選擇課表" delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
            [alert show];
        }
    }

}
- (IBAction)btNext:(id)sender {
    if(courseindex < 23) {
        courseindex++;
        [self LoadCourse];
    }
}
- (IBAction)btPrew:(id)sender {

    if(courseindex > 0) {
        courseindex--;
        [self LoadCourse];
    }
}
- (IBAction)btStopClass:(id)sender {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdBye;
    command.utenClass = uten_class;
    command.rUtenClass = r_uten_class;
    command.c1 = [bookItems objectAtIndex:c1[playindex]];
    command.c2 = [NSString stringWithFormat:@"%04d", c2[playindex]];
    command.c3 = [NSString stringWithFormat:@"%04d", c3[playindex]];
    NSError *error = nil;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    // 送出停止課程
    [session publishData:data onTopic:uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
    }];
}
-(void) LoadCourse {
    OHMySQLUser *user;
    NSString *bookstr,*classstr,*typestr;

    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    NSString *l2=@"";
    /*
    switch([l2da selectedRowInComponent:0]) {
        case 0: l2=@"A1"; break;
        case 1: l2=@"A2"; break;
        case 2: l2=@"A3"; break;
        case 3: l2=@"A4"; break;
        case 4: l2=@"A5"; break;
        default: l2=@"A6"; break;
    }
     */
    l2=[NSString stringWithFormat: @"A%d",[l2da selectedRowInComponent:0]+1];
    NSString *course=[NSString stringWithFormat: @"%@-%02d",l2,courseindex+1];
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"course" condition:[NSString stringWithFormat: @"course='%@'", course]];
    NSError *error = nil;
    NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
    [SetArray removeAllObjects];
    for(int i=0;i<[tasks count];i++) {
        NSDictionary *dict = [tasks objectAtIndex:i];
        int index=[[dict objectForKey:@"iindex"] intValue];
        NSString *context= [[NSString alloc] initWithData:[dict objectForKey:@"context"]                      encoding:NSUTF8StringEncoding];
        [SetArray addObject:context];
    }
    [coordinator disconnect];
    //Default
    bookptr=0;
    classItems= [self updateclass];
    [classda reloadAllComponents];
    
    dyItems = SetArray;
    [dyTableView reloadData];
    mytitle.text=[l1Items objectAtIndex:courseindex];
}
//點選cell後會呼叫此function告知哪個cell已經被選擇(0開始)
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    playindex=indexPath.row;
    NSLog(@"已選擇的cell編號:%ld",indexPath.row);
    NSLog(@"cell值: %@",[dyItems objectAtIndex:indexPath.row]);
    NSLog(@"\r\n");
}
//返回總共有多少cell筆數
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return dyItems.count;
}
//根據cellForRowAtIndexPath來取得目前TableView需要哪個cell的資料
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 取得tableView目前使用的cell
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"DyDataCell" forIndexPath: indexPath];
    // 將指定資料顯示於tableview提供的text
    cell.textLabel.text = dyItems[indexPath.row];
    /*
    static NSString *CellIdentifier = @"Cell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
    // Configure the cell...
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
    }
    // Display recipe in the table cell
    UILabel *recipeNameLabel = (UILabel *)[cell viewWithTag:101];
    recipeNameLabel.text = dyItems[indexPath.row];
    */
    return cell;
}
///
// The number of columns of data
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView
{
    return 1;
}
// The number of rows of data
- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component
{
    if(pickerView==bookda) return bookItems.count;
    if(pickerView==classda) return classItems.count;
    if(pickerView==typeda) return typeItems.count;
    if(pickerView==l1da) return l1Items.count;
    if(pickerView==l2da) return l2Items.count;
    if (pickerView == self.modulePickerView) {
        return self.moduleItems.count;
    }
    if (pickerView == self.waitPickerView) {
        return self.waitItems.count;
    }
    return bookItems.count;
}
// The data to return for the row and component (column) that's being passed in
- (NSString*)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component
{
    if(pickerView==bookda) return bookItems[row];
    if(pickerView==classda) return classItems[row];
    if(pickerView==typeda) return typeItems[row];
    if(pickerView==l1da) return l1Items[row];
    if(pickerView==l2da) return l2Items[row];
    if (pickerView == self.modulePickerView) {
        return [self.moduleItems objectAtIndex:row];
    }
    if (pickerView == self.waitPickerView) {
        return [self.waitItems objectAtIndex:row];
    }
    return bookItems[row];
}
- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    NSLog(@"DBG select : %d",row);
    if(pickerView==classda) {
        NSLog(@"DBG Class :%@",classItems[row]);
        SCL=classItems[row];
        uten_class = [[NSString alloc] initWithFormat:(@"uten_%@"),classItems[row]];
        r_uten_class = [[NSString alloc] initWithFormat:(@"r_uten_%@"),classItems[row]];
        
    }
    if(pickerView==bookda) {
        bookptr=row;
        //classItems= [self updateclass];
        //[classda reloadAllComponents];
    }
    if(pickerView==l1da) {
        /*
        if([dyItems count] > 0) {
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要編輯其他課程?\n(請先存檔先前的紀錄)" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"編輯其他",nil];
            [alertView show];
        } else*/
        [self LoadCourse];
        
    }
    if(pickerView==l2da) {
        /*
        if([dyItems count] > 0) {
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要編輯其他課程?\n(請先存檔先前的紀錄)" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"編輯其他",nil];
            [alertView show];
        } else */
        [self LoadCourse];
    }
    if (pickerView == self.modulePickerView) {
        self.currentWriteModule = row;
    }
    else if (pickerView == self.waitPickerView) {
        self.waitSeconds = row;
    }
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    switch (buttonIndex) {
        case 0:
            NSLog(@"Cancel Button Pressed");
            break;
        case 1:
            [self LoadCourse];
            NSLog(@"Button 1 Pressed");
            break;
        default:
            break;
    }
}
- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    if (command.isClassStart) {
        if(hall==0) study=0;
        hall++;
        mystudy.text=[NSString stringWithFormat:@"課程進行中:%02d ,  完成課程:%02d",hall, study];
    }
    else if (command.isClassStop) {
        if(study==0) hall=0;
        study++;
        mystudy.text=[NSString stringWithFormat:@"課程進行中:%02d ,  完成課程:%02d",hall, study];
    }
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
