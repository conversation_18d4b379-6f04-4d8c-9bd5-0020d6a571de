//
//  eClassViewController.m
//  uten
//
//  Created by <PERSON> on 2019/8/1.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "eClassViewController.h"
#import "OHMySQL.h"
@interface eClassViewController () {
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    __weak IBOutlet UITextField *pClass;
    
    __weak IBOutlet UIButton *pTeacher1;
    __weak IBOutlet UIButton *pTeacher2;
    __weak IBOutlet UIButton *pTeacher3;
    __weak IBOutlet UIButton *pClassLevel;
    __weak IBOutlet UIButton *pClassTime;
    __weak IBOutlet UIButton *pStudent01;
    __weak IBOutlet UIButton *pStudent02;
    __weak IBOutlet UIButton *pStude<PERSON>03;
    __weak IBOutlet UIButton *pStudent04;
    __weak IBOutlet UIButton *pStudent05;
    __weak IBOutlet UIButton *pStudent06;
    __weak IBOutlet UIButton *pStudent07;
    __weak IBOutlet UIButton *pStudent08;
    __weak IBOutlet UIButton *pStudent09;
    __weak IBOutlet UIButton *pStudent10;
    __weak IBOutlet UIButton *pStudent11;
    __weak IBOutlet UIButton *pStudent12;
    __weak IBOutlet UIButton *pStudent13;
    __weak IBOutlet UIButton *pStudent14;
    __weak IBOutlet UIButton *pStudent15;
    __weak IBOutlet UIButton *pStudent16;
    __weak IBOutlet UIButton *pStudent17;
    __weak IBOutlet UIButton *pStudent18;
    __weak IBOutlet UIButton *pStudent19;
    __weak IBOutlet UIButton *pStudent20;
    __weak IBOutlet UIButton *pButton;
    __weak IBOutlet UIButton *pButtonDelete;
    int state;
    int messageid;
}
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation eClassViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    //
    state=1;
    [self ClearEditItem];
}
- (IBAction)btDelete:(id)sender {
    messageid=2;
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要刪除紀錄還是要退出編輯" delegate:self cancelButtonTitle:@"退出編輯" otherButtonTitles:@"刪除本記錄",@"取消",nil];
    [alertView show];
}
- (IBAction)btButton:(id)sender {
    switch(state)
    {
        case 1:
        {
            [self ClearEditItem];
            pButtonDelete.hidden=false;
            [pButton setImage:[UIImage imageNamed:@"btokb.png"] forState:UIControlStateNormal];
            state=2;
        }
            break;
        case 2:
        {
            messageid=1;
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否存檔" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"存檔",nil];
            [alertView show];
        }
            break;
    }
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    
    switch (buttonIndex) {
        case 0:
            switch(messageid) {
                case 2:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btaddb.png"] forState:UIControlStateNormal];
                    state=1;
                }
                    break;
            }
            break;
        case 1:
            switch(messageid) {
                case 1:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btaddb.png"] forState:UIControlStateNormal];
                    state=1;
                    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"存檔完成!!" delegate:self cancelButtonTitle:@"確定" otherButtonTitles:nil];
                    [alertView show];
                }
                    break;
                case 2:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btaddb.png"] forState:UIControlStateNormal];
                    state=1;
                    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"刪除成功!!" delegate:self cancelButtonTitle:@"確定" otherButtonTitles:nil];
                    [alertView show];
                }
                    break;
            }
            break;
        default:
            break;
    }
    
}
-(void) ClearEditItem {

    [pTeacher1 setTitle:@"" forState:UIControlStateNormal];
    [pTeacher2 setTitle:@"" forState:UIControlStateNormal];
    [pTeacher3 setTitle:@"" forState:UIControlStateNormal];
    [pClassLevel setTitle:@"" forState:UIControlStateNormal];
    [pClassTime setTitle:@"" forState:UIControlStateNormal];

    [pStudent01 setTitle:@"" forState:UIControlStateNormal];
    [pStudent02 setTitle:@"" forState:UIControlStateNormal];
    [pStudent03 setTitle:@"" forState:UIControlStateNormal];
    [pStudent04 setTitle:@"" forState:UIControlStateNormal];
    [pStudent05 setTitle:@"" forState:UIControlStateNormal];
    [pStudent06 setTitle:@"" forState:UIControlStateNormal];
    [pStudent07 setTitle:@"" forState:UIControlStateNormal];
    [pStudent08 setTitle:@"" forState:UIControlStateNormal];
    [pStudent09 setTitle:@"" forState:UIControlStateNormal];
    [pStudent10 setTitle:@"" forState:UIControlStateNormal];
    [pStudent11 setTitle:@"" forState:UIControlStateNormal];
    [pStudent12 setTitle:@"" forState:UIControlStateNormal];
    [pStudent13 setTitle:@"" forState:UIControlStateNormal];
    [pStudent14 setTitle:@"" forState:UIControlStateNormal];
    [pStudent15 setTitle:@"" forState:UIControlStateNormal];
    [pStudent16 setTitle:@"" forState:UIControlStateNormal];
    [pStudent17 setTitle:@"" forState:UIControlStateNormal];
    [pStudent18 setTitle:@"" forState:UIControlStateNormal];
    [pStudent19 setTitle:@"" forState:UIControlStateNormal];
    [pStudent20 setTitle:@"" forState:UIControlStateNormal];
    pClass.text=@"";

}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {
    
    [self dismissViewControllerAnimated:NO completion:nil];
}
@end
