//
//  PlayViewController.m
//  uten
//
//  Created by <PERSON> on 2019/9/11.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "PlayViewController.h"
#import "ViewController.h"
#import "ClassViewCell.h"
#import "WriterViewController.h"
#import "WriteMutilViewController.h"
#import "WriteMutilWordViewController.h"
#import "WriteSentenceViewController.h"
#import "WriteWordViewController.h"
#import "SayViewController.h"
#import "eMaterialViewController.h"
#import "OHMySQL.h"
#import "JHUD.h"
#import "Game01Controller.h"
#import <Masonry/Masonry.h>
#import "UTENEnum.h"
#import <ReactiveObjC/ReactiveObjC.h>


@interface PlayViewController () {
    NSArray *dyItems;
    IBOutlet UITableView *dyTableView;
    IBOutlet UIPickerView *bookda;
    IBOutlet UIPickerView *classda;
    IBOutlet UIPickerView *typeda;
    IBOutlet UIPickerView *l1da;
    IBOutlet UIPickerView *l2da;
    IBOutlet UILabel *lmark;
    
    IBOutlet UIImageView *preView;
    IBOutlet UILabel *ltitle;
    NSArray *bookItems;
    NSArray *classItems;
    NSArray *typeItems;
    NSArray *typeItems1;
    NSArray *l1Items;
    NSArray *l2Items;
    NSMutableArray *SetArray;
    int bookptr;
    int c1[100];
    int c2[100];
    int c3[100];
    int cptr;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    NSString *ServerIP;
    NSString *tword[1000];
    int twptr;
    NSString *lword[200];
    int lwptr;
    int markmode;
    int markstart;
    int markend;
    int bookpic;
    
    NSString *seltag;
    NSString *uclass;
    JHUD *hudView;
    NSString *uten_class,*r_uten_class;
    
}
@property (weak, nonatomic) IBOutlet UILabel *lbBook;
@property (weak, nonatomic) IBOutlet UILabel *lbLesson;
@property (weak, nonatomic) IBOutlet UILabel *lbClass;
@property (weak, nonatomic) IBOutlet UILabel *lbType;
// module picker view
@property (weak, nonatomic) IBOutlet UIPickerView *modulePickerView;
// module items
@property (strong, nonatomic) NSArray<NSString *> *moduleItems;
// selected write module
@property (assign, nonatomic) WriteModule currentWriteModule;
@end
@implementation PlayViewController
- (void)viewDidLoad {
    [super viewDidLoad];
    twptr=0;
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    bookpic=0;
    DDLogDebugTag(@"PlayViewController", @"ID:%@", ID);
    DDLogDebugTag(@"PlayViewController", @"TYPE:%@", TYPE);
    DDLogDebugTag(@"PlayViewController", @"CNAME:%@", CNAME);
    DDLogDebugTag(@"PlayViewController", @"ENAME:%@", ENAME);
    DDLogDebugTag(@"PlayViewController", @"uten_class:%@", uten_class);
    DDLogDebugTag(@"PlayViewController", @"r_uten_class:%@", r_uten_class);
    // Do any additional setup after loading the view.
    // Cell 資料使用陣列儲存，你也可以使用其他方式提供
    //dyItems = @[@"資料第一項", @"資料第二項", @"資料第三項", @"資料第四項"];
    bookItems = [self updatebook];
    /*
    寫3(寫.16字練3亂2)
    說3(說.16字練3亂2)
    寫6(寫.16字練6亂3)
    說6(說.16字練6亂3)
    寫9(寫.16字練9亂5)
    說9(說.16字練9亂5)
     
    音節寫6(寫.16字練6亂3)
    音節說6(說.16字練6亂3)
    寫1順(寫.200字練1)
    說1順(說.200字練1)
    寫1亂(寫.200字亂1)
    總考錯誤練習
    寫1順計(寫.200字練1)
    寫1亂計(寫.200字亂1)
    說1英亂計(說.200字亂1)
    說1中亂計(說.200字亂1)
     
    課本圖預習1
    課本圖預習3
    課本圖預習6
    課本圖預習9
    課本圖考試.順
    課本圖考試.亂
     */
    /*
    typeItems = @[@"寫3", @"說3", @"寫6", @"說6",@"寫9", @"說9",
                  @"音節寫6", @"音節說6", @"寫1順", @"說字1順", @"寫字1亂", @"總考錯誤練習",
                  @"寫1順計", @"寫1亂計", @"說1英亂計", @"說1中亂計", @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"];
     */
    typeItems = @[@"寫字3", @"寫字6",@"寫字9",@"寫字1順",@"寫字1順(計)",// @"寫字1亂", @"寫字1亂(計)",
                  @"寫拼3", @"寫拼6",@"寫拼9",@"寫拼1順", // @"寫拼1亂",@"寫拼1順(計)", @"寫拼1亂(計)",
                  @"說字3", @"說字6",@"說字9",@"說字1順", @"說字1順(計)", //@"說字1亂",  @"說字1亂(計)",
                  @"說拼3", @"說拼6",@"說拼9",@"說拼1順", @"說拼1順(計)", @"說拼1亂(計)", //@"說拼1亂",
                  @"寫句1順", @"寫句1亂",@"寫句1順(計)", @"寫句1亂(計)",
                  @"說句1順", @"說句1亂",//@"說句1順(計)", @"說句1亂(計)",
                  @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"];
  //  typeItems1 = @[@"課本圖-預習1", @"課本圖-預習3", @"課本圖-預習6", @"課本圖-預習9",@"課本圖-考試.順", @"課本圖-考試.亂",
  //                 @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"];
    l1Items = @[@"第01堂", @"第02堂", @"第03堂", @"第04堂",@"第05堂", @"第06堂",
                @"第07堂", @"第08堂", @"第09堂", @"第10堂",@"第11堂", @"第12堂",
                @"第13堂", @"第14堂", @"第15堂", @"第16堂",@"第17堂", @"第18堂",
                @"第19堂", @"第20堂", @"第21堂", @"第22堂",@"第23堂", @"第24堂",
                ];
    
    l2Items = @[@"A1", @"A2", @"A3", @"A4",@"A5", @"A6",@"A7", @"A8", @"A9", @"A10",
                @"A11", @"A12", @"A13", @"A14",@"A15", @"A16",@"A17", @"A18", @"A19", @"A20",
                @"A21", @"A22", @"A23", @"A24",@"A25", @"A26",@"A27", @"A28", @"A29", @"A30",
                @"A31", @"A32", @"A33", @"A34",@"A35", @"A36",@"A37", @"A38", @"A39", @"A40",
                @"A41", @"A42", @"A43", @"A44",@"A45", @"A46",@"A47", @"A48", @"A49", @"A50",
                @"A51", @"A52", @"A53", @"A54",@"A55", @"A56",@"A57", @"A58", @"A59", @"A60",
                @"A61", @"A62", @"A63", @"A64",@"A65", @"A66",@"A67", @"A68", @"A69", @"A70",
                @"A71", @"A72", @"A73", @"A74",@"A75", @"A76",@"A77", @"A78", @"A79", @"A80",
                @"A81", @"A82", @"A83", @"A84",@"A85", @"A86",@"A87", @"A88", @"A89", @"A90",
                @"A91", @"A92", @"A93", @"A94",@"A95", @"A96",@"A97", @"A98", @"A99"];
    [self printData];
    bookptr=0;
    classItems= [self updateclass];
    SetArray = [NSMutableArray array];
    dyItems = SetArray;
    // 初始化cell的樣式及名稱，告訴UITableView每個cell顯示的樣式，這裡指定只顯示預設(一行文字)
    UITableViewCell *tableViewCell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"DyDataCell"];
    
    // 設定Cell名稱、樣式
    [dyTableView registerClass:tableViewCell.class forCellReuseIdentifier:tableViewCell.reuseIdentifier];


    // DataSource 設定self，必需寫好對應的function才能取得正確資料
    dyTableView.dataSource = self;
    
    // Delegate 設定self，必需寫好對應的delegate function才能取得正確資訊
    dyTableView.delegate = self;
 
    bookda.dataSource = self;
    bookda.delegate = self;
    classda.dataSource = self;
    classda.delegate = self;
    typeda.dataSource = self;
    typeda.delegate = self;
    l1da.dataSource = self;
    l1da.delegate = self;
    l2da.dataSource = self;
    l2da.delegate = self;
    cptr=0;
    markmode=0;
    lmark.text=@"";
    [self LoadCourse];
    [self setupLayout];
}

- (void)setupLayout {
    // module items 配置 "預設", "連擊99", "轟炸竹東"
    // availableWriteModules map to string
    NSArray<NSNumber *> *modules = availableWriteModules();
    self.moduleItems = [modules.rac_sequence map:^NSString *(NSNumber *module) {
            return displayForWriteModule(module.integerValue);
        }].array;
    // 新增 picker view, 內容有 "預設", "連擊99", "轟炸竹東"
    UIPickerView *modulePickerView = [[UIPickerView alloc] init];
    self.modulePickerView = modulePickerView;
    modulePickerView.dataSource = self;
    modulePickerView.delegate = self;
    modulePickerView.translatesAutoresizingMaskIntoConstraints = NO;

    // 把所有的 UIPickerView 加到橫向的 UIStackView
    NSArray<UIView *> *pickers = @[
        l2da, 
        bookda, // 課本(l1ubu4)
        l1da, // 課表(第一堂)
        classda, // 題目(ball)
        modulePickerView, // 模組
        typeda, // 題型(寫字3)
    ];
    // remove all from superview
    for (UIView *picker in pickers) {
        [picker removeFromSuperview];
    }
    UIStackView *picksStackView = [[UIStackView alloc] initWithArrangedSubviews:pickers];
    picksStackView.axis = UILayoutConstraintAxisHorizontal;
    picksStackView.distribution = UIStackViewDistributionFillEqually;
    picksStackView.spacing = 10;
    picksStackView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:picksStackView];
    [picksStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft).offset(20);
        make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight).offset(-20);
        // bottom
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(0);
        make.height.equalTo(@160);
    }];
    // titles
    {
        // "模組" UILabel
        UILabel *lbModule = UILabel.new;
        // font size: 28
        lbModule.text = @"模組";
        // font color from lbBook
        lbModule.textColor = self.lbBook.textColor;
        // font size from lbBook
        lbModule.font = self.lbBook.font;
        NSArray<UIView *> *labels = @[
            UILabel.new, // empty
            self.lbBook, // 課本
            self.lbLesson, // 課表
            self.lbClass, // 題目
            lbModule, // 模組
            self.lbType, // 題型
        ];
        UIStackView *stackView = [[UIStackView alloc] initWithArrangedSubviews:labels];
        stackView.axis = UILayoutConstraintAxisHorizontal;
        stackView.distribution = UIStackViewDistributionFillEqually;
        stackView.spacing = 10;
        stackView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.view addSubview:stackView];
        [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
            make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
            // align picksStackView top
            make.bottom.equalTo(picksStackView.mas_top).offset(40);
            make.height.equalTo(@32);
        }];
    }
}

-(NSArray *) updatebook {
    NSArray *da=[[NSArray alloc] init];
    NSString *ss=@"";
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"class.csv"]];
    NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"class FIle:%@\n%@",DirFile,classContent);
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    for(int i=0;i<23+13;i++) {
            NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
            if([split1 count] > 2) {
                if([ss length] > 5) ss = [ss stringByAppendingString:@","];
                ss = [ss stringByAppendingString:split1[2]];
            }
    }
    /*
    ss = [ss stringByAppendingString:@"休息1分鐘"];
    ss = [ss stringByAppendingString:@","];
    ss = [ss stringByAppendingString:@"休息3分鐘"];
    ss = [ss stringByAppendingString:@","];
    ss = [ss stringByAppendingString:@"休息5分鐘"];
    */
    da = [ss componentsSeparatedByString:@","];
 
    return da;
}
-(NSArray *) updateclass {
    NSArray *da=[[NSArray alloc] init];
    NSString *ss=@"";
    NSString *sstr=@"";
    twptr=0;
    if ([bookItems[bookptr] isEqualToString:@"l1u1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"l1u5u8_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"l2u1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"l2u5u8_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"lbu1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"lbu5u8_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"up1u1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"up1u5u8_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"up2u1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"up2u5u8_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"up3u1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"up3u5u8_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"upsu1u4_s"]) sstr=bookItems[bookptr];
    if ([bookItems[bookptr] isEqualToString:@"upsu5u8_s"]) sstr=bookItems[bookptr];
    
    if([sstr length] > 5){
        
        NSString *od=@"";
        NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
        NSString *DirFile1=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:sstr]];
        NSString *DirFile=[DirFile1 stringByAppendingString:@".csv"];
        NSError *error;
        NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:&error];
        if(classContent==nil) classContent = [NSString stringWithContentsOfFile:DirFile encoding:-2147483646 error:&error];
        NSLog(@"read caption FIle:%@\n%@\n%@--%@",DirFile1,DirFile,classContent,error);
        NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
        if(bookpic==1) {
            for(int i=1;i< [splitLine count];i++) {
                    NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
                    if([split1 count] > 2) {
                        if([split1[0] isEqualToString:od]==false) {
                            od=split1[0];
                            if([ss length] > 5 ) ss = [ss stringByAppendingString:@","];
                            ss = [ss stringByAppendingString:od];
                        
                        }
                    }
            }
        } else {
            for(int i=1;i< [splitLine count];i++) {
                    NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
                    if([split1 count] > 2) {
                            od=split1[5];
                            if([ss length] > 1 ) ss = [ss stringByAppendingString:@","];
                            ss = [ss stringByAppendingString:od];
                    }
            }
        }
        /*
        ss=@"P02,P03,P04,P05,P06,P07,P08,P09,P10,P11,P12,P13,P14,P15,P16,P17_1,P17_2,P19,P20,P21_1,P21_2,P23,P24,P25,P26,P27_1,P27_2,P28,P29,P30,P31,P32,P33,P34,P35_1,P35_2,P37";
         */
        /*
        typeItems =  @[@"課本圖-預習1", @"課本圖-預習3", @"課本圖-預習6",@"課本圖-預習9",@"課本圖-考試.順", @"課本圖-考試.亂",
                       @"句說英順顯中1",@"句說英順顯中3",@"句說英順顯中6",@"句說英順顯中9",
                       @"句說英亂顯英1",@"句說英亂顯英3",@"句說英亂顯英6",@"句說英亂顯英9",
                       @"句說中亂顯英1",@"句說中亂顯英3",@"句說中亂顯英6",@"句說中亂顯英9",
                       @"句寫順顯中1",@"句寫順顯中3",@"句寫順顯中6",@"句寫順顯中9",
                       @"句寫亂顯中1",@"句寫亂顯中3",@"句寫亂顯中6",@"句寫亂顯中9",
                       @"句寫順聽中1",@"句寫順聽中3",@"句寫順聽中6",@"句寫順聽中9",
                       @"句寫亂聽中1",@"句寫亂聽中3",@"句寫亂聽中6",@"句寫亂聽中9",
                       @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"];
         */
        typeItems =  @[@"說書1", @"說書3", @"說書6",@"說書9",@"說書1順(計)",@"說書1亂(計)",
                       @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"];
        [typeda reloadAllComponents];
        da = [ss componentsSeparatedByString:@","];
        return da;
    }
    OHMySQLUser *user;
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"wordsort " condition:[NSString stringWithFormat:@"class = '%@'", bookItems[bookptr]]];
    NSError *error = nil;
    NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];

    for(int i=0;i<[tasks count];i++) {
        NSDictionary *dict = [tasks objectAtIndex:i];
        tword[i]=[[NSString alloc] initWithData:[dict objectForKey:@"word"]  encoding:NSUTF8StringEncoding];
        /*
        ID[UserMax]=[dict objectForKey:@"id"];
        TYPE[UserMax]=[dict objectForKey:@"type"];
        CNAME[UserMax]=[[NSString alloc] initWithData:[dict objectForKey:@"ename"]  encoding:NSUTF8StringEncoding];
        ENAME[UserMax]=[[NSString alloc] initWithData:[dict objectForKey:@"cname"]  encoding:NSUTF8StringEncoding];
        QRCODE[UserMax]=[[NSString alloc] initWithData:[dict objectForKey:@"qrcode"]  encoding:NSUTF8StringEncoding];
        UserMax++;
         */
    }
    twptr=[tasks count];
    /*
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",bookItems[bookptr],bookItems[bookptr]]];
    
    for(int i=0;i<21;i++) {
        NSString *localFilePath = [DirTest stringByAppendingPathComponent:[NSString stringWithFormat:@"l%03d.csv",i+1]];
        NSString* content = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
        NSLog(@"Class:%@",content);
        NSArray *splitLine = [content componentsSeparatedByString:@"\n"];
        if([splitLine count] >= 4) {
            NSArray *split1=[splitLine[1] componentsSeparatedByString:@","];
            NSArray *splitT=[splitLine[2] componentsSeparatedByString:@","];
            NSArray *split2=[splitLine[3] componentsSeparatedByString:@","];
            if(split2.count > 1) {
                for(int k=0;k<split2.count-1;k++) {
                    tword[twptr]=split2[k];
                    twptr++;
                }
            }
          //  ss = [ss stringByAppendingString:splitT[1]];
          //  ss = [ss stringByAppendingString:@","];
        }
    }
    */
    
    
    for(int i=0;i<twptr;i++) {
          ss = [ss stringByAppendingString:tword[i]];
          ss = [ss stringByAppendingString:@","];
    }
    da = [ss componentsSeparatedByString:@","];
    /*
    typeItems = @[@"寫字3", @"寫字6",@"寫字9",@"寫字1順", //@"寫字1亂",@"寫字1順(計)", @"寫字1亂(計)",
                  @"寫拼3", @"寫拼6",@"寫拼9",@"寫拼1順", @"寫拼1亂",//@"寫拼1順(計)", @"寫拼1亂(計)",
                  @"說字3", @"說字6",@"說字9",@"說字1順", //@"說字1亂",@"說字1順(計)", @"說字1亂(計)",
                  @"說拼3", @"說拼6",@"說拼9",@"說拼1順", @"說拼1亂",//@"說拼1順(計)", @"說拼1亂(計)",
                  @"寫句1順", @"寫句1亂",//@"寫句1順(計)", @"寫句1亂(計)",
                  @"說句1順", @"說句1亂",//@"說句1順(計)", @"說句1亂(計)",
                  @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"]; */
     
    typeItems = @[@"寫字3", @"寫字6",@"寫字9",@"寫字1順",@"寫字1順(計)",// @"寫字1亂", @"寫字1亂(計)",
                  @"寫拼3", @"寫拼6",@"寫拼9",@"寫拼1順", // @"寫拼1亂",@"寫拼1順(計)", @"寫拼1亂(計)",
                  @"說字3", @"說字6",@"說字9",@"說字1順", @"說字1順(計)", //@"說字1亂",  @"說字1亂(計)",
                  @"說拼3", @"說拼6",@"說拼9",@"說拼1順", @"說拼1順(計)", @"說拼1亂(計)", //@"說拼1亂",
                  @"寫句1順", @"寫句1亂",@"寫句1順(計)", @"寫句1亂(計)",
                  @"說句1順", @"說句1亂",//@"說句1順(計)", @"說句1亂(計)",
                  @"休息1分鐘", @"休息3分鐘", @"休息5分鐘"];
    //@"寫字1順(計)"@"寫句1順(計)", @"寫句1亂(計)",
    //@"說字1順(計)" @"說拼1順(計)", @"說拼1亂(計)"
    [typeda reloadAllComponents];
    return da;
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {
    [self dismissViewControllerAnimated:NO completion:nil];
}
-(void) CreateCourseDB {
    OHMySQLUser *user;
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    //OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"course" condition:nil];
    for(int j=1;j<=2;j++) {
        for(int i=1;i<=100;i++) {
            OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory INSERT:@"course" set:@{ @"course": [NSString stringWithFormat: @"A1-%02d", j], @"iindex": [NSString stringWithFormat: @"%d", i], @"context": @"", @"updater": @"", @"update_date": @"2019-10-19 12:12:12" }];
            NSError *error = nil;
            NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
        }
    }
   [coordinator disconnect];
}
-(void) DeleteCourse{
    OHMySQLUser *user;
    NSString *l2=@"";
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    /*
    switch([l2da selectedRowInComponent:0]) {
        case 0: l2=@"A1"; break;
        case 1: l2=@"A2"; break;
        case 2: l2=@"A3"; break;
        case 3: l2=@"A4"; break;
        case 4: l2=@"A5"; break;
        default: l2=@"A6"; break;
    }
     */
    l2=[NSString stringWithFormat: @"A%d",[l2da selectedRowInComponent:0]+1];
    NSString *course=[NSString stringWithFormat: @"%@-%02d",l2,[l1da selectedRowInComponent:0]+1];
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory DELETE:@"course" condition:[NSString stringWithFormat: @"course='%@'", course]];
    NSError *error=nil;
    [queryContext executeQueryRequest:query error:&error];
    [coordinator disconnect];
}
-(void) LoadCourse {
    OHMySQLUser *user;
    NSString *bookstr,*classstr,*typestr;
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    NSString *l2=@"";
    /*
    switch([l2da selectedRowInComponent:0]) {
        case 0: l2=@"A1"; break;
        case 1: l2=@"A2"; break;
        case 2: l2=@"A3"; break;
        case 3: l2=@"A4"; break;
        case 4: l2=@"A5"; break;
        default: l2=@"A6"; break;
    }
     */
    l2=[NSString stringWithFormat: @"A%d",[l2da selectedRowInComponent:0]+1];
    NSString *course=[NSString stringWithFormat: @"%@-%02d",l2,[l1da selectedRowInComponent:0]+1];
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"course" condition:[NSString stringWithFormat: @"course='%@'", course]];
    NSError *error = nil;
    NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
    [SetArray removeAllObjects];
    for(int i=0;i<[tasks count];i++) {
        NSDictionary *dict = [tasks objectAtIndex:i];
        int index=[[dict objectForKey:@"iindex"] intValue];
        NSString *context= [[NSString alloc] initWithData:[dict objectForKey:@"context"]                      encoding:NSUTF8StringEncoding];
        [SetArray addObject:context];
    }
    [coordinator disconnect];
    //Default
    bookptr=0;
    classItems= [self updateclass];
    [classda reloadAllComponents];
    
    dyItems = SetArray;
    [dyTableView reloadData];
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:0];
    [dyTableView selectRowAtIndexPath:indexPath
                           animated:NO
                     scrollPosition:UITableViewScrollPositionNone];
}
-(void) InsertCourse {
    OHMySQLUser *user;
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    NSString *l2=@"";
    /*
    switch([l2da selectedRowInComponent:0]) {
        case 0: l2=@"A1"; break;
        case 1: l2=@"A2"; break;
        case 2: l2=@"A3"; break;
        case 3: l2=@"A4"; break;
        case 4: l2=@"A5"; break;
        default: l2=@"A6"; break;
    }*/
    l2=[NSString stringWithFormat: @"A%d",[l2da selectedRowInComponent:0]+1];
    NSString *course=[NSString stringWithFormat: @"%@-%02d",l2,[l1da selectedRowInComponent:0]+1];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    NSString *strDate = [dateFormatter stringFromDate:[NSDate date]];
    for(int i=0;i<SetArray.count;i++) {
        NSString *str=[NSString stringWithFormat: @"%@",SetArray[i]];
        NSString *context = [str stringByReplacingOccurrencesOfString:@"'"withString:@"\''"];
        NSLog(@"context ==== %@", context);

        OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory INSERT:@"course" set:@{ @"course": course ,@"iindex": [NSString stringWithFormat: @"%d", i+1], @"context": context, @"updater": CNAME, @"update_date": strDate }];
        NSError *error = nil;
        NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
    }
    [coordinator disconnect];
}
- (IBAction)btUpdate:(id)sender {
   // [self CreateCourseDB];
    [self DeleteCourse];
    [self InsertCourse];
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"已更新課程" delegate:self cancelButtonTitle:@"確認" otherButtonTitles:nil];
    [alertView show];
}
- (IBAction)btAddClass:(id)sender {
    int dd=0;
    if((SetArray.count)==0) {
        [SetArray addObject:@""];
        dyItems = SetArray;
        [dyTableView reloadData];
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
    } else {
        if([SetArray[SetArray.count-1] length] > 3) {
            lwptr=0;
            [SetArray addObject:@""];
            dyItems = SetArray;
            [dyTableView reloadData];

            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
            [dyTableView selectRowAtIndexPath:indexPath
                                   animated:NO
                             scrollPosition:UITableViewScrollPositionNone];
        }
    }
}
- (IBAction)btAddWord:(id)sender {
    NSString *bookstr,*classstr,*typestr;
    c1[cptr]=[bookda selectedRowInComponent:0];
    c2[cptr]=[classda selectedRowInComponent:0];
    c3[cptr]=[typeda selectedRowInComponent:0];
    cptr++;
//    [SetArray removeAllObjects];
//    for(int i=0;i<cptr;i++) {
    int i=(cptr-1);
    bookstr= [bookItems objectAtIndex:c1[i]];
    classstr= [classItems objectAtIndex:c2[i]];
    typestr= [typeItems objectAtIndex:c3[i]];
    [SetArray removeLastObject];
    if ([typestr isEqualToString:@"休息1分鐘"]) { [SetArray addObject:[NSString stringWithFormat:@"[%@]",typestr]];
        dyItems = SetArray;
        [dyTableView reloadData];
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
        return; }
    if ([typestr isEqualToString:@"休息3分鐘"]) { [SetArray addObject:[NSString stringWithFormat:@"[%@]",typestr]];
        dyItems = SetArray;
        [dyTableView reloadData];
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
        return; }
    if ([typestr isEqualToString:@"休息5分鐘"]) { [SetArray addObject:[NSString stringWithFormat:@"[%@]",typestr]];
        dyItems = SetArray;
        [dyTableView reloadData];
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
        return; }
    
    //if([typestr )
    
    if(lwptr> 200) lwptr=200;
    if(markmode==2) {
        for(int j=markstart;j<=markend;j++) {
            lword[lwptr]=[classItems objectAtIndex:j];
            lwptr++;
        }
    } else {
        lword[lwptr]=classstr;
        lwptr++;
    }
    NSString *s=[NSString stringWithFormat:@"[%@][%@]",bookstr,typestr];
    for(int i=0;i<lwptr;i++) {
        s=[NSString stringWithFormat:@"%@[%@]",s,lword[i]];
    }
    [SetArray addObject:s];
    /*
    switch(lwptr) {
        case  1: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@]",bookstr,
                                      typestr, lword[0]]]; break;
        case  2: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1]]]; break;
        case  3: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2]]]; break;
        case  4: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3]]]; break;
        case  5: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4]]]; break;
        case  6: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5]]]; break;
        case  7: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6]]]; break;
        case  8: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7]]]; break;
        case  9: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8]]]; break;
        case 10: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9]]]; break;
        case 11: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                     typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9],lword[10]]]; break;
        case 12: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9],lword[10],lword[11]]]; break;
        case 13: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9],lword[10],lword[11],lword[12]]]; break;
        case 14: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9],lword[10],lword[11],lword[12],lword[13]]]; break;
        case 15: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9],lword[10],lword[11],lword[12],lword[13],lword[14]]]; break;
        case 16: [SetArray addObject:[NSString stringWithFormat:@"[%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@][%@]",bookstr,
                                      typestr, lword[0],lword[1],lword[2],lword[3],lword[4],lword[5],lword[6],lword[7],lword[8],lword[9],lword[10],lword[11],lword[12],lword[13],lword[14],lword[15]]]; break;
     
    }*/
    dyItems = SetArray;
    [dyTableView reloadData];
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
    [dyTableView selectRowAtIndexPath:indexPath
                           animated:NO
                     scrollPosition:UITableViewScrollPositionNone];
    /// clear mark
    markmode=0;
    lmark.text=@"";
    /// and add class
    /*
    int dd=0;
    if((SetArray.count)==0) {
        [SetArray addObject:@""];
        dyItems = SetArray;
        [dyTableView reloadData];
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
    } else {
        if([SetArray[SetArray.count-1] length] > 3) {
            lwptr=0;
            [SetArray addObject:@""];
            dyItems = SetArray;
            [dyTableView reloadData];

            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
            [dyTableView selectRowAtIndexPath:indexPath
                                   animated:NO
                             scrollPosition:UITableViewScrollPositionNone];
        }
    }
     */
}
- (IBAction)btDeleteClass:(id)sender {
    NSString *bookstr,*classstr,*typestr;
    NSIndexPath *sIP=[dyTableView indexPathForSelectedRow];
    NSInteger row;
    if(sIP==nil) return;
    if([SetArray count] < 1) {
        return;
    }
    row=sIP.row;
    [SetArray removeObjectAtIndex:row];
    dyItems = SetArray;
    [dyTableView reloadData];
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:[SetArray count]-1 inSection:0];
    [dyTableView selectRowAtIndexPath:indexPath
                           animated:NO
                     scrollPosition:UITableViewScrollPositionNone];
}
- (IBAction)btUpClass:(id)sender {
    
    NSInteger row;
    NSIndexPath *sIP=[dyTableView indexPathForSelectedRow];
    if(sIP==nil) return;
    row=sIP.row;
    if(row!=0) {
        [SetArray exchangeObjectAtIndex:row withObjectAtIndex: row-1];
        dyItems = SetArray;
        [dyTableView reloadData];
    
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row-1 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
    }
}
- (IBAction)btDownClass:(id)sender {
    NSInteger row;
    NSIndexPath *sIP=[dyTableView indexPathForSelectedRow];
    if(sIP==nil) return;
    row=sIP.row;
    if(row < ([SetArray count]-1)) {
        [SetArray exchangeObjectAtIndex:row withObjectAtIndex: row+1];
        dyItems = SetArray;
        [dyTableView reloadData];
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row+1 inSection:0];
        [dyTableView selectRowAtIndexPath:indexPath
                               animated:NO
                         scrollPosition:UITableViewScrollPositionNone];
    }
}
- (IBAction)btPlayQuick:(id)sender {
    DDLogDebugTag(@"PlayViewController", @"btPlayQuick");
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    // debug
    //NSString *str=@"$uten_class01,START,type,lbu1u4,tag(lxxxx),00004~";
    /*
    WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
    [myViewController setValue:@"[up3u5u8][寫1順][tall][short][old][young]" forKey:@"seltag"];
    [myViewController setValue:@"up3u5u8" forKey:@"uclass"];
    [self presentViewController:myViewController animated:YES completion:nil];
    return;
  */
    //
    
    
    
    
    NSIndexPath *sIP=[dyTableView indexPathForSelectedRow];
    if(sIP==nil) return;
    int playindex=sIP.row;
    NSString *str=[NSString stringWithFormat:@"$%@,START,%04d,%@,%04d,%@,end~",uten_class,c3[playindex],[bookItems objectAtIndex:c1[playindex]],c2[playindex],SetArray[playindex]];
    DDLogDebugTag(@"PlayViewController", @"str:%@", str);
    NSArray *ss = [str componentsSeparatedByString:@","];
    DDLogDebugTag(@"PlayViewController", @"ss:%@", ss);
    //$uten_class01,START,type,lbu1u4,tag(lxxxx),00004~
    if([ss count] > 5) {
        NSString *tmp = [[NSString alloc] initWithFormat:(@"$%@"),uten_class];
        if ([ss[0] isEqualToString:tmp]) {
            if ([ss[1] isEqualToString:@"START"]) {
                NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
                NSArray *da = [ss[5] componentsSeparatedByCharactersInSet:set];
                DDLogDebugTag(@"PlayViewController", @"da=%@", da);
                if(da.count > 3) {
                    DDLogDebugTag(@"PlayViewController", @"ss=%@", ss[5]);
                    DDLogDebugTag(@"PlayViewController", @"da=%@", da[1]);
                    if (([da[3] isEqualToString:@"寫字3"])|([da[3] isEqualToString:@"寫字6"])|([da[3] isEqualToString:@"寫字9"])) {
                        WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                        myViewController.writeModule = self.currentWriteModule;
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    if (([da[3] isEqualToString:@"寫字1順"])| ([da[3] isEqualToString:@"寫字1亂"])) {
                        WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                        myViewController.writeModule = self.currentWriteModule;
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    if (([da[3] isEqualToString:@"寫拼3"])|([da[3] isEqualToString:@"寫拼6"])|([da[3] isEqualToString:@"寫拼9"])) {
                        WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                        myViewController.writeModule = self.currentWriteModule;
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    if (([da[3] isEqualToString:@"寫拼1順"])| ([da[3] isEqualToString:@"寫拼1亂"])) {
                        WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                        myViewController.writeModule = self.currentWriteModule;
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    
                    //Database
                    if (([da[3] isEqualToString:@"寫字1順(計)"]) | ([da[3] isEqualToString:@"寫句1順(計)"])| ([da[3] isEqualToString:@"寫句1亂(計)"])) {
                        WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                        myViewController.writeModule = self.currentWriteModule;
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    
                    if (([da[3] isEqualToString:@"說字3"])|([da[3] isEqualToString:@"說字6"])|([da[3] isEqualToString:@"說字9"])) {
                        //JAMES0417
                        Game01Controller *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"Game01Controller"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                        /*
                        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                        [myViewController setValue:ss[5]forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                         */
                        
                    }
                    if (([da[3] isEqualToString:@"說字1順"])|([da[3] isEqualToString:@"說字1亂"])) {
                        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    if (([da[3] isEqualToString:@"說句1順"])|([da[3] isEqualToString:@"說句1亂"])) {
                        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    //DataBase
                    if (([da[3] isEqualToString:@"說字1順(計)"])|([da[3] isEqualToString:@"說拼1順(計)"])|([da[3] isEqualToString:@"說拼1亂(計)"])) {
                        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    if (([da[3] isEqualToString:@"說拼3"])|([da[3] isEqualToString:@"說拼6"])|([da[3] isEqualToString:@"說拼9"])) {
                        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    if (([da[3] isEqualToString:@"說拼1順"])|([da[3] isEqualToString:@"說拼1亂"])) {
                        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                    }
                    
                    if (([da[3] isEqualToString:@"說書1"])|([da[3] isEqualToString:@"說書3"])|([da[3] isEqualToString:@"說書6"])|([da[3] isEqualToString:@"說書9"]))  {
                        eMaterialViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"eMaterialViewController"];
                        [myViewController setValue:ss[5] forKey:@"seltag"];
                        [myViewController setValue:da[1] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                        
                    }
                }
                /*
                switch([ss[2] intValue]) {
                    case 0: {
                            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                            [myViewController setValue:ss[4] forKey:@"seltag"];
                            [myViewController setValue:ss[3] forKey:@"uclass"];
                            [self presentViewController:myViewController animated:YES completion:nil];
                            }
                            break;
                    case 1: {
                        WriteWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteWordViewController"];
                        [myViewController setValue:ss[4] forKey:@"seltag"];
                        [myViewController setValue:ss[3] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                        }
                        break;

                    case 2: {
                        WriteMutilWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilWordViewController"];
                        [myViewController setValue:ss[4] forKey:@"seltag"];
                        [myViewController setValue:ss[3] forKey:@"uclass"];
                        [self presentViewController:myViewController animated:YES completion:nil];
                        }
                        break;
                    case 3: {
                        WriteMutilViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilViewController"];
                            [myViewController setValue:ss[4] forKey:@"seltag"];
                            [myViewController setValue:ss[3] forKey:@"uclass"];
                            [self presentViewController:myViewController animated:YES completion:nil];
                        }
                        break;
                    default:
                        {
                            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                            [myViewController setValue:ss[2] forKey:@"cType"];
                            [myViewController setValue:ss[4] forKey:@"seltag"];
                            [myViewController setValue:ss[3] forKey:@"uclass"];
                            [self presentViewController:myViewController animated:YES completion:nil];
                        }
                        break;
                }
                 */
            }
        }
    } else {
    }
}
- (IBAction)btMaker:(id)sender {
    switch(markmode) {
        case 0: markstart=[classda selectedRowInComponent:0];
                lmark.text=[NSString stringWithFormat:@"標記範圍:%@",[classItems objectAtIndex:markstart]];
                markmode=1;
                break;
        case 1: markend=[classda selectedRowInComponent:0];
                if(markend > markstart) {
                    lmark.text=[NSString stringWithFormat:@"標記範圍:%@ ~ %@",[classItems objectAtIndex:markstart],[classItems objectAtIndex:markend]];
                    markmode=2;
                } else {
                    markmode=0;
                    lmark.text=@"";
                }
                break;
        case 2: markmode=0;
                lmark.text=@"";
                break;
            
    }
}
- (IBAction)btPlayClass:(id)sender {
    DDLogDebugTag(@"PlayViewController", @"btPlayClass");
    NSString *bookstr;
    NSInteger bookp,classp,typep;
    bookp = [bookda selectedRowInComponent:0];
    classp = [classda selectedRowInComponent:0];
    typep = [typeda selectedRowInComponent:0];
    bookstr= [bookItems objectAtIndex:bookp];
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    NSLog(@"book:%@ , class:%d",bookstr,classp);
    switch(typep) {
        case 0: {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            myViewController.writeModule = self.currentWriteModule;
            [myViewController setValue:[NSString stringWithFormat: @"%ld", classp] forKey:@"seltag"];
            [myViewController setValue:bookstr forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
            break;
        case 1: {
            WriteWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteWordViewController"];
            [myViewController setValue:[NSString stringWithFormat: @"%ld", classp] forKey:@"seltag"];
            [myViewController setValue:bookstr forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
            break;
            
        case 2: {
            WriteMutilWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilWordViewController"];
            [myViewController setValue:[NSString stringWithFormat: @"%ld", classp] forKey:@"seltag"];
            [myViewController setValue:bookstr forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
            break;
            
        case 3: {
            WriteMutilViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilViewController"];
            [myViewController setValue:[NSString stringWithFormat: @"%ld", classp] forKey:@"seltag"];
            [myViewController setValue:bookstr forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
            break;
        case 4: {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:@"12" forKey:@"cType"];
            [myViewController setValue:[NSString stringWithFormat: @"%ld", classp] forKey:@"seltag"];
            [myViewController setValue:bookstr forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
            break;
        case 5: {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:@"11" forKey:@"cType"];
            [myViewController setValue:[NSString stringWithFormat: @"%ld", classp] forKey:@"seltag"];
            [myViewController setValue:bookstr forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
            break;
    }
}
//點選cell後會呼叫此function告知哪個cell已經被選擇(0開始)
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    NSLog(@"已選擇的cell編號:%ld",indexPath.row);
    NSLog(@"cell值: %@",[dyItems objectAtIndex:indexPath.row]);
    NSLog(@"\r\n");
    DDLogDebugTag(@"PlayViewController", @"已選擇的cell=%@", self.currentDyItem);
}
//返回總共有多少cell筆數
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return dyItems.count;
}
//根據cellForRowAtIndexPath來取得目前TableView需要哪個cell的資料
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    // 取得tableView目前使用的cell
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"DyDataCell" forIndexPath: indexPath];
    // 將指定資料顯示於tableview提供的text
    cell.textLabel.text = dyItems[indexPath.row];
    /*
    static NSString *CellIdentifier = @"Cell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
    // Configure the cell...
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
    }
    // Display recipe in the table cell
    UILabel *recipeNameLabel = (UILabel *)[cell viewWithTag:101];
    recipeNameLabel.text = dyItems[indexPath.row];
    */
    return cell;
}
///
// The number of columns of data
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView
{
    return 1;
}
// The number of rows of data
- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component
{
    if(pickerView==bookda) return bookItems.count;
    if(pickerView==classda) return classItems.count;
    if(pickerView==typeda) return typeItems.count;
    if(pickerView==l1da) return l1Items.count;
    if(pickerView==l2da) return l2Items.count;
    if (pickerView == self.modulePickerView) {
        return self.moduleItems.count;
    }
    return bookItems.count;
}
// The data to return for the row and component (column) that's being passed in
- (NSString*)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component
{
    if(pickerView==bookda) return bookItems[row];
    if(pickerView==classda) return classItems[row];
    if(pickerView==typeda) return typeItems[row];
    if(pickerView==l1da) return l1Items[row];
    if(pickerView==l2da) return l2Items[row];
    if (pickerView == self.modulePickerView) {
        return [self.moduleItems objectAtIndex:row];
    }
    return bookItems[row];
}
- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    NSLog(@"JAMES select : %d",row);
    if(pickerView==bookda) {
        DDLogDebugTag(@"PlayViewController", @"課本=%@", self.currentBookItem);
        bookptr=row;
        bookpic=1;
        classItems= [self updateclass];
        [classda reloadAllComponents];
    }
    if(pickerView==l1da) {
        DDLogDebugTag(@"PlayViewController", @"課表=%@", self.currentL1Item);
        if([dyItems count] > 0) {
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要編輯其他課程?\n(請先存檔先前的紀錄)" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"編輯其他",nil];
            [alertView show];
        } else [self LoadCourse];
        
    }
    if(pickerView==l2da) {
        DDLogDebugTag(@"PlayViewController", @"課本=%@", self.currentL2Item);
        if([dyItems count] > 0) {
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要編輯其他課程?\n(請先存檔先前的紀錄)" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"編輯其他",nil];
            [alertView show];
        } else [self LoadCourse];
    }
    if(pickerView==classda) {
        DDLogDebugTag(@"PlayViewController", @"題目=%@", self.currentClassItem);
        //NSLog(@"%@",[typeItems objectAtIndex:[typeda selectedRowInComponent:0]]);
        NSString *s=[typeItems objectAtIndex:[typeda selectedRowInComponent:0]];
        if ([s containsString:@"課本圖"]) {
            NSLog(@"%@",[classItems objectAtIndex:[classda selectedRowInComponent:0]]);
            NSString *imgname=[classItems objectAtIndex:[classda selectedRowInComponent:0]];
            preView.image=[UIImage imageNamed:[NSString stringWithFormat:@"%@.jpg", imgname]];
            bookpic=1;
        }
        if ([s containsString:@"句"]) {
            ltitle.text=[classItems objectAtIndex:[classda selectedRowInComponent:0]];
        } else  ltitle.text=@"";
    }
    if(pickerView==typeda) {
        DDLogDebugTag(@"PlayViewController", @"題型=%@", self.currentTypeItem);
        NSString *s=[typeItems objectAtIndex:[typeda selectedRowInComponent:0]];
        if ([s containsString:@"句"]) {
            NSLog(@"句");
            bookpic=0;
            classItems= [self updateclass];
            [classda reloadAllComponents];
        }
        if ([s containsString:@"課本圖"]) {
            bookpic=1;
            classItems= [self updateclass];
            [classda reloadAllComponents];
        }
    }
    if (pickerView == self.modulePickerView) {
        DDLogDebugTag(@"PlayViewController", @"模組=%@", self.currentWriteModuleItem);
        self.currentWriteModule = row;
    }
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    switch (buttonIndex) {
        case 0:
            NSLog(@"Cancel Button Pressed");
            break;
        case 1:
            [self LoadCourse];
            NSLog(@"Button 1 Pressed");
            break;
        default:
            break;
    }
}
- (IBAction)CalTime:(id)sender {
    float totaltime=0.0f;
    /*
    hudView = [[JHUD alloc]initWithFrame:self.view.bounds];
    hudView.frame= CGRectMake(352.0f, 234.0f, 300.0f, 300.0f);
    //hudView.alpha=0.8f;
    hudView.messageLabel.text = @"課程計算中,請稍候!";
     
    [hudView showAtView:self.view hudType:JHUDLoadingTypeCircleJoin];
     */
    for(int i=0;i<[SetArray count];i++) {
        NSString *str=[NSString stringWithFormat:@"$%@,START,%04d,%@,%04d,%@,end~",uten_class,c3[i],[bookItems objectAtIndex:c1[i]],c2[i],SetArray[i]];
        NSArray *ss = [str componentsSeparatedByString:@","];
        NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
        NSArray *da = [ss[5] componentsSeparatedByCharactersInSet:set];
        seltag=ss[5];
        uclass=da[1];
        totaltime+=[self calclasstime];
        //NSLog(@"CalTime:%1.1f",[self calclasstime]);
    }
    //[JHUD hideForView:self.view];
    
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:[NSString stringWithFormat:@"本課程共耗時%1.1f秒",totaltime] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
    [alert show];
    NSLog(@"totaltime:%1.1f",totaltime);
    
}
-(float) calclasstime {
    NSString *ss[401][16];
    NSString *cc[401][16];
    NSString *ee[401][16];
    
    int gptr;
    NSString *DirSound;
    float audioDurationSeconds=0;
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile1=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:uclass]];
    NSString *DirFile=[DirFile1 stringByAppendingString:@".csv"];
    //NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    
    NSError *error;
    NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:&error];
    if(classContent==nil) classContent = [NSString stringWithContentsOfFile:DirFile encoding:-2147483646 error:&error];
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    
    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
    NSArray *lda = [seltag componentsSeparatedByCharactersInSet:set];
    gptr=0;
    int gcnt=0;
    if ([lda[1] isEqualToString:@"休息1分鐘"]) {
        return 60.0f;
        
    }
    if ([lda[1] isEqualToString:@"休息3分鐘"]) {
        return 180.0f;

    }
    if ([lda[1] isEqualToString:@"休息5分鐘"]) {
        return 300.0f;
    }
    if ([lda[3] isEqualToString:@"課本圖-預習1"])  gcnt=1;
    if ([lda[3] isEqualToString:@"課本圖-預習3"])  gcnt=3;
    if ([lda[3] isEqualToString:@"課本圖-預習6"])  gcnt=6;
    if ([lda[3] isEqualToString:@"課本圖-預習9"])  gcnt=9;
    if(gcnt==0) return 0.0f;
    DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",uclass,uclass]];
    audioDurationSeconds = 0;
    //CMTimeGetSeconds(audioDuration);
        for(int k=5;k<[lda count];k+=2) {
            for(int i=1;i< [splitLine count];i++) {
                    NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
                    if([lda[k] isEqualToString:split1[0]]) {
                        for(int g=0;g<gcnt;g++) {
                            
                            if([split1[7] isEqualToString:@"*"]) { ss[gptr][0]=split1[8]; cc[gptr][0]=split1[9];  }
                            else { ss[gptr][0]=split1[10]; cc[gptr][0]=split1[11]; }
                            {
                                NSString *base = DirSound;
                                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[gptr][0]];
                                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                                
                                AVURLAsset* audioAsset = [AVURLAsset URLAssetWithURL:soundUrl options:nil];
                                CMTime audioDuration = audioAsset.duration;
                                audioDurationSeconds += CMTimeGetSeconds(audioDuration)+1.0f;
                            }
                            for(int j=0;j<[split1 count];j++) {
                                if([split1[j] containsString:@"_2c.mp3"]) {
                                    ss[gptr][1]=split1[j];
                                    //
                                    NSString *base = DirSound;
                                    NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[gptr][1]];
                                    NSURL *soundUrl = [NSURL fileURLWithPath:path];
                                    
                                    AVURLAsset* audioAsset = [AVURLAsset URLAssetWithURL:soundUrl options:nil];
                                    CMTime audioDuration = audioAsset.duration;
                                    audioDurationSeconds += CMTimeGetSeconds(audioDuration);
                                    break;
                                }
                            }
                            ee[gptr][0]=split1[5];
                            gptr++;
                        }
                    }
            }
        }
    return audioDurationSeconds;
}

- (void)printData {
    DDLogDebugTag(@"PlayViewController", @"課本 l2Items=%@", l2Items);
    DDLogDebugTag(@"PlayViewController", @"課本 bookItems=%@", bookItems);
    DDLogDebugTag(@"PlayViewController", @"課表 l1Items=%@", l1Items);
    DDLogDebugTag(@"PlayViewController", @"題目 classItems=%@", classItems);
    DDLogDebugTag(@"PlayViewController", @"模組 moduleItems=%@", self.moduleItems);
    DDLogDebugTag(@"PlayViewController", @"題型 typeItems=%@", typeItems);
    DDLogDebugTag(@"PlayViewController", @"TableView dyItems=%@", dyItems);
}

- (NSString *)currentL1Item {
    NSInteger row = [l1da selectedRowInComponent:0];
    return [l1Items objectAtIndex:row];
}

- (NSString *)currentL2Item {
    NSInteger row = [l2da selectedRowInComponent:0];
    return [l2Items objectAtIndex:row];
}

- (NSString *)currentClassItem {
    NSInteger row = [classda selectedRowInComponent:0];
    return [classItems objectAtIndex:row];
}

- (NSString *)currentBookItem {
    NSInteger row = [bookda selectedRowInComponent:0];
    return [bookItems objectAtIndex:row];
}

- (NSString *)currentTypeItem {
    NSInteger row = [typeda selectedRowInComponent:0];
    return [typeItems objectAtIndex:row];
}

- (NSString *)currentWriteModuleItem {
    NSInteger row = [self.modulePickerView selectedRowInComponent:0];
    return [self.moduleItems objectAtIndex:row];
}

- (NSString *)currentDyItem {
    NSIndexPath *indexPath=[dyTableView indexPathForSelectedRow];
    return [dyItems objectAtIndex:indexPath.row];
}

@end
