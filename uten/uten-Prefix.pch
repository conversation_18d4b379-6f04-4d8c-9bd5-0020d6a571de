//
//  uten-Prefix.pch
//  uten
//
//  Created on 2025/02/20
//

#ifndef uten_Prefix_pch
#define uten_Prefix_pch

#define LUMBERJACK

// Include any system framework and library headers here
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <CocoaLumberjack/CocoaLumberjack.h>
#import <MQTTClient/MQTTLog.h>

// Include project specific headers here
#import "UTENConstants.h"
#import "UTENEnum.h"
#import "AppRoutes.h"

// Add your custom macros and definitions here
#ifdef DEBUG
    #define DLog(...) NSLog(__VA_ARGS__)
    #define DDLogInfoTag(tag, frmt, ...) \
        LOG_MAYBE(LOG_ASYNC_ENABLED, LOG_LEVEL_DEF, DDLogFlagInfo, 0, tag, __PRETTY_FUNCTION__, frmt, ##__VA_ARGS__)
    #define DDLogErrorTag(tag, frmt, ...) \
        LOG_MAYBE(LOG_ASYNC_ENABLED, LOG_LEVEL_DEF, DDLogFlagError, 0, tag, __PRETTY_FUNCTION__, frmt, ##__VA_ARGS__)
    #define DDLogWarnTag(tag, frmt, ...) \
        LOG_MAYBE(LOG_ASYNC_ENABLED, LOG_LEVEL_DEF, DDLogFlagWarning, 0, tag, __PRETTY_FUNCTION__, frmt, ##__VA_ARGS__)
    #define DDLogDebugTag(tag, frmt, ...) \
        LOG_MAYBE(LOG_ASYNC_ENABLED, LOG_LEVEL_DEF, DDLogFlagDebug, 0, tag, __PRETTY_FUNCTION__, frmt, ##__VA_ARGS__)
    #define DDLogVerboseTag(tag, frmt, ...) \
        LOG_MAYBE(LOG_ASYNC_ENABLED, LOG_LEVEL_DEF, DDLogFlagVerbose, 0, tag, __PRETTY_FUNCTION__, frmt, ##__VA_ARGS__)
#else
    #define DLog(...)
    #define DDLogInfoTag(tag, frmt, ...)
    #define DDLogErrorTag(tag, frmt, ...)
    #define DDLogWarnTag(tag, frmt, ...)
    #define DDLogDebugTag(tag, frmt, ...)
    #define DDLogVerboseTag(tag, frmt, ...)
#endif

// 設定日誌等級
// static const DDLogLevel ddLogLevel = DDLogLevelDebug;

#endif /* uten_Prefix_pch */
