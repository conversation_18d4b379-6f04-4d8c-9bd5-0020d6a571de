//
//  PureSayViewController.m
//  uten
//
//  Created by 簡大翔 on 2021/10/27.
//  Copyright © 2021 bekubee. All rights reserved.
//

#import "PureSayViewController.h"
#import <OpenEars/OEPocketsphinxController.h>
#import <OpenEars/OEFliteController.h>
#import <OpenEars/OELanguageModelGenerator.h>
#import <OpenEars/OELogging.h>
#import <OpenEars/OEAcousticModel.h>
#import "GRRequestsManager.h"
#import "GRListingRequest.h"
#import <Slt/Slt.h>
#import "SSZipArchive.h"
#import "JHUD.h"
@interface PureSayViewController () <AVAudioRecorderDelegate,GRRequestsManagerDelegate>  {
    AVAudioPlayer *_audioPlayer;
    NSMutableDictionary *recordSetting;
    AVAudioRecorder *recorder;
    NSString *recorderFilePath;
    Boolean isRecord;
    NSString *eword[209];
    JHUD *hudView;
    NSTimer *cTimer;
    int state;
    Boolean StartTraining;
    int title;
    int loop;
    IBOutlet UILabel *M1Info;
    IBOutlet UILabel *M2Info;
    IBOutlet UILabel *M3Info;
    IBOutlet UIButton *StRecord;
    NSString *ENAME,*CNAME;
    NSString *ServerIP;
    Boolean StartMod;
    int ModelCnt;
    
}
@property (nonatomic, strong) Slt *slt;
@property (nonatomic, strong) GRRequestsManager *requestsManager;
@property (nonatomic, strong) OEEventsObserver *openEarsEventsObserver;
@property (nonatomic, strong) OEPocketsphinxController *pocketsphinxController;
@property (nonatomic, strong) OEFliteController *fliteController;
@property (nonatomic, copy) NSString *pathToFirstDynamicallyGeneratedLanguageModel;
@property (nonatomic, copy) NSString *pathToFirstDynamicallyGeneratedDictionary;
@property (nonatomic, copy) NSString *pathToSecondDynamicallyGeneratedLanguageModel;
@property (nonatomic, copy) NSString *pathToSecondDynamicallyGeneratedDictionary;

@property (nonatomic, assign) BOOL usingStartingLanguageModel;
@property (nonatomic, assign) int restartAttemptsDueToPermissionRequests;
@property (nonatomic, assign) BOOL startupFailedDueToLackOfPermissions;
@end

@implementation PureSayViewController
#define DOCUMENTS_FOLDER [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"]
- (void)viewDidLoad {
    [super viewDidLoad];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    ENAME = [defaults objectForKey:@"ENAME"];
    CNAME = [defaults objectForKey:@"CNAME"];
   // ID=[NSString stringWithFormat:@"James001"];
    
    NSString *filePathAndDirectory =[NSString stringWithFormat:@"%@/%@",DOCUMENTS_FOLDER,CNAME];
    
    NSError *error1;

    if (![[NSFileManager defaultManager] createDirectoryAtPath:filePathAndDirectory
                                   withIntermediateDirectories:NO
                                                    attributes:nil
                                                         error:&error1])
    {
        NSLog(@"Create directory error: %@", error1);
    }
    
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:ServerIP user:@"uten" password:@"zZ54775178"];
    self.requestsManager.delegate=self;
    state=0;
    StartMod=false;
    ModelCnt=0;
    NSString *ee[209]={
        @"a",@"apple",@"art",@"at",
        @"baby",@"bad",@"bag",@"ball",@"banana",@"bathroom",@"bear",@"bedroom",
        @"big",@"bike",@"bird",@"black",@"blue",@"boat",@"book",
        @"boy",@"breakfast",@"brother",@"brown",@"bus",@"by",@"cake",@"can",@"car",
        @"card",@"cat",@"chair",@"chicken",@"clean",@"coat",
        @"coffee",@"cold",@"color",@"come",@"computer",@"cook",@"cow",@"cup",
        @"dance",@"desk",@"dinner",@"dirty",@"do",@"doctor",@"dog",
        @"door",@"draw",@"dress",@"drink",@"duck",@"ears",@"eat",@"egg",
        @"eight",@"eighteen",@"eighty",@"elephant",@"eleven",@"english",@"eraser",@"excited",@"eyes",@"face",
        @"fall",@"farm",@"farmer",@"father",@"fifteen",@"fifty",
        @"fish",@"five",@"flower",@"foot",@"four",    //11
        @"fourteen",@"friends",@"frog",@"fruit",@"get",@"girl",
        @"go",@"grapes",@"gray",@"green",@"hair",@"hand",
        @"happy",@"hat",@"head",@"home",@"horse",@"hospital",
        @"hot",@"house",@"in",@"japan",
        @"juice",@"jump",@"key",@"kitchen",@"kite",
        @"leg",@"lion",@"listen",@"long",@"lunch",
        @"mad",@"math",@"milk",@"monkey",@"moon",@"mother",
        @"mouth",@"music",@"new",@"night",@"nine",
        @"nineteen",@"ninety",@"nose",@"not",@"nurse",
        @"old",@"on",@"one",@"orange",@"pants",@"park",@"pen",  //21
        @"pencil",@"pig",@"pink",@"pizza",@"plane",@"play",@"purple",@"rabbit",
        @"rainy",@"read",@"red",@"restaurant",@"rice",@"ride",@"robot",@"ruler",@"run",@"sad",
        @"sandwich",@"school",@"science",@"seven",@"seventeen",@"seventh",
        @"shirt",@"shoes",@"shop",@"short",@"sick",@"sing",@"sister",
        @"sit",@"six",@"sixteen",@"sixth",@"skirt",@"sky",@"sleep",@"small",@"sock",
        @"sofa",@"soup",@"stand",@"star",@"student",@"study",
        @"sun",@"sunny",@"supermarket",@"sweater",@"swim",@"table",
        @"talk",@"tall",@"tea",@"teacher",@"ten",
        @"thin",@"thirty",@"three",@"tiger",
        @"tired",@"train",@"tree",@"turtle",@"twelve",@"twenty", //31
        @"two",@"uncle",@"under",@"walk",@"wash",@"watch",
        @"water",@"white",@"window",@"yellow",
    };
    for(int i=0;i<209;i++) eword[i]=ee[i];
    // Do any additional setup after loading the view.
    self.fliteController = [[OEFliteController alloc] init];
    self.openEarsEventsObserver = [[OEEventsObserver alloc] init];
    self.openEarsEventsObserver.delegate = self;
    self.slt = [[Slt alloc] init];
    [OELogging startOpenEarsLogging]; // Uncomment me for OELogging, which is verbose logging about internal OpenEars operations such as audio settings. If you have issues, show this logging in the forums.
    [OEPocketsphinxController sharedInstance].verbosePocketSphinx = TRUE; // Uncomment this for much more verbose speech recognition engine output. If you have issues, show this logging in the forums.
    
    [self.openEarsEventsObserver setDelegate:self]; // Make this class the delegate of OpenEarsObserver so we can get all of the messages about what OpenEars is doing.
    
    [[OEPocketsphinxController sharedInstance] setActive:TRUE error:nil]; // Call this before setting any OEPocketsphinxController characteristics
   
    
    NSMutableArray *persons = [NSMutableArray array];
    for (int i = 0; i < 209; i++) {
        [persons addObject:ee[i]];
    }
    
    NSArray *firstLanguageArray = [NSArray arrayWithArray:persons];

     
    

    OELanguageModelGenerator *languageModelGenerator = [[OELanguageModelGenerator alloc] init];
    NSError *error = [languageModelGenerator generateLanguageModelFromArray:firstLanguageArray withFilesNamed:@"FirstOpenEarsDynamicLanguageModel" forAcousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"]]; // Change "AcousticModelEnglish" to "AcousticModelSpanish" in order to create a language model for Spanish recognition instead of English.
    if(error) {
        NSLog(@"Dynamic language generator reported error %@", [error description]);
    } else {
        self.pathToFirstDynamicallyGeneratedLanguageModel = [languageModelGenerator pathToSuccessfullyGeneratedLanguageModelWithRequestedName:@"FirstOpenEarsDynamicLanguageModel"];
        self.pathToFirstDynamicallyGeneratedDictionary = [languageModelGenerator pathToSuccessfullyGeneratedDictionaryWithRequestedName:@"FirstOpenEarsDynamicLanguageModel"];
    }
    
    /*
    if(![OEPocketsphinxController sharedInstance].isListening) {
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't already listening.
    }
     */
    isRecord=false;
    cTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    M3Info.text=@"準備中....";
    M2Info.text=eword[title];
    M1Info.text=[NSString stringWithFormat:@"第%03d字/%d次",title+1,loop];
}
-(void)timesUp:(NSTimer *)timer{
    if(StartTraining) {
        state++;
        switch(state) {
            case 5: {
                    loop++;
                    if(loop > 10) {
                        if(title < 209) {
                            title++;
                            loop=1;
                        }
                    }
                    NSString *pewPewPath = [[NSBundle mainBundle] pathForResource:eword[title] ofType:@"mp3"];
                    NSURL *soundUrl = [NSURL fileURLWithPath:pewPewPath];
                    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
                    [audioSession setCategory:AVAudioSessionCategoryPlayback error:nil];
                     NSURL *url = [NSURL fileURLWithPath:pewPewPath];
                    NSError *error;
                    _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:url error:&error];
                    _audioPlayer.numberOfLoops = 0;
                    [_audioPlayer play];
                    M3Info.text=@"播放中....";
                M2Info.text=eword[title];
                M1Info.text=[NSString stringWithFormat:@"第%03d字/%d次",title+1,loop];
                    
                }
                break;
            case 7:
                if(_audioPlayer.isPlaying) state=5;
                
                break;
            case 8:
                break;
                
        }
        /*
        if(state++ > 100) {
            hudView.hidden=YES;
        }
         */
    } else state=0;
    if(StartMod) {
        
        switch(ModelCnt) {

            case 210:
                hudView.messageLabel.text = @"分析檔案....!";
                break;
            case 300:
                hudView.messageLabel.text = @"建立完成....!";
            {
                char *saves = "abcd";
                NSData *data = [[NSData alloc] initWithBytes:saves length:4];
                NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
                NSString *documentsDirectory = [paths objectAtIndex:0];
                NSString *appFile =  [documentsDirectory stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.model",CNAME]];
                [data writeToFile:appFile atomically:YES];
                NSLog(@"DBG File:%@",appFile);
            }
                break;
            case 330:
                hudView.hidden=YES;
                StartMod=false;
                ModelCnt=0;
                break;
            default:
                if(ModelCnt < 209) {
                    hudView.messageLabel.text = [NSString stringWithFormat:@"檢查:%@",eword[ModelCnt]];
                    
                    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];

                    NSString* foofile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/%@_09.caf",CNAME,eword[ModelCnt]]];
                    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:foofile];
                    // debug
                    //fileExists=true;
                    if(!fileExists) {
                        M3Info.text=[NSString stringWithFormat:@"音檔不完整:%@",eword[ModelCnt]];
                        hudView.hidden=YES;
                        StartMod=false;
                        ModelCnt=0;
                    }
                }
                break;

                
        }
        ModelCnt++;
    }
        
}
- (IBAction)btBack:(id)sender {

  //  [self dismissViewControllerAnimated:NO completion:nil];
}
- (IBAction)btRecordPress:(id)sender {
    [self startRecording];
    isRecord=true;
}
- (IBAction)btRecordUp:(id)sender {
    [self stopRecording];
    isRecord=false;
}
- (IBAction)btRecord:(id)sender {
    if(!isRecord) {
        [self startRecording];
        isRecord=true;
    } else {
        [self stopRecording];
        isRecord=false;
        
    }
}
- (IBAction)btDelete:(id)sender {
    
    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString* foofile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@",CNAME]];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    [fileManager removeItemAtPath:foofile error:nil];
    
    NSString *filePathAndDirectory =[NSString stringWithFormat:@"%@/%@",documentsPath,CNAME];
    
    NSError *error1;

    if (![[NSFileManager defaultManager] createDirectoryAtPath:filePathAndDirectory
                                   withIntermediateDirectories:NO
                                                    attributes:nil
                                                         error:&error1])
    {
        NSLog(@"Create directory error: %@", error1);
    }
    M3Info.text=[NSString stringWithFormat:@"清除完成"];

}

- (IBAction)btTesting:(id)sender {
    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString* foofile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.model",CNAME]];
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:foofile];
    NSLog(@"DBG Model:%@",foofile);
    if(fileExists) {
        if(![OEPocketsphinxController sharedInstance].isListening) {
            [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't already listening.
        }
    } else {

        M3Info.text=[NSString stringWithFormat:@"找不到Model檔案"];
        //test
        /*
        char *saves = "abcd";
        NSData *data = [[NSData alloc] initWithBytes:saves length:4];
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *documentsDirectory = [paths objectAtIndex:0];
        NSString *appFile =  [documentsDirectory stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/%@.model",CNAME,CNAME]];
        [data writeToFile:appFile atomically:YES];
         */
    }
    /*
    NSString *path = [NSString stringWithFormat:@"%@/001.caf",DOCUMENTS_FOLDER];
    NSURL *soundUrl = [NSURL fileURLWithPath:path];

    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [audioSession setCategory:AVAudioSessionCategoryPlayback error:nil];
    
     NSURL *url = [NSURL fileURLWithPath:path];
    NSError *error;
    _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:url error:&error];
    _audioPlayer.numberOfLoops = 0;
    [_audioPlayer play];
    NSLog(@"playing");

    NSLog(@"PLAY:%@", path);
     
     */
    /*
    NSString *pewPewPath = [[NSBundle mainBundle] pathForResource:@"dog" ofType:@"mp3"];
    NSURL *soundUrl = [NSURL fileURLWithPath:pewPewPath];

    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [audioSession setCategory:AVAudioSessionCategoryPlayback error:nil];
    
     NSURL *url = [NSURL fileURLWithPath:pewPewPath];
    NSError *error;
    _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:url error:&error];
    _audioPlayer.numberOfLoops = 0;
    [_audioPlayer play];
    NSLog(@"playing");

    NSLog(@"PLAY:%@", pewPewPath);
    */
    
    
}
- (IBAction)btUpload:(id)sender {
    /*
    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString* foofile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.model",CNAME]];
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:foofile];
    NSLog(@"DBG Model:%@",foofile);
    if(fileExists) {
        NSString* filePath = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/%@_09.caf",CNAME,eword[0]]];
        NSString *remote= [NSString stringWithFormat:@"upload/%@_%@_09.caf",CNAME,eword[0]];
        [self.requestsManager addRequestForUploadFileAtLocalPath:filePath toRemotePath:remote];
        [self.requestsManager startProcessingRequests];
    } else {

        M3Info.text=[NSString stringWithFormat:@"找不到Model檔案"];

    }
*/
    /*
    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString* foofile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.model",CNAME]];
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:foofile];
    NSString* filePath = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/%@_09.caf",CNAME,eword[0]]];
    NSData *pdfData = [NSData dataWithContentsOfFile:foofile];
       NSArray *activityItems = [NSArray arrayWithObjects: pdfData, nil];
       UIActivityViewController *activityController = [[UIActivityViewController alloc] initWithActivityItems:activityItems applicationActivities:nil];
       //if iPhone
       if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone) {
           [self presentViewController:activityController animated:YES completion:nil];
       }
       //if iPad
       else {
           // Change Rect to position Popover
           UIPopoverController *popup = [[UIPopoverController alloc] initWithContentViewController:activityController];
           [popup presentPopoverFromRect:CGRectMake(self.view.frame.size.width/2, self.view.frame.size.height/4, 0, 0)inView:self.view permittedArrowDirections:UIPopoverArrowDirectionAny animated:YES];
       }
    */
    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString* zipfile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.zip",CNAME]];
   // NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES)lastObject];
      // zip压缩包保存路径
    //  NSString *path = [cachesPath stringByAppendingPathComponent:@"SSZipArchive.zip"];
      // 需要压缩的文件夹路径
    NSString* filePath = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/",CNAME]];
      // 创建不带密码zip压缩包
    BOOL isSuccess = [SSZipArchive createZipFileAtPath:zipfile withContentsOfDirectory:filePath ];
    if(isSuccess) {
        NSData *pdfData = [NSData dataWithContentsOfFile:zipfile];
        NSArray *activityItems = [NSArray arrayWithObjects: pdfData, nil];
        UIActivityViewController *activityController = [[UIActivityViewController alloc] initWithActivityItems:activityItems applicationActivities:nil];
           //if iPhone
        if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone) {
               [self presentViewController:activityController animated:YES completion:nil];
           }
           //if iPad
        else {
               // Change Rect to position Popover
               UIPopoverController *popup = [[UIPopoverController alloc] initWithContentViewController:activityController];
               [popup presentPopoverFromRect:CGRectMake(self.view.frame.size.width/2, self.view.frame.size.height/4, 0, 0)inView:self.view permittedArrowDirections:UIPopoverArrowDirectionAny animated:YES];
        }
    }
      // 创建带密码zip压缩包
      //BOOL isSuccess = [SSZipArchive createZipFileAtPath:path withContentsOfDirectory:folderPath withPassword:@"SSZipArchive.zip"];

}
- (IBAction)btBuild:(id)sender {

    hudView = [[JHUD alloc]initWithFrame:self.view.bounds];
    hudView.frame= CGRectMake(352.0f, 234.0f, 300.0f, 300.0f);
    //hudView.alpha=0.8f;
    hudView.messageLabel.text = @"模型建立中......,請稍候!";
    [hudView showAtView:self.view hudType:JHUDLoadingTypeCircleJoin];
    hudView.hidden=NO;
    StartMod=true;
    ModelCnt=0;
}
- (IBAction)btStartNow:(id)sender {

    StartTraining=true;
    state=0;
}
- (IBAction)btPrevWord:(id)sender {

    if(title > 0) {loop=0; title--; M1Info.text=[NSString stringWithFormat:@"第%03d字/%d次",title+1,1];  M2Info.text=eword[title]; }
}
- (IBAction)btNextWord:(id)sender {

    if(title < 209) {loop=0; title++; M1Info.text=[NSString stringWithFormat:@"第%03d字/%d次",title+1,1];  M2Info.text=eword[title]; }
    
}
-(NSArray *)listFileAtPath:(NSString *)path
{
    //-----> LIST ALL FILES <-----//
    NSLog(@"LISTING ALL FILES FOUND");
    int count;
    NSArray *directoryContent = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:path error:NULL];
    for (count = 0; count < (int)[directoryContent count]; count++)
    {
        
        NSNumber *fileSizeValue = nil;
        NSString *filePath=[NSString stringWithFormat:@"%@/%@",DOCUMENTS_FOLDER,[directoryContent objectAtIndex:count]];
        NSURL *fileURL = [NSURL fileURLWithPath:filePath];
        NSError *fileSizeError = nil;
        [fileURL getResourceValue:&fileSizeValue
                           forKey:NSURLFileSizeKey
                            error:&fileSizeError];
        NSLog(@"File %d: %@  %@", (count + 1),  [directoryContent objectAtIndex:count],fileSizeValue);
        /*
        if (fileSizeValue) {
        NSLog(@"File %d: %@  %@", (count + 1), [directoryContent objectAtIndex:count],fileSizeValue);
        } else {
            NSLog(@"File %d: %@", (count + 1), [directoryContent objectAtIndex:count]);
        }
        */
    }
    return directoryContent;
}
#pragma mark -
#pragma mark OEEventsObserver delegate methods

// What follows are all of the delegate methods you can optionally use once you've instantiated an OEEventsObserver and set its delegate to self.
// I've provided some pretty granular information about the exact phase of the Pocketsphinx listening loop, the Audio Session, and Flite, but I'd expect
// that the ones that will really be needed by most projects are the following:
//
//- (void) pocketsphinxDidReceiveHypothesis:(NSString *)hypothesis recognitionScore:(NSString *)recognitionScore utteranceID:(NSString *)utteranceID;
//- (void) audioSessionInterruptionDidBegin;
//- (void) audioSessionInterruptionDidEnd;
//- (void) audioRouteDidChangeToRoute:(NSString *)newRoute;
//- (void) pocketsphinxDidStartListening;
//- (void) pocketsphinxDidStopListening;
//
// It isn't necessary to have a OEPocketsphinxController or a OEFliteController instantiated in order to use these methods.  If there isn't anything instantiated that will
// send messages to an OEEventsObserver, all that will happen is that these methods will never fire.  You also do not have to create a OEEventsObserver in
// the same class or view controller in which you are doing things with a OEPocketsphinxController or OEFliteController; you can receive updates from those objects in
// any class in which you instantiate an OEEventsObserver and set its delegate to self.


// This is an optional delegate method of OEEventsObserver which delivers the text of speech that Pocketsphinx heard and analyzed, along with its accuracy score and utterance ID.
- (void) pocketsphinxDidReceiveHypothesis:(NSString *)hypothesis recognitionScore:(NSString *)recognitionScore utteranceID:(NSString *)utteranceID {
    
    NSLog(@"Local callback: The received hypothesis is %@ with a score of %@ and an ID of %@", hypothesis, recognitionScore, utteranceID); // Log it.
    if([hypothesis isEqualToString:@"change model"]) { // If the user says "change model", we will switch to the alternate model (which happens to be the dynamically generated model).
        
        // Here is an example of language model switching in OpenEars. Deciding on what logical basis to switch models is your responsibility.
        // For instance, when you call a customer service line and get a response tree that takes you through different options depending on what you say to it,
        // the models are being switched as you progress through it so that only relevant choices can be understood. The construction of that logical branching and
        // how to react to it is your job; OpenEars just lets you send the signal to switch the language model when you've decided it's the right time to do so.
        
        if(self.usingStartingLanguageModel) { // If we're on the starting model, switch to the dynamically generated one.
            
            [[OEPocketsphinxController sharedInstance] changeLanguageModelToFile:self.pathToSecondDynamicallyGeneratedLanguageModel withDictionary:self.pathToSecondDynamicallyGeneratedDictionary];
            self.usingStartingLanguageModel = FALSE;
            
        } else { // If we're on the dynamically generated model, switch to the start model (this is an example of a trigger and method for switching models).
            
            [[OEPocketsphinxController sharedInstance] changeLanguageModelToFile:self.pathToFirstDynamicallyGeneratedLanguageModel withDictionary:self.pathToFirstDynamicallyGeneratedDictionary];
            self.usingStartingLanguageModel = TRUE;
        }
    }
    
    ///self.heardTextView.text = [NSString stringWithFormat:@"Heard: \"%@\"", hypothesis]; // Show it in the status box.
    
    // This is how to use an available instance of OEFliteController. We're going to repeat back the command that we heard with the voice we've chosen.
    //[self.fliteController say:[NSString stringWithFormat:@"You said %@",hypothesis] withVoice:self.slt];
    NSLog(@"JAMES Say:%@",hypothesis);
    M3Info.text=[NSString stringWithFormat:@"Say:%@", hypothesis];
   // if([hypothesis isEqualToString:eword[nowIdx]]) Answer=true;
}

#ifdef kGetNbest
- (void) pocketsphinxDidReceiveNBestHypothesisArray:(NSArray *)hypothesisArray { // Pocketsphinx has an n-best hypothesis dictionary.
    NSLog(@"Local callback:  hypothesisArray is %@",hypothesisArray);
}
#endif
// An optional delegate method of OEEventsObserver which informs that there was an interruption to the audio session (e.g. an incoming phone call).
- (void) audioSessionInterruptionDidBegin {
    NSLog(@"Local callback:  AudioSession interruption began."); // Log it.
    ///self.statusTextView.text = @"Status: AudioSession interruption began."; // Show it in the status box.
    NSError *error = nil;
    if([OEPocketsphinxController sharedInstance].isListening) {
        error = [[OEPocketsphinxController sharedInstance] stopListening]; // React to it by telling Pocketsphinx to stop listening (if it is listening) since it will need to restart its loop after an interruption.
        if(error) NSLog(@"Error while stopping listening in audioSessionInterruptionDidBegin: %@", error);
    }
}

// An optional delegate method of OEEventsObserver which informs that the interruption to the audio session ended.
- (void) audioSessionInterruptionDidEnd {
    NSLog(@"Local callback:  AudioSession interruption ended."); // Log it.
    ///self.statusTextView.text = @"Status: AudioSession interruption ended."; // Show it in the status box.
    // We're restarting the previously-stopped listening loop.
    if(![OEPocketsphinxController sharedInstance].isListening){
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't currently listening.
    }
}

// An optional delegate method of OEEventsObserver which informs that the audio input became unavailable.
- (void) audioInputDidBecomeUnavailable {
    NSLog(@"Local callback:  The audio input has become unavailable"); // Log it.
    ///self.statusTextView.text = @"Status: The audio input has become unavailable"; // Show it in the status box.
    NSError *error = nil;
    if([OEPocketsphinxController sharedInstance].isListening){
        error = [[OEPocketsphinxController sharedInstance] stopListening]; // React to it by telling Pocketsphinx to stop listening since there is no available input (but only if we are listening).
        if(error) NSLog(@"Error while stopping listening in audioInputDidBecomeUnavailable: %@", error);
    }
}

// An optional delegate method of OEEventsObserver which informs that the unavailable audio input became available again.
- (void) audioInputDidBecomeAvailable {
    NSLog(@"Local callback: The audio input is available"); // Log it.
    ///self.statusTextView.text = @"Status: The audio input is available"; // Show it in the status box.
    if(![OEPocketsphinxController sharedInstance].isListening) {
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition, but only if we aren't already listening.
    }
}
// An optional delegate method of OEEventsObserver which informs that there was a change to the audio route (e.g. headphones were plugged in or unplugged).
- (void) audioRouteDidChangeToRoute:(NSString *)newRoute {
    NSLog(@"Local callback: Audio route change. The new audio route is %@", newRoute); // Log it.
    ///self.statusTextView.text = [NSString stringWithFormat:@"Status: Audio route change. The new audio route is %@",newRoute]; // Show it in the status box.
    
    NSError *error = [[OEPocketsphinxController sharedInstance] stopListening]; // React to it by telling the Pocketsphinx loop to shut down and then start listening again on the new route
    
    if(error)NSLog(@"Local callback: error while stopping listening in audioRouteDidChangeToRoute: %@",error);
    
    if(![OEPocketsphinxController sharedInstance].isListening) {
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't already listening.
    }
}

// An optional delegate method of OEEventsObserver which informs that the Pocketsphinx recognition loop has entered its actual loop.
// This might be useful in debugging a conflict between another sound class and Pocketsphinx.
- (void) pocketsphinxRecognitionLoopDidStart {
    
    NSLog(@"Local callback: Pocketsphinx started."); // Log it.
    ///self.statusTextView.text = @"Status: Pocketsphinx started."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx is now listening for speech.
- (void) pocketsphinxDidStartListening {
    
    NSLog(@"Local callback: Pocketsphinx is now listening."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx is now listening."; // Show it in the status box.
    
//    self.startButton.hidden = TRUE; // React to it with some UI changes.
//    self.stopButton.hidden = FALSE;
//    self.suspendListeningButton.hidden = FALSE;
//    self.resumeListeningButton.hidden = TRUE;
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx detected speech and is starting to process it.
- (void) pocketsphinxDidDetectSpeech {
    NSLog(@"Local callback: Pocketsphinx has detected speech."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has detected speech."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx detected a second of silence, indicating the end of an utterance.
// This was added because developers requested being able to time the recognition speed without the speech time. The processing time is the time between
// this method being called and the hypothesis being returned.
- (void) pocketsphinxDidDetectFinishedSpeech {
    NSLog(@"Local callback: Pocketsphinx has detected a second of silence, concluding an utterance."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has detected finished speech."; // Show it in the status box.
}


// An optional delegate method of OEEventsObserver which informs that Pocketsphinx has exited its recognition loop, most
// likely in response to the OEPocketsphinxController being told to stop listening via the stopListening method.
- (void) pocketsphinxDidStopListening {
    NSLog(@"Local callback: Pocketsphinx has stopped listening."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has stopped listening."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx is still in its listening loop but it is not
// Going to react to speech until listening is resumed.  This can happen as a result of Flite speech being
// in progress on an audio route that doesn't support simultaneous Flite speech and Pocketsphinx recognition,
// or as a result of the OEPocketsphinxController being told to suspend recognition via the suspendRecognition method.
- (void) pocketsphinxDidSuspendRecognition {
    NSLog(@"Local callback: Pocketsphinx has suspended recognition."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has suspended recognition."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx is still in its listening loop and after recognition
// having been suspended it is now resuming.  This can happen as a result of Flite speech completing
// on an audio route that doesn't support simultaneous Flite speech and Pocketsphinx recognition,
// or as a result of the OEPocketsphinxController being told to resume recognition via the resumeRecognition method.
- (void) pocketsphinxDidResumeRecognition {
    NSLog(@"Local callback: Pocketsphinx has resumed recognition."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has resumed recognition."; // Show it in the status box.
}

// An optional delegate method which informs that Pocketsphinx switched over to a new language model at the given URL in the course of
// recognition. This does not imply that it is a valid file or that recognition will be successful using the file.
- (void) pocketsphinxDidChangeLanguageModelToFile:(NSString *)newLanguageModelPathAsString andDictionary:(NSString *)newDictionaryPathAsString {
    NSLog(@"Local callback: Pocketsphinx is now using the following language model: \n%@ and the following dictionary: %@",newLanguageModelPathAsString,newDictionaryPathAsString);
}

// An optional delegate method of OEEventsObserver which informs that Flite is speaking, most likely to be useful if debugging a
// complex interaction between sound classes. You don't have to do anything yourself in order to prevent Pocketsphinx from listening to Flite talk and trying to recognize the speech.
- (void) fliteDidStartSpeaking {
    NSLog(@"Local callback: Flite has started speaking"); // Log it.
 //   self.statusTextView.text = @"Status: Flite has started speaking."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Flite is finished speaking, most likely to be useful if debugging a
// complex interaction between sound classes.
- (void) fliteDidFinishSpeaking {
    NSLog(@"Local callback: Flite has finished speaking"); // Log it.
 //   self.statusTextView.text = @"Status: Flite has finished speaking."; // Show it in the status box.
}

- (void) pocketSphinxContinuousSetupDidFailWithReason:(NSString *)reasonForFailure { // This can let you know that something went wrong with the recognition loop startup. Turn on [OELogging startOpenEarsLogging] to learn why.
    NSLog(@"Local callback: Setting up the continuous recognition loop has failed for the reason %@, please turn on [OELogging startOpenEarsLogging] to learn more.", reasonForFailure); // Log it.
 //   self.statusTextView.text = @"Status: Not possible to start recognition loop."; // Show it in the status box.
}

- (void) pocketSphinxContinuousTeardownDidFailWithReason:(NSString *)reasonForFailure { // This can let you know that something went wrong with the recognition loop startup. Turn on [OELogging startOpenEarsLogging] to learn why.
    NSLog(@"Local callback: Tearing down the continuous recognition loop has failed for the reason %@, please turn on [OELogging startOpenEarsLogging] to learn more.", reasonForFailure); // Log it.
 //   self.statusTextView.text = @"Status: Not possible to cleanly end recognition loop."; // Show it in the status box.
}

- (void) testRecognitionCompleted { // A test file which was submitted for direct recognition via the audio driver is done.
    NSLog(@"Local callback: A test file which was submitted for direct recognition via the audio driver is done."); // Log it.
    NSError *error = nil;
    if([OEPocketsphinxController sharedInstance].isListening) { // If we're listening, stop listening.
        error = [[OEPocketsphinxController sharedInstance] stopListening];
        if(error) NSLog(@"Error while stopping listening in testRecognitionCompleted: %@", error);
    }
    
}
/** Pocketsphinx couldn't start because it has no mic permissions (will only be returned on iOS7 or later).*/
- (void) pocketsphinxFailedNoMicPermissions {
    NSLog(@"Local callback: The user has never set mic permissions or denied permission to this app's mic, so listening will not start.");
    self.startupFailedDueToLackOfPermissions = TRUE;
    if([OEPocketsphinxController sharedInstance].isListening){
        NSError *error = [[OEPocketsphinxController sharedInstance] stopListening]; // Stop listening if we are listening.
        if(error) NSLog(@"Error while stopping listening in micPermissionCheckCompleted: %@", error);
    }
}

/** The user prompt to get mic permissions, or a check of the mic permissions, has completed with a TRUE or a FALSE result  (will only be returned on iOS7 or later).*/
- (void) micPermissionCheckCompleted:(BOOL)result {
    if(result) {
        self.restartAttemptsDueToPermissionRequests++;
        if(self.restartAttemptsDueToPermissionRequests == 1 && self.startupFailedDueToLackOfPermissions) { // If we get here because there was an attempt to start which failed due to lack of permissions, and now permissions have been requested and they returned true, we restart exactly once with the new permissions.
            
            if(![OEPocketsphinxController sharedInstance].isListening) { // If there was no error and we aren't listening, start listening.
                [[OEPocketsphinxController sharedInstance]
                 startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel
                 dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary
                 acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"]
                 languageModelIsJSGF:FALSE]; // Start speech recognition.
                
                self.startupFailedDueToLackOfPermissions = FALSE;
            }
        }
    }
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/


- (void) startRecording{



        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        NSError *err = nil;
        [audioSession setCategory :AVAudioSessionCategoryPlayAndRecord error:&err];
        if(err){
            NSLog(@"audioSession: %@ %d %@", [err domain], [err code], [[err userInfo] description]);
            return;
        }
        [audioSession setActive:YES error:&err];
        err = nil;
        if(err){
            NSLog(@"audioSession: %@ %d %@", [err domain], [err code], [[err userInfo] description]);
            return;
        }

        recordSetting = [[NSMutableDictionary alloc] init];

        [recordSetting setValue :[NSNumber numberWithInt:kAudioFormatLinearPCM] forKey:AVFormatIDKey];
        [recordSetting setValue:[NSNumber numberWithFloat:44100.0] forKey:AVSampleRateKey];
        [recordSetting setValue:[NSNumber numberWithInt: 2] forKey:AVNumberOfChannelsKey];

        [recordSetting setValue :[NSNumber numberWithInt:16] forKey:AVLinearPCMBitDepthKey];
        [recordSetting setValue :[NSNumber numberWithBool:NO] forKey:AVLinearPCMIsBigEndianKey];
        [recordSetting setValue :[NSNumber numberWithBool:NO] forKey:AVLinearPCMIsFloatKey];



// Create a new dated file
        NSDate *now = [NSDate dateWithTimeIntervalSinceNow:0];
        NSString *caldate = [now description];
    recorderFilePath = [NSString stringWithFormat:@"%@/%@/%@_%02d.caf", DOCUMENTS_FOLDER, CNAME, eword[title],loop] ;

    NSLog(@"record PATH:%@",recorderFilePath);
        NSURL *url = [NSURL fileURLWithPath:recorderFilePath];
        err = nil;
        recorder = [[ AVAudioRecorder alloc] initWithURL:url settings:recordSetting error:&err];
    
        if(!recorder){
            NSLog(@"recorder: %@ %d %@", [err domain], [err code], [[err userInfo] description]);
            UIAlertView *alert =
            [[UIAlertView alloc] initWithTitle: @"Warning"
                                       message: [err localizedDescription]
                                      delegate: nil
                             cancelButtonTitle:@"OK"
                             otherButtonTitles:nil];
            [alert show];
            
            return;
        }

        //prepare to record
    M3Info.text=@"錄音中....";
        [recorder setDelegate:self];
        [recorder prepareToRecord];
        recorder.meteringEnabled = YES;

        BOOL audioHWAvailable = audioSession.inputIsAvailable;
        if (! audioHWAvailable) {
            UIAlertView *cantRecordAlert =
            [[UIAlertView alloc] initWithTitle: @"Warning"
                                       message: @"Audio input hardware not available"
                                      delegate: nil
                             cancelButtonTitle:@"OK"
                             otherButtonTitles:nil];
            [cantRecordAlert show];
            
            return;
        }

        // start recording
        [recorder recordForDuration:(NSTimeInterval) 10];

}

- (void) stopRecording{

        [recorder stop];

        NSURL *url = [NSURL fileURLWithPath: recorderFilePath];
        NSError *err = nil;
        NSData *audioData = [NSData dataWithContentsOfFile:[url path] options: 0 error:&err];
        if(!audioData)
            NSLog(@"audio data: %@ %d %@", [err domain], [err code], [[err userInfo] description]);
        state=0;
    M3Info.text=@"錄音結束....";
        //[recorder deleteRecording];

/*
        NSFileManager *fm = [NSFileManager defaultManager];

        err = nil;
        [fm removeItemAtPath:[url path] error:&err];
        if(err)
            NSLog(@"File Manager: %@ %d %@", [err domain], [err code], [[err userInfo] description]);

*/
}

- (void)audioRecorderDidFinishRecording:(AVAudioRecorder *) aRecorder successfully:(BOOL)flag
{

NSLog (@"audioRecorderDidFinishRecording:successfully:");
    
// your actions here
    [self listFileAtPath:[NSString stringWithFormat:@"%@/%@",DOCUMENTS_FOLDER,@"Class01"]];
    NSLog(@"PATH:%@",DOCUMENTS_FOLDER);
}
@end
