//#import "RankingL2ViewController.h"
//#import <QuartzCore/QuartzCore.h>
//#import <OHMySQL/OHMySQL.h>
//
//@interface RankingL2ViewController ()
//@property (nonatomic, strong) UILabel *numberLabel;
//@property (nonatomic, strong) UIButton *receivedButton;
//@property (nonatomic, assign) NSInteger savedRandomNumber;
//
//@end
//
//@implementation RankingL2ViewController
//

//(轉場傳送.文字+圖片)----------------------------------------
//- (void)viewDidLoad {
//    [super viewDidLoad];
//    
//    switch (self.buttonTag) {
//        case 1:
//            self.label1.text = @"課程";
//            self.label2.text = @"UP1.U1-U4.堂數";
//            self.label3.text = @"總量";
//            self.label4.text = @"進度";
//            self.label5.text = @"完成";
//            self.label6.text = @"欠課";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL001.png"];
//            break;
//        case 2:
//            self.label1.text = @"單字";
//            self.label2.text = @"UP1.U1-U4.單字";
//            self.label3.text = @"總量";
//            self.label4.text = @"進度";
//            self.label5.text = @"通過";
//            self.label6.text = @"錯誤";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL002.png"];
//            break;
//        case 3:
//            self.label1.text = @"口說";
//            self.label2.text = @"UP1.U1-U4.說句";
//            self.label3.text = @"總量";
//            self.label4.text = @"進度";
//            self.label5.text = @"完成";
//            self.label6.text = @"未完";
//            self.label7.text = @"錯誤";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL003.png"];
//            break;
//        case 4:
//            self.label1.text = @"句型";
//            self.label2.text = @"UP1.U1-U4.寫句";
//            self.label3.text = @"總量";
//            self.label4.text = @"進度";
//            self.label5.text = @"完成";
//            self.label6.text = @"未完";
//            self.label7.text = @"錯誤";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL004.png"];
//            break;
//        case 5:
//            self.label1.text = @"對話";
//            self.label2.text = @"生活對話.說句";
//            self.label3.text = @"總量";
//            self.label4.text = @"進度";
//            self.label5.text = @"欠量";
//            self.label6.text = @"錯誤";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL005.png"];
//            break;
//        case 6:
//            self.label1.text = @"寫字幣";
//            self.label2.text = @"寫字.字量金幣";
//            self.label3.text = @"總字量";
//            self.label4.text = @"三星獎勵";
//            self.label5.text = @"勤學獎勵";
//            self.label6.text = @"簽到獎勵";
//            self.label7.text = @"樂透獎勵";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL006.png"];
//            break;
//        case 7:
//            self.label1.text = @"寫句幣";
//            self.label2.text = @"寫句.句量金幣";
//            self.label3.text = @"總句量";
//            self.label4.text = @"三星獎勵";
//            self.label5.text = @"勤學獎勵";
//            self.label6.text = @"簽到獎勵";
//            self.label7.text = @"樂透獎勵";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL007.png"];
//            break;
//        case 8:
//            self.label1.text = @"說字幣";
//            self.label2.text = @"說字.字量金幣";
//            self.label3.text = @"總字量";
//            self.label4.text = @"三星獎勵";
//            self.label5.text = @"勤學獎勵";
//            self.label6.text = @"簽到獎勵";
//            self.label7.text = @"樂透獎勵";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL008.png"];
//            break;
//        case 9:
//            self.label1.text = @"說句幣";
//            self.label2.text = @"說句.句量金幣";
//            self.label3.text = @"總句量";
//            self.label4.text = @"三星獎勵";
//            self.label5.text = @"勤學獎勵";
//            self.label6.text = @"簽到獎勵";
//            self.label7.text = @"樂透獎勵";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL009.png"];
//            break;
//        case 10:
//            self.label1.text = @"(未)";
//            self.label2.text = @"(未)";
//            self.label3.text = @"";
//            self.label4.text = @"";
//            self.label5.text = @"";
//            self.label6.text = @"";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL010.png"];
//            break;
//        case 11:
//            self.label1.text = @"排名幣";
//            self.label2.text = @"排行榜獎勵.金幣";
//            self.label3.text = @"總量排行";
//            self.label4.text = @"三星排行";
//            self.label5.text = @"勤學排行";
//            self.label6.text = @"樂透排行";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL011.png"];
//            break;
//        case 12:
//            self.label1.text = @"簽到幣";
//            self.label2.text = @"今日簽到.獎勵";
//            self.label3.text = @"今日點名";
//            self.label4.text = @"單字簽到";
//            self.label5.text = @"口說簽到";
//            self.label6.text = @"句型簽到";
//            self.label7.text = @"樂透獎勵";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL012.png"];
//            break;
//        case 13:
//            self.label1.text = @"時數幣";
//            self.label2.text = @"時數累績.獎勵";
//            self.label3.text = @"本期累積時數";
//            self.label4.text = @"全累積總時數";
//            self.label5.text = @"破百時數獎勵";
//            self.label6.text = @"破2百時數獎勵";
//            self.label7.text = @"樂透獎勵";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL013.png"];
//            break;
//        case 14:
//            self.label1.text = @"加課幣";
//            self.label2.text = @"加課累積.獎勵";
//            self.label3.text = @"加課累積時數";
//            self.label4.text = @"時數獎勵";
//            self.label5.text = @"效能獎勵";
//            self.label6.text = @"樂透獎勵";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL014.png"];
//            break;
//        case 15:
//            self.label1.text = @"(未)";
//            self.label2.text = @"(未)";
//            self.label3.text = @"";
//            self.label4.text = @"";
//            self.label5.text = @"";
//            self.label6.text = @"";
//            self.label7.text = @"";
//            self.label8.text = @"";
//            self.label9.text = @"";
//            self.imageView.image = [UIImage imageNamed:@"RL015.png"];
//            break;
//        default:
//            break;
//    }
//}
//(轉場傳送.文字+圖片)----------------------------------------


//
//- (IBAction)sign:(id)sender {
//    [self showsign];
//}
//
//- (IBAction)redenvelope:(id)sender {
//    [self showRedEnvelope];
//}
//
//- (void)showRedEnvelope {
//    // 隱藏標籤和圖片
//    self.label1.hidden = YES;
//    self.label2.hidden = YES;
//    self.label3.hidden = YES;
//    self.label4.hidden = YES;
//    self.label5.hidden = YES;
//    self.label6.hidden = YES;
//    self.label7.hidden = YES;
//    self.label8.hidden = YES;
//    self.label9.hidden = YES;
//    self.imageView.hidden = YES;
//
//    // 定義圖片名稱的數組
//    NSArray *imageNames = @[@"獎勵機制_道具080", @"獎勵機制_道具081", @"獎勵機制_道具082"];
//    // 生成隨機數
//    NSUInteger randomIndex = arc4random_uniform((u_int32_t)imageNames.count);
//    // 根據隨機數選擇圖片名稱
//    NSString *selectedImageName = imageNames[randomIndex];
//    // 初始化 UIImageView 並設置圖片
//    UIImageView *redEnvelope = [[UIImageView alloc] initWithImage:[UIImage imageNamed:selectedImageName]];
//    redEnvelope.frame = CGRectMake(150, 120, 400, 400); // 設置具體尺寸
//    redEnvelope.contentMode = UIViewContentModeScaleAspectFit;
//    redEnvelope.userInteractionEnabled = YES; // 啟用用戶交互
//    [self.view addSubview:redEnvelope];
//    
//    // 動畫（震動效果）
//    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"position"];
//    animation.duration = 0.07;
//    animation.repeatCount = 4;
//    animation.autoreverses = YES;
//    animation.fromValue = [NSValue valueWithCGPoint:CGPointMake(redEnvelope.center.x - 10, redEnvelope.center.y)];
//    animation.toValue = [NSValue valueWithCGPoint:CGPointMake(redEnvelope.center.x + 10, redEnvelope.center.y)];
//    [redEnvelope.layer addAnimation:animation forKey:@"position"];
//    
//    // 添加點擊手勢
//    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(hideRedEnvelope:)];
//    [redEnvelope addGestureRecognizer:tapGesture];
//}
//
//- (void)hideRedEnvelope:(UITapGestureRecognizer *)gesture {
//    UIImageView *redEnvelope = (UIImageView *)gesture.view;
//    
//    // 保存隨機數字
//    NSInteger randomNumber = arc4random_uniform(50) + 1;  // 隨機數字範圍 1 到 50
//    self.savedRandomNumber = randomNumber; // 使用屬性來保存數字
//
//    [redEnvelope removeFromSuperview];
//    
//    // 顯示數字
//    self.numberLabel = [[UILabel alloc] initWithFrame:CGRectMake(100, 100, 100, 50)];
//    self.numberLabel.text = [NSString stringWithFormat:@"%ld", (long)randomNumber];
//    self.numberLabel.textColor = [UIColor blackColor];
//    self.numberLabel.textAlignment = NSTextAlignmentCenter;
//    [self.view addSubview:self.numberLabel];
//}
//
//- (IBAction)update:(id)sender {
//    [self insertNumberIntoDatabase:self.savedRandomNumber];
//}
//
//
//- (void)showsign {
//    self.showEnbotton = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"籤筒01"]];
//    self.showEnbotton.frame = CGRectMake(150, 120, 400, 400); // 設置具體尺寸
//    self.showEnbotton.center = self.view.center; // 確保圖片在屏幕中央
//    self.label1.hidden = YES;
//    self.label2.hidden = YES;
//    self.label3.hidden = YES;
//    self.label4.hidden = YES;
//    self.label5.hidden = YES;
//    self.label6.hidden = YES;
//    self.label7.hidden = YES;
//    self.label8.hidden = YES;
//    self.label9.hidden = YES;
//    self.imageView.hidden = YES;
//    self.showEnbotton.userInteractionEnabled = YES; // 啟用用戶交互
//    // 添加動畫
//    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"position"];
//    animation.duration = 0.07;
//    animation.repeatCount = 4;
//    animation.autoreverses = YES;
//    animation.fromValue = [NSValue valueWithCGPoint:CGPointMake(self.showEnbotton.center.x , self.showEnbotton.center.y - 10)];
//    animation.toValue = [NSValue valueWithCGPoint:CGPointMake(self.showEnbotton.center.x , self.showEnbotton.center.y + 10)];
//    [self.showEnbotton.layer addAnimation:animation forKey:@"position"];
//    // 添加點擊手勢識別器到 qiantongImageView
//    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTap:)];
//    [self.showEnbotton addGestureRecognizer:tapGesture];
//    [self.view addSubview:self.showEnbotton]; // 將圖片添加到視圖上
//}
//
//- (void)handleTap:(UITapGestureRecognizer *)gestureRecognizer {
//    // 移除點擊的 UIImageView
//    [self.showEnbotton removeFromSuperview];
//    // 生成隨機數
//    NSArray *imageNames = @[@"籤筒21", @"籤筒22", @"籤筒23", @"籤筒24", @"籤筒25"];
//    NSUInteger randomIndex = arc4random_uniform((u_int32_t)imageNames.count);
//    // 根據隨機數選擇圖片名稱
//    NSString *selectedImageName = imageNames[randomIndex];
//    // 初始化 UIImageView 並設置圖片
//    UIImageView *redEnvelope = [[UIImageView alloc] initWithImage:[UIImage imageNamed:selectedImageName]];
//    redEnvelope.frame = CGRectMake(150, 120, 400, 400); // 設置具體尺寸
//    redEnvelope.contentMode = UIViewContentModeScaleAspectFit;
//    redEnvelope.userInteractionEnabled = YES;
//    [self.view addSubview:redEnvelope];
//}
//
//- (void)insertNumberIntoDatabase:(NSInteger)number {
//    
//    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"成績已上傳！" message:
//                              [NSString stringWithFormat:@"你的分數已記錄！"] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
//    [alertView show];
//    OHMySQLUser *user;
//    user = [[OHMySQLUser alloc] initWithUserName:@"uten"
//                                         password:@"1qazXSW@3edcVFR$"
//                                       serverName:@"uten.synology.me"
//                                           dbName:@"uten"
//                                             port:3307
//                                           socket:@"/run/mysqld/mysqld10.sock"];
//    
//    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
//    [coordinator connect];
//    [coordinator setEncoding:CharsetEncodingUTF8MB4];
//    
//    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
//    queryContext.storeCoordinator = coordinator;
//    
//    // 插入從UILabel獲取的數字到dannytest表
//    NSDictionary *insertData = @{@"CNAME": @"Uten2022Student001",
//                                 @"ENAME": @(number),  // 使用獲取到的數字
//                                 @"ABOOK": @0,
//                                 @"TYPE1": @0,
//                                 @"TYPE2": @0,
//                                 @"SCORE": @0,
//                                 @"PUNISH": @0,
//                                 @"DATE": @0};
//    
//    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory INSERT:@"dannytest" set:insertData];
//    
//    NSError *error = nil;
//    [queryContext executeQueryRequest:query error:&error];
//    
//    if (error) {
//        NSLog(@"Error: %@", error.localizedDescription);
//    } else {
//        NSLog(@"Data inserted successfully into dannytest.");
//    }
//    
//    [coordinator disconnect];
//}
//
//@end

#import "RankingL2ViewController.h"
#import <QuartzCore/QuartzCore.h>
#import <OHMySQL/OHMySQL.h>
#import "RankingListViewController.h"

@interface RankingL2ViewController () <UIAlertViewDelegate>
@property (nonatomic, strong) UILabel *numberLabel;
@property (nonatomic, strong) UIButton *receivedButton;
@property (nonatomic, assign) NSInteger savedRandomNumber;
@end

@implementation RankingL2ViewController
NSString *單字下載; //全域使用
- (void)viewDidLoad {
    [super viewDidLoad];
    //將登入使用者的最新一筆資料下載
    OHMySQLUser *user = [[OHMySQLUser alloc] initWithUserName:@"uten"
                                                         password:@"1qazXSW@3edcVFR$"
                                                       serverName:@"uten.synology.me"
                                                           dbName:@"uten"
                                                             port:3307
                                                           socket:@"/run/mysqld/mysqld10.sock"];
        
        // 2. 設定資料庫協調器並連接----------
        OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
        [coordinator connect];
        [coordinator setEncoding:CharsetEncodingUTF8MB4];
        
        // 3. 建立查詢上下文----------
        OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
        queryContext.storeCoordinator = coordinator;
    //        NSString *condition = @"通過分數 = 80 AND CNAME != 123 AND 字母順位 = 0 AND 待罰 = 0";
    //        OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"AllStudentABC" condition:condition]; //可一次多種條件判斷式
        
        // 4. 使用 OHMySQLQueryRequestFactory 創建 SELECT 查詢請求----------
        NSString *RankingCNAME = [[NSUserDefaults standardUserDefaults] objectForKey:@"CNAME2"];
        NSString *queryCondition = [NSString stringWithFormat:@"CNAME = '%@' ORDER BY created_at DESC LIMIT 1", RankingCNAME];
        OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"AllStudentABC" condition:queryCondition];  //ORDER BY timestamp DESC LIMIT 1似乎是選取最新一筆資料
        
        NSError *error = nil;
        NSArray *results = [queryContext executeQueryRequestAndFetchResult:query error:&error];
        
        if (error) {
            NSLog(@"Error: %@", error.localizedDescription);
        } else {
            
            // 5. 迭代查詢結果----------
            for (NSDictionary *row in results) {
                
                
                NSData *data2 = row[@"字母"];
                單字下載 = [[NSString alloc] initWithData:data2 encoding:NSUTF8StringEncoding];
                
                NSString *字母軌跡下載;
                NSData *data7 = row[@"字母軌跡"];
                字母軌跡下載 = [[NSString alloc] initWithData:data7 encoding:NSUTF8StringEncoding]; //解碼data二進制轉string
                
                
                NSData *data = [字母軌跡下載 dataUsingEncoding:NSUTF8StringEncoding];
                NSError *error;
                NSArray *array = [NSJSONSerialization JSONObjectWithData:data options : 0 error : &error];
                if ( !error ) {
                    NSUInteger n = array.count;
                    int LMA4[n][4];     // 初始化二維陣列 LMA4
                    // 將 JSON 內容轉換為 int 型態並存入 LMA4
                    for (NSUInteger i = 0; i < n; i++) {
                        NSArray *subArray = array[i];
                        for (NSUInteger j = 0; j < 4; j++) {
                            LMA4[i][j] = [ subArray[ j ] intValue];
                        }   //將 LMA3[][] 下載下來後，存入 LMA4[][] 中。
                        NSLog(@"LMA4[%lu](%d,%d,%d,%d)",(unsigned long)i,LMA4[i][0],LMA4[i][1],LMA4[i][2],LMA4[i][3]);
                    } // 現在 LMA4 中已經存入 JSON 轉換過來的 int 型態數值
                } else {
                    NSLog(@"JSON 解析失敗: %@", error.localizedDescription);
                }
                NSNumber *data88 = row[@"通過分數"];
                int data8 = [data88 intValue];
                
                NSNumber *data99 = row[@"待罰"];
                int data9 = [data99 intValue];
                
                NSLog(@"CNAME: %@", row[@"CNAME"]);
                NSLog(@"字母: %@", 單字下載);
                NSLog(@"字母軌跡: %@", 字母軌跡下載);
                NSLog(@"通過分數: %d", data8);
                NSLog(@"待罰: %d", data9);
            }
        }
        // 6. 斷開連接----------
        [coordinator disconnect];
    //將登入使用者的最新一筆資料下載
    switch (self.buttonTag) {
            case 1:
                self.label1.text = @"課程";
                self.label2.text = @"UP1.U1-U4.堂數";
                self.label3.text = [NSString stringWithFormat:@"最新字母：%@", 單字下載];
                self.label4.text = @"進度";
                self.label5.text = @"完成";
                self.label6.text = @"欠課";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL001.png"];
                break;
            case 2:
                self.label1.text = @"單字";
                self.label2.text = @"UP1.U1-U4.單字";
                self.label3.text = @"總量";
                self.label4.text = @"進度";
                self.label5.text = @"通過";
                self.label6.text = @"錯誤";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL002.png"];
                break;
            case 3:
                self.label1.text = @"口說";
                self.label2.text = @"UP1.U1-U4.說句";
                self.label3.text = @"總量";
                self.label4.text = @"進度";
                self.label5.text = @"完成";
                self.label6.text = @"未完";
                self.label7.text = @"錯誤";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL003.png"];
                break;
            case 4:
                self.label1.text = @"句型";
                self.label2.text = @"UP1.U1-U4.寫句";
                self.label3.text = @"總量";
                self.label4.text = @"進度";
                self.label5.text = @"完成";
                self.label6.text = @"未完";
                self.label7.text = @"錯誤";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL004.png"];
                break;
            case 5:
                self.label1.text = @"對話";
                self.label2.text = @"生活對話.說句";
                self.label3.text = @"總量";
                self.label4.text = @"進度";
                self.label5.text = @"欠量";
                self.label6.text = @"錯誤";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL005.png"];
                break;
            case 6:
                self.label1.text = @"寫字幣";
                self.label2.text = @"寫字.字量金幣";
                self.label3.text = @"總字量";
                self.label4.text = @"三星獎勵";
                self.label5.text = @"勤學獎勵";
                self.label6.text = @"簽到獎勵";
                self.label7.text = @"樂透獎勵";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL006.png"];
                break;
            case 7:
                self.label1.text = @"寫句幣";
                self.label2.text = @"寫句.句量金幣";
                self.label3.text = @"總句量";
                self.label4.text = @"三星獎勵";
                self.label5.text = @"勤學獎勵";
                self.label6.text = @"簽到獎勵";
                self.label7.text = @"樂透獎勵";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL007.png"];
                break;
            case 8:
                self.label1.text = @"說字幣";
                self.label2.text = @"說字.字量金幣";
                self.label3.text = @"總字量";
                self.label4.text = @"三星獎勵";
                self.label5.text = @"勤學獎勵";
                self.label6.text = @"簽到獎勵";
                self.label7.text = @"樂透獎勵";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL008.png"];
                break;
            case 9:
                self.label1.text = @"說句幣";
                self.label2.text = @"說句.句量金幣";
                self.label3.text = @"總句量";
                self.label4.text = @"三星獎勵";
                self.label5.text = @"勤學獎勵";
                self.label6.text = @"簽到獎勵";
                self.label7.text = @"樂透獎勵";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL009.png"];
                break;
            case 10:
                self.label1.text = @"(未)";
                self.label2.text = @"(未)";
                self.label3.text = @"";
                self.label4.text = @"";
                self.label5.text = @"";
                self.label6.text = @"";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL010.png"];
                break;
            case 11:
                self.label1.text = @"排名幣";
                self.label2.text = @"排行榜獎勵.金幣";
                self.label3.text = @"總量排行";
                self.label4.text = @"三星排行";
                self.label5.text = @"勤學排行";
                self.label6.text = @"樂透排行";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL011.png"];
                break;
            case 12:
                self.label1.text = @"簽到幣";
                self.label2.text = @"今日簽到.獎勵";
                self.label3.text = @"今日點名";
                self.label4.text = @"單字簽到";
                self.label5.text = @"口說簽到";
                self.label6.text = @"句型簽到";
                self.label7.text = @"樂透獎勵";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL012.png"];
                break;
            case 13:
                self.label1.text = @"時數幣";
                self.label2.text = @"時數累績.獎勵";
                self.label3.text = @"本期累積時數";
                self.label4.text = @"全累積總時數";
                self.label5.text = @"破百時數獎勵";
                self.label6.text = @"破2百時數獎勵";
                self.label7.text = @"樂透獎勵";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL013.png"];
                break;
            case 14:
                self.label1.text = @"加課幣";
                self.label2.text = @"加課累積.獎勵";
                self.label3.text = @"加課累積時數";
                self.label4.text = @"時數獎勵";
                self.label5.text = @"效能獎勵";
                self.label6.text = @"樂透獎勵";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL014.png"];
                break;
            case 15:
                self.label1.text = @"(未)";
                self.label2.text = @"(未)";
                self.label3.text = @"";
                self.label4.text = @"";
                self.label5.text = @"";
                self.label6.text = @"";
                self.label7.text = @"";
                self.label8.text = @"";
                self.label9.text = @"";
                self.imageView.image = [UIImage imageNamed:@"RL015.png"];
                break;
            default:
                break;
        }

    
    
}

- (IBAction)sign:(id)sender {
    [self showsign];
    
    
    
    
}

- (IBAction)redenvelope:(id)sender {
    [self showRedEnvelope];
}

- (void)showRedEnvelope {
    // 隐藏标签和图片
    self.label1.hidden = YES;
    self.label2.hidden = YES;
    self.label3.hidden = YES;
    self.label4.hidden = YES;
    self.label5.hidden = YES;
    self.label6.hidden = YES;
    self.label7.hidden = YES;
    self.label8.hidden = YES;
    self.label9.hidden = YES;
    self.imageView.hidden = YES;

    // 定义图片名称的数组
    NSArray *imageNames = @[@"獎勵機制_道具080", @"獎勵機制_道具081", @"獎勵機制_道具082"];
    // 生成随机数
    NSUInteger randomIndex = arc4random_uniform((u_int32_t)imageNames.count);
    // 根据随机数选择图片名称
    NSString *selectedImageName = imageNames[randomIndex];
    // 初始化 UIImageView 并设置图片
    UIImageView *redEnvelope = [[UIImageView alloc] initWithImage:[UIImage imageNamed:selectedImageName]];
    redEnvelope.frame = CGRectMake(150, 120, 400, 400); // 设置具体尺寸
    redEnvelope.contentMode = UIViewContentModeScaleAspectFit;
    redEnvelope.userInteractionEnabled = YES; // 启用用户交互
    [self.view addSubview:redEnvelope];
    
    // 动画（震动效果）
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"position"];
    animation.duration = 0.07;
    animation.repeatCount = 4;
    animation.autoreverses = YES;
    animation.fromValue = [NSValue valueWithCGPoint:CGPointMake(redEnvelope.center.x - 10, redEnvelope.center.y)];
    animation.toValue = [NSValue valueWithCGPoint:CGPointMake(redEnvelope.center.x + 10, redEnvelope.center.y)];
    [redEnvelope.layer addAnimation:animation forKey:@"position"];
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(hideRedEnvelope:)];
    [redEnvelope addGestureRecognizer:tapGesture];
}

- (void)hideRedEnvelope:(UITapGestureRecognizer *)gesture {
    UIImageView *redEnvelope = (UIImageView *)gesture.view;
    
    // 保存随机数字
    NSInteger randomNumber = arc4random_uniform(50) + 1;  // 随机数字范围 1 到 50
    self.savedRandomNumber = randomNumber; // 使用属性来保存数字

    [redEnvelope removeFromSuperview];
    
    // 显示数字
    self.numberLabel = [[UILabel alloc] initWithFrame:CGRectMake(100, 100, 100, 50)];
    self.numberLabel.text = [NSString stringWithFormat:@"%ld", (long)randomNumber];
    self.numberLabel.textColor = [UIColor blackColor];
    self.numberLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:self.numberLabel];
}

- (IBAction)update:(id)sender {
    [self insertNumberIntoDatabase:self.savedRandomNumber];
}

- (void)showsign {
    self.showEnbotton = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"籤筒01"]];
    self.showEnbotton.frame = CGRectMake(150, 120, 400, 400); // 设置具体尺寸
    self.showEnbotton.center = self.view.center; // 确保图片在屏幕中央
    self.label1.hidden = YES;
    self.label2.hidden = YES;
    self.label3.hidden = YES;
    self.label4.hidden = YES;
    self.label5.hidden = YES;
    self.label6.hidden = YES;
    self.label7.hidden = YES;
    self.label8.hidden = YES;
    self.label9.hidden = YES;
    self.imageView.hidden = YES;
    self.showEnbotton.userInteractionEnabled = YES; // 启用用户交互
    // 添加动画
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"position"];
    animation.duration = 0.07;
    animation.repeatCount = 4;
    animation.autoreverses = YES;
    animation.fromValue = [NSValue valueWithCGPoint:CGPointMake(self.showEnbotton.center.x , self.showEnbotton.center.y - 10)];
    animation.toValue = [NSValue valueWithCGPoint:CGPointMake(self.showEnbotton.center.x , self.showEnbotton.center.y + 10)];
    [self.showEnbotton.layer addAnimation:animation forKey:@"position"];
    // 添加点击手势识别器到 qiantongImageView
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTap:)];
    [self.showEnbotton addGestureRecognizer:tapGesture];
    [self.view addSubview:self.showEnbotton]; // 将图片添加到视图上
}

- (void)handleTap:(UITapGestureRecognizer *)gestureRecognizer {
    // 移除点击的 UIImageView
    [self.showEnbotton removeFromSuperview];
    // 生成随机数
    NSArray *imageNames = @[@"籤筒21", @"籤筒22", @"籤筒23", @"籤筒24", @"籤筒25"];
    NSUInteger randomIndex = arc4random_uniform((u_int32_t)imageNames.count);
    // 根据随机数选择图片名称
    NSString *selectedImageName = imageNames[randomIndex];
    // 初始化 UIImageView 并设置图片
    UIImageView *redEnvelope = [[UIImageView alloc] initWithImage:[UIImage imageNamed:selectedImageName]];
    redEnvelope.frame = CGRectMake(150, 120, 400, 400); // 设置具体尺寸
    redEnvelope.contentMode = UIViewContentModeScaleAspectFit;
    redEnvelope.userInteractionEnabled = YES;
    [self.view addSubview:redEnvelope];
}

- (void)insertNumberIntoDatabase:(NSInteger)number {
    OHMySQLUser *user;
    user = [[OHMySQLUser alloc] initWithUserName:@"uten"
                                         password:@"1qazXSW@3edcVFR$"
                                       serverName:@"uten.synology.me"
                                           dbName:@"uten"
                                             port:3307
                                           socket:@"/run/mysqld/mysqld10.sock"];
    
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    
    // 插入从UILabel获取的数字到dannytest表
    NSDictionary *insertData = @{@"CNAME": @"Uten2022Student001",
                                 @"ENAME": @(number),  // 使用获取到的数字
                                 @"ABOOK": @0,
                                 @"TYPE1": @0,
                                 @"TYPE2": @0,
                                 @"SCORE": @0,
                                 @"PUNISH": @0,
                                 @"DATE": @0};
    
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory INSERT:@"dannytest" set:insertData];
    
    NSError *error = nil;
    [queryContext executeQueryRequest:query error:&error];
    
    if (error) {
        NSLog(@"Error: %@", error.localizedDescription);
    } else {
        NSLog(@"Data inserted successfully into dannytest.");
    }
    
    [coordinator disconnect];
    
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"成績已上傳！"
                                                        message:[NSString stringWithFormat:@"你的分數已記錄！"]
                                                       delegate:self
                                              cancelButtonTitle:@"OK"
                                              otherButtonTitles:nil];
    [alertView show];
}

#pragma mark - UIAlertViewDelegate

- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
    if (buttonIndex == alertView.cancelButtonIndex) {
        // 返回到RankingListViewController
        for (UIViewController *controller in self.navigationController.viewControllers) {
            if ([controller isKindOfClass:[RankingListViewController class]]) {
                [self.navigationController popToViewController:controller animated:YES];
                break;
            }
        }
    }
}

@end
