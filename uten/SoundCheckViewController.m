//
//  SoundCheckViewController.m
//  uten
//
//  Created by 蔡駿寓 on 2024/4/30.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "SoundCheckViewController.h"
#import <QuartzCore/QuartzCore.h>
#import <Masonry/Masonry.h>
#import <ReactiveObjC/ReactiveObjC.h>
#import "SoundCheckViewModel.h"
#import "UTENBasicModel.h"
#import "UTENBasicModel+X.h"
#import "UTENNclassModel.h"
#import "UTENNstudentModel.h"
#import "BasicTableViewCell.h"
#import "GRRequestsManager.h"
#import "GRRequestsManager+X.h"
#import "GRRequestProtocol.h"
#import "UTENSoundCheckModel.h"
#import "UTENSoundCheckModel+X.h"
#import "UTENConstants.h"
#import "NSUserDefaults+X.h"
#import "UTENBasicTypes.h"
#import <AVFoundation/AVFoundation.h>
#import "UTENDialogArgs.h"
#import "UTENUtility.h"
#import "UTENMySql.h"
#import "UTENMediaModel.h"

@interface SoundCheckViewController () <UITableViewDataSource, UITableViewDelegate, GRRequestsManagerDelegate, AVAudioPlayerDelegate>
//(旋轉背景圖) 設定參數
@property (weak, nonatomic) IBOutlet UIImageView *imageView1;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView2;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView3;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView4;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView5;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView6;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView7;//(旋轉)
//(喵咪搖尾巴) 設定參數
//@property (weak, nonatomic) IBOutlet UIImageView *imageView;//(手指)
//@property (strong, nonatomic) NSArray *imageNames;          //(手指)
//@property (nonatomic) NSInteger currentImageIndex;          //(手指)
//@property (strong, nonatomic) NSTimer *timer;               //(手指)
@property (strong, nonatomic) SoundCheckViewModel *viewModel;
@property (nonatomic, readonly, strong) UITableView *tableView1;
@property (nonatomic, readonly, strong) UITableView *tableView2;
@property (nonatomic, readonly, strong) UITableView *tableViewFiles;
@property (nonatomic, readonly, strong) UITableView *tableViewSpeakers;
@property (nonatomic, strong) GRRequestsManager *requestsManager;
@property (weak, nonatomic) IBOutlet UIImageView *imgUp;
@property (weak, nonatomic) IBOutlet UIImageView *imgDown;
@property (weak, nonatomic) IBOutlet UIImageView *imgYes;
@property (weak, nonatomic) IBOutlet UIImageView *imgNo;
@property (weak, nonatomic) IBOutlet UIImageView *imgAvatar;
// 聲明一個 AVAudioPlayer 對象：在您的類的接口中聲明一個 AVAudioPlayer 對象作為屬性。
@property (strong, nonatomic) AVAudioPlayer *audioPlayer;
// 聲明一個儲存 Completion 的 NSDictionay, filename 為 key, Completion 為 value
@property (strong, nonatomic) NSMutableDictionary<NSString *, Completion> *completions;

@end

@implementation SoundCheckViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // (旋轉背景圖) 設置每個圖片視圖的圖片
    self.imageView1.image = [UIImage imageNamed:@"SoundCheck 15.png"];//(旋轉)
    self.imageView2.image = [UIImage imageNamed:@"SoundCheck 13.png"];//(旋轉)
    self.imageView3.image = [UIImage imageNamed:@"SoundCheck 12.png"];//(旋轉)
    self.imageView4.image = [UIImage imageNamed:@"SoundCheck 11.png"];//(旋轉)
    self.imageView5.image = [UIImage imageNamed:@"SoundCheck 12.png"];//(旋轉)
    self.imageView6.image = [UIImage imageNamed:@"SoundCheck 11.png"];//(旋轉)
    self.imageView7.image = [UIImage imageNamed:@"SoundCheck 14.png"];//(旋轉)
    
    // (旋轉背景圖) 啟動每個圖片視圖的旋轉動畫  //20秒1圈，或10秒1圈
    [self startRotatingImageView:self.imageView1 withDuration:40.0];    //(旋轉)
    [self startRotatingImageView:self.imageView2 withDuration:30.0];    //(旋轉)
    [self startRotatingImageView:self.imageView3 withDuration:35.0];    //(旋轉)
    [self startRotatingImageView:self.imageView4 withDuration:30.0];    //(旋轉)
    [self startRotatingImageView:self.imageView5 withDuration:35.0];    //(旋轉)
    [self startRotatingImageView:self.imageView6 withDuration:40.0];    //(旋轉)
    [self startRotatingImageView:self.imageView7 withDuration:35.0];    //(旋轉)

//        //(搖.手指) 初始化圖片名稱，導入2張圖片
//        self.imageNames = @[@"SoundCheck011.png", @"SoundCheck012.png"];
//        self.currentImageIndex = 0;
//
//        //(搖.手指) 設置初始圖片
//        self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];
//
//        //(搖.手指) 設置定時器以自動切換圖片，0.5秒切換1張，repeat重複
//        self.timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(nextImage) userInfo:nil repeats:YES];
//    //-----
//
    [self setupLayout];
    [self setupViewModel];
    [self setupFtp];
}

- (void)setupFtp {
    self.requestsManager = [GRRequestsManager createInstance];
    self.requestsManager.delegate = self;
}

- (void)setupViewModel {
    self.viewModel = [[SoundCheckViewModel alloc] init];
    // class
    {
        @weakify(self);
        // [[RACObserve(self.viewModel, nclass) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSArray<UTENNclassModel *> *  _Nullable list) {
        [[RACObserve(self.viewModel, soundCheckModels) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSArray<UTENSoundCheckModel *> * _Nullable list) {
            NSLog(@"list: %@", list);
            @strongify(self);
            [self.tableView1 reloadData];
            self.viewModel.selectedClass = @"";
        }];
    }
    // observe class
    {
        @weakify(self);
        // [[RACObserve(self.viewModel, filtedStudents) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSArray<UTENNstudentModel *> *  _Nullable list) {
        [[RACObserve(self.viewModel, selectedClass) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSString * _Nullable value) {
            // NSLog(@"list: %@", list);
            @strongify(self);
            [self.tableView2 reloadData];
            self.viewModel.selectedLesson = @"";
        }];
    }
    // observe lesson
    {
        @weakify(self);
        [[RACObserve(self.viewModel, selectedLesson) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSString * _Nullable value) {
            // NSLog(@"list: %@", list);
            @strongify(self);
            [self.tableViewSpeakers reloadData];
            self.viewModel.selectedSpeaker = @"";
        }];
    }
    // observe speaker
    {
        @weakify(self);
        [[RACObserve(self.viewModel, selectedSpeaker) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSString * _Nullable value) {
            // NSLog(@"list: %@", list);
            @strongify(self);
            [self.tableViewFiles reloadData];
            self.viewModel.selectedSoundCheckModelIndex = NSNotFound;
        }];
    }
    // observe sound check
    {
        @weakify(self);
        [[RACObserve(self.viewModel, selectedSoundCheckModelIndex) takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNumber * _Nullable value) {
            @strongify(self);
            // 更新選擇的 cell 讓其顯示為選取狀態
            [self reloadSelectedIndexView];
        }];
    }
    // 取得學生資料
    [self.viewModel fetchNstudent];
    // 取得班級資料
    [self.viewModel fetchNhistory];
    // 取得聲音檢查資料
    [self.viewModel fetchMediaDb];
}

// 本地更名
- (void)renameLocalFile:(UTENSoundCheckModel *)model completion:(Completion)completion {
    // remove local file
    NSString *src = model.origin;
    NSLog(@"src: %@", src);
    NSString *local = [NSString stringWithFormat:@"%@/%@", NSTemporaryDirectory(), src];
    NSLog(@"local: %@", local);
    // new filename
    NSString *dest = model.fullname;
    NSLog(@"dest: %@", dest);
    NSString *newLocal = [NSString stringWithFormat:@"%@/%@", NSTemporaryDirectory(), dest];
    NSLog(@"newLocal: %@", newLocal);
    // error var
    NSError *error;
    NSFileManager *fileManager = [NSFileManager defaultManager];
    // check file exists
    if ([fileManager fileExistsAtPath:local]) {
        // remove file
        // [fileManager removeItemAtPath:local error:&error];
        [fileManager moveItemAtPath:local toPath:newLocal error:&error];
        if (error) {
            NSLog(@"move file failed: %@", error);
            if (completion != nil) {
                completion(NO, error);
            }
        } else {
            NSLog(@"move file success");
            // reset filename
            NSString *dest = model.fullname;
            [model parse:dest];
            if (completion != nil) {
                completion(YES, nil);
            }
        }
    } else {
        NSLog(@"file not exists");
        if (completion != nil) {
            completion(NO, [NSError errorWithDomain:@"SoundCheckViewController" code:0 userInfo:@{NSLocalizedDescriptionKey: @"file not exists"}]);
        }
    }
}

// 更名
- (void)renameFile:(UTENSoundCheckModel *)model completion:(Completion)completion {
    __block Completion deleteCompletion = ^(BOOL success, NSError *error) {
        if (success) {
            NSLog(@"delete file success");
            // rename local file
            [self renameLocalFile:model completion:completion];
        }
    };
    if (model.isModified) {
        // 上傳檔案
        @weakify(self);
        [self uploadFile:model completion:^(BOOL success, NSError *error) {
            @strongify(self);
            if (success) {
                // 刪除遠端檔案
                [self deleteFile:model.origin completion:deleteCompletion];
            }
        }];
    }
}

// 上傳檔案
- (void)uploadFile:(UTENSoundCheckModel *)model completion:(Completion)completion {
    // remote file path
    NSString *dest = model.fullname;
    NSLog(@"dest: %@", dest);
    NSString *remote = [NSString stringWithFormat:@"%@/%@", UTENConstants.ftpUploadPath, dest];
    NSLog(@"remote: %@", remote);
    // local file path
    NSString *src = model.origin;
    NSLog(@"src: %@", src);
    NSString *local = [NSString stringWithFormat:@"%@/%@", NSTemporaryDirectory(), src];
    NSLog(@"local: %@", local);
    // check file exists
    if ([[NSFileManager defaultManager] fileExistsAtPath:local]) {
        NSLog(@"file exists");
        // save completion
        if (completion != nil) {
            if (self.completions == nil) {
                self.completions = @{}.mutableCopy;
            }
            self.completions[dest] = completion;
        }
        // add request for upload file
        [self.requestsManager addRequestForUploadFileAtLocalPath:local toRemotePath:remote];
        [self.requestsManager startProcessingRequests];
    } else {
        NSLog(@"file not exists");
        if (completion != nil) {
            completion(NO, [NSError errorWithDomain:@"SoundCheckViewController" code:0 userInfo:@{NSLocalizedDescriptionKey: @"file not exists"}]);
        }
    }
}

// 刪除檔案
- (void)deleteFile:(NSString *)dest completion:(Completion)completion {
    // remote file path
    // NSString *dest = model.origin;
    NSLog(@"dest: %@", dest);
    NSString *remote = [NSString stringWithFormat:@"%@/%@", UTENConstants.ftpUploadPath, dest];
    NSLog(@"remote: %@", remote);
    // delete completion
    if (completion != nil) {
        if (self.completions == nil) {
            self.completions = @{}.mutableCopy;
        }
        self.completions[dest] = completion;
    }
    // add request for delete file
    [self.requestsManager addRequestForDeleteFileAtPath:remote];
    [self.requestsManager startProcessingRequests];

    // local file path
    // NSString *src = model.origin;
    // NSLog(@"src: %@", src);
    // NSString *local = [NSString stringWithFormat:@"%@/%@", NSTemporaryDirectory(), src];
    // NSLog(@"local: %@", local);
    // // check file exists
    // if ([[NSFileManager defaultManager] fileExistsAtPath:local]) {
    //     NSLog(@"file exists");
    //     //
    // } else {
    //     NSLog(@"file not exists");
    //     if (completion != nil) {
    //         completion(NO, [NSError errorWithDomain:@"SoundCheckViewController" code:0 userInfo:@{NSLocalizedDescriptionKey: @"file not exists"}]);
    //     }
    // }
}

- (void)downloadFile:(NSString *)filename completion:(Completion)completion {
    NSLog(@"downloadFile: %@", filename);
    // remote file path
    NSString *remote = [NSString stringWithFormat:@"%@/%@", UTENConstants.ftpUploadPath, filename];
    DDLogDebugTag(@"audio", @"remote: %@", remote);
    // local file path
    NSString *local = [NSString stringWithFormat:@"%@/%@", NSTemporaryDirectory(), filename];
    DDLogDebugTag(@"audio", @"local: %@", local);
    // save completion
    if (completion != nil) {
        // completion = ^(BOOL success, NSError *error) {
        //     if (success) {
        //         NSLog(@"download file success");
        //     } else {
        //         NSLog(@"download file failed: %@", error);
        //     }
        // };
        if (self.completions == nil) {
            // self.completions = [NSMutableDictionary dictionary];
            self.completions = @{}.mutableCopy;
        }
        self.completions[filename] = completion;
    }
    // add request for download file
    [self.requestsManager addRequestForDownloadFileAtRemotePath:remote toLocalPath:local];
    [self.requestsManager startProcessingRequests];
}

- (BOOL)playSound:(NSString *)path {
    DDLogDebugTag(@"audio", @"playSound: %@", path);
    // 產生暫存目錄 (tmp)
    // NSString *tmpDirectory = NSTemporaryDirectory();
    // 組合檔名
    // NSString *path = [NSString stringWithFormat:@"%@/%@", tmpDirectory, filename];
    // 檢查檔案是否存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
        DDLogDebugTag(@"audio", @"file exists");
        // 播放音效
        NSURL *url = [NSURL fileURLWithPath:path];
        NSError *error;
        self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:url error:&error];
        if (error) {
            NSLog(@"Error in audioPlayer: %@", [error localizedDescription]);
            return NO;
        } else {
            self.audioPlayer.delegate = self;
            [self.audioPlayer prepareToPlay];
        }
        [self.audioPlayer play];
        return YES;
    }
    DDLogWarnTag(@"audio", @"file not exists");
    return NO;
}

- (void)playSound {
    // 產生暫存目錄 (tmp)
    NSString *tmpDirectory = NSTemporaryDirectory();
    // 取得 sound check model
    UTENMediaModel *currentSoundCheckModel = self.viewModel.currentSoundCheckModel;
    if (currentSoundCheckModel == nil) {
        DDLogWarnTag(@"audio", @"currentSoundCheckModel is nil");
        // 拋出例外
        @throw [NSException exceptionWithName:@"SoundCheckViewController" reason:@"currentSoundCheckModel is nil" userInfo:nil];
        // return;
    }
    // 取得 sound check model 的 sound
    NSString *sound = currentSoundCheckModel.file;
    // 組合檔名
    NSString *filename = [NSString stringWithFormat:@"%@/%@", tmpDirectory, sound];
    // /Users/<USER>/Library/Developer/CoreSimulator/Devices/82826422-4313-4605-9D3B-29E514148FB7/data/Containers/Data/Application/E9ACC202-113A-4B23-97A4-CD28D71DCCAC/tmp//baby_brother_2c+E0AN1+05+s031.mp3
    DDLogDebugTag(@"audio", @"filename: %@", filename);
    // 檢查檔案是否存在 
    if ([[NSFileManager defaultManager] fileExistsAtPath:filename]) {
        DDLogDebugTag(@"audio", @"file exists");
        // 播放音效
        [self playSound:filename];
    }
    else {
        DDLogWarnTag(@"audio", @"file not exists, start download");
        [self downloadFile:sound completion:^(BOOL success, NSError *error) {
            if (success) {
                DDLogDebugTag(@"audio", @"download file success");
                // 播放音效
                [self playSound:filename];
            } else {
                DDLogErrorTag(@"audio", @"download file failed: %@", error);
            }
        }];
    }
}

#pragma mark - AVAudioPlayerDelegate

- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    // 音頻播放完成時調用
}

- (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError *)error {
    // 音頻解碼錯誤發生時調用
}

- (void)setupLayout {
    // 設定 avatar 圖檔
    const NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *avatarPath = [defaults imagePath];
    self.imgAvatar.image = [UIImage imageNamed:avatarPath];
    // 使用 ReactiveObjC 設定 imgUp click event
    {
        UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] init];
        [self.imgUp addGestureRecognizer:tapGestureRecognizer];
        self.imgUp.userInteractionEnabled = YES;
        @weakify(self);
        [[[tapGestureRecognizer rac_gestureSignal] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
            @strongify(self);
            NSLog(@"imgUp was tapped");
            // 設定 view model 的 current index up
            [self.viewModel movePrevious];
        }];
    }
    // 設定 imgDown click event
    {
        UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] init];
        [self.imgDown addGestureRecognizer:tapGestureRecognizer];
        self.imgDown.userInteractionEnabled = YES;
        @weakify(self);
        [[[tapGestureRecognizer rac_gestureSignal] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
            @strongify(self);
            NSLog(@"imgDown was tapped");
            // 設定 view model 的 current index down
            [self.viewModel moveNext];
        }];
    }
    // 設定 imgYes
    {
        UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] init];
        [self.imgYes addGestureRecognizer:tapGestureRecognizer];
        self.imgYes.userInteractionEnabled = YES;
        @weakify(self);
        [[[tapGestureRecognizer rac_gestureSignal] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
            @strongify(self);
            NSLog(@"imgYes was tapped");
            [self makeRight];
            [self.viewModel moveNext];
        }];
    }
    // 設定 imgNo
    {
        UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] init];
        [self.imgNo addGestureRecognizer:tapGestureRecognizer];
        self.imgNo.userInteractionEnabled = YES;
        @weakify(self);
        [[[tapGestureRecognizer rac_gestureSignal] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
            @strongify(self);
            NSLog(@"imgNo was tapped");
            [self makeWrong];
            [self.viewModel moveNext];
        }];
    }
    // 搜尋所有的子視圖，並將其從父視圖中移除(UITableView)
    // 其實只需要改 storyboard 就可以了
    // 因考量到協作開發合併不易，所以這裡用程式碼移除
    for (UIView *subview in self.view.subviews) {
        if ([subview isKindOfClass:[UITableView class]]) {
            [subview removeFromSuperview];
        }
    }
    UIView *backgroundView = [[UIView alloc] init];
    [self.view addSubview:backgroundView];
    [backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top).offset(60);
        make.left.equalTo(self.view.mas_left).offset(20);
        make.right.equalTo(self.view.mas_right).offset(-20);
        make.bottom.equalTo(self.view.mas_bottom).offset(-140);
        // make.height.equalTo(@600);
    }];
    // 半透明白色背景
    backgroundView.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.5];
    backgroundView.translatesAutoresizingMaskIntoConstraints = NO;
    // group view
    UIView *containerView = [[UIView alloc] init];
    [backgroundView addSubview:containerView];
    // 使用 masonry 設定 padding
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backgroundView.mas_top).offset(30);
        make.left.equalTo(backgroundView.mas_left).offset(30);
        make.right.equalTo(backgroundView.mas_right).offset(-30);
        make.bottom.equalTo(backgroundView.mas_bottom).offset(-30);
    }];
    UIColor *defaultSeparatorColor = [UIColor colorWithWhite:0.8 alpha:1];
    // 新增 table view 到 stack view 中
    _tableView1 = [[UITableView alloc] init];
    _tableView1.translatesAutoresizingMaskIntoConstraints = NO;
    [containerView addSubview:_tableView1];
    // 設定 table view 的 delegate 和 dataSource    
    _tableView1.delegate = self;
    _tableView1.dataSource = self;
    [_tableView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(containerView.mas_top);
        make.bottom.equalTo(containerView.mas_bottom);
        make.left.equalTo(containerView.mas_left);
        // 設定寬度 115
        make.width.equalTo(@115);
    }];
    _tableView1.layer.borderWidth = 1;
    _tableView1.layer.borderColor = defaultSeparatorColor.CGColor;

    // @weakify(self);
    // [[self rac_signalForSelector:@selector(tableView:didSelectRowAtIndexPath:)
    //                 fromProtocol:@protocol(UITableViewDelegate)] subscribeNext:^(RACTuple *tuple) {
    //     NSIndexPath *indexPath = tuple.last;
    //     @strongify(self);
    //     [[NSNotificationCenter defaultCenter] postNotificationName:RACTestNotification object:indexPath];
    //     self.selectIndex = indexPath.row;
    // }];
    
    // 新增 table view 2 到 stack view 中
    _tableView2 = [[UITableView alloc] init];
    _tableView2.translatesAutoresizingMaskIntoConstraints = NO;
    [containerView addSubview:_tableView2];
    // 設定 table view 2 的 delegate 和 dataSource
    _tableView2.delegate = self;
    _tableView2.dataSource = self;
    [_tableView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(containerView.mas_top);
        make.bottom.equalTo(containerView.mas_bottom);
        make.left.equalTo(_tableView1.mas_right);
        make.width.equalTo(@80);
        // make.right.equalTo(containerView.mas_right).offset(-10);
    }];
    // border use very light gray color
    _tableView2.layer.borderWidth = 1;
    _tableView2.layer.borderColor = defaultSeparatorColor.CGColor;
    {
        // 新增 table view speakers 到 stack view 中
        _tableViewSpeakers = [[UITableView alloc] init];
        _tableViewSpeakers.translatesAutoresizingMaskIntoConstraints = NO;
        [containerView addSubview:_tableViewSpeakers];
        [_tableViewSpeakers mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(containerView.mas_top);
            make.bottom.equalTo(containerView.mas_bottom);
            make.left.equalTo(_tableView2.mas_right);
            make.width.equalTo(@120);
            // make.right.equalTo(containerView.mas_right).offset(-10);
        }];
        // [stackView addArrangedSubview:_tableViewSpeakers];
        // 設定 table view speakers 的 delegate 和 dataSource
        _tableViewSpeakers.delegate = self;
        _tableViewSpeakers.dataSource = self;
        _tableViewSpeakers.layer.borderWidth = 1;
        _tableViewSpeakers.layer.borderColor = defaultSeparatorColor.CGColor;
    }
    // 新增 horizontal stack view
    UIStackView *stackView = [[UIStackView alloc] init];
    [containerView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(containerView.mas_top);
        make.bottom.equalTo(containerView.mas_bottom);
        make.left.equalTo(_tableViewSpeakers.mas_right);
        make.right.equalTo(containerView.mas_right);
    }];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.distribution = UIStackViewDistributionFillEqually;
    stackView.alignment = UIStackViewAlignmentFill;
    // stackView.spacing = 10;
    stackView.translatesAutoresizingMaskIntoConstraints = NO;
    // 新增 table view 3 到 stack view 中
    _tableViewFiles = [[UITableView alloc] init];
    [stackView addArrangedSubview:_tableViewFiles];
    _tableViewFiles.translatesAutoresizingMaskIntoConstraints = NO;
    // 設定 table view files 的 delegate 和 dataSource
    _tableViewFiles.delegate = self;
    _tableViewFiles.dataSource = self;
    _tableViewFiles.layer.borderWidth = 1;
    _tableViewFiles.layer.borderColor = defaultSeparatorColor.CGColor;
}

// 設定 view model 的 confirm result to right
- (void)makeRight {
    return;
    UTENSoundCheckModel *currentSoundCheckModel = self.viewModel.currentSoundCheckModel;
    if (currentSoundCheckModel != nil) {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        if (defaults.isMaster) {
            currentSoundCheckModel.teacherConfirmResult = ConfirmResultRight;
            [self reloadSelectedIndexView];
        } else if (defaults.isStudent) {
            currentSoundCheckModel.checkerConfirmResult = ConfirmResultRight;
            [self reloadSelectedIndexView];
        }
        // 檔案更名
        @weakify(self);
        [self renameFile:currentSoundCheckModel completion:^(BOOL success, NSError *error) {
            // @strongify(self);
            if (success) {
                NSLog(@"rename file success");
            } else {
                NSLog(@"rename file failed: %@", error);
            }
        }];
    }
}

// 設定 view model 的 confirm result to wrong 
- (void)makeWrong {
    return;
    UTENSoundCheckModel *currentSoundCheckModel = self.viewModel.currentSoundCheckModel;
    if (currentSoundCheckModel != nil) {
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        if (defaults.isMaster) {
            currentSoundCheckModel.teacherConfirmResult = ConfirmResultWrong;
            [self reloadSelectedIndexView];
        } else if (defaults.isStudent) {
            currentSoundCheckModel.checkerConfirmResult = ConfirmResultWrong;
            [self reloadSelectedIndexView];
        }
        // 檔案更名
        @weakify(self);
        [self renameFile:currentSoundCheckModel completion:^(BOOL success, NSError *error) {
            // @strongify(self);
            if (success) {
                NSLog(@"rename file success");
            } else {
                NSLog(@"rename file failed: %@", error);
            }
        }];
    }
}

// update selected index
- (void)reloadSelectedIndexView {
    const NSInteger index = self.viewModel.selectedSoundCheckModelIndex;
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:index inSection:0];
    [self.tableViewFiles reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
}

- (void)startRotatingImageView:(UIImageView *)imageView withDuration:(CGFloat)duration {
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];//創建基本動畫  //(旋轉)
    rotationAnimation.fromValue = @(0);  //旋轉的起點為 0度。       //(旋轉)
    rotationAnimation.toValue = @(M_PI * 2);//表示轉360度         //(旋轉)
    //rotationAnimation.toValue = @(M_PI * 2 * 120 / 360);//旋轉弧長度.轉1/3圈
    rotationAnimation.fillMode = kCAFillModeForwards;//確定重新來 //(旋轉)
    rotationAnimation.removedOnCompletion = NO;//確定重新來       //(旋轉)
    rotationAnimation.duration = duration;//持續時間.旋轉速度      //(旋轉)
    rotationAnimation.repeatCount = INFINITY;//無限次重復         //(旋轉)
    [imageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];// 將動畫添加到圖片的layer中   //(旋轉)
}

#pragma mark - GRRequestsManagerDelegate

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteListingRequest:(id<GRRequestProtocol>)request listing:(NSArray *)listing {
    DDLogDebugTag(@"ftp", @"Directory listing: %@", listing);
    // NSLog(@"Directory listing: %@", ((GRListingRequest *)request).filesInfo);
    // loop to sound check model
    NSMutableArray<UTENSoundCheckModel *> *models = [NSMutableArray array];
    for (NSString *filename in listing) {
        UTENSoundCheckModel *model = [UTENSoundCheckModel modelWithString:filename];
        [models addObject:model];
    }
    self.viewModel.soundCheckModels = models;
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailRequest:(id<GRRequestProtocol>)request withError:(NSError *)error {
    DDLogErrorTag(@"ftp", @"Request failed: %@", error);
    // complete failed
    // const Completion completion = self.completions[request.remotePath.lastPathComponent];
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didScheduleRequest:(id<GRRequestProtocol>)request {
    DDLogDebugTag(@"ftp", @"Request scheduled");
}

// did complete create directory
- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteCreateDirectoryRequest:(id<GRRequestProtocol>)request {
    DDLogDebugTag(@"ftp", @"Created directory");
}

// did complete delete file
- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDeleteRequest:(id<GRRequestProtocol>)request {
    DDLogDebugTag(@"ftp", @"Deleted file");
    NSString *remoteFilePath = request.path; // 或其他適當的屬性
    // /uten/upload/baby_brother_2c-E0AN1-05-s031.mp3
    DDLogDebugTag(@"ftp", @"remoteFilePath: %@", remoteFilePath);
    // 取出檔名
    NSString *filename = [remoteFilePath lastPathComponent];
    // baby_brother_2c-E0AN1-05-s031.mp3
    DDLogDebugTag(@"ftp", @"filename: %@", filename);
    // notify completion
    const Completion completion = self.completions[filename];
    if (completion != nil) {
        completion(YES, nil);
    }
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompletePercent:(float)percent forRequest:(id<GRRequestProtocol>)request {
    DDLogDebugTag(@"ftp", @"percent: %f", percent);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteUploadRequest:(id<GRDataExchangeRequestProtocol>)request {
    DDLogDebugTag(@"ftp", @"Uploaded file");
    NSString *localFilePath = request.localFilePath; // 或其他適當的屬性
    // /Users/<USER>/Library/Developer/CoreSimulator/Devices/82826422-4313-4605-9D3B-29E514148FB7/data/Containers/Data/Application/A996CFEB-730E-4CEA-A1F6-775D96F7F4AB/tmp//baby_brother_2c+E0AN1+05+s031.mp3
    DDLogDebugTag(@"ftp", @"localFilePath: %@", localFilePath);
    // 取出檔名
    NSString *fullRemotePath = request.fullRemotePath; // 或其他適當的屬性
    // ftp://uten.synology.me/uten/upload/baby_brother_2c%2BE0AN1%2B05%2Bs031.mp3
    DDLogDebugTag(@"ftp", @"fullRemotePath: %@", fullRemotePath);
    // 取出檔名
    NSString *filename = [fullRemotePath lastPathComponent];
    // baby_brother_2c%2BE0AN1%2B05%2Bs031.mp3
    DDLogDebugTag(@"ftp", @"filename: %@", filename);
    // notify completion
    const Completion completion = self.completions[filename];
    if (completion != nil) {
        completion(YES, nil);
    }
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDownloadRequest:(id<GRDataExchangeRequestProtocol>)request {
    DDLogDebugTag(@"ftp", @"Downloaded file");
    NSString *localFilePath = request.localFilePath; // 或其他適當的屬性
    // /Users/<USER>/Library/Developer/CoreSimulator/Devices/82826422-4313-4605-9D3B-29E514148FB7/data/Containers/Data/Application/A996CFEB-730E-4CEA-A1F6-775D96F7F4AB/tmp//baby_brother_2c+E0AN1+05+s031.mp3
    DDLogDebugTag(@"ftp", @"localFilePath: %@", localFilePath);
    NSString *fullRemotePath = request.fullRemotePath; // 或其他適當的屬性
    // ftp://uten.synology.me/uten/upload/baby_brother_2c%2BE0AN1%2B05%2Bs031.mp3
    DDLogDebugTag(@"ftp", @"fullRemotePath: %@", fullRemotePath);
    // 取出檔名
    NSString *filename = [fullRemotePath lastPathComponent];
    // baby_brother_2c%2BE0AN1%2B05%2Bs031.mp3
    DDLogDebugTag(@"ftp", @"filename: %@", filename);
    // notify completion
    const Completion completion = self.completions[filename];
    if (completion != nil) {
        completion(YES, nil);
    }
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailWritingFileAtPath:(NSString *)path forRequest:(id<GRDataExchangeRequestProtocol>)request error:(NSError *)error {
    DDLogErrorTag(@"ftp", @"Failed writing file at path: %@", path);
}

- (void)requestsManagerDidCompleteQueue:(id<GRRequestsManagerProtocol>)requestsManager {
    DDLogDebugTag(@"ftp", @"Queue completed");
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    // class
    if (tableView == self.tableView1) {
        return self.viewModel.allClasses.count;
    }
    // lesson
    if (tableView == self.tableView2) {
        return self.viewModel.allLessons.count;
    }
    // sound check with selected class and lesson
    if (tableView == self.tableViewFiles) {
        return self.viewModel.mediaList.count;
    }
    if (tableView == self.tableViewSpeakers) {
        return self.viewModel.allSpeakers.count;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    // log index path
    NSLog(@"indexPath: %@", indexPath);
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"CellIdentifier"];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"CellIdentifier"];
    }
    // class
    if (tableView == self.tableView1) {
        // const UTENNclassModel *data = self.viewModel.nclass[indexPath.row];
        NSString *className = self.viewModel.allClasses[indexPath.row];
        cell.textLabel.text = className;
        // 灰色字
        cell.textLabel.textColor = [UIColor darkGrayColor];
        return cell;
    }

    if (tableView == self.tableView2) {
        // BasicTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BasicCell"];
        // if (cell == nil) {
        //     cell = [[BasicTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"BasicCell"];
        // }
        // 配置您的 cell
        // const UTENNstudentModel *data = self.viewModel.filtedStudents[indexPath.row];
        // cell.titleLabel.text = data.ename;
        // cell.subtitleLabel.text = data.cname;
        NSString *lessonName = self.viewModel.allLessons[indexPath.row];
        cell.textLabel.text = lessonName;
        return cell;
    }

    if (tableView == self.tableViewFiles) {
        UTENMediaModel *model = self.viewModel.mediaList[indexPath.row];
        DDLogDebugTag(@"audio", @"model file: %@", model.file);
        cell.textLabel.text = model.file;
        // backgroundColor
        // cell.backgroundColor = model.backgroundColor;
        if (self.viewModel.selectedSoundCheckModelIndex == indexPath.row) {
            // NSNotFound
            // [UIView animateWithDuration:1.0 // 閃爍時長
            //               delay:0.0 // 延遲時間
            //             options:(UIViewAnimationOptionAutoreverse | UIViewAnimationOptionRepeat | UIViewAnimationOptionAllowUserInteraction)
            //          animations:^{
            //             [UIView setAnimationRepeatCount:INFINITY]; // 重複無限次
            //             cell.textLabel.alpha = 0.2; // 透明度變化到
            //          } completion:^(BOOL finished) {
            //             cell.textLabel.alpha = 1.0; // 最終透明度恢復
            //          }];
        }
        return cell;
    }

    if (tableView == self.tableViewSpeakers) {
        NSString *speaker = self.viewModel.allSpeakers[indexPath.row];
        cell.textLabel.text = speaker;
        return cell;
    }

    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // class
    if (tableView == self.tableView1) {
        NSLog(@"tableView1: %ld", indexPath.row);
        // view model 設定選擇的課程，顯示對應的學生
        // self.viewModel.selectedNclass = self.viewModel.nclass[indexPath.row];
        self.viewModel.selectedClass = self.viewModel.allClasses[indexPath.row];
    }
    // lesson
    if (tableView == self.tableView2) {
        NSLog(@"tableView2: %ld", indexPath.row);
        self.viewModel.selectedLesson = self.viewModel.allLessons[indexPath.row];
    }
    if (tableView == self.tableViewFiles) {
        NSLog(@"tableViewFiles: %ld", indexPath.row);
        if (self.viewModel.selectedSoundCheckModelIndex != indexPath.row) {
            self.viewModel.selectedSoundCheckModelIndex = indexPath.row;
        }
        // try catch
        @try {
            [self playSound];
        } @catch (NSException *exception) {
            NSLog(@"Exception: %@", exception);
        }
    }
    if (tableView == self.tableViewSpeakers) {
        NSLog(@"tableViewSpeakers: %ld", indexPath.row);
        self.viewModel.selectedSpeaker = self.viewModel.allSpeakers[indexPath.row];
    }
}

//- (void)nextImage {
//    //(搖.手指) 更新圖片索引
//    self.currentImageIndex = (self.currentImageIndex + 1) % self.imageNames.count;
//    
//    //(搖.手指) 圖片視圖為下一張圖片
//    self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];
//}

//- (void)dealloc {
//    //(搖.手指) 確保定時器被正確無誤地停止
//    [self.timer invalidate];//(搖.手指)
//    self.timer = nil;       //(搖.手指)
//}                           //(搖.手指)

//返回.上一層
- (IBAction)btBack:(id)sender {
     [self dismissViewControllerAnimated:NO completion:nil];
}//返回.上一層


@end
