//
//  selector 41 ViewController.m
//  uten
//
//  Created by 蔡駿寓 on 2024/11/25.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "selector41ViewController.h"
#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
#import <Foundation/Foundation.h>                           //拆解字串中的文字用
@interface selector41ViewController ()

@property (strong, nonatomic) NSMutableArray *FOword1;      //寓.宣告 FOword1
@property (strong, nonatomic) NSMutableArray *FOword2;      //寓.宣告 FOword2
//@property (nonatomic, strong) NSString *FOwordAns;          //寓.改全域.屬性 //沒用到可刪
@property (strong, nonatomic) NSTimer *questionTimer;       //用來儲存計時器物件
@property (assign, nonatomic) float countDownValue;         //用來記錄倒數時間
@property (nonatomic,assign) NSInteger currentQuestionIndex;//現在題目到那一題
@property (nonatomic,assign) NSInteger totalQuestions;      //所有題目數量

//@property (strong, nonatomic) NSArray *FOword101;






@property (weak, nonatomic) IBOutlet UILabel *btlbBookUnit; //發音(冊.課)欄位
@property (weak, nonatomic) IBOutlet UILabel *btlb41Q;      //題目欄.文字
@property (weak, nonatomic) IBOutlet UIImageView *btimg41Q; //題目欄.圖
@property (weak, nonatomic) IBOutlet UILabel *btlb411;      //從Label改到Button
@property (weak, nonatomic) IBOutlet UILabel *btlb412;      //從Label改到Button
@property (weak, nonatomic) IBOutlet UILabel *btlb413;      //從Label改到Button
@property (weak, nonatomic) IBOutlet UILabel *btlb414;      //從Label改到Button
@property (weak, nonatomic) IBOutlet UIImageView *btimg411; //按鍵A圖
@property (weak, nonatomic) IBOutlet UIImageView *btimg412; //按鍵B圖
@property (weak, nonatomic) IBOutlet UIImageView *btimg413; //按鍵C圖
@property (weak, nonatomic) IBOutlet UIImageView *btimg414; //按鍵D圖
@property (weak, nonatomic) IBOutlet UIButton *bt411;       //按鍵A
@property (weak, nonatomic) IBOutlet UIButton *bt412;       //按鍵B
@property (weak, nonatomic) IBOutlet UIButton *bt413;       //按鍵C
@property (weak, nonatomic) IBOutlet UIButton *bt414;       //按鍵D

@property (nonatomic, strong) AVAudioPlayer *audioPlayer;   //播音檔
@property (nonatomic, strong) NSMutableArray<NSString *> *answerButton;//改成全域//4按鍵內容

@end

//NSString *FOword2[1000];
//NSString *TypeIndex ;
NSString *FOword2Ans ;                //存放正解
NSInteger audioPlayerIndex = 0;       //現在播音檔.到哪一次 //加＊代表指標,會每做1次加8
NSInteger countDownActionIndex = 0;   //現在倒數計時.到哪一次
//BOOL NeedSkip = NO;

@implementation selector41ViewController
//====(執行順序.地圖說明)========(重做.2025 0112)
//==<ViewdidLoad>====
//1.導入 FOword101數據
//    清空 FOword1
//    導入 FOword1 = FOword101
//2.預設不為空  answerButton[0]
//3.靜音模式也可播放
//4.執行.setupFOword2
//    setupFOword2(判斷.亂數洗牌)(課1~13亂)
//5.<執行.秀１題>====
//    查.題目數完return( QIndex >= self.totalQuestions )
//    設.抬頭.文字.btlbBookUnit
//    查.題目數完return( i >= self.FOword2.count )//重複可刪
//    導入.題目( question = FOword2 ) 音檔( playMp3 = self.FOword2[ i+1 ] )
//    有音檔.播音檔
//    導入.( FOword2Ans = question; )//好像多餘,查.是否可刪
//(未)<判斷函式>依.(Range.始-終)(Type. AZ. ComPH. LWord)
//    同上.依範圍分配給(AZ)(ComPH.複合音at)(LWord.課單字)
//    執行.隨機洗牌 setupAnswerButton
//    啟動倒數(2秒)
//====(執行順序.地圖說明)========
//======(預設.測試.按鈕設定)======
//    int IndexLesson = 0;                            //課(多餘，不用，可刪除)
    NSString *IndexBook = @"PH3LWordQ[13][8]";      //冊
    int IndexWordStart = 0;                         //課/字.從這課開始
    int IndexWordEnd = 0;                           //課/字.到那課結束
    bool IndexAZUnit = 0;                           //單課依序(bool).AZa~z自然發音.
    bool IndexComPHUnit = 0;                        //單課依序(bool).ComPH複合音.
    bool IndexLessonUnit = 0;                       //單課依序(bool).課單字(FOwords.不洗牌)
    bool IndexLsUnit = 0;                           //多課依序(bool)多課預習習.(唸一遍)
    bool IndexAZRandom = 0;                         //亂數(bool)AZ.a~z自然發音.亂數練習
    bool IndexComPHRandom = 0;                      //亂數(bool)ComPH.複合音.亂數練習
    bool IndexLessonRandom = 0;                     //亂數(bool)單課.課單字.採亂數數次(FOwords.洗牌用)
    bool IndexLsRandom = 0;                         //亂數(bool)多課.總複習.
    int IndexRepeat = 1;                            //練習次數.3.6.9次
    int IndexWrongRepeat = 0;                       //錯罰次數.0.1.2.5.10
//    分配類型(AZ.亂/順)(ComPH亂/順)(LWord亂/順)
//======(預設.測試.按鈕設定)======
//======(導入.資料庫)======
static NSString *PHAZQ[26][2]= {
    {@"a",@"ph_a.mp3"},{@"b",@"ph_b.mp3"},{@"c",@"ph_c.mp3"},
    {@"d",@"ph_d.mp3"},{@"e",@"ph_e.mp3"},{@"f",@"ph_f.mp3"},
    {@"g",@"ph_g.mp3"},{@"h",@"ph_h.mp3"},{@"i",@"ph_i.mp3"},
    {@"j",@"ph_j.mp3"},{@"k",@"ph_k.mp3"},{@"l",@"ph_l.mp3"},
    {@"m",@"ph_m.mp3"},{@"n",@"ph_n.mp3"},{@"o",@"ph_o.mp3"},
    {@"p",@"ph_p.mp3"},{@"q",@"ph_q.mp3"},{@"r",@"ph_r.mp3"},
    {@"s",@"ph_s.mp3"},{@"t",@"ph_t.mp3"},{@"u",@"ph_u.mp3"},
    {@"v",@"ph_v.mp3"},{@"w",@"ph_w.mp3"},{@"x",@"ph_x.mp3"},
    {@"y",@"ph_y.mp3"},{@"z",@"ph_z.mp3"} };               //發音字母26個(含.mp3)

static NSString *PHAZA4[26][4]= {
    {@"a",@"cdu",@"efo",@"bghijklmnpqrstvwxyz"},   //AnswerAZ[26][4]
    {@"b",@"dq",@"chkpv",@"aefgijlmnorstuwxyz"},
    {@"c",@"eo",@"bhktx",@"adfgijlmnpqrsuvwyz"},
    {@"d",@"bpq",@"ckt",@"aefghijlmnorsuvwxyz"},
    {@"e",@"co",@"af",@"bdghijklmnpqrstuvwxyz"},
    {@"f",@"jlt",@"hv",@"abcdegikmnopqrsuwxyz"},
    {@"g",@"ad",@"q",@"bcefhijklmnoprstuvwxyz"},
    {@"h",@"bn",@"fv",@"acdegijklmopqrstuwxyz"},
    {@"i",@"jl",@"e",@"abcdfghkmnopqrstuvwxyz"},
    {@"j",@"il",@"g",@"abcdefhkmnopqrstuvwxyz"},
    {@"k",@"lb",@"chtx",@"adefgijmnopqrsuvwyz"},
    {@"l",@"ij",@"aekto",@"bcdfghmnpqrsuvwxyz"},
    {@"m",@"nw",@"lpv",@"abcdefghijkoqrstuxyz"},
    {@"n",@"hru",@"m",@"abcdefgijklopqstvwxyz"},
    {@"o",@"cu",@"ae",@"bdfghijklmnpqrstvwxyz"},
    {@"p",@"bdq",@"ckbfh",@"aegijlmnorstuvwxz"},
    {@"q",@"bdp",@"g",@"acefhijklmnorstuvwxyz"},
    {@"r",@"nh",@"vo",@"abcdefgijklmpqstuwxyz"},
    {@"s",@"ce",@"xz",@"abdfghijklmnopqrtuvwy"},
    {@"t",@"rfl",@"cbdk",@"aeghijmnoqpsuvwxyz"},
    {@"u",@"acnv",@"eo",@"bdfghijklmpqrstwxyz"},
    {@"v",@"nuw",@"fh",@"abcdegijklmopqrstxyz"},
    {@"w",@"mv",@"oq",@"abcdefghijklnprstuxyz"},
    {@"x",@"y",@"sz",@"abcdefghijklmnopqrtuvw"},
    {@"y",@"jgx",@"iqw",@"abcdefhklmnoprstuvz"},
    {@"z",@"cx",@"os",@"abdefghijklmnpqrtuvwy"} };   //發音字母26個(挑4選ABCD用)(正解.形.音.隨機)

static NSString *PH3ComQ[15][2]={
    {@"at",@"bat_at.mp3"},
    {@"an",@"can_an.mp3"},
    {@"ad",@"bad_ad.mp3"},
    {@"ack",@"back_ack.mp3"},
    {@"ig",@"big_ig.mp3"},
    {@"ill",@"hill_ill.mp3"},
    {@"ing",@"king_ing.mp3"},
    {@"ick",@"lick_ick.mp3"},
    {@"op",@"hop_op.mp3"},
    {@"ot",@"not_ot.mp3"},
    {@"ock",@"rock_ock.mp3"},
    {@"ox",@"fox_ox.mp3"},
    {@"un",@"bun_un.mp3"},
    {@"ug",@"bug_ug.mp3"},
    {@"uck",@"duck_uck.mp3"} };   //PH3複合音15個(含.mp3)

static NSString *PH3ComA4[15][7]={            //AnswerComPH3[15][7]
    {@"at",@"an",@"ad",@"ack",@"un",@"ug",@"uck"},
    {@"an",@"at",@"ad",@"ack",@"un",@"ug",@"uck"},
    {@"ad",@"at",@"an",@"ack",@"un",@"ug",@"uck"},
    {@"ack",@"at",@"an",@"ad",@"un",@"ug",@"uck"},
    {@"ig",@"ill",@"ing",@"ick",@"ug",@"ock",@"uck"},
    {@"ill",@"ig",@"ing",@"ick",@"ug",@"ock",@"uck"},
    {@"ing",@"ig",@"ill",@"ick",@"ug",@"ock",@"uck"},
    {@"ick",@"ig",@"ill",@"ing",@"ug",@"ock",@"uck"},
    {@"op",@"ot",@"ock",@"ox",@"un",@"ug",@"uck"},
    {@"ot",@"op",@"ock",@"ox",@"un",@"ug",@"uck"},
    {@"ock",@"op",@"ot",@"ox",@"un",@"ug",@"uck"},
    {@"ox",@"op",@"ot",@"ock",@"un",@"ug",@"uck"},
    {@"un",@"ug",@"uck",@"op",@"ot",@"ock",@"ox"},
    {@"ug",@"un",@"uck",@"op",@"ot",@"ock",@"ox"},
    {@"uck",@"un",@"ug",@"op",@"ot",@"ock",@"ox"} };  //PH3複合音(挑4選ABCD用),正解[123]選1[4567]選2

static NSArray *PH3LWordQ;
// static NSString *PH3LWordQ[13][8]= {   //AnswerRandPH3[13][8]
//     {@"bat",@"bat_2e.mp3",@"cat",@"cat_2e.mp3",@"hat",@"hat_2e.mp3",@"rat",@"rat_2e.mp3"},
//     {@"can",@"can_2e.mp3",@"man",@"man_2e.mp3",@"pan",@"pan_2e.mp3",@"van",@"van_2e.mp3"},
//     {@"bad",@"bad_2e.mp3",@"dad",@"dad_2e.mp3",@"mad",@"mad_2e.mp3",@"sad",@"sad_2e.mp3"},
//     {@"back",@"back_2e.mp3",@"pack",@"pack_2e.mp3",@"sack",@"sack_2e.mp3",@"snack",@"snack_2e.mp3"},
//     {@"big",@"big_2e.mp3",@"pig",@"pig_2e.mp3",@"wig",@"wig_2e.mp3",@"twig",@"twig_2e.mp3"},
//     {@"Bill",@"bill_2e.mp3",@"Jill",@"jill_2e.mp3",@"hill",@"hill_2e.mp3",@"ill",@"ill_2e.mp3"},
//     {@"king",@"king_2e.mp3",@"ring",@"ring_2e.mp3",@"sing",@"sing_2e.mp3",@"wings",@"wings_2e.mp3"},
//     {@"lick",@"lick_2e.mp3",@"Nick",@"Nick_2e.mp3",@"kick",@"kick_2e.mp3",@"chick",@"chick_2e.mp3"},
//     {@"hop",@"hop_2e.mp3",@"mop",@"mop_2e.mp3",@"pop",@"pop_2e.mp3",@"top",@"top_2e.mp3"},
//     {@"not",@"not_2e.mp3",@"dot",@"dot_2e.mp3",@"hot",@"hot_2e.mp3",@"pot",@"pot_2e.mp3"},
//     {@"rock",@"rock_2e.mp3",@"sock",@"sock_2e.mp3",@"fox",@"fox_2e.mp3",@"ox",@"ox_2e.mp3"},
//     {@"bun",@"bun_2e.mp3",@"nun",@"nun_2e.mp3",@"run",@"run_2e.mp3",@"sun",@"sun_2e.mp3"},
//     {@"bug",@"bug_2e.mp3",@"rug",@"rug_2e.mp3",@"duck",@"duck_2e.mp3",@"truck",@"truck_2e.mp3"} };
     //PH3.課單字.13課(含.mp3)

static NSString *PH3LWordA4[13][4]= {
    {@"bat",@"cat",@"hat",@"rat"},
    {@"can",@"man",@"pan",@"van"},
    {@"bad",@"dad",@"mad",@"sad"},
    {@"back",@"pack",@"sack",@"snack"},
    {@"big",@"pig",@"wig",@"twig"},
    {@"Bill",@"Jill",@"hill",@"ill"},
    {@"king",@"ring",@"sing",@"wings"},
    {@"lick",@"Nick",@"kick",@"chick"},
    {@"hop",@"mop",@"pop",@"top"},
    {@"not",@"dot",@"hot",@"pot"},
    {@"rock",@"sock",@"fox",@"ox"},
    {@"bun",@"nun",@"run",@"sun"},
    {@"bug",@"rug",@"duck",@"truck"} };    //PH3.課單字(挑4選ABCD用),正解+依序放入

//(課1~課13<順>)======
//    NSArray  *FOword101 = @[
//            @"b", @"bat_b.mp3", @"at", @"bat_at.mp3", @"bat", @"bat_2e.mp3",
//            @"c", @"cat_c.mp3", @"at", @"cat_at.mp3", @"cat", @"cat_2e.mp3",
//            @"h", @"hat_h.mp3", @"at", @"hat_at.mp3", @"hat", @"hat_2e.mp3",
//            @"r", @"rat_r.mp3", @"at", @"rat_at.mp3", @"rat", @"rat_2e.mp3"
//    ]; //導入(課１亂.資料組)(6*4*2=48遍,約1.5分鍾)
  //(課1~課13<順>)======
static NSArray *PH3AllWord;
// static NSString *PH3AllWord[52][6]= {
// {@"b", @"bat_b.mp3", @"at", @"bat_at.mp3", @"bat", @"bat_2e.mp3"},
// {@"c", @"cat_c.mp3", @"at", @"cat_at.mp3", @"cat", @"cat_2e.mp3"},
// {@"h", @"hat_h.mp3", @"at", @"hat_at.mp3", @"hat", @"hat_2e.mp3"},
// {@"r", @"rat_r.mp3", @"at", @"rat_at.mp3", @"rat", @"rat_2e.mp3"},
// {@"c", @"can_c.mp3", @"an", @"can_an.mp3", @"can", @"can_2e.mp3"},
// {@"m", @"man_m.mp3", @"an", @"man_an.mp3", @"man", @"man_2e.mp3"},
// {@"p", @"pan_p.mp3", @"an", @"pan_an.mp3", @"pan", @"pan_2e.mp3"},
// {@"v", @"van_v.mp3", @"an", @"van_an.mp3", @"van", @"van_2e.mp3"},
// {@"b", @"bad_b.mp3", @"ad", @"bad_ad.mp3", @"bad", @"bad_2e.mp3"},
// {@"d", @"dad_d.mp3", @"ad", @"dad_ad.mp3", @"dad", @"dad_2e.mp3"},
// {@"m", @"mad_m.mp3", @"ad", @"mad_ad.mp3", @"mad", @"mad_2e.mp3"},
// {@"s", @"sad_s.mp3", @"ad", @"sad_ad.mp3", @"sad", @"sad_2e.mp3"},
// {@"b", @"back_b.mp3", @"ack", @"back_ack.mp3", @"back", @"back_2e.mp3"},
// {@"p", @"pack_p.mp3", @"ack", @"pack_ack.mp3", @"pack", @"pack_2e.mp3"},
// {@"s", @"sack_s.mp3", @"ack", @"sack_ack.mp3", @"sack", @"sack_2e.mp3"},
// {@"sn", @"snack_sn.mp3", @"ack", @"snack_ack.mp3", @"snack", @"snack_2e.mp3"},
// {@"b", @"big_b.mp3", @"ig", @"big_ig.mp3", @"big", @"big_2e.mp3"},
// {@"p", @"pig_p.mp3", @"ig", @"pig_ig.mp3", @"pig", @"pig_2e.mp3"},
// {@"w", @"wig_w.mp3", @"ig", @"wig_ig.mp3", @"wig", @"wig_2e.mp3"},
// {@"tw", @"twig_tw.mp3", @"ig", @"twig_ig.mp3", @"twig", @"twig_2e.mp3"},
// {@"B", @"bill_b.mp3", @"ill", @"bill_ill.mp3", @"Bill", @"bill_2e.mp3"},
// {@"J", @"jill_j.mp3", @"ill", @"jill_ill.mp3", @"Jill", @"jill_2e.mp3"},
// {@"h", @"hill_h.mp3", @"ill", @"hill_ill.mp3", @"hill", @"hill_2e.mp3"},
// {@"*", @"*.mp3", @"*", @"*.mp3", @"ill", @"ill_2e.mp3"},
// {@"k", @"king_k.mp3", @"ing", @"king_ing.mp3", @"king", @"king_2e.mp3"},
// {@"r", @"ring_r.mp3", @"ing", @"ring_ing.mp3", @"ring", @"ring_2e.mp3"},
// {@"s", @"sing_s.mp3", @"ing", @"sing_ing.mp3", @"sing", @"sing_2e.mp3"},
// {@"w", @"wings_w.mp3", @"ings", @"wings_ings.mp3", @"wings", @"wings_2e.mp3"},
// {@"l", @"lick_l.mp3", @"ick", @"lick_ick.mp3", @"lick", @"lick_2e.mp3"},
// {@"N", @"Nick_N.mp3", @"ick", @"Nick_ick.mp3", @"Nick", @"Nick_2e.mp3"},
// {@"k", @"kick_k.mp3", @"ick", @"kick_ick.mp3", @"kick", @"kick_2e.mp3"},
// {@"ch", @"chick_ch.mp3", @"ick", @"chick_ick.mp3", @"chick", @"chick_2e.mp3"},
// {@"h", @"hop_h.mp3", @"op", @"hop_op.mp3", @"hop", @"hop_2e.mp3"},
// {@"m", @"mop_m.mp3", @"op", @"mop_op.mp3", @"mop", @"mop_2e.mp3"},
// {@"p", @"pop_p.mp3", @"op", @"pop_op.mp3", @"pop", @"pop_2e.mp3"},
// {@"t", @"top_t.mp3", @"op", @"top_op.mp3", @"top", @"top_2e.mp3"},
// {@"n", @"not_n.mp3", @"ot", @"not_ot.mp3", @"not", @"not_2e.mp3"},
// {@"d", @"dot_d.mp3", @"ot", @"dot_ot.mp3", @"dot", @"dot_2e.mp3"},
// {@"h", @"hot_h.mp3", @"ot", @"hot_ot.mp3", @"hot", @"hot_2e.mp3"},
// {@"p", @"pot_p.mp3", @"ot", @"pot_ot.mp3", @"pot", @"pot_2e.mp3"},
// {@"r", @"rock_r.mp3", @"ock", @"rock_ock.mp3", @"rock", @"rock_2e.mp3"},
// {@"s", @"sock_s.mp3", @"ock", @"sock_ock.mp3", @"sock", @"sock_2e.mp3"},
// {@"f", @"fox_f.mp3", @"ox", @"fox_ox.mp3", @"fox", @"fox_2e.mp3"},
// {@"*", @"*.mp3", @"*", @"*.mp3", @"ox", @"ox_2e.mp3"},
// {@"b", @"bun_b.mp3", @"un", @"bun_un.mp3", @"bun", @"bun_2e.mp3"},
// {@"n", @"nun_n.mp3", @"un", @"nun_un.mp3", @"nun", @"nun_2e.mp3"},
// {@"r", @"run_r.mp3", @"un", @"run_un.mp3", @"run", @"run_2e.mp3"},
// {@"s", @"sun_s.mp3", @"un", @"sun_un.mp3", @"sun", @"sun_2e.mp3"},
// {@"b", @"bug_b.mp3", @"ug", @"bug_ug.mp3", @"bug", @"bug_2e.mp3"},
// {@"r", @"rug_r.mp3", @"ug", @"rug_ug.mp3", @"rug", @"rug_2e.mp3"},
// {@"d", @"duck_d.mp3", @"uck", @"duck_uck.mp3", @"duck", @"duck_2e.mp3"},
// {@"tr", @"truck_tr.mp3", @"uck", @"truck_uck.mp3", @"truck", @"truck_2e.mp3"} };
 //發音三13課.全部     //@"*", @"*.mp3",加判斷式剔除，指標數量才不會錯誤
//======(導入.資料庫)======

//====(編輯FOword2成為2*(6+6)遍.資料流)==============
- (void)setupFOword2{
    DDLogDebugTag(@"4pick1", @"setupFOword2");
    //    self.FOword2 = [NSMutableArray array];//在viewDidLoad已設定成可以變動的
    [self.FOword2 removeAllObjects]; //先清空，避免舊資料殘留//全域使用
    
    NSUInteger countFOword1 = self.FOword1.count; //計算FOword1 資料筆數，放入countFOword1。
    NSLog(@"<FOword2>FOword1目前共有countFOword1= %lu 筆資料",countFOword1);//印出結果
    
    int indexFOword2 = 0;  //計數指針歸零。
    
    for (int i = 0; i < countFOword1; i+=2 ) {
        //==<字迴圈.(字＋ mp3)>========
        NSLog(@"<FOword2>做IndexRepeat=(%d)遍，countFOword1=%lu  ,  i=%d < countFOword1/2=%lu, ",IndexRepeat,countFOword1,i,(countFOword1/2));
        for (int r = 0; r < IndexRepeat; r++) {
            [self.FOword2 addObject:self.FOword1[i]];      //(字)
            [self.FOword2 addObject:self.FOword1[i + 1]];  //(.mp3)
            NSLog(@"<FOword2>FOword1[%d]=%@  ,  FOword1[%d+1]=%@",i,self.FOword1[i],i,self.FOword1[i+1]);
            NSLog(@"<FOword2>FOword2[%d]=%@  ,  FOword2[%d+1]=%@",indexFOword2,self.FOword2[indexFOword2],indexFOword2,self.FOword2[indexFOword2+1]);
            indexFOword2 +=2 ;
        }
        //==<字迴圈.(字＋ mp3)>========
//==(先關掉，等做音檔的迴圈做好，再打開)========
        //==<.mp3迴圈.(mp3+ 字)>========
//        for (int r = 0; r < IndexRepeat; r++) {
//            [self.FOword2 addObject:self.FOword1[i + 1]];  //(.mp3)
//            [self.FOword2 addObject:self.FOword1[i]];      //(字)
//            NSLog(@"<FOword2>FOword1[%d]=%@  ,  FOword1[%d+1]=%@",i,self.FOword1[i],i,self.FOword1[i+1]);
//            NSLog(@"<FOword2>FOword2[%d]=%@  ,  FOword2[%d+1]=%@",indexFOword2,self.FOword2[indexFOword2],indexFOword2,self.FOword2[indexFOword2+1]);
//            indexFOword2 +=2 ;
//        }
        //==<.mp3迴圈.(mp3+ 字)>========
//==(先關掉，等做音檔的迴圈做好，再打開)========
    
    }//for (int i = 0; i < countFOword1; i+=2 ){}結束
    

//====(亂數洗牌.洗題目)================================
    NSLog(@"<FOword2.亂.洗牌>IndexLessonRandom.單課複習(%d),IndexLsRandom.多課複習(%d),IndexAZRandom.發音a-z(%d),IndexComPHRandom.發音.複合(%d)",IndexLessonRandom,IndexLsRandom,IndexAZRandom,IndexComPHRandom);
    if( IndexLessonRandom == 1 || IndexLsRandom == 1 || IndexAZRandom == 1 || IndexComPHRandom == 1 ){
        NSLog(@"<FOword2.亂.洗牌>判斷成立，執行(FOword2)洗牌亂數");
        
        //(洗牌亂數)================================
        //2.產生一個可改動的陣列，用來洗牌 //變動後的變數 mutableWords (變動:mutable)
        //    NSMutableArray *mutableWords = [NSMutableArray array];        //初始化mutableWords為可變陣列
        //    for (int i = 0; i < 4; i++) {
        //        [mutableWords addObject:self.FOword2[i]];   //使用addObject將每個元素加入mutableWords
        //    }    //下面這行替代這4行，好處不會有空值報錯
        
        NSMutableArray *RandomWords = [NSMutableArray arrayWithArray:self.FOword2];  //初始化為可變陣列
        NSLog(@"<FOword2.亂.洗牌>RandomWords[i]=FOword2[i]:[0:%@],[1:%@],[2:%@],[3:%@] ",RandomWords[0],RandomWords[1],RandomWords[2],RandomWords[3]);
        
        //3.洗牌演算法//選出要教換的座位j//透過 arc4random_uniform(n) 可以取得(0到 n-1)的亂數
        for (NSUInteger i=(RandomWords.count)/2; i > 0; i--) {
            NSLog(@"<FOword2.亂.洗牌>(全部%lu),(全部/2=i=%lu)",i*2,i);
            
            NSUInteger j = arc4random_uniform((u_int32_t)i);                                         //取得(0~4)之間的亂數(不含4)
            NSLog(@"<FOword2.亂.洗牌>(產生亂數=j=%lu),(全部/2=i=%lu)",j,i);
            
            //        NSLog(@"4按鍵前：mutableWords[%lu]=%@,交換的j.mutableWords[%lu]=%@ ",(unsigned long)(i-1),mutableWords[(i-1)],(unsigned long)j,mutableWords[j]);
            
            [RandomWords exchangeObjectAtIndex: (i-1)*2 withObjectAtIndex: j*2 ];                         //交換位置//2個座位交換
            [RandomWords exchangeObjectAtIndex: (i-1)*2+1 withObjectAtIndex: j*2+1 ];                         //交換位置//2個座位交換
            NSLog(@"<FOword2.亂.洗牌>字.洗牌前RandomWords(%lu)=%@,原RandomWords(%lu)=%@",(i-1)*2,RandomWords[(i-1)*2],j*2,RandomWords[j*2]);
            NSLog(@"<FOword2.亂.洗牌>音.洗牌前RandomWords(%lu)=%@,原RandomWords(%lu)=%@",(i-1)*2+1,RandomWords[(i-1)*2+1],(j*2+1),RandomWords[j*2+1]);
            
            //        NSLog(@"4按鍵後：mutableWords[%lu]=%@,交換的j.mutableWords[%lu]=%@ ",(unsigned long)(i-1),mutableWords[(i-1)],(unsigned long)j,mutableWords[j]);
        }  //for (NSUInteger i=(RandomWords.count)/2; i > 0; i--)
        //==印出來看看========
        for(int i=0; i< self.FOword2.count ;i++){
            NSLog(@"<FOword2.亂.洗牌>.未FOword2(%d)=%@，已換FOword2(%d)=%@",i,self.FOword2[i],i,RandomWords[i]);
            self.FOword2[i] = RandomWords[i];
        }
        //==印出來看看========
        
    }//if(有亂數)//
//====(亂數洗牌.洗題目)================================
    
}//- (void)setupFOword2{}結束
//====(編輯FOword2成為2*(6+6)遍.資料流)==============

//====(創建四按鍵資料(AZ).各挑1個)====================
//導出AnswerButton[0~3]
- (void)randomPickFromAnswerAZ:(NSInteger)QAZIndex {
    [self.answerButton removeAllObjects]; //先清空，避免舊資料殘留//全域使用
    NSString *randomAAZ[4] ={};              //區域內計算用，用來儲存運算結果的陣列
    //(防呆)空值跳出========
    if (!self.FOword2[QAZIndex*2] ) {
        NSLog(@"<AZ>第%ld題,空值跳出,FOword2[%ld]= %@ ",QAZIndex,QAZIndex*2,self.FOword2[QAZIndex*2]);//題目.全域可顯示.
        return;
    }
    
    NSLog(@"<AZ>FOword2Ans= %@ ",FOword2Ans);//題目.全域可顯示.
    //(防呆)空值跳出========
    //(防呆)未找到配對值,跳出========
    int rowIndex = 0;
    BOOL found = NO;
    //    FOword2Ans
    NSLog(@"<AZ>第%ld題,FOword2Ans=%@=%@=FOword2[%ld]",QAZIndex,FOword2Ans,self.FOword2[QAZIndex*2],QAZIndex);
    for (int i = 0; i < 26; i++) {
        if ([self.FOword2[QAZIndex*2] isEqualToString:PHAZA4[i][0]]) {
            NSLog(@"<AZ>(if成立)第%ld題,FOword2[%ld]=%@=%@=PHAZA4[%d][0]",QAZIndex,(QAZIndex*2),self.FOword2[QAZIndex*2],PHAZA4[i][0],i);
            rowIndex = i;
            found = YES;
            break;
        }
    }
    if (!found) {
        self.answerButton[0] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[1] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[2] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[3] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        NSLog(@"<AZ>answerButton[0](%@)[1](%@)[2](%@)[3](%@)",self.answerButton[0],self.answerButton[1],self.answerButton[2],self.answerButton[3]);
        NSLog(@"<AZ>FOword2Ans未找到配對值,跳出(～AZ),PHAZA4題目為: %@", FOword2Ans);
        return;
    }
    //(防呆)未找到配對值,跳出========
    
    
//    self.FOwordAns = nil;  //清零
//    self.FOwordAns = PHAZA4[rowIndex][0];//此項為正解(相同於:FOword2[n])=====
    
    NSLog(@"<AZ>正解PHAZA4[%d][0]=%@=%@=FOword2[%ld]= ",rowIndex,PHAZA4[rowIndex][0],self.FOword2[QAZIndex*2],QAZIndex);
    for (int AAZIndex = 0; AAZIndex < 4; AAZIndex++) {
        NSString *originalString = PHAZA4[rowIndex][AAZIndex];
        NSUInteger length = [originalString length];
        if (!originalString || originalString.length == 0) { //如果字串長度為0，保護防護======
            randomAAZ[AAZIndex] = @"";
            continue;
        }                       //如果字串長度為0，保護防護======
        
        NSUInteger randomIndex = arc4random_uniform((u_int32_t)length);//產生.0~(length-1)的隨機索引
        unichar c = [originalString characterAtIndex:randomIndex];     //取得該索引位置的字元
        NSString *randomChar = [NSString stringWithFormat:@"%C", c];   //再把它包成.NSString
        randomAAZ[AAZIndex] = randomChar;                              //放進結果陣列
        [self.answerButton addObject:randomChar];                      //放進answerButton
        NSLog(@"<AZ>各取1字:randomAAZ[%d]=answerButton=%@ , %@",AAZIndex,randomAAZ[AAZIndex],self.answerButton[AAZIndex]);
    }
    
    NSLog(@"<AZ>正解self.answerButton[0] = %@ ",self.answerButton[0]);
    NSLog(@"4按鍵.<AZ>洗牌前(answerButton[0]:%@)",self.answerButton[0]);
    NSLog(@"4按鍵.<AZ>洗牌前(answerButton[1]:%@)",self.answerButton[1]);
    NSLog(@"4按鍵.<AZ>洗牌前(answerButton[2]:%@)",self.answerButton[2]);
    NSLog(@"4按鍵.<AZ>洗牌前(answerButton[3]:%@)",self.answerButton[3]);
}
//====(創建四按鍵資料(AZ).各挑1個)====================


//====(創建四按鍵資料(ComPH)(複合.at).各挑1個)============
//導出AnswerButton[0~3]
- (void)randomPickFromAnswerComPH:(NSInteger)QComPHIndex{      //n為第n課
    [self.answerButton removeAllObjects];                       //先清空，避免舊資料殘留//全域使用
    
    //(防呆)空值跳出========
    if (!self.FOword2[QComPHIndex*2] ) {
        NSLog(@"<ComPH>第%ld題,空值跳出,FOword2[%ld]= %@ ",QComPHIndex,QComPHIndex*2,self.FOword2[QComPHIndex*2]);//題目.全域可顯示.
        return;
    }
    NSLog(@"<ComPH>第%ld題,FOword2[%ld]= %@ ",QComPHIndex,QComPHIndex*2,self.FOword2[QComPHIndex*2]);//題目.全域可顯示.
    NSLog(@"<ComPH>第%ld題,FOword2Ans= %@ ",QComPHIndex,FOword2Ans);    //題目正解.全域可顯示.
    //    NSString *randomAAZ[4]={};   //(沒用到)            //區域內計算用，用來儲存運算結果的陣列//randomComPH
    //(防呆)空值跳出========
    //(防呆)未找到配對值,跳出========
    int rowIndex = 0;
    BOOL found = NO;
    for (int i = 0; i < 15; i++) {
        if ([self.FOword2[QComPHIndex*2] isEqualToString:PH3ComA4[i][0]]) { //目前設定使用先在第1課，待按鍵完成再改
            rowIndex = i;
            found = YES;
            break;
        }
    }
    if (!found) {
        self.answerButton[0] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[1] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[2] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[3] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        NSLog(@"<AZ>answerButton[0](%@)[1](%@)[2](%@)[3](%@)",self.answerButton[0],self.answerButton[1],self.answerButton[2],self.answerButton[3]);
        NSLog(@"<ComPH>無法匹配,FOword2[%ld]題目為: %@",QComPHIndex*2,self.FOword2[QComPHIndex*2]);
        return;
    }
    //(防呆)未找到配對值,跳出========

    NSLog(@"<ComPH>第%ld題,正解PH3ComA4[%d][0]=%@=FOword2[%d]=%@ ",QComPHIndex,rowIndex,PH3ComA4[rowIndex][0],rowIndex,self.FOword2[QComPHIndex*2]);
    
    //1.將PH3ComA4[n][0]放入answerButton[0]
    [self.answerButton addObject:PH3ComA4[rowIndex][0]];
    
    //2.隨機選擇PH3ComA4[n][1]到PH3ComA4[n][3]，放入 answerButton[1]
    int randomIndex1 = arc4random_uniform(3) + 1;          //隨機選擇索引範圍 1 到 3
    [self.answerButton addObject:PH3ComA4[rowIndex][randomIndex1]];
    
    //3.隨機選擇PH3ComA4[n][4]到PH3ComA4[n][6]，放入 answerButton[2]
    int randomIndex2 = arc4random_uniform(3) + 4;           //隨機選擇索引範圍 4 到 6
    [self.answerButton addObject:PH3ComA4[rowIndex][randomIndex2]];
    
    //4.再次隨機選擇PH3ComA4[n][4]到PH3ComA4[n][6]，不與answerButton[2]重複，放入 answerButton[3]
    int randomIndex3;
    do {
        randomIndex3 = arc4random_uniform(3) + 4;            //隨機選擇索引範圍 4 到 6
    } while (randomIndex3 == randomIndex2);                  //確保與randomIndex2不重複
    [self.answerButton addObject:PH3ComA4[rowIndex][randomIndex3]];
    
    //    }
    
    //測試用。======================================
    //測試用。======================================
    
    // 印出結果
    NSLog(@"<ComPH>正解self.answerButton[0] = %@ ",self.answerButton[0]);
    NSLog(@"4按鍵.<ComPH>洗牌前(answerButton[0]:%@)",self.answerButton[0]);
    NSLog(@"4按鍵.<ComPH>洗牌前(answerButton[1]:%@)",self.answerButton[1]);
    NSLog(@"4按鍵.<ComPH>洗牌前(answerButton[2]:%@)",self.answerButton[2]);
    NSLog(@"4按鍵.<ComPH>洗牌前(answerButton[3]:%@)",self.answerButton[3]);
}
//====(創建四按鍵資料(ComPH)(複合.at).各挑1個)============

//====(創建四按鍵資料(LWord)(複合.bat.cat.hat.rat).各挑1個)============
//導出AnswerButton[0~3]
- (void)randomPickFromAnswerLWord:(NSInteger)QLWordIndex{      //n為第n課
    [self.answerButton removeAllObjects];                       //先清空，避免舊資料殘留//全域使用
    NSLog(@"<LWord>QIndex(%ld)題,剛進入randomPickFromAnswerLWord",QLWordIndex);
    //(防呆)空值跳出========
    if (!self.FOword2[QLWordIndex*2] ) {
        NSLog(@"<LWord>第%ld題,空值跳出,FOword2[%ld]= %@ ",QLWordIndex,QLWordIndex*2,self.FOword2[QLWordIndex*2]);//題目.全域可顯示.
        return;
    }
    NSLog(@"<LWord>第%ld題,FOword2[%ld]= %@ ",QLWordIndex,QLWordIndex*2,self.FOword2[QLWordIndex*2]);//題目.全域可顯示.
    NSLog(@"<LWord>第%ld題,FOword2Ans= %@ ",QLWordIndex,FOword2Ans);    //題目正解.全域可顯示.
    //    NSString *randomAAZ[4]={};   //(沒用到)            //區域內計算用，用來儲存運算結果的陣列//randomComPH
    //(防呆)空值跳出========
    //(防呆)＋(找配對值)，未找到配對值,跳出========
    int rowIndexL = 0;
    int rowIndexNum = 0;
    BOOL found = NO;

    //==(課1~13.正課使用3種其中1種)<四選用同課>============
    if( IndexLessonUnit == 1 ){
        NSLog(@"<LWord>進入(課1~13.正課)" );
        for (int i = 0; i < 13; i++) {          //原4項(j)，改８項(j*2) 。
            for (int j = 0; j < 4; j++) {
                NSLog(@"<LWord>(課1~13.正課)未FOword2[%ld]=%@=%@=PH3LWordQ[i=%d][j*2=%d]",(QLWordIndex*2),self.FOword2[QLWordIndex*2],PH3LWordQ[i][j*2],i,j*2);
                if ([self.FOword2[QLWordIndex*2] isEqualToString:PH3LWordQ[i][j*2]]) { //目前設定使用先在第1課，待按鍵完成再改
                    rowIndexL = i;
                    rowIndexNum =j*2;
                    NSLog(@"<LWord>(課1~13.正課)正確FOword2[%ld]=%@=%@=PH3LWordQ[rowIndexL=%d][rowIndexNum=%d]",(QLWordIndex*2),self.FOword2[QLWordIndex*2],PH3LWordQ[rowIndexL][rowIndexNum],rowIndexL,rowIndexNum);
                    found = YES;  // 跳出外層和內層迴圈
                    break ;
                }
            }
            NSLog(@"<LWord>(課1~13.正課)found=%d",found );
            if(found){ break ;}
        }
    
        //--多課亂數--1.題目大範圍--(在FOWord2中已經<1.多課在FOWord101編排><2.多次><3.洗牌>)
        //----------2.(4選)大範圍.全部字PH3LWordQ[13][4]---(檢查是否<多課總複習>亂數.是.選全部字)
        //====(4選項導入)answerButton[]中，再交給下一段處理！========
        //資料導入answerButton[0－>1－>2...]依序，在這裡檢查很久才處理好！
        self.answerButton[0] = PH3LWordQ[rowIndexL][rowIndexNum];//必須先放值進去，否則空值出錯誤
        for (int i=0,j=1 ; i*2<8 ;i++){
            if ( rowIndexNum == i*2 ) {
                self.answerButton[0] = PH3LWordQ[rowIndexL][i*2];
                NSLog(@"<LWord>(課1~13.正課)交換<rowIndexNum(%d)==(%d)i*2>#answerButton<0>=%@=%@=PH3LWordQ[rowIndexL%d][%d]",rowIndexNum,i*2,self.answerButton[0],PH3LWordQ[rowIndexL][i*2],rowIndexL,i*2);
            }else {
                self.answerButton[j] = PH3LWordQ[rowIndexL][i*2];
                NSLog(@"<LWord>(課1~13.正課)沒換<rowIndexNum(%d)==(%d)i*2>.answerButton[%d]=%@=%@=PH3LWordQ[rowIndexL%d][%d]",rowIndexNum,i*2,j,self.answerButton[j],PH3LWordQ[rowIndexL][i*2],rowIndexL,i*2);
                j++;
            }
        }   //for (int i=0,j=1 ; i*2<8 ;i++)
        //====(4選項導入)answerButton[]中，再交給下一段處理！========
    }  //if( IndexLessonUnit == 1 ){結束}
    //==(課1~13.正課使用3種其中1種)<四選用同課>============
    
    //==(課單字.複習用.單課.多課)<四選用全部52字>============
    NSLog(@"<LWord>IndexLessonRandom=%d,IndexLsRandom=%d",IndexLessonRandom,IndexLsRandom);
    if( IndexLessonRandom == 1 || IndexLsRandom == 1 ){
        NSLog(@"<LWord>進入(多課亂數)(課單字.單課.多課.複習用)" );
        //1.放正解answerButton[0]
        [self.answerButton addObject:self.FOword2[QLWordIndex*2]];
        
        //2.隨機選擇PH3LWordA4[-][-]，放入 answerButton[1][2][3]
        NSString *answerButtonK ;                //暫存放
        for( int i=0; i<3; i++) {
            do{
                int randomIndex1 = arc4random_uniform(13) + 0;          //隨機選擇索引範圍 0 到 12
                int randomIndex2 = arc4random_uniform(4)  + 0;          //隨機選擇索引範圍 0 到 3
                answerButtonK = PH3LWordA4[randomIndex1][randomIndex2];
                NSLog(@"<LWord>(多課亂數)尋找.課單字.亂.answerButtonK=%@",answerButtonK);
            }
            while( self.FOword2[QLWordIndex*2] == answerButtonK );
            [self.answerButton addObject:answerButtonK];
            NSLog(@"<LWord>(多課亂數)可用.課單字.亂.answerButtonK=%@",self.answerButton);
        } //for( int i=0; i<3; i++) 結束
        found = YES;
        NSLog(@"<LWord>(多課亂數)found = %d (課單字.單課.多課.複習用)",found );
    } //if( IndexLessonRandom == 1 ||  IndexLsRandom == 1 ){結束}
    //==(課單字.複習用.單課.多課)<四選用全部52字>============
    
    
    NSLog(@"<LWord>found = %d",found );
    NSLog(@"4按鍵.<LWord>後.表:answerButton[0]:(%@)",self.answerButton[0]);
    NSLog(@"4按鍵.<LWord>後.表:answerButton[1]:(%@)",self.answerButton[1]);
    NSLog(@"4按鍵.<LWord>後.表:answerButton[2]:(%@)",self.answerButton[2]);
    NSLog(@"4按鍵.<LWord>後.表:answerButton[3]:(%@)",self.answerButton[3]);
    NSLog(@"<LWord>配對已成功");
    
    
    //(防呆)＋(找配對值)，未找到配對值,跳出========
    if (!found) {
        NSLog(@"<LWord>沒找到found = %d (課單字.單課.多課.複習用)",found );
        self.answerButton[0] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[1] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[2] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        self.answerButton[3] = @"*";                       //預設初始值，避免預先執行時,空值報錯
        NSLog(@"<LWord>沒找到answerButton[0](%@)[1](%@)[2](%@)[3](%@)",self.answerButton[0],self.answerButton[1],self.answerButton[2],self.answerButton[3]);
        NSLog(@"<LWord>沒找到.無法匹配,FOword2[%ld]題目為: %@",QLWordIndex*2,self.FOword2[QLWordIndex*2]);
        return;
    }
    //(防呆)＋(找配對值)，未找到配對值,跳出========
    
    NSLog(@"<LWord>FOword2[%ld]=%@=%@=PH3LWordQ[rowIndexL=%d][rowIndexNum=%d]",(QLWordIndex*2),self.FOword2[QLWordIndex*2],PH3LWordQ[rowIndexL][rowIndexNum],rowIndexL,rowIndexNum);
    
    NSLog(@"<LWord>正解.self.answerButton[0] = %@=%@=PH3LWordQ[%d][%d]",self.answerButton[0],PH3LWordQ[rowIndexL][rowIndexNum],rowIndexL,rowIndexNum);
    NSLog(@"4按鍵.<LWord>後.表:(%@)answerButton[0]:(%@)",PH3LWordQ[rowIndexL][0],self.answerButton[0]);
    NSLog(@"4按鍵.<LWord>後.表:(%@)answerButton[1]:(%@)",PH3LWordQ[rowIndexL][1],self.answerButton[1]);
    NSLog(@"4按鍵.<LWord>後.表:(%@)answerButton[2]:(%@)",PH3LWordQ[rowIndexL][2],self.answerButton[2]);
    NSLog(@"4按鍵.<LWord>後.表:(%@)answerButton[3]:(%@)",PH3LWordQ[rowIndexL][3],self.answerButton[3]);
    
}//- (void)randomPickFromAnswerLWord:(NSInteger)QLWordIndex{
//====(創建四按鍵資料(LWord)(複合.bat.cat.hat.rat).各挑1個)============


//====(編輯四按鍵資料.隨機洗牌)==============
//導入AnswerButton[0~3]
//導出.mutableWords[0~3].導入到按鈕.bt411~bt414
- (void)setupAnswerButton{
    //(防呆)空值跳出========
    //1.避免４個資料少１個
    if (self.answerButton.count < 4) {
        NSLog(@"<洗牌.按鈕>Error: answerButton (setupAnswerButton)資料不足");
        return;
    }
    //(防呆)空值跳出========
    //2.產生一個可改動的陣列，用來洗牌 //變動後的變數 mutableWords (變動:mutable)
    //    NSMutableArray *mutableWords = [NSMutableArray array];//初始化mutableWords為可變陣列
    NSMutableArray *mutableWords = [NSMutableArray arrayWithArray:self.answerButton];//初始化為可變陣列
    
    //    for (int i = 0; i < 4; i++) {
    //        [mutableWords addObject:self.answerButton[i]];   //使用addObject將每個元素加入mutableWords
    //    }
    NSLog(@"<洗牌.按鈕>mutableWords[i]=self.answerButton[i]:[0:%@],[1:%@],[2:%@],[3:%@] ",mutableWords[0],mutableWords[1],mutableWords[2],mutableWords[3]);
    
    //3.洗牌演算法//選出要教換的座位j//透過 arc4random_uniform(n) 可以取得(0到 n-1)的亂數
    for (NSUInteger i=(mutableWords.count); i > 0; i--) {
        NSUInteger j = arc4random_uniform((u_int32_t)i);        //取得(0~4)之間的亂數(不含4)
        //        NSLog(@"4按鍵前：mutableWords[%lu]=%@,交換的j.mutableWords[%lu]=%@ ",(unsigned long)(i-1),mutableWords[(i-1)],(unsigned long)j,mutableWords[j]);
        [mutableWords exchangeObjectAtIndex: (i-1) withObjectAtIndex: j ];//交換位置//2個座位交換
        //        NSLog(@"4按鍵後：mutableWords[%lu]=%@,交換的j.mutableWords[%lu]=%@ ",(unsigned long)(i-1),mutableWords[(i-1)],(unsigned long)j,mutableWords[j]);
    }
    
    //====(洗牌後.導入四按鍵)=====
    NSLog(@"4按鍵.<洗牌>後.正解self.answerButton[0] = %@",self.answerButton[0]);
    NSLog(@"4按鍵.<洗牌>後.按鍵411(%@)後:前(%@)answerButton[0]",mutableWords[(0)],self.answerButton[0]);
    NSLog(@"4按鍵.<洗牌>後.按鍵412(%@)後:前(%@)answerButton[1]",mutableWords[(1)],self.answerButton[1]);
    NSLog(@"4按鍵.<洗牌>後.按鍵413(%@)後:前(%@)answerButton[2]",mutableWords[(2)],self.answerButton[2]);
    NSLog(@"4按鍵.<洗牌>後.按鍵414(%@)後:前(%@)answerButton[3]",mutableWords[(3)],self.answerButton[3]);
    [self.bt411 setTitle:mutableWords[0] forState:UIControlStateNormal];
    [self.bt412 setTitle:mutableWords[1] forState:UIControlStateNormal];
    [self.bt413 setTitle:mutableWords[2] forState:UIControlStateNormal];
    [self.bt414 setTitle:mutableWords[3] forState:UIControlStateNormal];
    //====(洗牌後.導入四按鍵)=====
}//- (void)setupAnswerButton{結束}
//====(編輯四按鍵資料.隨機洗牌)==============

//====(播音檔函式)=====
- (void)playAudioWithFileName:(NSString *)fileName {
    NSString *filePath = [[NSBundle mainBundle] pathForResource:fileName ofType:nil];
    NSLog(@"<播音檔>驗證是否有跑%@",filePath);   //查後路徑在 uten.app/裡 a.mp3
    if (!filePath) {
        NSLog(@"<播音檔>音檔 %@ 不存在", fileName);
        return;
    }
    NSURL *fileURL = [NSURL fileURLWithPath:filePath];
    NSError *error;
    
    self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:fileURL error:&error];
    if (error) {
        NSLog(@"<播音檔>播放音檔失敗: %@", error.localizedDescription);
        return;
    }
    [self.audioPlayer prepareToPlay];
    [self.audioPlayer play];    // 播放
    NSLog(@"<播音檔>正在播放第 %ld 次音檔: %@",(long)audioPlayerIndex, fileName);
    audioPlayerIndex++;
    NSLog(@"<播音檔>播完後.加1後.第 %ld 次",(long)audioPlayerIndex);
}
//====(播音檔函式)=====

//====(倒數計時器)=====
- (void)countDownAction {

    //==如果題目已經完成，立即停止計時器並返回====
    if (self.currentQuestionIndex >= self.totalQuestions) {
        //==題目.已做完========
        [self.questionTimer invalidate];
        self.questionTimer = nil;
        //==題目.已做完========
        NSLog(@"<倒數>全題目結束。停止計時，不進入下一題(currentQuestionIndex=%ld)",self.currentQuestionIndex);
        return;
    }
    //==如果題目已經完成，立即停止計時器並返回====
//    //(防呆)空值跳出========
//    if (self.answerButton.count < 4) {
//        NSLog(@"<倒數>answerButton 資料不足，不倒數");
//        return;
//    }
//    //(防呆)空值跳出========
    
    NSLog(@"<倒數>進入前.第 %ld 次(countDownAction)countDownAction",countDownActionIndex);
    self.countDownValue -= 0.2f;
//    self.countDownValue -= 0.1f;
        NSLog(@"<倒數>倒數中.剩餘 %lf秒", self.countDownValue);
    if (self.countDownValue <= 0 || self.answerButton.count < 4) {
        //==倒數.時間到========
        [self.questionTimer invalidate];
        self.questionTimer = nil;          //時間到計數器歸零
        //==倒數.時間到========
//        if (self.currentQuestionIndex >= self.totalQuestions) {
//            NSLog(@"所有題目結束。停止計時器，無需進入下一題。");
//            return;                 //停止後續邏輯
//        }
        
        //====(未答.顯示正確按鈕圖片)=====
        // 顯示正確答案圖片
        UIButton *correctButton = [self getCorrectButton];
        if (correctButton) {
            NSLog(@"<倒數>超時.正解.圖片selector41032.png");//檢查
            UIImage *correctImage = [UIImage imageNamed:@"selector41032.png"];
            [correctButton setBackgroundImage:correctImage forState:UIControlStateNormal];
        }

        // 延遲 0.5 秒重置按鈕圖片並顯示下一题
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            NSLog(@"<倒數>超時.正解.圖片.播0.5秒");//檢查
            [self resetButtonBackgrounds];
        });
        //====(未答.顯示正確按鈕圖片)=====
        
        
//        countDownActionIndex++;   //可不用使用上也正確？？？//
//        NSLog(@"加1後(countDownActionIndex)第 %ld 次",(long)countDownActionIndex);
        
        // 顯示下一題------------------
        NSLog(@"<倒數>逾時未作答 -> 當作錯誤?");
        self.currentQuestionIndex++;
        NSLog(@"<倒數>currentQuestionIndex++.後.第 %ld 題",self.currentQuestionIndex);
        [self showQuestionAtIndex:self.currentQuestionIndex];
    }
}
//====(倒數計時器)=====
//====(獲取.正確按鈕)=====
// 获取正确的按钮
- (UIButton *)getCorrectButton {
    NSString *correctAnswer = self.answerButton[0]; // 正確答案
    if ([self.bt411.currentTitle isEqualToString:correctAnswer]) {
        return self.bt411;
    } else if ([self.bt412.currentTitle isEqualToString:correctAnswer]) {
        return self.bt412;
    } else if ([self.bt413.currentTitle isEqualToString:correctAnswer]) {
        return self.bt413;
    } else if ([self.bt414.currentTitle isEqualToString:correctAnswer]) {
        return self.bt414;
    }
    return nil;
}
//====(獲取.正確按鈕)=====
//====(洗按钮圖)=====
// 重置按钮背景图片为默认值
- (void)resetButtonBackgrounds {
    UIImage *defaultImage = [UIImage imageNamed:@"selector41013.png"];
    [self.bt411 setBackgroundImage:defaultImage forState:UIControlStateNormal];
    [self.bt412 setBackgroundImage:defaultImage forState:UIControlStateNormal];
    [self.bt413 setBackgroundImage:defaultImage forState:UIControlStateNormal];
    [self.bt414 setBackgroundImage:defaultImage forState:UIControlStateNormal];
}
//====(洗按钮圖)=====


//====(秀1題.showQuestionAtIndex)=========================
- (void)showQuestionAtIndex:(NSInteger)QIndex {          //秀Index所指的題
    DDLogDebugTag(@"4pick1", @"showQuestionAtIndex: %ld", (long)QIndex);
    NSLog(@"執行前.<秀1題>第(%ld)題QIndex=%ld,題目總數totalQuestions=%ld",self.currentQuestionIndex,QIndex,self.totalQuestions);
    if (QIndex >= self.totalQuestions) {
        NSLog(@"<秀1題>所有題目結束QIndex= %ld,totalQuestions=%ld",QIndex,self.totalQuestions);
 
        //==直接返回.大廳========
        [AppRoutes popToRoot];
        //==直接返回.大廳========
        return;                                          //題目全部出完，結束或切換畫面
    }
    
    
    //==顯示抬頭--------
            self.btlbBookUnit.text = @"發音三.第1課";                     //抬頭.發音三.第1課//暫封
    //==顯示題目--------
    NSInteger i = QIndex * 2;
//    NSInteger i = QIndex;
    
    if (i >= self.FOword2.count) {
        NSLog(@"<秀1題>Error: FOword2 資料索引越界，索引 i = %ld, FOword2.count = %lu", i, (unsigned long)self.FOword2.count);
        return;
    }
    
    NSString *question = self.FOword2[i];                            //秀題目
    NSString *playMp3 = self.FOword2[i + 1];                         //playMp3播音檔
    NSLog(@"<秀1題>第QIndex=(%ld)題.FOword2[%ld]題目=%@",QIndex,i,self.FOword2[i]);
    self.btlb41Q.text    = question;                                 //秀題目question=FOword2[i]
    self.btimg41Q.image  = [UIImage imageNamed:@"selector41008"];    //秀圖片
    NSLog(@"<秀1題>第QIndex=(%ld)題.FOword2[%ld]音檔playMp3=%@",QIndex,i+1,self.FOword2[i+1]);//
    //如果播放音檔
    if ([playMp3 rangeOfString:@".mp3"].location != NSNotFound) {
        NSLog(@"<秀1題>判斷字串後.(播音檔)秀1題 playMp3 = %@",playMp3);
        [self playAudioWithFileName:playMp3];                        //如果是音檔存在.播放音檔
    }
    
    
//==暫封測試<測1-13課.亂>2025 0106====
    //產生四個按鈕資料 (以 AZ 為例)
    FOword2Ans = question;   //正解.FOword2Ans=question=FOword2[i]
    
    //    - (void)showQuestionAtIndex      :(NSInteger)QIndex {
    //    - (void)randomPickFromAnswerComPH:(int)n             {      //n為第n課
//    [self showQuestionAtIndex:self.currentQuestionIndex];
    //randomPickFromAnswerComPH
    
    //IndexRepeat = 1;                            //練習次數.3.6.9次
    //(36 = IndexRepeat*6)
    if( IndexLessonUnit == 1 /*單課依序.課單字(3)*/){
            //除36餘數, 依範圍分配給<AZ>,<ComPH.複合音at>,<LWord.課單字>
        //====暫封存(無音檔版本)========
            if(                                                 QIndex % (IndexRepeat*3) < (IndexRepeat*1)){
                NSLog(@"<秀1題>QIndex(%ld)題,指派到<AZ>",QIndex);
                [self randomPickFromAnswerAZ:QIndex];//執行.<AZ.a~z>創建.4按鍵答案
            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*1) && QIndex % (IndexRepeat*6) < (IndexRepeat*2) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<AZ>",QIndex);
//                [self randomPickFromAnswerAZ:QIndex];//執行.<AZ.a~z>創建.4按鍵答案 //欠音檔程式.暫測.待關
//            }
            if( QIndex % (IndexRepeat*3) >= (IndexRepeat*1) && QIndex % (IndexRepeat*3) < (IndexRepeat*2) ){
                NSLog(@"<秀1題>QIndex(%ld)題,指派到<ComPH>",QIndex);
                [self randomPickFromAnswerComPH:QIndex];//執行.<ComPH.複合音at>創建.4按鍵答案
            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*3) && QIndex % (IndexRepeat*6) < (IndexRepeat*4) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<ComPH>",QIndex);
//                [self randomPickFromAnswerComPH:QIndex];//執行.<ComPH.複合音at>創建.4按鍵答案//欠音檔程式.暫測.待關
//            }
            if( QIndex % (IndexRepeat*3) >= (IndexRepeat*2) && QIndex % (IndexRepeat*3) < (IndexRepeat*3) ){
                NSLog(@"<秀1題>QIndex(%ld)題,指派到<LWord>",QIndex);
                [self randomPickFromAnswerLWord:QIndex];//執行.<LWord.課單字>創建.4按鍵答案
            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*5) && QIndex % (IndexRepeat*6) < (IndexRepeat*6) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<LWord>",QIndex);
//                [self randomPickFromAnswerLWord:QIndex];//執行.<LWord.課單字>創建.4按鍵答案//欠音檔程式.暫測.待關
//            }
        //====暫封存(無音檔版本)========
//        //====暫封存(有音檔版本)========
//            if( QIndex % (IndexRepeat*6) < (IndexRepeat*1)){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<AZ>",QIndex);
//                [self randomPickFromAnswerAZ:QIndex];//執行.<AZ.a~z>創建.4按鍵答案
//            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*1) && QIndex % (IndexRepeat*6) < (IndexRepeat*2) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<AZ>",QIndex);
//                [self randomPickFromAnswerAZ:QIndex];//執行.<AZ.a~z>創建.4按鍵答案 //欠音檔程式.暫測.待關
//            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*2) && QIndex % (IndexRepeat*6) < (IndexRepeat*3) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<ComPH>",QIndex);
//                [self randomPickFromAnswerComPH:QIndex];//執行.<ComPH.複合音at>創建.4按鍵答案
//            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*3) && QIndex % (IndexRepeat*6) < (IndexRepeat*4) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<ComPH>",QIndex);
//                [self randomPickFromAnswerComPH:QIndex];//執行.<ComPH.複合音at>創建.4按鍵答案//欠音檔程式.暫測.待關
//            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*4) && QIndex % (IndexRepeat*6) < (IndexRepeat*5) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<LWord>",QIndex);
//                [self randomPickFromAnswerLWord:QIndex];//執行.<LWord.課單字>創建.4按鍵答案
//            }
//            if( QIndex % (IndexRepeat*6) >= (IndexRepeat*5) && QIndex % (IndexRepeat*6) < (IndexRepeat*6) ){
//                NSLog(@"<秀1題>QIndex(%ld)題,指派到<LWord>",QIndex);
//                [self randomPickFromAnswerLWord:QIndex];//執行.<LWord.課單字>創建.4按鍵答案//欠音檔程式.暫測.待關
//            }
//        //====暫封存(有音檔版本)========
        
        
    } //發音3.4.5.6，單課.正課學習.含<AZ>,<ComPH.複合音at>,<LWord.課單字>

    if( IndexLessonRandom == 1 ||  IndexLsRandom == 1/*單課亂數.課單字.只有單字(4)(5)*/){
        [self randomPickFromAnswerLWord:QIndex];//執行.<LWord.課單字>創建.4按鍵答案
        NSLog(@"<秀1題>QIndex(%ld)題,指派到<LWord>單課.多課亂數.課單字&",QIndex);
    }//發音3.4.5.6(單課&多課)課單字.亂數複習.6遍 & 1遍
    
    if( IndexComPHRandom == 1 /*PH3複合音.亂數複習(1)*/){
        [self randomPickFromAnswerComPH:QIndex];//執行.<ComPH.複合音at>創建.4按鍵答案
    }
    if( IndexAZRandom == 1 /*AZ.a~z自然發音.PH全.亂(0)*/){
        [self randomPickFromAnswerAZ:QIndex];//執行.<AZ.a~z>創建.4按鍵答案
    }

    [self setupAnswerButton];     //4按鍵答案.隨機洗牌.含導入4按鈕
    
    //啟動倒數
    self.countDownValue = 2.0f;                                         //設定從2秒開始倒數
    NSLog(@"<秀1題>進入前.(showQuestionAtIndex)countDownAction");
    self.questionTimer = [NSTimer scheduledTimerWithTimeInterval:0.2f
                                                          target:self
                                                        selector:@selector(countDownAction)
                                                        userInfo:nil
                                                         repeats:YES];
}//- (void)showQuestionAtIndex:(NSInteger)QIndex {} 秀1題.結束
//====(秀1題.showQuestionAtIndex)=========================

// 讀取 CSV 檔案並轉換為二維陣列數據結構
- (NSMutableArray *)loadCSV:(NSString *)csvFilePath error:(NSError **)outError {
    if (!csvFilePath || csvFilePath.length == 0) {
        if (outError) {
            *outError = [NSError errorWithDomain:@"UtenErrorDomain" 
                                            code:100 
                                        userInfo:@{NSLocalizedDescriptionKey: @"CSV 路徑為空"}];
        }
        DDLogErrorTag(@"4pick1", @"CSV 路徑為空");
        return nil;
    }
    
    NSError *error;
    NSString *csvString = [NSString stringWithContentsOfFile:csvFilePath encoding:NSUTF8StringEncoding error:&error];
    
    if (error) {
        if (outError) {
            *outError = [NSError errorWithDomain:@"UtenErrorDomain" 
                                            code:101 
                                        userInfo:@{
                                            NSLocalizedDescriptionKey: @"讀取 CSV 檔案失敗",
                                            NSUnderlyingErrorKey: error,
                                            @"filePath": csvFilePath
                                        }];
        }
        DDLogErrorTag(@"4pick1", @"讀取 CSV 檔案失敗: %@", error.localizedDescription);
        return nil;
    }
    
    if (csvString.length == 0) {
        if (outError) {
            *outError = [NSError errorWithDomain:@"UtenErrorDomain" 
                                            code:102 
                                        userInfo:@{
                                            NSLocalizedDescriptionKey: @"CSV 檔案內容為空",
                                            @"filePath": csvFilePath
                                        }];
        }
        DDLogErrorTag(@"4pick1", @"CSV 檔案內容為空");
        return nil;
    }
    
    // 分割成行
    NSArray *rows = [csvString componentsSeparatedByString:@"\n"];
    NSMutableArray *resultArray = [NSMutableArray array];
    
    if (rows.count == 0) {
        if (outError) {
            *outError = [NSError errorWithDomain:@"UtenErrorDomain" 
                                            code:103 
                                        userInfo:@{
                                            NSLocalizedDescriptionKey: @"CSV 檔案沒有有效行",
                                            @"filePath": csvFilePath
                                        }];
        }
        DDLogErrorTag(@"4pick1", @"CSV 檔案沒有有效行");
        return nil;
    }
    
    for (NSString *row in rows) {
        if (row.length == 0) continue; // 跳過空行
        
        // 分割每一行為欄位
        NSArray *columns = [row componentsSeparatedByString:@","];
        
        if (columns.count == 0) {
            DDLogWarnTag(@"4pick1", @"警告：發現空行或無效行");
            continue;
        }
        
        [resultArray addObject:columns];
    }
    
    if (resultArray.count == 0) {
        if (outError) {
            *outError = [NSError errorWithDomain:@"UtenErrorDomain" 
                                            code:104 
                                        userInfo:@{
                                            NSLocalizedDescriptionKey: @"解析 CSV 後沒有有效資料",
                                            @"filePath": csvFilePath
                                        }];
        }
        DDLogErrorTag(@"4pick1", @"解析 CSV 後沒有有效資料");
        return nil;
    }
    
    DDLogDebugTag(@"4pick1", @"成功加載 CSV 檔案，共 %lu 行有效資料", (unsigned long)resultArray.count);
    return resultArray;
}

- (void)loadCourse {
    NSError *csvError;
    // 讀取 PH3AllWord.csv
    NSString *path = [[NSBundle mainBundle] pathForResource:@"assets/PH3AllWord" ofType:@"csv"];
    DDLogDebugTag(@"4pick1", @"path=%@", path);
    NSMutableArray *list = [self loadCSV:path error:&csvError];
    if (csvError) {
        DDLogErrorTag(@"4pick1", @"Error: %@", csvError.localizedDescription);
    } else {
        PH3AllWord = list;
    }
    // 讀取 PH3LWordQ.csv
    path = [[NSBundle mainBundle] pathForResource:@"assets/PH3LWordQ" ofType:@"csv"];
    DDLogDebugTag(@"4pick1", @"path=%@", path);
    list = [self loadCSV:path error:&csvError];
    if (csvError) {
        DDLogErrorTag(@"4pick1", @"Error: %@", csvError.localizedDescription);
    } else {
        PH3LWordQ = list;
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    DDLogDebugTag(@"4pick1", @"viewDidLoad");
    [self loadCourse];
    //==接收selector<410.按鈕>資料========
    // 在另一個頁面取出整數
    NSInteger myInteger = [[NSUserDefaults standardUserDefaults] integerForKey:@"myInteger"];
    int ActionNum = (int)myInteger;
    printf("<viewDidLoad>(接收)<selector410.跳轉頁面.按鈕>資料>按下ActionNum(%d)按鈕",ActionNum);
    //==接收selector<410.按鈕>資料========ｖ
    //======(預設.測試.按鈕設定)======
//    IndexBook = @"------   [13][8]";            //冊
    IndexWordStart = 0;                         //課/字.從這課開始
    IndexWordEnd = 0;                           //課/字.到那課結束
    IndexAZUnit = 0;                            //單課依序(bool).AZa~z自然發音.
    IndexComPHUnit = 0;                         //單課依序(bool).ComPH複合音.
    IndexLessonUnit = 0;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
    IndexLsUnit = 0;                            //多課依序(bool)多課預習習.         2(唸一遍.PH3~6)
    IndexAZRandom = 0;                          //亂數(bool)AZ.a~z自然發音.亂數練習 0(a~z.PH全)
    IndexComPHRandom = 0;                       //亂數(bool)ComPH.複合音.亂數練習   1(複合音.PH3~6)
    IndexLessonRandom = 0;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
    IndexLsRandom = 0;                          //亂數(bool)多課.課單字.總複習.     5(多課.課單字.PH3~6)
    IndexRepeat = 1;                            //練習次數.3.6.9次
    IndexWrongRepeat = 0;                       //錯罰次數.0.1.2.5.10
    //    分配類型(AZ.亂/順)(ComPH亂/順)(LWord亂/順)
    //======(預設.測試.按鈕設定)======
    //==轉換selector<410.按鈕>資料========
    NSLog(@"<viewDidLoad>(初始化)IndexBook=%@,IndexWordStart=%d,IndexWordEnd=%d",IndexBook,IndexWordStart,IndexWordEnd);
    NSLog(@"<viewDidLoad>(初始化)IndexAZUnit=%d,IndexComPHUnit=%d,IndexLessonUnit=%d,IndexLsUnit=%d",IndexAZUnit,IndexComPHUnit,IndexLessonUnit,IndexLsUnit);
    NSLog(@"<viewDidLoad>(初始化)IndexAZRandom=%d,IndexComPHRandom=%d,IndexLessonRandom=%d,IndexLsRandom=%d",IndexAZRandom,IndexComPHRandom,IndexLessonRandom,IndexLsRandom);
    NSLog(@"<viewDidLoad>(初始化)IndexRepeat=%d,IndexWrongRepeat=%d",IndexRepeat,IndexWrongRepeat);
    DDLogDebugTag(@"4pick1", @"ActionNum=%d", ActionNum);

    switch(ActionNum){
        case 1: {
            //(發音字母a~z)複習.AZ亂數.做1遍.錯罰2
            IndexBook = @"PHAZQ[26][2]";                //冊
            IndexAZRandom = 1;                          //亂數(bool)AZ.a~z自然發音.亂數練習 0(a~z.PH全)
            IndexRepeat = 2;                            //練習次數.3.6.9次
            IndexWrongRepeat = 2;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 2:{
            //(PH3複合音)複習.ComPH亂數.做1遍.錯罰2
            IndexBook = @"PH3ComQ[15][2]";              //冊
            IndexComPHRandom = 1;                       //亂數(bool)ComPH.複合音.亂數練習   1(複合音.PH3~6)
            IndexRepeat = 2;                            //練習次數.3.6.9次
            IndexWrongRepeat = 2;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 3:{
            //(PH3課本唸一遍).1~13.   //<LWord>bat課單字.1~13.
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 52;                           //課/字.到那課結束
            IndexLessonUnit = 1;            //            //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 1;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 4:{
            //(PH3.課1).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 4;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 5:{
            //(PH3.課2).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 4;                         //課/字.從這課開始
            IndexWordEnd = 8;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 6:{
            //(PH3.課3).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 9;                         //課/字.從這課開始
            IndexWordEnd = 12;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 7:{
            //(PH3.課4).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 12;                         //課/字.從這課開始
            IndexWordEnd = 16;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 8:{
            //(PH3.課5).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 16;                         //課/字.從這課開始
            IndexWordEnd = 20;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 9:{
            //(PH3.課6).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 20;                         //課/字.從這課開始
            IndexWordEnd = 24;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 10:{
            //(PH3.課7).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 24;                         //課/字.從這課開始
            IndexWordEnd = 28;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 11:{
            //(PH3.課8).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 28;                         //課/字.從這課開始
            IndexWordEnd = 32;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 12:{
            //(PH3.課9).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 32;                         //課/字.從這課開始
            IndexWordEnd = 36;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 13:{
            //(PH3.課10).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 36;                         //課/字.從這課開始
            IndexWordEnd = 40;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 14:{
            //(PH3.課11).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 40;                         //課/字.從這課開始
            IndexWordEnd = 44;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 15:{
            //(PH3.課12).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 44;                         //課/字.從這課開始
            IndexWordEnd = 48;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 16:{
            //(PH3.課13).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"PH3AllWord[52][6]";            //冊
            IndexWordStart = 48;                         //課/字.從這課開始
            IndexWordEnd = 52;                           //課/字.到那課結束
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
            
         //====複習.課單字================
            
        case 17:{
            //(PH3.複1).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 1;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 18:{
            //(PH3.複2).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 1;                         //課/字.從這課開始
            IndexWordEnd = 2;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 19:{
            //(PH3.複3).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 2;                         //課/字.從這課開始
            IndexWordEnd = 3;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 20:{
            //(PH3.複4).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 3;                         //課/字.從這課開始
            IndexWordEnd = 4;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 21:{
            //(PH3.複5).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 4;                         //課/字.從這課開始
            IndexWordEnd = 5;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 22:{
            //(PH3.複6).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 5;                         //課/字.從這課開始
            IndexWordEnd = 6;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 23:{
            //(PH3.複7).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 6;                         //課/字.從這課開始
            IndexWordEnd = 7;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 24:{
            //(PH3.複8).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 7;                         //課/字.從這課開始
            IndexWordEnd = 8;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 25:{
            //(PH3.複9).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 8;                         //課/字.從這課開始
            IndexWordEnd = 9;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 26:{
            //(PH3.複10).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 9;                         //課/字.從這課開始
            IndexWordEnd = 10;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 27:{
            //(PH3.複11).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 10;                         //課/字.從這課開始
            IndexWordEnd = 11;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 28:{
            //(PH3.複12).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 11;                         //課/字.從這課開始
            IndexWordEnd = 12;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 29:{
            //(PH3.複13).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 12;                         //課/字.從這課開始
            IndexWordEnd = 13;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 6;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 30:{
            //(PH3.複1-4).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 4;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 2;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 31:{
            //(PH3.複5-8).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 4;                         //課/字.從這課開始
            IndexWordEnd = 7;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 2;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 32:{
            //(PH3.複9-13).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 8;                         //課/字.從這課開始
            IndexWordEnd = 12;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 2;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 33:{
            //(PH3.複1-13).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 12;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 1;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
        case 34:{
            //(PH3.複1).複習課單字.4字.   //<LWord>bat課單字
            IndexBook = @"PH3LWordQ[13][8]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 1;                           //課/字.到那課結束
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexRepeat = 1;                            //練習次數.3.6.9次
            IndexWrongRepeat = 1;                       //錯罰次數.0.1.2.5.10
            break;
        }
   
            
            
            
            
            
            
            
            
        case 98:{
            //(PH3.課1).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"------   [13][8]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 0;                           //課/字.到那課結束
            
            IndexAZRandom = 0;                          //亂數(bool)AZ.a~z自然發音.亂數練習 0(a~z.PH全)
            IndexComPHRandom = 0;                       //亂數(bool)ComPH.複合音.亂數練習   1(複合音.PH3~6)
            IndexLsUnit = 0;                            //多課依序(bool)多課預習習.         2(唸一遍.PH3~6)
            IndexLessonUnit = 1;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexLessonRandom = 1;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexLsRandom = 1;                          //亂數(bool)多課.課單字.總複習.     5(多課.課單字.PH3~6)
            
            IndexRepeat = 1;                            //練習次數.3.6.9次
            IndexWrongRepeat = 0;                       //錯罰次數.0.1.2.5.10
            break;
        }

        case 99:{
            //(PH3.課1).1~13.   //正課學習.含3部分.<AZ>b.<ComPH>at.<LWord>bat課單字
            IndexBook = @"------   [13][8]";            //冊
            IndexWordStart = 0;                         //課/字.從這課開始
            IndexWordEnd = 0;                           //課/字.到那課結束
            IndexAZUnit = 0;                            //單課依序(bool).AZa~z自然發音.
            IndexComPHUnit = 0;                         //單課依序(bool).ComPH複合音.
            IndexLessonUnit = 0;                        //單課依序(bool).單課.3種正課學習    3(課1~課13.PH3~6)
            IndexLsUnit = 0;                            //多課依序(bool)多課預習習.         2(唸一遍.PH3~6)
            IndexAZRandom = 0;                          //亂數(bool)AZ.a~z自然發音.亂數練習 0(a~z.PH全)
            IndexComPHRandom = 0;                       //亂數(bool)ComPH.複合音.亂數練習   1(複合音.PH3~6)
            IndexLessonRandom = 0;                      //亂數(bool)單課.課單字.採亂數數次   4(課1~13.課單字.PH3~6)
            IndexLsRandom = 0;                          //亂數(bool)多課.課單字.總複習.     5(多課.課單字.PH3~6)
            IndexRepeat = 1;                            //練習次數.3.6.9次
            IndexWrongRepeat = 0;                       //錯罰次數.0.1.2.5.10
            break;
        }

        default:{
            
        }
    }    //switch(ActionNum){}
    NSLog(@"<viewDidLoad>(設定後)IndexBook=%@,IndexWordStart=%d,IndexWordEnd=%d",IndexBook,IndexWordStart,IndexWordEnd);
    NSLog(@"<viewDidLoad>(設定後)IndexAZUnit=%d,IndexComPHUnit=%d,IndexLessonUnit=%d,IndexLsUnit=%d",IndexAZUnit,IndexComPHUnit,IndexLessonUnit,IndexLsUnit);
    NSLog(@"<viewDidLoad>(設定後)IndexAZRandom=%d,IndexComPHRandom=%d,IndexLessonRandom=%d,IndexLsRandom=%d",IndexAZRandom,IndexComPHRandom,IndexLessonRandom,IndexLsRandom);
    NSLog(@"<viewDidLoad>(設定後)IndexRepeat=%d,IndexWrongRepeat=%d",IndexRepeat,IndexWrongRepeat);
    //==轉換selector<410.按鈕>資料========
    
    
//==<設定.FOword1>====依selector<410.按鈕>資料，產生(FOword1)========
//說明:在viewDidLoad中，無法使用函式.-(void)setFOword1{
        //--定義.IndexBook 的值----
        //    NSString *IndexBook = @"PHAZQ[ 26 ][ 2 ]";
        NSLog(@"<setFOword1>提取數字.IndexBook=%@,IndexWordStart=%d,IndexWordEnd=%d)",IndexBook,IndexWordStart,IndexWordEnd);
        
        //==(提取數字)====提取[26]&[2]中的數字================
        //--使用正則表達式提取[26]&[2]中的數字----
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\[\\s*(\\d+)\\s*\\]" options:0 error:nil];
        NSArray<NSTextCheckingResult *> *matches = [regex matchesInString:IndexBook options:0 range:NSMakeRange(0, IndexBook.length)];
        //--初始化(m)(n)----
        int m = 0, n = 0;
        if (matches.count >= 2) {
            //--提取第一個匹配的數字作為(m)----
            NSTextCheckingResult *match1 = matches[0];
            NSString *mString = [IndexBook substringWithRange:[match1 rangeAtIndex:1]];
            m  = mString.intValue;
            //--提取第二個匹配的數字作為(n)----
            NSTextCheckingResult *match2 = matches[1];
            NSString *nString = [IndexBook substringWithRange:[match2 rangeAtIndex:1]];
            n  = nString.intValue;
            NSLog(@"<setFOword1>提取數字(m = %d)(n = %d),%@[%d][%d]",m,n,IndexBook,m,n);
        } else {
            NSLog(@"<setFOword1>IndexBook 格式錯誤，無法提取數字");
            return;
        }
        //==(提取數字)====提取[26]&[2]中的數字================
    //==(提取文字)===="PHAZQ[26][2]"中"["前的文字================
    // 找到 '[' 的位置
    NSString *IndexBookText = @"";
        NSRange bracketRange = [IndexBook rangeOfString:@"["];
        if (bracketRange.location != NSNotFound) {
            // 提取 '[' 前的子字符串
            NSString *IndexBookText = [IndexBook substringToIndex:bracketRange.location];
            NSLog(@"<setFOword1>原IndexBook=%@,提取文字=%@",IndexBook,IndexBookText);
        } else {
            NSLog(@"<setFOword1>找不到 '[' 字符，無法提取子字符串。");
        }
    //==(提取文字)===="PHAZQ[26][2]"中"["前的文字================

    //==(依<起.終>導入FOword1)====導入(選取資料)到FOword1========
    self.FOword1 = [NSMutableArray array];         //設為可變動陣列 //不可關掉
    [self.FOword1 removeAllObjects];               //先清空，避免舊資料殘留//全域使用
    int iStart=0;
    int mEnd=IndexWordEnd;
    if( (IndexWordStart==0 && IndexWordEnd==0) ){
        iStart = IndexWordStart;
        mEnd = IndexWordEnd + m ;
        NSLog(@"<setFOWord1>(無起終點)iStart=%d=%d=IndexWordStart,mEnd=%d=%d+%d=(IndexWordEnd+m),n=%d",iStart,IndexWordStart,mEnd,IndexWordEnd,m,n);
    }else {
        iStart = IndexWordStart;
        mEnd = IndexWordEnd;
        NSLog(@"<setFOWord1>(有設.起終點)iStart=%d=%d=起IndexWordStart,mEnd=%d=%d=終IndexWordEnd,n=%d",iStart,IndexWordStart,mEnd,IndexWordEnd,n);
    }
                for (int i = iStart,k=0; i < (mEnd) && i<m ;i++) {
                    // 限制行數不超過 m
                    for (int j = 0; j < (n) && j<n   ;j++) {
                        NSString *value = nil;
                        if( [IndexBook isEqualToString:@"PHAZQ[26][2]"] ){
                            value = PHAZQ[i][j];  // 直接取值
                            [self.FOword1 addObject:value];      // 加入到一維 NSMutableArray
                            NSLog(@"<setFOWord1>value=%@=%@=PHAZQ[%d][%d]",value,PHAZQ[i][j],i,j);
                            
                        }else if( [IndexBook isEqual: @"PH3ComQ[15][2]"] ){
                            value = PH3ComQ[i][j];  // 直接取值
                            [self.FOword1 addObject:value];      // 加入到一維 NSMutableArray
                            NSLog(@"<setFOWord1>value=%@=%@=PH3ComQ[%d][%d]",value,PH3ComQ[i][j],i,j);
                            
                        }else if( [IndexBook isEqual: @"PH3LWordQ[13][8]"] ){
                            value = PH3LWordQ[i][j];  // 直接取值
                            [self.FOword1 addObject:value];      // 加入到一維 NSMutableArray
                            NSLog(@"<setFOWord1>value=%@=%@=PH3LWordQ[%d][%d]",value,PH3LWordQ[i][j],i,j);
                            
                        }else if( [IndexBook isEqual: @"PH3AllWord[52][6]"] ){
                            value = PH3AllWord[i][j];  // 直接取值
                            DDLogDebugTag(@"4pick1", @"PH3AllWord[%d][%d]=%@",i,j,value);
                            [self.FOword1 addObject:value];      // 加入到一維 NSMutableArray
                            NSLog(@"<setFOWord1>value=%@=%@=PH3AllWord[%d][%d]",value,PH3AllWord[i][j],i,j);
                        }  //只做到PH3，PH4.5.6後續再做
                        
//                        [self.FOword1 addObject:value];      // 加入到一維 NSMutableArray
//                        NSLog(@"<setFOword1>導入FOword1(%d)=%@=%@=value=PHAZQ[%d][%d]",k,self.FOword1[k],PHAZQ[i][j],i,j);
                        k++;
                    }
                }//for (int i = s; i < t && i <  m ; i++)結束
    //==(依<起.終>導入FOword1)====導入(選取資料)到FOword1========
    //==<設定.FOword1>====依selector<410.按鈕>資料，產生(FOword1)========

    
    
    //==========================================================================
    //            static NSString *PHAZQ[26][2]= {
    //                {@"a",@"ph_a.mp3"},{@"b",@"ph_b.mp3"},{@"c",@"ph_c.mp3"},
    //                {@"d",@"ph_d.mp3"},{@"e",@"ph_e.mp3"},{@"f",@"ph_f.mp3"},
    //                {@"g",@"ph_g.mp3"},{@"h",@"ph_h.mp3"},{@"i",@"ph_i.mp3"},
    //                {@"j",@"ph_j.mp3"},{@"k",@"ph_k.mp3"},{@"l",@"ph_l.mp3"},
    //                {@"m",@"ph_m.mp3"},{@"n",@"ph_n.mp3"},{@"o",@"ph_o.mp3"},
    //                {@"p",@"ph_p.mp3"},{@"q",@"ph_q.mp3"},{@"r",@"ph_r.mp3"},
    //                {@"s",@"ph_s.mp3"},{@"t",@"ph_t.mp3"},{@"u",@"ph_u.mp3"},
    //                {@"v",@"ph_v.mp3"},{@"w",@"ph_w.mp3"},{@"x",@"ph_x.mp3"},
    //                {@"y",@"ph_y.mp3"},{@"z",@"ph_z.mp3"} };               //發音字母26個(含.mp3)
    
    
    //暫關測FOword1
//            self.FOword101  = @[
//                @"a", @"ph_a.mp3", @"b", @"ph_b.mp3", @"c", @"ph_c.mp3",
//                @"d", @"ph_d.mp3", @"e", @"ph_e.mp3", @"f", @"ph_f.mp3",
//                @"g", @"ph_g.mp3", @"h", @"ph_h.mp3", @"i", @"ph_i.mp3",
//                @"j", @"ph_j.mp3", @"k", @"ph_k.mp3", @"l", @"ph_l.mp3",
//                @"m", @"ph_m.mp3", @"n", @"ph_n.mp3", @"o", @"ph_o.mp3",
//                @"p", @"ph_p.mp3", @"q", @"ph_q.mp3", @"r", @"ph_r.mp3",
//                @"s", @"ph_s.mp3", @"t", @"ph_t.mp3", @"u", @"ph_u.mp3",
//                @"v", @"ph_v.mp3", @"w", @"ph_w.mp3", @"x", @"ph_x.mp3",
//                @"y", @"ph_y.mp3", @"z", @"ph_z.mp3"
//            ]; //導入(課１題目.資料組)
    
    //            NSArray *FOword101 = @[
    //                @"b", @"bat_b.mp3", @"at", @"bat_at.mp3", @"bat", @"bat_2e.mp3",
    //                @"c", @"cat_c.mp3", @"at", @"cat_at.mp3", @"cat", @"cat_2e.mp3",
    //                @"h", @"hat_h.mp3", @"at", @"hat_at.mp3", @"hat", @"hat_2e.mp3",
    //                @"r", @"rat_r.mp3", @"at", @"rat_at.mp3", @"rat", @"rat_2e.mp3"];//導入(課１題目.資料組)
    //======(執行.button.課１.)======
//    NSArray *FOword101 = @[
//        @"b", @"bat_b.mp3", @"at", @"bat_at.mp3", @"bat", @"bat_2e.mp3",
//        @"c", @"cat_c.mp3", @"at", @"cat_at.mp3", @"cat", @"cat_2e.mp3",
//        @"h", @"hat_h.mp3", @"at", @"hat_at.mp3", @"hat", @"hat_2e.mp3",
//        @"r", @"rat_r.mp3", @"at", @"rat_at.mp3", @"rat", @"rat_2e.mp3"];//導入(課１題目.資料組)
//    NSArray *FOword101 = @[
//        @"b", @"bat_b.mp3", @"b", @"bat_b.mp3", @"b", @"bat_b.mp3",
//        @"b", @"bat_b.mp3", @"b", @"bat_b.mp3", @"b", @"bat_b.mp3",
//        @"b", @"bat_b.mp3", @"b", @"bat_b.mp3", @"b", @"bat_b.mp3",
//        @"b", @"bat_b.mp3", @"b", @"bat_b.mp3", @"b", @"bat_b.mp3"];//導入(課１題目.資料組)

//    [self.FOword1 removeAllObjects]; //先清空，避免舊資料殘留//全域使用
//    self.FOword1 = [self.FOword101 mutableCopy];//將NSArray型別區域變數FOword101指派給屬性self.FOword1
    //(執行.FOword2Player函式)(未)
    //======(執行.button.課１.)======
       
    
    //======(//預設初始值，避免預先執行時,空值報錯)======
    FOword2Ans = @"a";                               //預設初始值，避免預先執行時空值報錯
    NSLog(@"FOword2Ans(viewDidLoad): %@", FOword2Ans);
    self.answerButton = [NSMutableArray arrayWithCapacity:4];
    self.FOword2 = [NSMutableArray array];             //設定FOword2可變動的

    self.answerButton[0] = @"a";                       //預設初始值，避免預先執行時,空值報錯
    self.answerButton[1] = @"a";                       //預設初始值，避免預先執行時,空值報錯
    self.answerButton[2] = @"a";                       //預設初始值，避免預先執行時,空值報錯
    self.answerButton[3] = @"a";                       //預設初始值，避免預先執行時,空值報錯
     //======(//預設初始值，避免預先執行時,空值報錯)======
    
    
    //======(設定靜音模式也可播放，設定Audio Session)======
    NSError *sessionError = nil;
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback
                                     withOptions:AVAudioSessionCategoryOptionMixWithOthers
                                           error:&sessionError];
    if (sessionError) {
        NSLog(@"設定音訊類別錯誤: %@", sessionError.localizedDescription);
    }
    [[AVAudioSession sharedInstance] setActive:YES error:nil];
    //======(設定靜音模式也可播放，設定Audio Session)======
    
    
    //======(FOword2播放器.FOword2Player)======
    [self setupFOword2];//(執行FOword2.6遍的資料流)
    //======(FOword2播放器.FOword2Player)======
    
    //==(動作執行區)===========================================================================
                self.currentQuestionIndex = 0;                              //目前做到哪裡  (現在點)
                self.totalQuestions = self.FOword2.count / 2;               //假設兩筆為一題(結束點)
                NSLog(@"進入前.<秀1題>.第%ld個",self.currentQuestionIndex);
                [self showQuestionAtIndex:self.currentQuestionIndex];       //執行
                
    //==(動作執行區)===========================================================================
    
    
    
    
    
    
    
    //======(導入.課１資料)======
//    _FOword1 = @[]; //清空資料 //將指向一個新的空陣列；無法(就地)刪除所有元素，有清除原有資料的效果
    //_FOword1，就是(直接對底層變數)做(存取)。
    
//    self.FOword1 = @[
//        @"b", @"bat_b.mp3", @"at", @"bat_at.mp3", @"bat", @"bat_2e.mp3",
//        @"c", @"cat_c.mp3", @"at", @"cat_at.mp3", @"cat", @"cat_2e.mp3",
//        @"h", @"hat_h.mp3", @"at", @"hat_at.mp3", @"hat", @"hat_2e.mp3",
//        @"r", @"rat_r.mp3", @"at", @"rat_at.mp3", @"rat", @"rat_2e.mp3"
//    ];  //導入(課１題目.資料組)//未來將導入廣播來的資料
    
    //導入(課1~課13題目.資料組)
    //導入(課１-4.題目亂數.資料組)
    //導入(課5-8.題目亂數.資料組)
    //導入(課9-13.題目亂數.資料組)
    //導入(課１-13.題目亂數.資料組)
    //======(導入.課１資料)======
    

    
    
    
    

    
    
    
    //======(欠.for迴圈.執行１題題)
//    NSUInteger countFOword2 = self.FOword2.count; //計算FOword2 資料筆數，放入countFOword2。
//    NSLog(@"FOword2目前共有countFOword2= %lu 筆資料",countFOword2);//印出結果
    
    
//    for(int i=0,j=0 ;i<countFOword2 ;i+=2,j++){  //暫封測用 2025 0103
//    int i=0;//暫封測用 2025 0103
//    int j=0;//暫封測用 2025 0103

                //======(秀出題目.按鈕４選.音檔))======
//        NSLog(@"第%d題,j=%d,i=%d,題目(FOword2[%d]=%@,FOword2[%d]=%@)",j,j,i,i,self.FOword2[i],i+1,self.FOword2[i+1]);//顯示.題目.正解
//        self.btlbBookUnit.text = @"發音三.第1課";                     //抬頭.發音三.第1課
        
        
        
        
        //迴圈判斷.mp3播(圖+音)或字====
//        if ([self.FOword2[i] rangeOfString:@".mp3"].location != NSNotFound) {
//            NSLog(@"有(.mp3)= %@", self.FOword2[i]);
        //此段播音檔的部分先不做，第二階段做
            
            
//        }else if([self.FOword2[i+1] rangeOfString:@".mp3"].location != NSNotFound){
//            NSLog(@"(沒有.mp3)= %@", self.FOword2[i]);
//            self.btimg41Q.image = [UIImage imageNamed:@"selector41008"];//導入題目圖(需要留)
//            self.btlb41Q.text = self.FOword2[i+1]; //(不需導入,測試用.可刪)      //導入題目文;文(i),圖(i+1)
//            FOword2Ans = self.FOword2[i];
//            FOword2Ans = @"k";        //測試 2025 0103下午.可刪
//            FOword2Ans = @"at";       //測試 2025 0103下午.可刪
//            FOword2Ans = @"bat";      //測試 2025 0103下午.可刪
            



            // --- 1. 建立四個按鈕的文字，並隨機洗牌 ---
//            [self randomPickFromAnswerAZ];//執行創建.4按鍵答案.各挑1個//可換不同的型態使用//測試 2025 0103下午
//            [self setupAnswerButton];     //四按鍵資料.隨機洗牌.含導入4按鈕             //測試 2025 0103下午
          
            //======(啟動倒數計時器)======
//            self.countDownValue = 3.0f;    //每次出題都要重新設定"倒數秒數"(n可自訂，比如先設定5秒)
//              //啟動倒數計時器//每(0.1)秒觸發一次countDownAction)
//            NSLog(@"進入前.(viewDidLoad)countDownAction");
//            self.questionTimer = [NSTimer scheduledTimerWithTimeInterval:1
//                                                                     target:self
//                                                                   selector:@selector(countDownAction)
//                                                                   userInfo:nil
//                                                                    repeats:YES];
            //======(啟動倒數計時器)======
            
            // --- 2. 撥放音檔 (正解 + ".mp3") ---
            //====(播音檔.檔名+.mp3)=========================
//            NSString *correctAnswer = self.answerButton[0]; // 正解的答案 (如 c, g, h)
//            NSString *audioFileName = [NSString stringWithFormat:@"%@.mp3", correctAnswer];//音檔名稱(正解加副檔名
//            NSLog(@"進入前.(播音檔)audiofilename(answerButton[0]+.mp3)第 %ld 次: %@",(long)audioPlayerIndex,audioFileName);
//            [self playAudioWithFileName:audioFileName];//調用( playAudioWithFileName )播音檔函式，播audioFileName
            //====(播音檔.檔名+.mp3)=========================
            
            // --- 3. 啟動倒數計時器 (2 秒) ---
//            //======(啟動倒數計時器)======
//            self.countDownValue = 2.0f;    //每次出題都要重新設定"倒數秒數"(n可自訂，比如先設定5秒)
//              //啟動倒數計時器//每(0.1)秒觸發一次countDownAction)
//            self.questionTimer = [NSTimer scheduledTimerWithTimeInterval:0.1
//                                                                     target:self
//                                                                   selector:@selector(countDownAction)
//                                                                   userInfo:nil
//                                                                    repeats:YES];
//            //======(啟動倒數計時器)======

//            NSString *TypeIndex ;
//        }else{
//            NSLog(@"這組題目(i,i+1)欠(.mp3)= %@", self.FOword2[i]);
//        }
//        self.btlb41Q.text = self.FOword2[i];                             //導入題目文;文(i),圖(i+1)
//        self.btimg41Q.image = [UIImage imageNamed:@"selector41008"];//導入題目圖
        //迴圈判斷(.mp3)播哪一種====
        //======(秀出(題目.按鈕４選))======

//    } //for迴圈 //暫封測用 2025 0103

  
    //======(FOword2播放器.FOword2Player)======
 
}//- (void)viewDidLoad {}

#pragma mark - 按鈕事件處理
//==跳出<(四選一程式>(回大廳)========
- (IBAction)btBack:(id)sender {
    //====停止播放音檔========
    if (self.audioPlayer.isPlaying) {
            [self.audioPlayer stop];
            self.audioPlayer = nil; // 释放音频资源
            NSLog(@"<btBack>已停止音频播放并释放资源");
        }
    //====停止播放音檔========
    //====停止計時器========
    if (self.questionTimer) {
            [self.questionTimer invalidate];
            self.questionTimer = nil; // 防止计时器继续触发
            NSLog(@"<btBack>已停止计时器");
        }
    //====停止計時器========
    //==直接返回.大廳========
//    [self dismissViewControllerAnimated:NO completion:nil]; //返回上一層。
    [AppRoutes popToRoot];
    //==直接返回.大廳========
}//(IBAction)btBack:(id)sender {結束}
//==跳出<(四選一程式>(回大廳)========
#pragma mark - 按鈕事件處理(4按鍵)
- (IBAction)bt411:(id)sender {
    [self btAnswerPressed:sender];
}
- (IBAction)bt412:(id)sender {
    [self btAnswerPressed:sender];
}
- (IBAction)bt413:(id)sender {
    [self btAnswerPressed:sender];
}
- (IBAction)bt414:(id)sender {
    [self btAnswerPressed:sender];
}

#pragma mark - 檢查按鈕輸入
//- (void)checkButton:(UIButton *)button {
//    NSString *buttonTitle = button.currentTitle;
//    
//    // 檢查 buttonTitle 是否為 nil
//    if (buttonTitle == nil) {
//        NSLog(@"Error: buttonTitle is nil");
//        return;
//    }
//    NSLog(@"4按鍵.鍵Button前(answerButton[0]:%@)",self.answerButton[0]);
//    NSLog(@"4按鍵.鍵Button前(answerButton[1]:%@)",self.answerButton[1]);
//    NSLog(@"4按鍵.鍵Button前(answerButton[2]:%@)",self.answerButton[2]);
//    NSLog(@"4按鍵.鍵Button前(answerButton[3]:%@)",self.answerButton[3]);
//    
//    NSLog(@"buttonTitle = %@", buttonTitle);
//    NSLog(@"answerButton[0]= %@", self.answerButton[0]);
//    
//    if ([buttonTitle isEqualToString:self.answerButton[0]]) {
//        NSLog(@"Button %@ pressed: %@ 正確", button, buttonTitle);
//        // 在這裡直接做「正確」時的後續動作
//        // 例如：分數＋1、跳出Alert、或更新某個Label顯示「正確」
//    } else {
//        NSLog(@"Button %@ pressed: %@ 錯誤", button, buttonTitle);
//        
//        // 在這裡直接做「錯誤」時的後續動作
//    }
//}
- (IBAction)btAnswerPressed:(UIButton *)sender {
    
    // 防止 answerButton 為空
    if (self.answerButton.count == 0) {
        NSLog(@"<btAnswerPressed>Error:( answerButton )資料不足，無法檢查答案");
        return;
    }
    NSString *buttonTitle = sender.currentTitle;
    

    
    // 檢查是否為 self.answerButton[0] (正解)
    if ([buttonTitle isEqualToString:self.answerButton[0]]) {
        NSLog(@"<btAnswerPressed>答對，跳下一題");
    } else {
        NSLog(@"<btAnswerPressed>答錯，播圖片");
        //====觸發按鈕.播放答錯圖片========
        UIImage *selectedImage = [UIImage imageNamed:@"selector41019.png"];//selector41016
        NSLog(@"<btAnswerPressed>播.答錯.圖片selector41019.png");//檢查
        if (selectedImage) {
            [sender setBackgroundImage:selectedImage forState:UIControlStateNormal];
        } else {
            NSLog(@"<btAnswerPressed>錯誤：圖片selector41019.png 未找到");//檢查
        }
        //====觸發按鈕.播放答錯圖片========
        //====0.5秒後.洗按鈕圖========
        // 延迟一段时间后显示下一题
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                // 重置所有按钮的背景图片
                [self resetButtonBackgrounds];//播.洗按鈕圖
            });
        //====0.5秒後.洗按鈕圖========
    }
    //====觸發按鈕.(答題).時間到計數器歸零========
    [self.questionTimer invalidate];
    self.questionTimer = nil;         //時間到計數器歸零
    //====觸發按鈕.(答題).時間到計數器歸零========

    // 顯示下一題------------------
    self.currentQuestionIndex++;
    [self showQuestionAtIndex:self.currentQuestionIndex];
}






@end



















