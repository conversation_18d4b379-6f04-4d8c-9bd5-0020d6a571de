//
//  eMaterialViewController.m
//  uten
//
//  Created by <PERSON> on 2019/8/1.
//  Copyright 2019 bekubee. All rights reserved.
//

#import "eMaterialViewController.h"
#import "ViewController.h"
#import "UIImage+Color.h"
#import "UIImage+Rotate.h"
#import "UIImage+SubImage.h"
#import "UIImage+Gif.h"
#import "MQTTClient.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "UTENEnum.h"
#import <AVFoundation/AVFoundation.h>
#import <MMKV/MMKV.h>
#import "NSUserDefaults+X.h"
#import "UTENMediaModel.h"

@interface eMaterialViewController () {
    MQTTSession *session;
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    IBOutlet UILabel *cname;
    IBOutlet UIImageView *iv;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    UIView *wView[10];
    NSTimer *cTimer;
    int timecnt;
    NSString *DirTest;
    NSString *DirSound;
    NSString *DirWord;
    int SYSCount;
    int SYSType;  //1:SPLIT SAY 2:ALL SAY
    int SYSPARA1; //MIN LOOP (N);
    int SYSPARA2; //MAX LOOP (M)
    int SYSPARA3; //Random time(R)
    int SYSPARA4;
    NSString *ss[401][16];
    NSString *cc[401][16];
    NSString *ee[401][16];
    int scnt[401];
    AVAudioPlayer *_audioPlayer;
    int SoundStep;
    int impos;
    NSString *im[400];
    int gptr;
    CGRect nf[400];
    NSString *ServerIP;
    NSString *uten_class,*r_uten_class;
    float audioDurationSeconds;
    int follow;
    int songdly;
    AVAudioRecorder *recorder;
    NSURL *recordingURL;
}
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation eMaterialViewController

- (void)viewDidLoad {

    impos=0;
    songdly=0;
    follow=0;
    /*
    NSString *sk[10][2]={
        {@"hi_what_is_your_name_2e.mp3",@""},
        {@"i_am_kate_2e.mp3",@""},
        {@"hi_what_is_your_name_2e.mp3",@""},
        {@"i_am_kate_2e.mp3",@""},
        {@"hi_what_is_your_name_2e.mp3",@""},
        {@"i_am_jenny_2e.mp3",@""},
        {@"hi_what_is_your_name_2e.mp3",@""},
        {@"i_am_scott_2e.mp3",@""},
        {@"hi_what_is_your_name_2e.mp3",@""},
        {@"i_am_andy_2e.mp3",@""}
    };
    NSString *ee[400][4]={
        {@"Hi! What is your name?",@"",@"",@""},
        {@"I am Kate.",@"",@"",@""},
        {@"Hi! What is your name?",@"",@"",@""},
        {@"I am Kate.",@"",@"",@""},
        {@"Hi! What is your name?",@"",@"",@""},
        {@"I am Jenny.",@"",@"",@""},
        {@"Hi! What is your name?",@"",@"",@""},
        {@"I am Scott.",@"",@"",@""},
        {@"Hi! What is your name?",@"",@"",@""},
        {@"I am Andy.",@"",@"",@""}};
    NSString *cc[400][4]={
        {@"嗨! 你叫什麼名字?",@"",@"",@""},
        {@"我是凱特。",@"",@"",@""},
        {@"嗨! 你叫什麼名字?",@"",@"",@""},
        {@"我是凱特。",@"",@"",@""},
        {@"嗨! 你叫什麼名字?",@"",@"",@""},
        {@"我是珍妮。",@"兒",@"",@""},
        {@"嗨! 你叫什麼名字?",@"",@"",@""},
        {@"我是史考特。",@"",@"",@""},
        {@"嗨! 你叫什麼名字?",@"",@"",@""},
        {@"我是安迪。",@"",@"",@""}};
     */
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    
    ServerIP = [defaults objectForKey:@"ServerIP"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    
    
    NSLog(@"seltag:%@\nuclass:%@",_seltag,_uclass);
    
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
     transport.host = ServerIP;
     transport.port = 1883;
     
     session = [[MQTTSession alloc] init];
     session.transport = transport;
     session.delegate=self;
     [session connectWithConnectHandler:^(NSError *error) {
        // Do some work

        [self publishCommandClassStart];

        NSLog(@"Subscription %@",uten_class);
        [session subscribeToTopic:uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                NSLog(@"Subscription failed %@", error.localizedDescription);
            } else {
                NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
            }
        }];

     }];
    
    NSString *od=@"";
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile1=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:_uclass]];
    NSString *DirFile=[DirFile1 stringByAppendingString:@".csv"];
    //NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    
    NSError *error;
    NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:&error];
    if(classContent==nil) classContent = [NSString stringWithContentsOfFile:DirFile encoding:-2147483646 error:&error];
    NSLog(@"lbu1u4_s FIle:%@\n%@",DirFile1,DirFile);
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    
    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
    NSArray *lda = [_seltag componentsSeparatedByCharactersInSet:set];
    gptr=0;
    int gcnt=1;
    if ([lda[3] isEqualToString:@"說書1"])  { gcnt=1; follow=1; songdly=0;}
    if ([lda[3] isEqualToString:@"說書3"])  gcnt=3;
    if ([lda[3] isEqualToString:@"說書6"])  gcnt=6;
    if ([lda[3] isEqualToString:@"說書9"])  gcnt=9;
    DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
    audioDurationSeconds = 0;
    //CMTimeGetSeconds(audioDuration);
    
        for(int k=5;k<[lda count];k+=2) {
            for(int i=1;i< [splitLine count];i++) {
                    NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
                    if([lda[k] isEqualToString:split1[0]]) {
                        for(int g=0;g<gcnt;g++) {
                            im[gptr]=[NSString stringWithFormat:@"%@.jpg", split1[0]];
                            if([split1[7] isEqualToString:@"*"]) { ss[gptr][0]=split1[8]; cc[gptr][0]=split1[9];  }
                            else { ss[gptr][0]=split1[10]; cc[gptr][0]=split1[11]; }
                            {
                                NSString *base = DirSound;
                                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[gptr][0]];
                                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                                
                                AVURLAsset* audioAsset = [AVURLAsset URLAssetWithURL:soundUrl options:nil];
                                CMTime audioDuration = audioAsset.duration;
                                audioDurationSeconds += CMTimeGetSeconds(audioDuration)+1.0f;
                            }
                            for(int j=0;j<[split1 count];j++) {
                                if([split1[j] containsString:@"_2c.mp3"]) {
                                    ss[gptr][1]=split1[j];
                                    //
                                    NSString *base = DirSound;
                                    NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[gptr][1]];
                                    NSURL *soundUrl = [NSURL fileURLWithPath:path];
                                    
                                    AVURLAsset* audioAsset = [AVURLAsset URLAssetWithURL:soundUrl options:nil];
                                    CMTime audioDuration = audioAsset.duration;
                                    audioDurationSeconds += CMTimeGetSeconds(audioDuration);
                                    break;
                                }
                            }
                            ee[gptr][0]=split1[5];
                            nf[gptr].origin.x=[[split1[1]  substringFromIndex:1] floatValue];
                            nf[gptr].origin.y=[[split1[2]  substringToIndex:[split1[2] length]-1] floatValue]+48;
                            nf[gptr].size.width=[[split1[3]  substringFromIndex:1] floatValue];
                            nf[gptr].size.height=[[split1[4]  substringToIndex:[split1[4] length]-1] floatValue];
                            gptr++;
                        }
                    }
            }
        }
     
    if(gcnt != 1) {
    for(int k=5;k<[lda count];k+=2) {
        for(int g=0;g<gcnt;g++) {
        for(int i=1;i< [splitLine count];i++) {
                NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
                if([lda[k] isEqualToString:split1[0]]) {
                    
                        im[gptr]=[NSString stringWithFormat:@"%@.jpg", split1[0]];
                        if([split1[7] isEqualToString:@"*"]) { ss[gptr][0]=split1[8]; cc[gptr][0]=split1[9];  }
                        else { ss[gptr][0]=split1[10]; cc[gptr][0]=split1[11]; }
                        {
                            NSString *base = DirSound;
                            NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[gptr][0]];
                            NSURL *soundUrl = [NSURL fileURLWithPath:path];
                            
                            AVURLAsset* audioAsset = [AVURLAsset URLAssetWithURL:soundUrl options:nil];
                            CMTime audioDuration = audioAsset.duration;
                            audioDurationSeconds += CMTimeGetSeconds(audioDuration)+1.0f;
                        }
                        for(int j=0;j<[split1 count];j++) {
                            if([split1[j] containsString:@"_2c.mp3"]) {
                                ss[gptr][1]=split1[j];
                                //
                                NSString *base = DirSound;
                                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[gptr][1]];
                                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                                
                                AVURLAsset* audioAsset = [AVURLAsset URLAssetWithURL:soundUrl options:nil];
                                CMTime audioDuration = audioAsset.duration;
                                audioDurationSeconds += CMTimeGetSeconds(audioDuration);
                                break;
                            }
                        }
                        ee[gptr][0]=split1[5];
                        nf[gptr].origin.x=[[split1[1]  substringFromIndex:1] floatValue];
                        nf[gptr].origin.y=[[split1[2]  substringToIndex:[split1[2] length]-1] floatValue]+48;
                        nf[gptr].size.width=[[split1[3]  substringFromIndex:1] floatValue];
                        nf[gptr].size.height=[[split1[4]  substringToIndex:[split1[4] length]-1] floatValue];
                        gptr++;
                    }
                }
        }
    }
    }
    //
    

    wView[0] = [[UIView alloc] initWithFrame: CGRectMake(260, 380+48,  360, 50)] ;
    wView[0].layer.borderWidth = 5;
    wView[0].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[0]];
  
    wView[1] = [[UIView alloc] initWithFrame: CGRectMake(0, 0,  1024, 768)] ;
    wView[1].layer.borderWidth = 100;
    wView[1].alpha=0.5;
    wView[1].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[1]];
  /*
    wView[2] = [[UIView alloc] initWithFrame: CGRectMake(47, 268+48,  544, 51)] ;
    wView[2].layer.borderWidth = 5;
    wView[2].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[2]];
    
    wView[3] = [[UIView alloc] initWithFrame: CGRectMake(47, 319+48,  172, 46)] ;
    wView[3].layer.borderWidth = 5;
    wView[3].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[3]];

    wView[4] = [[UIView alloc] initWithFrame: CGRectMake(47, 365+48,  344, 51)] ;
    wView[4].layer.borderWidth = 5;
    wView[4].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[4]];
    wView[5] = [[UIView alloc] initWithFrame: CGRectMake(47, 416+48,  194, 45)] ;
    wView[5].layer.borderWidth = 5;
    wView[5].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[5]];
    wView[6] = [[UIView alloc] initWithFrame: CGRectMake(47, 461+48,  220, 235)] ;
    wView[6].layer.borderWidth = 5;
    wView[6].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[6]];
    wView[7] = [[UIView alloc] initWithFrame: CGRectMake(47, 507+48,  195, 48)] ;
    wView[7].layer.borderWidth = 5;
    wView[7].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[7]];
    
    wView[8] = [[UIView alloc] initWithFrame: CGRectMake(47, 555+48,  346, 48)] ;
    wView[8].layer.borderWidth = 5;
    wView[8].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[8]];
    
    wView[9] = [[UIView alloc] initWithFrame: CGRectMake(47, 603+48,  195, 41)] ;
    wView[9].layer.borderWidth = 5;
    wView[9].layer.borderColor = [[UIColor redColor] CGColor];
    [self.view addSubview:wView[9]];
    */
    
    //
    /*
    int index=2;
    NSString *_uclass=@"lbu1u4_s";
     NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
     DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",_uclass,_uclass]];
     DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
     DirWord=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//word//",_uclass,_uclass]];
     
     NSString *localFilePath = [DirTest stringByAppendingPathComponent:[NSString stringWithFormat:@"l%03d.csv",index+1]];
     NSString* content = [NSString stringWithContentsOfFile:localFilePath
                                                   encoding:NSUTF8StringEncoding
                                                      error:NULL];
     NSLog(@"CSV FIle:%@\n%@",localFilePath,content);
     NSArray *splitLine = [content componentsSeparatedByString:@"\n"];


    
    for(int i=0;i<10;i++) {
        ss[i][0]=sk[i][0];
        ss[i][1]=sk[i][1];
    }
    iv.image=[UIImage imageNamed:@"LB_02_1.jpg"];
    for(int i=0;i<10;i++) wView[i].alpha=0.0;
    
*/
    
    cTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    timecnt=0;
    SoundStep=0;
    //first
    iv.image=[UIImage imageNamed:im[impos]];
    if((wView[0].frame.origin.x!=nf[impos].origin.x)|(wView[0].frame.origin.y!=nf[impos].origin.y))
            wView[0].layer.borderColor = [[UIColor redColor] CGColor];
    wView[0].frame=nf[impos];
}
-(void)timesUp:(NSTimer *)timer{
    /*
    NSString *im[68][2]={
        {@"LB_02_1.jpg",@"LB_02_1.jpg"},
        {@"LB_03_1.jpg",@"LB_03_1.jpg"},
        {@"LB_03_1.jpg",@"LB_03_1.jpg"},
        {@"LB_03_1.jpg",@"LB_03_1.jpg"},
        {@"LB_03_1.jpg",@"LB_03_1.jpg"},
        {@"LB_05_1.jpg",@"LB_05_2.jpg"},
        {@"LB_06_1.jpg",@"L1_06_2.jpg"},
        {@"LB_07_1.jpg",@"L1_07_2.jpg"},
        {@"LB_08_1.jpg",@"L1_08_2.jpg"},
        {@"LB_09_1.jpg",@"L1_09_2.jpg"},
        {@"L1_10_1.jpg",@"L1_10_2.jpg"},
        {@"L1_11_1.jpg",@"L1_11_2.jpg"},
        {@"L1_12_1.jpg",@"L1_12_2.jpg"},
        {@"L1_13_1.jpg",@"L1_13_2.jpg"},
        {@"L1_14_1.jpg",@"L1_14_2.jpg"},
        {@"L1_15_1.jpg",@"L1_15_2.jpg"},
        {@"L1_16_1.jpg",@"L1_16_2.jpg"},
        {@"L1_17_1.jpg",@"L1_17_2.jpg"},
        {@"L1_18_1.jpg",@"L1_18_2.jpg"},
        {@"L1_19_1.jpg",@"L1_19_2.jpg"},
        {@"L1_20_1.jpg",@"L1_20_2.jpg"},
        {@"L1_21_1.jpg",@"L1_21_2.jpg"},
        {@"L1_22_1.jpg",@"L1_22_2.jpg"},
        {@"L1_23_1.jpg",@"L1_23_2.jpg"},
        {@"L1_24_1.jpg",@"L1_24_2.jpg"}};
     */
    timecnt++;
    switch(timecnt) {
        case 5: //0.5
            //iv.image=[UIImage imageNamed:im[impos++][0]];
            iv.image=[UIImage imageNamed:im[impos]];
            if((wView[0].frame.origin.x!=nf[impos].origin.x)|(wView[0].frame.origin.y!=nf[impos].origin.y))
                    wView[0].layer.borderColor = [[UIColor redColor] CGColor];
            wView[0].frame=nf[impos];
            wView[1].hidden=NO;
            //CGRect f=nf[impos];
            //f.origin.y+=nf[impos].size.height;
            //cname.frame=f;
            cname.text=[NSString stringWithFormat:@"%@ %@",ee[impos][0],cc[impos][0]];
            
            impos++;
            //wView[0] = [[UIView alloc] initWithFrame: CGRectMake(260, 380+48,  360, 50+impos*10)] ;
            //CGRect newFrame = wView[0].frame;
            //newFrame.size.width = 200;
            //newFrame.size.height =  50+impos*10;
            //wView[0].frame=newFrame;
            /*
            for(int i=0;i<10;i++) {
                if(SoundStep==i) wView[i].alpha=1.0;
                else wView[i].alpha=0.0;
            }
            */
            break;
        case 6:
            {
                NSString *base = DirSound;
                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[SoundStep][0]];
                NSLog(@"PLAYFILE0:%@",path);
                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                
                _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
                
                NSLog(@"Duration:%1.2f",audioDurationSeconds);
                
                [_audioPlayer setVolume:1.0];
                [_audioPlayer prepareToPlay];
                //if(wcount == 76) [_audioPlayer setVolume:1.0];
                //else [_audioPlayer setVolume:0.0];
                [_audioPlayer play];
            }
            break;
        case 7:
            songdly++;
            if(_audioPlayer.isPlaying) timecnt=6;
            break;
        case 8:
            {
                NSString *base = DirSound;
                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[SoundStep][1]];
                NSLog(@"PLAYFILE1:%@",path);
                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                
                _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
                [_audioPlayer setVolume:1.0];
                [_audioPlayer prepareToPlay];
                //if(wcount == 76) [_audioPlayer setVolume:1.0];
                //else [_audioPlayer setVolume:0.0];
                [_audioPlayer play];
            }
        case 9:
            songdly++;
            if(_audioPlayer.isPlaying) timecnt=8;
            break;
        case 11:
            SoundStep++;
            if(SoundStep < gptr) timecnt=0;
            break;
        case 10:
            if(follow) {
                if(songdly > 0) { songdly--; timecnt=9; }
            }
            break;
        case 15:
            [cTimer invalidate];
            cTimer = nil;
            [self publishCommandClassStop];
            [self dismissViewControllerAnimated:NO completion:nil];
            break;
            
    }
    
}
- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    NSLog(@"Mouse Down");
    UITouch *touch = [touches anyObject];
    CGPoint Point = [touch locationInView:self.view];
    
    // Start recording
    NSError *error;
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord error:&error];
    if (error) {
        NSLog(@"Error setting up audio session: %@", error.localizedDescription);
        return;
    }

    // Create recording URL in documents directory
    NSString *documentsPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    NSString *recordingName = [NSString stringWithFormat:@"%@_%ld.m4a", [NSUserDefaults standardUserDefaults].identifier, (long)[[NSDate date] timeIntervalSince1970]];
    NSString *recordingPath = [documentsPath stringByAppendingPathComponent:recordingName];
    recordingURL = [NSURL fileURLWithPath:recordingPath];

    NSString *groupId = [NSString stringWithFormat:@"%@", @(LocalTableMedia)];
    MMKV *mmkv = [MMKV mmkvWithID:groupId];
    // log path
    // Key: recording_1734874814.m4a
    // Val: /var/mobile/Containers/Data/Application/491F42AE-3FE8-4BBF-B14A-5C8908780E8C/Documents/recording_1734874814.m4a
    NSLog(@"Key: %@, Val: %@", recordingURL.lastPathComponent, recordingURL.path);
    
    // Create UTENMediaModel instance
    UTENMediaModel *mediaModel = [[UTENMediaModel alloc] init];
    mediaModel.file = recordingURL.lastPathComponent;
    mediaModel.userID = [NSUserDefaults standardUserDefaults].identifier;
    mediaModel.uClass = self.uclass;
    mediaModel.soundStep = @(SoundStep);
    mediaModel.vocabularyEn = ee[SoundStep][0];
    mediaModel.vocabularyZh = cc[SoundStep][0];
    NSDate *currentDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    NSString *dateString = [dateFormatter stringFromDate:currentDate];
    mediaModel.createdAt = dateString;
    mediaModel.updatedAt = dateString;
    
    // Convert to JSON and store in MMKV
    NSString *jsonString = [mediaModel toJSON:NSUTF8StringEncoding error:&error];
    if (!error && jsonString) {
        [mmkv setString:jsonString forKey:recordingURL.lastPathComponent];
        NSLog(@"Stored media model for key: %@", recordingURL.lastPathComponent);
    } else {
        NSLog(@"Failed to store media model: %@", error);
    }
    
    // Configure recording settings
    NSDictionary *recordSettings = @{
        AVFormatIDKey: @(kAudioFormatMPEG4AAC),
        AVSampleRateKey: @44100.0,
        AVNumberOfChannelsKey: @2,
        AVEncoderAudioQualityKey: @(AVAudioQualityHigh)
    };
    
    // Initialize and start recording
    recorder = [[AVAudioRecorder alloc] initWithURL:recordingURL settings:recordSettings error:&error];
    if (error) {
        NSLog(@"Error creating recorder: %@", error.localizedDescription);
        return;
    }
    
    [recorder prepareToRecord];
    [recorder record];
    
    int pos=SoundStep;
    CGRect nFrame=wView[0].frame;
    if((Point.x > nFrame.origin.x)&(Point.x < (nFrame.size.width+nFrame.origin.x)))
        if((Point.y > nFrame.origin.y)&(Point.y < (nFrame.size.height+nFrame.origin.y))) {
            wView[0].layer.borderColor = [[UIColor blueColor] CGColor];
            wView[1].hidden=YES;
        }
}
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event {
    // Stop recording if active
    if (recorder && recorder.isRecording) {
        [recorder stop];
        
        // Reset audio session
        NSError *error;
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        [audioSession setCategory:AVAudioSessionCategoryPlayback error:&error];
        if (error) {
            NSLog(@"Error resetting audio session: %@", error.localizedDescription);
        }
    }
    for(int i=0;i<8;i++)
        wView[i].layer.borderColor = [[UIColor redColor] CGColor];
}
- (void)touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event
{
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {
    [cTimer invalidate];
    cTimer = nil;

    [self publishCommandClassStop];

    [self dismissViewControllerAnimated:NO completion:nil];
}
- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    // 收到中斷訊息
    if (command.isBye) {
        [cTimer invalidate];
        cTimer = nil;
        [self publishCommandClassStop];
        //NSLog(@"PLAY END :[%d][%d] %.2f",musttime,mp3cnt++,mp3time);
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

// publish class start
- (void)publishCommandClassStart {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStart;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

// publish class stop
- (void)publishCommandClassStop {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

@end
