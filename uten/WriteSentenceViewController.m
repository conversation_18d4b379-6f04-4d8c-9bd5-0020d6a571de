//
//  WriteSentenceViewController.m
//  uten
//
//  Created by <PERSON> on 2019/2/12.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "WriteSentenceViewController.h"
#import "ViewController.h"
#import "UIImage+Color.h"
#import "UIImage+Rotate.h"
#import "UIImage+SubImage.h"
#import "UIImage+Gif.h"
#import "MQTTClient.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "UTENEnum.h"

@interface WriteSentenceViewController () <MQTTSessionDelegate> {
    MQTTSession *session;
    NSString *ID;
    IBOutlet UIButton *btStartTime;
    IBOutlet UIButton *btTimes;
    IBOutlet UILabel *laTimes;
    IBOutlet UIButton *btSingal;
    IBOutlet UILabel *laStatus;
    IBOutlet UILabel *lacWord;
    IBOutlet UILabel *inxWord;
    IBOutlet UIStepper *stp;
    IBOutlet UILabel *laPass;
    IBOutlet UILabel *laNG;
    NSTimer *cTimer;
    IBOutlet UIImageView *lframe;
    IBOutlet UIImageView *mframe;
    IBOutlet UIImageView *rframe;

    int classvalue;
    int wcount;
    NSString *eword[400];
    NSString *cword[400];
    int song[400];
    bool mark[400];
    NSString *ss[401][16];
    int scnt[401];
    int timecnt;
    int test01;
    int nowIdx;
    AVAudioPlayer *_audioPlayer;
    int TotalPass;
    int TotalNG;

    int SYSCount;
    int SYSType;  //1:SPLIT SAY 2:ALL SAY
    int SYSPARA1; //MIN LOOP (N);
    int SYSPARA2; //MAX LOOP (M)
    int SYSPARA3; //Random time(R)
    int SYSPARA4;
    UIImageView *W00[200];
    UIImageView *I00[200];
    int wordinx[200];
    UIImageView *S00[200];
    SignatureDrawView *dW00[200];
    NSString *DirTest;
    NSString *DirSound;
    NSString *DirWord;
    int SoundStep;
    int waitcnt;
    NSString *ServerIP;
    NSString *uten_class,*r_uten_class;
    int SayCH;
    int ShowCH;
    int gcnt;
    int gtype;
    int tapsel;
}

@end

@implementation WriteSentenceViewController

- (void)viewDidLoad {
    NSString *ee[400][4]={
        {@"bat",@"cat",@"hat",@"rat"},{@"can",@"man",@"pan",@"van"},{@"bad",@"dad",@"mad",@"sad"},{@"back",@"pack",@"sack",@"snack"},
        {@"big",@"pig",@"wig",@"twig"},{@"bill",@"jill",@"hill",@"ill"},{@"king",@"ring",@"sing",@"wings"},{@"lick",@"nick",@"kick",@"chick"},
        {@"hop",@"mop",@"pop",@"top"},{@"not",@"dot",@"hot",@"pot"},{@"rock",@"sock",@"fox",@"ox"},{@"bun",@"nun",@"run",@"sun"},
        {@"bug",@"rug",@"duck",@"truck"},{@"bat",@"cat",@"hat",@"rat"},{@"can",@"man",@"pan",@"van"},{@"bad",@"dad",@"mad",@"sad"},
        {@"back",@"pack",@"sack",@"snack"},{@"big",@"pig",@"wig",@"twig"},{@"bill",@"jill",@"hill",@"ill"},{@"king",@"ring",@"sing",@"wings"},
        {@"lick",@"Nick",@"kick",@"chick"},{@"hop",@"mop",@"pop",@"top"},{@"not",@"dot",@"hot",@"pot"},{@"rock",@"sock",@"fox",@"ox"},
        {@"bun",@"nun",@"run",@"sun"},{@"bug",@"rug",@"duck",@"truck"}};
    NSString *cc[400][4]={
        {@"蝙蝠",@"貓",@"帽子",@"田鼠"},{@"罐頭",@"男人",@"平底鍋",@"廂型車"},{@"壞",@"爹",@"發瘋的",@"傷心的"},{@"背",@"背包",@"紙袋",@"小點心"},
        {@"大",@"豬",@"假髮",@"細樹枝"},{@"比爾",@"吉兒",@"山丘",@"生病"},{@"國王",@"戒指",@"唱歌",@"翅膀 複"},{@"舔",@"尼克",@"踢",@"小雞"},
        {@"單腳跳",@"拖把",@"碰",@"陀螺"},{@"不",@"點",@"熱",@"鍋子"},{@"岩石",@"襪子",@"狐狸",@"公牛"},{@"小麵包",@"修女",@"跑",@"太陽"},
        {@"蟲子",@"小地毯",@"鴨子",@"卡車"},{@"蝙蝠",@"貓",@"帽子",@"田鼠"},{@"罐頭",@"男人",@"平底鍋",@"廂型車"},{@"壞",@"爹",@"發瘋的",@"傷心的"},
        {@"背",@"背包",@"紙袋",@"小點心"},{@"大",@"豬",@"假髮",@"細樹枝"},{@"比爾",@"吉兒",@"山丘",@"生病"},{@"國王",@"戒指",@"唱歌",@"翅膀 複"},
        {@"舔",@"尼克",@"踢",@"小雞"},{@"單腳跳",@"拖把",@"碰",@"陀螺"},{@"不",@"點",@"熱",@"鍋子"},{@"岩石",@"襪子",@"狐狸",@"公牛"},
        {@"小麵包",@"修女",@"跑",@"太陽"},{@"蟲子",@"小地毯",@"鴨子",@"卡車"}};
    [super viewDidLoad];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    // Do any additional setup after loading the view.
    NSLog(@"%@_%@", _seltag,_uclass);
    int index=[_seltag intValue];
    int loop=100;
    int st=18;
    int se=20;
    NSString *tmp;
    int itmp;
    
    bool imark;
    NSString *NN[10];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    ID = [defaults objectForKey:@"ID"];
    //Initial
    for(int i=0;i<200;i++) {
        W00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        W00[i].image=[UIImage imageNamed:@"a1.png"];
        W00[i].alpha=0.3f;
        [self.view addSubview:W00[i]];
        W00[i].hidden=YES;
        I00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        I00[i].image=[UIImage imageNamed:@"bk.png"];
        I00[i].alpha=1.0f;
        [self.view addSubview:I00[i]];
        I00[i].hidden=YES;
        dW00[i] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(50,50,25,75)];
        dW00[i].alpha=0.3f;
        [self.view addSubview:dW00[i]];
        dW00[i].hidden=YES;
        
        S00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,50)];
        S00[i].image=[UIImage imageNamed:@"O_Red.png"];
        S00[i].alpha=0.2f;
        S00[i].hidden=YES;
        S00[i].tag=i;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleTap:)];
        [S00[i] addGestureRecognizer:tap];
        [S00[i] setUserInteractionEnabled:YES];
        [self.view addSubview:S00[i]];

    }
    S00[0].alpha=1.0f;
    tapsel=0;

    //
    /*
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",_uclass,_uclass]];
    DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
    DirWord=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//word//",_uclass,_uclass]];
    
    NSString *localFilePath = [DirTest stringByAppendingPathComponent:[NSString stringWithFormat:@"l%03d.csv",index+1]];
    NSString* content = [NSString stringWithContentsOfFile:localFilePath
                                                  encoding:NSUTF8StringEncoding
                                                     error:NULL];
    NSLog(@"CSV FIle:%@\n%@",localFilePath,content);
    NSArray *splitLine = [content componentsSeparatedByString:@"\n"];
    if([splitLine count] >= 4) {
        NSArray *split1=[splitLine[1] componentsSeparatedByString:@","];
        NSArray *split2=[splitLine[3] componentsSeparatedByString:@","];
        SYSCount=[split1[0] intValue];
        SYSType=[split1[1] intValue];
        SYSPARA1=[split1[2] intValue];
        SYSPARA2=[split1[3] intValue];
        SYSPARA3=[split1[4] intValue];
        SYSPARA4=[split1[5] intValue];
        NSLog(@"String Count:%d\n",SYSCount);
        for(int i=0;i<SYSCount;i++) {
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.csv",split2[i]]];
            //localFilePath = [documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//base//word//%@.csv",split2[i]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[index][i]=split[1];
            cc[index][i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
    }
     */
    NSString *od=@"";
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile1=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:_uclass]];
    NSString *DirFile=[DirFile1 stringByAppendingString:@".csv"];
    //NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    
    NSError *error;
    NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:&error];
    if(classContent==nil) classContent = [NSString stringWithContentsOfFile:DirFile encoding:-2147483646 error:&error];
    NSLog(@"lbu1u4_s FIle:%@\n%@\n%@\n",DirFile1,DirFile,classContent);
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    
    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
    NSArray *lda = [_seltag componentsSeparatedByCharactersInSet:set];
    int gptr=0;
   
    for(int k=5;k<[lda count];k+=2) {
            for(int i=1;i< [splitLine count];i++) {
                    NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
                    if([lda[k] isEqualToString:split1[5]]) {
                        NSLog(@"INFO-%@",splitLine[i]);
                        if([split1[7] isEqualToString:@"*"]) {
                            ee[gptr][0]=split1[5];
                            cc[gptr][0]=split1[9];
                            ss[gptr][0]=split1[8];
                        }
                        else {
                            ee[gptr][0]=split1[5];
                            cc[gptr][0]=split1[11];
                            ss[gptr][0]=split1[10];
                        }
                        scnt[gptr]=1;
                        for(int j=0;j<[split1 count];j++) {
                            if([split1[j] containsString:@"_2c.mp3"]) {
                                //ss[gptr][1]=split1[j];
                                ss[gptr][0]=split1[j];
                                scnt[gptr]=2;
                                break;
                            }
                        }
                        NSLog(@"PLAY-%@-%@",ss[gptr][0],split1[7]);
                        gptr++;
                        break;
                    }
            }
    }
    DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
    index=0;
//   SYSPARA1=2;
 //   SYSPARA2=3;
 //   SYSPARA3=3;
 //   SYSCount=1;
    gcnt=1;
    gtype=1;
    if ([lda[3] isEqualToString:@"句寫順顯中1"]) { gtype=0; gcnt=1; }
    if ([lda[3] isEqualToString:@"句寫順顯中3"]) { gtype=0; gcnt=3; }
    if ([lda[3] isEqualToString:@"句寫順顯中6"]) { gtype=0; gcnt=6; }
    if ([lda[3] isEqualToString:@"句寫順顯中9"]) { gtype=0; gcnt=9; }
    if ([lda[3] isEqualToString:@"句寫亂顯中1"]) { gtype=1; gcnt=1; }
    if ([lda[3] isEqualToString:@"句寫亂顯中3"]) { gtype=1; gcnt=3; }
    if ([lda[3] isEqualToString:@"句寫亂顯中6"]) { gtype=1; gcnt=6; }
    if ([lda[3] isEqualToString:@"句寫亂顯中9"]) { gtype=1; gcnt=9; }
    if ([lda[3] isEqualToString:@"句寫順聽中1"]) { gtype=2; gcnt=1; }
    if ([lda[3] isEqualToString:@"句寫順聽中3"]) { gtype=2; gcnt=3; }
    if ([lda[3] isEqualToString:@"句寫順聽中6"]) { gtype=2; gcnt=6; }
    if ([lda[3] isEqualToString:@"句寫順聽中9"]) { gtype=2; gcnt=9; }
    if ([lda[3] isEqualToString:@"句寫亂聽中1"]) { gtype=3; gcnt=1; }
    if ([lda[3] isEqualToString:@"句寫亂聽中3"]) { gtype=3; gcnt=3; }
    if ([lda[3] isEqualToString:@"句寫亂聽中6"]) { gtype=3; gcnt=6; }
    if ([lda[3] isEqualToString:@"句寫亂聽中9"]) { gtype=3; gcnt=9; }
    if (gtype==0) {
        int pos=0;
        for(int i=0;i<gptr;i++) {
            for(int g=0;g<gcnt;g++) {
                eword[pos] = [ee[i][0] stringByReplacingOccurrencesOfString:@"@"withString:@","];
                cword[pos] =  cc[i][0];
                song[pos]=i;
                mark[pos]=true;
                pos++;
            }
        }
        SayCH=0;
        ShowCH=1;
        wcount=pos;
    }
    if (gtype==1) {
        int pos=0;
        for(int i=0;i<gptr;i++) {
            for(int g=0;g<gcnt;g++) {
                eword[pos] = [ee[i][0] stringByReplacingOccurrencesOfString:@"@"withString:@","];
                cword[pos] =  cc[i][0];
                song[pos]=i;
                mark[pos]=true;
                pos++;
            }
        }
        for(int i=0;i<gptr*gcnt;i++) {
            for(int j=i;j<gptr*gcnt-1;j++) {
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=(se-1)) {
                        tmp=eword[j]; eword[j]=eword[j+1]; eword[j+1]=tmp;
                        tmp=cword[j]; cword[j]=cword[j+1]; cword[j+1]=tmp;
                        itmp=song[j]; song[j]=song[j+1]; song[j+1]=itmp;
                        imark=mark[j]; mark[j]=mark[j+1]; mark[j+1]=imark;
                    } else {
                        tmp=eword[j]; eword[j]=eword[st]; eword[st]=tmp;
                        tmp=cword[j]; cword[j]=cword[st]; cword[st]=tmp;
                        itmp=song[j]; song[j]=song[st]; song[st]=itmp;
                        imark=mark[j]; mark[j]=mark[st]; mark[st]=imark;
                    }
                }
            }
        }
        SayCH=0;
        ShowCH=1;
        wcount=pos;
    }
    if (gtype==2) {
        int pos=0;
        for(int i=0;i<gptr;i++) {
            for(int g=0;g<gcnt;g++) {
                eword[pos] = [ee[i][0] stringByReplacingOccurrencesOfString:@"@"withString:@","];
                cword[pos] =  cc[i][0];
                song[pos]=i;
                mark[pos]=false;
                pos++;
            }
        }
        SayCH=1;
        ShowCH=0;
        wcount=pos;
    }
    if (gtype==3) {
        int pos=0;
        for(int i=0;i<gptr;i++) {
            for(int g=0;g<gcnt;g++) {
                eword[pos] = [ee[i][0] stringByReplacingOccurrencesOfString:@"@"withString:@","];
                cword[pos] =  cc[i][0];
                song[pos]=i;
                mark[pos]=false;
                pos++;
            }
        }
        for(int i=0;i<gptr*gcnt;i++) {
            for(int j=i;j<gptr*gcnt-1;j++) {
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=(se-1)) {
                        tmp=eword[j]; eword[j]=eword[j+1]; eword[j+1]=tmp;
                        tmp=cword[j]; cword[j]=cword[j+1]; cword[j+1]=tmp;
                        itmp=song[j]; song[j]=song[j+1]; song[j+1]=itmp;
                        imark=mark[j]; mark[j]=mark[j+1]; mark[j+1]=imark;
                    } else {
                        tmp=eword[j]; eword[j]=eword[st]; eword[st]=tmp;
                        tmp=cword[j]; cword[j]=cword[st]; cword[st]=tmp;
                        itmp=song[j]; song[j]=song[st]; song[st]=itmp;
                        imark=mark[j]; mark[j]=mark[st]; mark[st]=imark;
                    }
                }
            }
        }
        SayCH=1;
        ShowCH=0;
        wcount=pos;
    }
        /*
        for(int k=0;k<SYSPARA3;k++) {
            for(int j=0;j<SYSCount;j++) {
                eword[pos]=ee[index][j];
                cword[pos]=cc[index][j];
                song[pos]=j;
                mark[pos]=false;
                pos++;
                
            }
        }
        for(int i=0;i<SYSPARA3;i++) {
            for(int j=wcount+i*SYSCount;j<wcount+i*SYSCount+SYSCount;j++) {
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=(se-1)) {
                        tmp=eword[j]; eword[j]=eword[j+1]; eword[j+1]=tmp;
                        tmp=cword[j]; cword[j]=cword[j+1]; cword[j+1]=tmp;
                        itmp=song[j]; song[j]=song[j+1]; song[j+1]=itmp;
                        imark=mark[j]; mark[j]=mark[j+1]; mark[j+1]=imark;
                    } else {
                        tmp=eword[j]; eword[j]=eword[st]; eword[st]=tmp;
                        tmp=cword[j]; cword[j]=cword[st]; cword[st]=tmp;
                        itmp=song[j]; song[j]=song[st]; song[st]=itmp;
                        imark=mark[j]; mark[j]=mark[st]; mark[st]=imark;
                    }
                }
            }
        }
        wcount=pos;
         */
    
    
    nowIdx=0;
    timecnt=0;
    test01=7;
    lacWord.text=@"";
    
    
    /*
     int loop=100;
     for(int i=0;i<loop;i++) {
     NSString *tmp;
     if(arc4random_uniform(2)==1) { //swap
     tmp=eword[4]; eword[4]=eword[5]; eword[5]=tmp;
     tmp=cword[4]; cword[4]=cword[5]; cword[5]=tmp;
     }
     if(arc4random_uniform(2)==1) { //swap
     tmp=eword[5]; eword[5]=eword[6]; eword[6]=tmp;
     tmp=cword[5]; cword[5]=cword[6]; cword[6]=tmp;
     }
     if(arc4random_uniform(2)==1) { //swap
     tmp=eword[6]; eword[6]=eword[7]; eword[7]=tmp;
     tmp=cword[6]; cword[6]=cword[7]; cword[7]=tmp;
     }
     if(arc4random_uniform(2)==1) { //swap
     tmp=eword[7]; eword[7]=eword[4]; eword[4]=tmp;
     tmp=cword[7]; cword[7]=cword[4]; cword[4]=tmp;
     }
     }
     */
    cTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    TotalPass=0;
    TotalNG=0;
    laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
    laPass.text=[NSString stringWithFormat:@"%04d",TotalPass];
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
     transport.host = ServerIP;
     transport.port = 1883;
     
     session = [[MQTTSession alloc] init];
     session.transport = transport;
     session.delegate=self;
     [session connectWithConnectHandler:^(NSError *error) {
         // Do some work

         [self publishCommandClassStart];

         NSLog(@"Subscription %@",uten_class);
         [session subscribeToTopic:uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
             if (error) {
                 NSLog(@"Subscription failed %@", error.localizedDescription);
             } else {
                 NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
             }
         }];

     }];
    
}
-(int ) getWordInx:(NSString *)str
{
    
        if([str isEqualToString:@"a"]) return 0;
        if([str isEqualToString:@"b"]) return 1;
        if([str isEqualToString:@"c"]) return 2;
        if([str isEqualToString:@"d"]) return 3;
        if([str isEqualToString:@"e"]) return 4;
        if([str isEqualToString:@"f"]) return 5;
        if([str isEqualToString:@"g"]) return 6;
        if([str isEqualToString:@"h"]) return 7;
        if([str isEqualToString:@"i"]) return 8;
        if([str isEqualToString:@"j"]) return 9;
        if([str isEqualToString:@"k"]) return 10;
        if([str isEqualToString:@"l"]) return 11;
        if([str isEqualToString:@"m"]) return 12;
        if([str isEqualToString:@"n"]) return 13;
        if([str isEqualToString:@"o"]) return 14;
        if([str isEqualToString:@"p"]) return 15;
        if([str isEqualToString:@"q"]) return 16;
        if([str isEqualToString:@"r"]) return 17;
        if([str isEqualToString:@"s"]) return 18;
        if([str isEqualToString:@"t"]) return 19;
        if([str isEqualToString:@"u"]) return 20;
        if([str isEqualToString:@"v"]) return 21;
        if([str isEqualToString:@"w"]) return 22;
        if([str isEqualToString:@"x"]) return 23;
        if([str isEqualToString:@"y"]) return 24;
        if([str isEqualToString:@"z"]) return 25;
     
    return 0;
}
-(NSString *) getWordPng:(NSString *)str
{
    if([str isEqualToString:@"a"]) return @"a1.png";
    if([str isEqualToString:@"b"]) return @"b1.png";
    if([str isEqualToString:@"c"]) return @"c1.png";
    if([str isEqualToString:@"d"]) return @"d1.png";
    if([str isEqualToString:@"e"]) return @"e1.png";
    if([str isEqualToString:@"f"]) return @"f1.png";
    if([str isEqualToString:@"g"]) return @"g1.png";
    if([str isEqualToString:@"h"]) return @"h1.png";
    if([str isEqualToString:@"i"]) return @"i1.png";
    if([str isEqualToString:@"j"]) return @"j1.png";
    if([str isEqualToString:@"k"]) return @"k1.png";
    if([str isEqualToString:@"l"]) return @"l1.png";
    if([str isEqualToString:@"m"]) return @"m1.png";
    if([str isEqualToString:@"n"]) return @"n1.png";
    if([str isEqualToString:@"o"]) return @"o1.png";
    if([str isEqualToString:@"p"]) return @"p1.png";
    if([str isEqualToString:@"q"]) return @"q1.png";
    if([str isEqualToString:@"r"]) return @"r1.png";
    if([str isEqualToString:@"s"]) return @"s1.png";
    if([str isEqualToString:@"t"]) return @"t1.png";
    if([str isEqualToString:@"u"]) return @"u1.png";
    if([str isEqualToString:@"v"]) return @"v1.png";
    if([str isEqualToString:@"w"]) return @"w1.png";
    if([str isEqualToString:@"x"]) return @"x1.png";
    if([str isEqualToString:@"y"]) return @"y1.png";
    if([str isEqualToString:@"z"]) return @"z1.png";
    if([str isEqualToString:@"A"]) return @"a2.png";
    if([str isEqualToString:@"B"]) return @"b2.png";
    if([str isEqualToString:@"C"]) return @"c2.png";
    if([str isEqualToString:@"D"]) return @"d2.png";
    if([str isEqualToString:@"E"]) return @"e2.png";
    if([str isEqualToString:@"F"]) return @"f2.png";
    if([str isEqualToString:@"G"]) return @"g2.png";
    if([str isEqualToString:@"H"]) return @"h2.png";
    if([str isEqualToString:@"I"]) return @"i2.png";
    if([str isEqualToString:@"J"]) return @"j2.png";
    if([str isEqualToString:@"K"]) return @"k2.png";
    if([str isEqualToString:@"L"]) return @"l2.png";
    if([str isEqualToString:@"M"]) return @"m2.png";
    if([str isEqualToString:@"N"]) return @"n2.png";
    if([str isEqualToString:@"O"]) return @"o2.png";
    if([str isEqualToString:@"P"]) return @"p2.png";
    if([str isEqualToString:@"Q"]) return @"q2.png";
    if([str isEqualToString:@"R"]) return @"r2.png";
    if([str isEqualToString:@"S"]) return @"s2.png";
    if([str isEqualToString:@"T"]) return @"t2.png";
    if([str isEqualToString:@"U"]) return @"u2.png";
    if([str isEqualToString:@"V"]) return @"v2.png";
    if([str isEqualToString:@"W"]) return @"w2.png";
    if([str isEqualToString:@"X"]) return @"x2.png";
    if([str isEqualToString:@"Y"]) return @"y2.png";
    if([str isEqualToString:@"Z"]) return @"z2.png";
    if([str isEqualToString:@"."]) return @"a3.png";
    if([str isEqualToString:@","]) return @"b3.png";
    if([str isEqualToString:@"'"]) return @"c3.png";
    if([str isEqualToString:@"\""]) return @"d3.png";
    if([str isEqualToString:@"?"]) return @"e3.png";
    if([str isEqualToString:@"!"]) return @"f3.png";
    if([str isEqualToString:@"("]) return @"g3.png";
    if([str isEqualToString:@")"]) return @"h3.png";
    if([str isEqualToString:@":"]) return @"i3.png";
    if([str isEqualToString:@"..."]) return @"j3.png";
    return @"";
}
- (void) UpdateFrame {
    CGRect frame;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    if(x < 0) x=0;
    for(int i=0;i<60;i++) {
        W00[i].hidden=YES;
        dW00[i].hidden=YES;
        S00[i].hidden=YES;
    }
    if(len > 34) {
        frame = lframe.frame; frame.origin.y=200;
        frame.origin.x= x;
        frame.size.width=24;
        frame.size.height=160;
        lframe.frame= frame;
        
        frame = mframe.frame; frame.origin.y=200;
        frame.origin.x= x+24;
        frame.size.width=30*len-10;
        
        //frame.size.width=24;
        frame.size.height=160;
        mframe.frame= frame;
        
        frame = rframe.frame; frame.origin.y=200;
        frame.origin.x= x+30*len;
        frame.size.width=24;
        frame.size.height=160;
        
        rframe.frame= frame;
        
        frame = laTimes.frame; frame.origin.y=140;
        frame.origin.x= x;
        laTimes.frame= frame;
        
        frame = btTimes.frame; frame.origin.y=140;
        frame.origin.x= x;
        btTimes.frame= frame;
    } else {
        frame = lframe.frame; frame.origin.y=280;
        frame.origin.x= x;
        frame.size.width=24;
        frame.size.height=80;
        lframe.frame= frame;
        
        frame = mframe.frame; frame.origin.y=280;
        frame.origin.x= x+24;
        frame.size.width=30*len-10;
        
        //frame.size.width=24;
        frame.size.height=80;
        mframe.frame= frame;
        
        frame = rframe.frame; frame.origin.y=280;
        frame.origin.x= x+30*len;
        frame.size.width=24;
        frame.size.height=80;
        
        rframe.frame= frame;
        
        frame = laTimes.frame; frame.origin.y=220;
        frame.origin.x= x;
        laTimes.frame= frame;
        
        frame = btTimes.frame; frame.origin.y=220;
        frame.origin.x= x;
        btTimes.frame= frame;
    }

    
    frame = laStatus.frame; frame.origin.y=420;
    frame.origin.x= x;
    laStatus.frame= frame;
    
    frame = lacWord.frame; frame.origin.y=220;
    frame.origin.x= x;
    lacWord.frame= frame;
    
    frame = btSingal.frame; frame.origin.y=220;
    frame.origin.x= x+30*len-20;
    btSingal.frame= frame;
    
    btSingal.hidden=YES;
    lframe.hidden=NO;
    mframe.hidden=NO;
    rframe.hidden=NO;
    laTimes.hidden=YES;
    laStatus.hidden=YES;
    lacWord.hidden=NO;
    btTimes.hidden=YES;
}
- (void) UpdateWord {
    NSString *oneWord;
    CGRect frame;
    NSRange needleRange;
    int len=eword[nowIdx].length;
    stp.maximumValue=len;
    stp.minimumValue=1;
    stp.value=1;
    int y=280;
    int x=(1024-len*30)/2-12;
    if(x < 0) x=0;
    NSLog(@"EEWORD:%@",eword[nowIdx]);
    
    for(int i=0;i<60;i++) {
        W00[i].hidden=YES; I00[i].hidden=YES;
        
        if(len >= (i+1)) {
            frame = W00[i].frame;
            if(i<34) {
                if(len>34) frame.origin.y=203;
                else frame.origin.y=283;
                frame.origin.x= x+12+i*30;
                
            }
            else {
                frame.origin.y=283;
                frame.origin.x= x+12+(i-34)*30;
            }
            frame.size.width=25;
            frame.size.height=75;
            W00[i].frame= frame;
            dW00[i].frame=frame;
            I00[i].frame=frame;
            
            frame.origin.y=363;
            frame.size.width=25;
            frame.size.height=25;
            S00[i].frame=frame;
            
            needleRange = NSMakeRange(i,1);
            oneWord=[eword[nowIdx] substringWithRange:needleRange];
            I00[i].hidden=NO; I00[i].alpha=1.0f;
            S00[i].hidden=NO; S00[i].alpha=0.2f;
            /*
            if([oneWord isEqualToString:@"."]) I00[i].alpha=0.3f;
            if([oneWord isEqualToString:@","]) I00[i].alpha=0.3f;
            if([oneWord isEqualToString:@"'"]) I00[i].alpha=0.3f;
            if([oneWord isEqualToString:@"?"]) I00[i].alpha=0.3f;
            if([oneWord isEqualToString:@"!"]) I00[i].alpha=0.3f;
            if([oneWord isEqualToString:@" "]) I00[i].alpha=0.3f;
            */
            W00[i].image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
            wordinx[i]=[self getWordInx:oneWord];
            W00[i].hidden=!mark[nowIdx];
            
            [dW00[i] erase];
            dW00[i].hidden=NO;
        }
        S00[0].alpha=1.0f;
        tapsel=0;
    }
    //I00[17].image=[[UIImage imageNamed: @"j3.png"] rotate:UIImageOrientationUp];
    /*
     if(len>=1) {
     frame = W01.frame; frame.origin.y=283;
     frame.origin.x= x+12;
     frame.size.width=25;
     frame.size.height=75;
     W01.frame= frame;
     dW01.frame=frame;
     I01.frame=frame;
     needleRange = NSMakeRange(0,1);
     oneWord=[eword[nowIdx] substringWithRange:needleRange];
     W01.image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
     W01.hidden=!mark[nowIdx];
     I01.hidden=NO;
     [dW01 erase];
     dW01.hidden=NO;
     } else { W01.hidden=YES; I01.hidden=YES; }
     if(len>=2) {
     
     frame = W02.frame; frame.origin.y=283;
     frame.origin.x= x+12+30;
     frame.size.width=25;
     frame.size.height=75;
     W02.frame= frame;
     dW02.frame=frame;
     I02.frame=frame;
     needleRange = NSMakeRange(1,1);
     oneWord=[eword[nowIdx] substringWithRange:needleRange];
     W02.image=[[UIImage imageNamed: [self getWordPng:oneWord] ] rotate:UIImageOrientationUp];
     I02.hidden=NO;
     W02.hidden=!mark[nowIdx];
     [dW02 erase];
     dW02.hidden=NO;
     } else { W02.hidden=YES; I02.hidden=YES; }
     if(len>=3) {
     frame = W03.frame; frame.origin.y=283;
     frame.origin.x= x+12+60;
     frame.size.width=25;
     frame.size.height=75;
     W03.frame= frame;
     dW03.frame=frame;
     I03.frame=frame;
     needleRange = NSMakeRange(2,1);
     oneWord=[eword[nowIdx] substringWithRange:needleRange];
     W03.image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
     I03.hidden=NO;
     W03.hidden=!mark[nowIdx];
     [dW03 erase];
     dW03.hidden=NO;
     } else { W03.hidden=YES; I03.hidden=YES; }
     if(len>=4) {
     frame = W04.frame; frame.origin.y=283;
     frame.origin.x= x+12+90;
     frame.size.width=25;
     frame.size.height=75;
     W04.frame= frame;
     dW04.frame=frame;
     I04.frame=frame;
     needleRange = NSMakeRange(3,1);
     oneWord=[eword[nowIdx] substringWithRange:needleRange];
     W04.image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
     W04.hidden=!mark[nowIdx];
     I04.hidden=NO;
     [dW04 erase];
     dW04.hidden=NO;
     } else { W04.hidden=YES; I04.hidden=YES; }
     if(len>=5) {
     frame = W05.frame; frame.origin.y=283;
     frame.origin.x= x+12+120;
     frame.size.width=25;
     frame.size.height=75;
     W05.frame= frame;
     dW05.frame=frame;
     I05.frame=frame;
     needleRange = NSMakeRange(4,1);
     oneWord=[eword[nowIdx] substringWithRange:needleRange];
     W05.image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
     I05.hidden=NO;
     W05.hidden=!mark[nowIdx];
     [dW05 erase];
     dW05.hidden=NO;
     
     } else { W05.hidden=YES; I05.hidden=YES; }
     */
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
    
}
-(void)timesUp:(NSTimer *)timer{
    timecnt++;
    switch(timecnt) {
        case 5: //0.5
            laTimes.text=[NSString stringWithFormat:@"%d",test01];
            if(ShowCH==1) {
                lacWord.text=cword[nowIdx];
            }
            else lacWord.text=@"";
            laStatus.text=@"Listen";
            [self UpdateFrame];
            [self UpdateWord];
            SoundStep=0;
            if((gtype==0)|(gtype==1)) { timecnt=6; waitcnt=600;}
            break;
        case 6:
        {
            NSString *base = DirSound;
            NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[song[nowIdx]][SoundStep]];
            NSURL *soundUrl = [NSURL fileURLWithPath:path];
            _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
            [_audioPlayer setVolume:1.0];
            //if(wcount == 76) [_audioPlayer setVolume:1.0];
            //else [_audioPlayer setVolume:0.0];
            [_audioPlayer play];
        }
            break;
        case 7:
            if(_audioPlayer.isPlaying) { waitcnt=600; timecnt=6; }
            else {
                SoundStep++;
                if(SayCH==1)
                   if(SoundStep < scnt[song[nowIdx]]) timecnt=5;
            }
            break;
        case 8:
        
            if(waitcnt!=0) {
                timecnt=7;
                waitcnt--;
            } else {
                
                int len=eword[nowIdx].length;
                int lcnt=0;
                for(int i=0;i<len;i++) {
                    [dW00[i] isWorNumber:wordinx[i]];
                    lcnt+=[dW00[i] isWord:W00[i].image];
                }
                NSLog(@"Total:%d,Len:%d",lcnt,len);
                lcnt/=len;
                if(lcnt >= 80) {
                    [btSingal setImage:[UIImage imageNamed:@"O_Red.png"] forState:UIControlStateNormal];
                    btSingal.hidden=NO;
                    TotalPass++;
                } else {
                    [btSingal setImage:[UIImage imageNamed:@"wait_red.png"] forState:UIControlStateNormal];
                    btSingal.hidden=NO;
                    TotalNG++;
                }
                laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
                laPass.text=[NSString stringWithFormat:@"%04d",TotalPass];
                laStatus.text=eword[nowIdx];
                laStatus.hidden=NO;
                    
                waitcnt=0; //SYSPARA4;
                timecnt=30;
            }
            break;
        case 55:
            if(nowIdx==(wcount-1)) timecnt=99;
            else {
                if(waitcnt==0) {
                    nowIdx++;
                    timecnt=4;
                } else {
                    timecnt=54;
                    waitcnt--;
                }
            }
            break;
        case 100:
        {
            /*
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"本單元測試結束" delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
            [alertView show];
             */
            [cTimer invalidate];
            cTimer = nil;
            [self publishCommandClassStop];
            [self dismissViewControllerAnimated:NO completion:nil];
        }
            break;
        case 102:
            timecnt=101;
            break;
            
    }
    //NSLog(@"TimeUp");
}
/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */


- (IBAction)btTest:(id)sender {
    
    /*
     UIGraphicsBeginImageContext(self.drawSignView.bounds.size);
     [[self.drawSignView.layer presentationLayer] renderInContext:UIGraphicsGetCurrentContext()];
     UIImage *viewImage = UIGraphicsGetImageFromCurrentImageContext();
     UIGraphicsEndImageContext();
     NSData *postData = UIImageJPEGRepresentation(viewImage, 1.0);
     */
}
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    [cTimer invalidate];
    cTimer = nil;
    [self dismissViewControllerAnimated:NO completion:nil];
    
}
- (IBAction)btBack:(id)sender {
    /*
    [cTimer invalidate];
    cTimer = nil;
    [session publishData:[[[NSString alloc] initWithFormat:(@"$%@,CLASS_STOP,%@,end~"),uten_class,ID] dataUsingEncoding:NSUTF8StringEncoding] onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
     }];
    [self dismissViewControllerAnimated:NO completion:nil];
     */
}

- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    // 收到中斷訊息
    if (command.isBye) {
        [cTimer invalidate];
        cTimer = nil;
        [self publishCommandClassStop];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

// publish class start
- (void)publishCommandClassStart {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStart;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

// publish command class stop
- (void)publishCommandClassStop {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Publish error: %@", error);
        }
    }];
}

/*
if([str isEqualToString:@"."]) return @"a3.png";
if([str isEqualToString:@","]) return @"b3.png";
if([str isEqualToString:@"'"]) return @"c3.png";
if([str isEqualToString:@"\""]) return @"d3.png";
if([str isEqualToString:@"?"]) return @"e3.png";
if([str isEqualToString:@"!"]) return @"f3.png";
if([str isEqualToString:@"("]) return @"g3.png";
if([str isEqualToString:@")"]) return @"h3.png";
if([str isEqualToString:@":"]) return @"i3.png";
if([str isEqualToString:@"..."]) return @"j3.png";
*/
- (IBAction)btS05:(id)sender {
  //  int v=stp.value-1;
    [dW00[tapsel] erase];
}
- (IBAction)btS01:(id)sender {
  //  int v=stp.value-1;
    dW00[tapsel].drawImage.image=[UIImage imageNamed:@"f3.png"];
}
- (IBAction)btS02:(id)sender {
   // int v=stp.value-1;
    dW00[tapsel].drawImage.image=[UIImage imageNamed:@"b3.png"];
}
- (IBAction)btS04:(id)sender {
   // int v=stp.value-1;
    dW00[tapsel].drawImage.image=[UIImage imageNamed:@"a3.png"];
}
- (IBAction)btS03:(id)sender {
   // int v=stp.value-1;
    dW00[tapsel].drawImage.image=[UIImage imageNamed:@"c3.png"];
}
- (IBAction)btS06:(id)sender {
   // int v=stp.value-1;
    dW00[tapsel].drawImage.image=[UIImage imageNamed:@"e3.png"];
}
- (IBAction)stpvc:(id)sender {
    int v=stp.value;
    inxWord.text=[NSString stringWithFormat:@"%d",v];
}
-(void)handleTap:(id)sender{
    UITapGestureRecognizer *tap = sender;
    UIImageView *imgView = (UIImageView*)tap.view;
    for(int i=0;i<200;i++) {
        S00[i].alpha=0.2f;
    }
    imgView.alpha=1.0f;
    NSLog(@"Tap it:%d",imgView.tag);
    tapsel=(int)imgView.tag;
   // [UIImage imageNamed:@"O_Red.png"];
}
@end
