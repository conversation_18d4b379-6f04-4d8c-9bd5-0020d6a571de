//
//  RL01ViewController.m
//  uten
//
//  Created by 蔡駿寓 on 2024/5/16.
//  Copyright © 2024 bekubee. All rights reserved.
//

//#import "RL01ViewController.h"
//
//@interface RL01ViewController ()
//
//@end
//
//@implementation RL01ViewController
//
//- (void)viewDidLoad {
//    [super viewDidLoad];
//}
//
//@end

#import "RL01ViewController.h"
#import "OHMySQL.h"
#import <QuartzCore/QuartzCore.h>



@interface RL01ViewController ()

@property (weak, nonatomic) IBOutlet UIImageView *imageView1;
@property (weak, nonatomic) IBOutlet UIImageView *imageView2;
@property (weak, nonatomic) IBOutlet UIImageView *imageView3;
@property (weak, nonatomic) IBOutlet UIImageView *imageView4;
@property (weak, nonatomic) IBOutlet UIImageView *imageView5;

@property (weak, nonatomic) IBOutlet UIImageView *imageView;
@property (strong, nonatomic) NSArray *imageNames;
@property (nonatomic) NSInteger currentImageIndex;
@property (strong, nonatomic) NSTimer *timer;

@end

@implementation RL01ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // 設置每個圖片視圖的圖片
    self.imageView1.image = [UIImage imageNamed:@"RankingList_011.png"];
    self.imageView2.image = [UIImage imageNamed:@"RankingList_031.png"];
    self.imageView3.image = [UIImage imageNamed:@"RankingList_041.png"];
    self.imageView4.image = [UIImage imageNamed:@"RankingList_031.png"];
    self.imageView5.image = [UIImage imageNamed:@"RankingList_041.png"];

    // 啟動每個圖片視圖的旋轉動畫
    [self startRotatingImageView:self.imageView1 withDuration:20.0];
    [self startRotatingImageView:self.imageView2 withDuration:20.0];
    [self startRotatingImageView:self.imageView3 withDuration:10.0];
    [self startRotatingImageView:self.imageView4 withDuration:25.0];
    [self startRotatingImageView:self.imageView5 withDuration:15.0];
    
        // 初始化圖片名稱數組
        self.imageNames = @[@"RankingList_0211.png", @"RankingList_0212.png", @"RankingList_0213.png", @"RankingList_0214.png"];
        self.currentImageIndex = 0;

        // 設置初始圖片
        self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];

        // 設置定時器以自動切換圖片
        self.timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(nextImage) userInfo:nil repeats:YES];
    
}

- (void)startRotatingImageView:(UIImageView *)imageView withDuration:(CGFloat)duration {
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.fromValue = @(0);
    rotationAnimation.toValue = @(M_PI * 2);
    rotationAnimation.fillMode = kCAFillModeForwards;//加2行.確保回歸原位
    rotationAnimation.removedOnCompletion = NO;//加2行.確保回歸原位
    rotationAnimation.duration = duration;
    rotationAnimation.repeatCount = INFINITY;
    [imageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
}
- (void)nextImage {
    // 更新圖片索引
    self.currentImageIndex = (self.currentImageIndex + 1) % self.imageNames.count;
    
    // 設置圖片視圖為下一張圖片
    self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];
}

- (void)dealloc {
    // 確保定時器被正確無誤地停止
    [self.timer invalidate];
    self.timer = nil;
}



















- (IBAction)btBack:(id)sender {
     [self dismissViewControllerAnimated:NO completion:nil];
}

@end


