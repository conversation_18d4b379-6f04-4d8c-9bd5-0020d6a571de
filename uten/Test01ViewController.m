//
//  Test01ViewController.m
//  uten
//
//  Created by <PERSON> on 2018/12/1.
//  Copyright © 2018 bekubee. All rights reserved.
//

#import "Test01ViewController.h"
#import "WriterViewController.h"
#import "WriteMutilViewController.h"
#import "WriteMutilWordViewController.h"
#import "WriteSentenceViewController.h"
#import "WriteWordViewController.h"
#import "SayViewController.h"
@interface Test01ViewController () {
    IBOutlet UIView *alertClass;
    IBOutlet UIImageView *faceView;
    IBOutlet UIPageControl *UPC;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    IBOutlet UIButton *btUpdate;
    IBOutlet UIButton *class00;
    IBOutlet UIButton *class01;
    IBOutlet UIButton *class02;
    IBOutlet UIButton *class03;
    IBOutlet UIButton *class04;
    IBOutlet UIButton *class05;
    IBOutlet UIButton *class06;
    IBOutlet UIButton *class07;
    IBOutlet UIButton *class08;
    IBOutlet UIButton *class09;
    IBOutlet UIButton *class10;
    IBOutlet UIButton *class11;
    IBOutlet UIButton *class12;
    IBOutlet UIButton *class13;
    IBOutlet UIButton *class14;
    IBOutlet UIButton *class15;
    IBOutlet UIButton *class16;
    IBOutlet UIButton *class17;
    IBOutlet UIButton *class18;
    IBOutlet UIButton *class19;
    IBOutlet UIButton *class20;
    IBOutlet UILabel *lt00;
    IBOutlet UILabel *lt01;
    IBOutlet UILabel *lt02;
    IBOutlet UILabel *lt03;
    IBOutlet UILabel *lt04;
    IBOutlet UILabel *lt05;
    IBOutlet UILabel *lt06;
    IBOutlet UILabel *lt07;
    IBOutlet UILabel *lt08;
    IBOutlet UILabel *lt09;
    IBOutlet UILabel *lt10;
    IBOutlet UILabel *lt11;
    IBOutlet UILabel *lt12;
    IBOutlet UILabel *lt13;
    IBOutlet UILabel *lt14;
    IBOutlet UILabel *lt15;
    IBOutlet UILabel *lt16;
    IBOutlet UILabel *lt17;
    IBOutlet UILabel *lt18;
    IBOutlet UILabel *lt19;
    IBOutlet UILabel *lt20;
    IBOutlet UILabel *ld00;
    IBOutlet UILabel *ld01;
    IBOutlet UILabel *ld02;
    IBOutlet UILabel *ld03;
    IBOutlet UILabel *ld04;
    IBOutlet UILabel *ld05;
    IBOutlet UILabel *ld06;
    IBOutlet UILabel *ld07;
    IBOutlet UILabel *ld08;
    IBOutlet UILabel *ld09;
    IBOutlet UILabel *ld10;
    IBOutlet UILabel *ld11;
    IBOutlet UILabel *ld12;
    IBOutlet UILabel *ld13;
    IBOutlet UILabel *ld14;
    IBOutlet UILabel *ld15;
    IBOutlet UILabel *ld16;
    IBOutlet UILabel *ld17;
    IBOutlet UILabel *ld18;
    IBOutlet UILabel *ld19;
    IBOutlet UILabel *ld20;
    
    //
    NSString *DirTest;
    //
    int cType[21];
    UIButton *btClass0[21];
    UILabel  *lvt0[21];
    UILabel  *lvd0[21];
    int mode;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    UIActivityIndicatorView *indicator;
    int downcount;
    NSString *classContent;
    NSString *upgradeContent;
}
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation Test01ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    if([TYPE intValue] < 10) btUpdate.hidden=NO;
    else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    //
    classContent=@"";
    // Do any additional setup after loading the view.
    btClass0[0]=class00; btClass0[1]=class01; btClass0[2]=class02; btClass0[3]=class03;
    btClass0[4]=class04; btClass0[5]=class05; btClass0[6]=class06; btClass0[7]=class07;
    btClass0[8]=class08; btClass0[9]=class09; btClass0[10]=class10; btClass0[11]=class11;
    btClass0[12]=class12; btClass0[13]=class13; btClass0[14]=class14; btClass0[15]=class15;
    btClass0[16]=class16; btClass0[17]=class17; btClass0[18]=class18; btClass0[19]=class19;
    btClass0[20]=class20;
    lvt0[0]=lt00; lvt0[1]=lt01; lvt0[2]=lt02; lvt0[3]=lt03; lvt0[4]=lt04; lvt0[5]=lt05; lvt0[6]=lt06;
    lvt0[7]=lt07; lvt0[8]=lt08; lvt0[9]=lt09; lvt0[10]=lt10; lvt0[11]=lt11; lvt0[12]=lt12; lvt0[13]=lt13;
    lvt0[14]=lt14; lvt0[15]=lt15; lvt0[16]=lt16; lvt0[17]=lt17; lvt0[18]=lt18; lvt0[19]=lt19; lvt0[20]=lt20;
    lvd0[0]=ld00; lvd0[1]=ld01; lvd0[2]=ld02; lvd0[3]=ld03; lvd0[4]=ld04; lvd0[5]=ld05; lvd0[6]=ld06;
    lvd0[7]=ld07; lvd0[8]=ld08; lvd0[9]=ld09; lvd0[10]=ld10; lvd0[11]=ld11; lvd0[12]=ld12; lvd0[13]=ld13;
    lvd0[14]=ld14; lvd0[15]=ld15; lvd0[16]=ld16; lvd0[17]=ld17; lvd0[18]=ld18; lvd0[19]=ld19; lvd0[20]=ld20;
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",_uclass,_uclass]];
    
    NSString *DbgTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class/l1u1u4//l1u1u4"]];
    NSLog(@"After FLIST:%@",[[NSFileManager defaultManager] contentsOfDirectoryAtPath:DbgTest error:NULL]);

    int scnt=0;
    for(int i=0;i<100;i++) {
        NSString *localFilePath = [DirTest stringByAppendingPathComponent:[NSString stringWithFormat:@"l%03d.csv",i+1]];
        NSString* content = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
        if(content!=nil) scnt++;
    }
    if((scnt%21)==0)
        UPC.numberOfPages=(scnt/21);
    else UPC.numberOfPages=(scnt/21)+1;
    UPC.currentPage=0;
    [self updateTitle];
    NSLog(@"%@", _uclass);
    UISwipeGestureRecognizer *swipeLeft = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(didSwipe:)];
    swipeLeft.direction = UISwipeGestureRecognizerDirectionLeft;
    [self.view addGestureRecognizer:swipeLeft];
    
    UISwipeGestureRecognizer *swipeRight = [[UISwipeGestureRecognizer alloc] initWithTarget:self  action:@selector(didSwipe:)];
    swipeRight.direction = UISwipeGestureRecognizerDirectionRight;
    [self.view addGestureRecognizer:swipeRight];
    
}
-(void) updateTitle {
    for(int i=0;i<21;i++) {
        [btClass0[i] setImage:[UIImage imageNamed: @"ispeak-day_green.png"] forState:UIControlStateNormal];
        btClass0[i].hidden=TRUE;
        lvt0[i].hidden=TRUE;
        lvd0[i].hidden=TRUE;
    }
    for(int i=0;i<21;i++) {
        NSString *localFilePath = [DirTest stringByAppendingPathComponent:[NSString stringWithFormat:@"l%03d.csv",i+1+UPC.currentPage*21]];
        NSString* content = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
        NSArray *splitLine = [content componentsSeparatedByString:@"\n"];
        if([splitLine count] >= 4) {
            NSArray *split1=[splitLine[1] componentsSeparatedByString:@","];
            NSArray *splitT=[splitLine[2] componentsSeparatedByString:@","];
            NSArray *split2=[splitLine[3] componentsSeparatedByString:@","];
            btClass0[i].hidden=FALSE;
            lvt0[i].hidden=FALSE;
            NSString *str=@"";
            cType[i]=[split1[1] intValue];
            /*
            switch([split1[1] intValue]) {
                case 1:  str=[NSString stringWithFormat:@"%@-寫字組",[NSString stringWithFormat:@"%03d",i+1+UPC.currentPage*21]]; break;
                case 2:  str=[NSString stringWithFormat:@"%@-寫單字",[NSString stringWithFormat:@"%03d",i+1+UPC.currentPage*21]]; break;
                case 3:  str=[NSString stringWithFormat:@"%@-寫練寫",[NSString stringWithFormat:@"%03d",i+1+UPC.currentPage*21]]; break;
                case 11: str=[NSString stringWithFormat:@"%@-說字組",[NSString stringWithFormat:@"%03d",i+1+UPC.currentPage*21]]; break;
                case 12: str=[NSString stringWithFormat:@"%@-說單字",[NSString stringWithFormat:@"%03d",i+1+UPC.currentPage*21]]; break;
                case 13: str=[NSString stringWithFormat:@"%@-說練寫",[NSString stringWithFormat:@"%03d",i+1+UPC.currentPage*21]]; break;

                default: str=[NSString stringWithFormat:@"%@",_uclass]; break;
            }
            */
            lvt0[i].text=splitT[0];
            lvd0[i].hidden=FALSE;
            /*
            NSString *str1=@"";
            for(int k=0;k<[split1[0] intValue];k++) {
                if(k==0) str1=[NSString stringWithFormat:@"%@",split2[k]];
                else str1=[NSString stringWithFormat:@"%@/%@",str1,split2[k]];
            }
            NSString *strX=[NSString stringWithFormat:@"%@",str1];
            if([str1 length] > 13) {
                strX=[NSString stringWithFormat:@"%@\n%@",[str1 substringToIndex:13],[str1 substringFromIndex:13]];
                
            }
             lvd0[i].text=strX;
             */
            lvd0[i].text=splitT[1];
            
        }
        
    }
}
- (void)didSwipe:(UISwipeGestureRecognizer*)swipe{
    
    if (swipe.direction == UISwipeGestureRecognizerDirectionLeft) {
        if(UPC.numberOfPages > UPC.currentPage) UPC.currentPage++;
         [self updateTitle];
        NSLog(@"Swipe Left");
    } else if (swipe.direction == UISwipeGestureRecognizerDirectionRight) {
        if(UPC.currentPage > 0) UPC.currentPage--;
         [self updateTitle];
        NSLog(@"Swipe Right");
    } else if (swipe.direction == UISwipeGestureRecognizerDirectionUp) {
        NSLog(@"Swipe Up");
    } else if (swipe.direction == UISwipeGestureRecognizerDirectionDown) {
        NSLog(@"Swipe Down");
    }
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btBack:(id)sender {
   // [self dismissViewControllerAnimated:NO completion:nil];
}
- (IBAction)btGotoNext:(id)sender {
    UIButton *button = (UIButton *)sender;
    _seltag = [NSString stringWithFormat:[NSString stringWithFormat:@"%d", button.tag+UPC.currentPage*21]];
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    //cType[button.tag]=11;
    if(cType[button.tag]<10) {
        cType[button.tag]=3;
        if(cType[button.tag]==1) {
            WriteWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteWordViewController"];
            [myViewController setValue:_seltag forKey:@"seltag"];
            [myViewController setValue:_uclass forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if(cType[button.tag]==2) {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            [myViewController setValue:_seltag forKey:@"seltag"];
            [myViewController setValue:_uclass forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if(cType[button.tag]==3) {
            WriteMutilWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilWordViewController"];
            [myViewController setValue:_seltag forKey:@"seltag"];
            [myViewController setValue:_uclass forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if(cType[button.tag]==4) {
            WriteSentenceViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteSentenceViewController"];
            [myViewController setValue:_seltag forKey:@"seltag"];
            [myViewController setValue:_uclass forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if(cType[button.tag]==5) {
            WriteMutilViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilViewController"];
            [myViewController setValue:_seltag forKey:@"seltag"];
            [myViewController setValue:_uclass forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
    } else {
        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
        _cType=[NSString stringWithFormat:@"%d", cType[button.tag]];
        [myViewController setValue:_cType forKey:@"cType"];
        [myViewController setValue:_seltag forKey:@"seltag"];
        [myViewController setValue:_uclass forKey:@"uclass"];
        [self presentViewController:myViewController animated:YES completion:nil];
    }
}
@end
