//
//  eStudentViewController.m
//  uten
//
//  Created by <PERSON> on 2019/8/1.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "eStudentViewController.h"
#import "OHMySQL.h"
@interface eStudentViewController () {
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    Boolean isUten;
    
    __weak IBOutlet UIButton *pStartDate;
    __weak IBOutlet UITextField *pClassLevel;
    __weak IBOutlet UITextField *pSeniority;
    __weak IBOutlet UITextField *pSchool;
    __weak IBOutlet UITextField *pClass;
    
    __weak IBOutlet UIButton *pct_study;
    __weak IBOutlet UIButton *pct_toll;
    __weak IBOutlet UIButton *pct_transfer;
    
    __weak IBOutlet UITextField *pct_daddy;
    __weak IBOutlet UITextField *ptel_daddy;
    __weak IBOutlet UITextField *pct_mon;
    __weak IBOutlet UITextField *ptel_mon;
    __weak IBOutlet UITextField *pct_grandfather;
    __weak IBOutlet UITextField *ptel_grandfather;
    __weak IBOutlet UITextField *pct_grandmonther;
    __weak IBOutlet UITextField *ptel_grandmonther;
    __weak IBOutlet UITextField *paddr_monther;
    __weak IBOutlet UITextField *paddr_grandmonther;
    
    __weak IBOutlet UITextField *pRemark1;
    __weak IBOutlet UITextField *pRemark2;
    __weak IBOutlet UITextField *pRemark3;
    __weak IBOutlet UITextField *pRemark4;
    
    __weak IBOutlet UIButton *pButton;
    __weak IBOutlet UITextField *pName;
    __weak IBOutlet UIButton *pButtonDelete;
    int state;
    int messageid;
    
}
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation eStudentViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    isUten=false;
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
   //
    /*
    OHMySQLUser *user;
    if(isUten) {
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                           password:@"1qazXSW@3edcVFR$"
                                         serverName:@"utenServer"
                                             dbName:@"uten"
                                               port:3307
                                             socket:@"/run/mysqld/mysqld10.sock"];
    } else {
        user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                           password:@"1qazXSW@3edcVFR$"
                                         serverName:@"114.32.242.189"
                                             dbName:@"uten"
                                               port:3307
                                             socket:@"/run/mysqld/mysqld10.sock"];
        
    }
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"student" condition:nil];
    NSError *error = nil;
    NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
    NSLog(@"%@",queryContext);
    for(int i=0;i<[tasks count];i++) {
        NSDictionary *dict = [tasks objectAtIndex:i];
        NSLog(@"id:%@",[dict objectForKey:@"id"]);
        NSLog(@"name:%@",[[NSString alloc] initWithData:[dict objectForKey:@"name"]  encoding:NSUTF8StringEncoding]);
    }
     */
    //
    state=1;
    [self ClearEditItem];
    //
}
- (IBAction)btDelete:(id)sender {
    messageid=2;
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要刪除紀錄還是要退出編輯" delegate:self cancelButtonTitle:@"退出編輯" otherButtonTitles:@"刪除本記錄",@"取消",nil];
    [alertView show];
}
- (IBAction)btButton:(id)sender {
    switch(state)
    {
        case 1:
        {
            [self ClearEditItem];
            pButtonDelete.hidden=false;
            [pButton setImage:[UIImage imageNamed:@"btoko.png"] forState:UIControlStateNormal];
            state=2;
        }
            break;
        case 2:
        {
            messageid=1;
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否存檔" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"存檔",nil];
            [alertView show];
        }
            break;
    }
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    
    switch (buttonIndex) {
        case 0:
            switch(messageid) {
                case 2:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btaddo.png"] forState:UIControlStateNormal];
                    state=1;
                }
                    break;
            }
            break;
        case 1:
            switch(messageid) {
                case 1:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btaddo.png"] forState:UIControlStateNormal];
                    state=1;
                    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"存檔完成!!" delegate:self cancelButtonTitle:@"確定" otherButtonTitles:nil];
                    [alertView show];
                }
                    break;
                case 2:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btaddo.png"] forState:UIControlStateNormal];
                    state=1;
                    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"刪除成功!!" delegate:self cancelButtonTitle:@"確定" otherButtonTitles:nil];
                    [alertView show];
                }
                    break;
            }
            break;
        default:
            break;
    }
    
}
-(void) ClearEditItem {
    pName.text=@"";
    [pStartDate setTitle:@"" forState:UIControlStateNormal];
    [pct_study setTitle:@"" forState:UIControlStateNormal];
    [pct_toll setTitle:@"" forState:UIControlStateNormal];
    [pct_transfer setTitle:@"" forState:UIControlStateNormal];
    [pStartDate setTitle:@"" forState:UIControlStateNormal];
    pClassLevel.text=@"";
    pSeniority.text=@"";
    pSchool.text=@"";
    pClass.text=@"";
    pct_daddy.text=@"";
    ptel_daddy.text=@"";
    pct_mon.text=@"";
    ptel_mon.text=@"";
    pct_grandfather.text=@"";
    ptel_grandfather.text=@"";
    pct_grandmonther.text=@"";
    ptel_grandmonther.text=@"";
    paddr_monther.text=@"";
    paddr_grandmonther.text=@"";
    pRemark1.text=@"";
    pRemark2.text=@"";
    pRemark3.text=@"";
    pRemark4.text=@"";
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {

    [self dismissViewControllerAnimated:NO completion:nil];
}
@end
