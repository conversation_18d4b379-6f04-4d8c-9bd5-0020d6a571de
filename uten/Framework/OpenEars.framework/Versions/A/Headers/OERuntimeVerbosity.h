//
//  OERuntimeVerbosity.h
//  OpenEars
//
//
//  Copyright Politepix UG (haftungsbeschränkt) 2014. All rights reserved.
//  http://www.politepix.com
//  Contact at http://www.politepix.com/contact
//
//  this file is licensed under the Politepix Shared Source license found 
//  found in the root of the source distribution. Please see the file "Version.txt" in the root of 
//  the source distribution for the version number of this OpenEars package.
/**\cond HIDDEN_SYMBOLS*/
#ifndef RuntimeVerbosity_h
#define RuntimeVerbosity_h

#define OEBoolStr(b) ((b) ? @"TRUE" : @"FALSE")
#define OEObjectNilness(b) ((b == nil) ? @"nil" : @"not nil")

#endif
/**\endcond */
