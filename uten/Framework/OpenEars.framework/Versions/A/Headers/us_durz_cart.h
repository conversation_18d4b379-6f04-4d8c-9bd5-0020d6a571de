/*************************************************************************/
/*                                                                       */
/*                   Carnegie Mellon University and                      */
/*                Centre for Speech Technology Research                  */
/*                     University of Edinburgh, UK                       */
/*                       Copyright (c) 1998-2001                         */
/*                        All Rights Reserved.                           */
/*                                                                       */
/*  Permission is hereby granted, free of charge, to use and distribute  */
/*  this software and its documentation without restriction, including   */
/*  without limitation the rights to use, copy, modify, merge, publish,  */
/*  distribute, sublicense, and/or sell copies of this work, and to      */
/*  permit persons to whom this work is furnished to do so, subject to   */
/*  the following conditions:                                            */
/*   1. The code must retain the above copyright notice, this list of    */
/*      conditions and the following disclaimer.                         */
/*   2. Any modifications must be clearly marked as such.                */
/*   3. Original authors' names are not deleted.                         */
/*   4. The authors' names are not used to endorse or promote products   */
/*      derived from this software without specific prior written        */
/*      permission.                                                      */
/*                                                                       */
/*  THE UNIVERSITY OF EDINBURGH, <PERSON><PERSON><PERSON><PERSON><PERSON> MELLON UNIVERSITY AND THE      */
/*  CONTRIBUTORS TO THIS WORK DISCLAIM ALL WARRANTIES WITH REGARD TO     */
/*  THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY   */
/*  AND FITNESS, IN NO EVENT SHALL THE UNIVERSITY OF EDINBURGH, CARNEGIE */
/*  MELLON UNIVERSITY NOR THE CONTRIBUTORS BE LIABLE FOR ANY SPECIAL,    */
/*  INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER          */
/*  RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN  AN ACTION   */
/*  OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF     */
/*  OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.       */
/*                                                                       */
/*************************************************************************/
/*             Author:  Alan W Black (<EMAIL>)                    */
/*               Date:  January 2001                                     */
/*************************************************************************/
/*******************************************************/
/**  Autogenerated cart tree for us_durz    */
/**  from .    */
/*******************************************************/

DEF_STATIC_CONST_VAL_STRING(val_0000,"pau");
DEF_STATIC_CONST_VAL_STRING(val_0001,"+");
DEF_STATIC_CONST_VAL_FLOAT(val_0002,-0.500000);
#define CTNODE_NO_0001 3
DEF_STATIC_CONST_VAL_STRING(val_0003,"BB");
DEF_STATIC_CONST_VAL_FLOAT(val_0004,2.000000);
#define CTNODE_NO_0003 5
DEF_STATIC_CONST_VAL_FLOAT(val_0005,0.000000);
#define CTNODE_NO_0000 6
DEF_STATIC_CONST_VAL_STRING(val_0006,"0");
DEF_STATIC_CONST_VAL_FLOAT(val_0007,1.500000);
DEF_STATIC_CONST_VAL_STRING(val_0008,"n");
DEF_STATIC_CONST_VAL_STRING(val_0009,"f");
DEF_STATIC_CONST_VAL_FLOAT(val_0010,-0.783163);
#define CTNODE_NO_0011 13
DEF_STATIC_CONST_VAL_FLOAT(val_0011,-0.222704);
#define CTNODE_NO_0010 14
DEF_STATIC_CONST_VAL_STRING(val_0012,"s");
DEF_STATIC_CONST_VAL_STRING(val_0013,"2");
DEF_STATIC_CONST_VAL_FLOAT(val_0014,0.764459);
#define CTNODE_NO_0015 17
DEF_STATIC_CONST_VAL_FLOAT(val_0015,0.700000);
DEF_STATIC_CONST_VAL_FLOAT(val_0016,0.567944);
#define CTNODE_NO_0017 19
DEF_STATIC_CONST_VAL_FLOAT(val_0017,0.053027);
#define CTNODE_NO_0014 20
DEF_STATIC_CONST_VAL_STRING(val_0018,"l");
DEF_STATIC_CONST_VAL_STRING(val_0019,"1");
DEF_STATIC_CONST_VAL_FLOAT(val_0020,0.766486);
#define CTNODE_NO_0021 23
DEF_STATIC_CONST_VAL_FLOAT(val_0021,0.279248);
#define CTNODE_NO_0023 25
DEF_STATIC_CONST_VAL_FLOAT(val_0022,0.056777);
#define CTNODE_NO_0020 26
DEF_STATIC_CONST_VAL_STRING(val_0023,"coda");
DEF_STATIC_CONST_VAL_FLOAT(val_0024,-0.038356);
#define CTNODE_NO_0027 29
DEF_STATIC_CONST_VAL_FLOAT(val_0025,-0.545853);
#define CTNODE_NO_0029 31
DEF_STATIC_CONST_VAL_FLOAT(val_0026,-0.765994);
#define CTNODE_NO_0026 32
DEF_STATIC_CONST_VAL_STRING(val_0027,"det");
DEF_STATIC_CONST_VAL_FLOAT(val_0028,0.300000);
DEF_STATIC_CONST_VAL_FLOAT(val_0029,1.000000);
DEF_STATIC_CONST_VAL_FLOAT(val_0030,0.160195);
#define CTNODE_NO_0034 36
DEF_STATIC_CONST_VAL_FLOAT(val_0031,0.713958);
#define CTNODE_NO_0033 37
DEF_STATIC_CONST_VAL_FLOAT(val_0032,-0.215472);
#define CTNODE_NO_0032 38
DEF_STATIC_CONST_VAL_STRING(val_0033,"r");
DEF_STATIC_CONST_VAL_FLOAT(val_0034,0.092772);
#define CTNODE_NO_0038 40
DEF_STATIC_CONST_VAL_FLOAT(val_0035,0.001328);
#define CTNODE_NO_0040 42
DEF_STATIC_CONST_VAL_FLOAT(val_0036,-0.334898);
#define CTNODE_NO_0009 43
DEF_STATIC_CONST_VAL_FLOAT(val_0037,0.500000);
DEF_STATIC_CONST_VAL_FLOAT(val_0038,0.200000);
DEF_STATIC_CONST_VAL_FLOAT(val_0039,-0.041618);
#define CTNODE_NO_0045 47
DEF_STATIC_CONST_VAL_FLOAT(val_0040,2.300000);
DEF_STATIC_CONST_VAL_FLOAT(val_0041,0.262952);
#define CTNODE_NO_0047 49
DEF_STATIC_CONST_VAL_FLOAT(val_0042,0.594794);
#define CTNODE_NO_0044 50
DEF_STATIC_CONST_VAL_STRING(val_0043,"mid");
DEF_STATIC_CONST_VAL_FLOAT(val_0044,-0.760444);
#define CTNODE_NO_0051 53
DEF_STATIC_CONST_VAL_FLOAT(val_0045,6.800000);
DEF_STATIC_CONST_VAL_STRING(val_0046,"a");
DEF_STATIC_CONST_VAL_FLOAT(val_0047,-0.450449);
#define CTNODE_NO_0054 56
DEF_STATIC_CONST_VAL_FLOAT(val_0048,1.300000);
DEF_STATIC_CONST_VAL_FLOAT(val_0049,-0.296956);
#define CTNODE_NO_0057 59
DEF_STATIC_CONST_VAL_FLOAT(val_0050,2.400000);
DEF_STATIC_CONST_VAL_FLOAT(val_0051,0.042287);
#define CTNODE_NO_0059 61
DEF_STATIC_CONST_VAL_FLOAT(val_0052,-0.154465);
#define CTNODE_NO_0056 62
DEF_STATIC_CONST_VAL_FLOAT(val_0053,3.600000);
DEF_STATIC_CONST_VAL_FLOAT(val_0054,1.200000);
DEF_STATIC_CONST_VAL_FLOAT(val_0055,-0.264199);
#define CTNODE_NO_0063 65
DEF_STATIC_CONST_VAL_FLOAT(val_0056,-0.541738);
#define CTNODE_NO_0062 66
DEF_STATIC_CONST_VAL_FLOAT(val_0057,-0.166623);
#define CTNODE_NO_0053 67
DEF_STATIC_CONST_VAL_FLOAT(val_0058,-0.571730);
#define CTNODE_NO_0050 68
DEF_STATIC_CONST_VAL_STRING(val_0059,"cc");
DEF_STATIC_CONST_VAL_FLOAT(val_0060,0.313289);
#define CTNODE_NO_0068 70
DEF_STATIC_CONST_VAL_FLOAT(val_0061,0.069582);
#define CTNODE_NO_0070 72
DEF_STATIC_CONST_VAL_FLOAT(val_0062,2.700000);
DEF_STATIC_CONST_VAL_FLOAT(val_0063,-0.367088);
#define CTNODE_NO_0073 75
DEF_STATIC_CONST_VAL_FLOAT(val_0064,-0.194887);
#define CTNODE_NO_0072 76
DEF_STATIC_CONST_VAL_FLOAT(val_0065,-0.063749);
#define CTNODE_NO_0043 77
DEF_STATIC_CONST_VAL_FLOAT(val_0066,-0.333421);
#define CTNODE_NO_0079 81
DEF_STATIC_CONST_VAL_FLOAT(val_0067,-0.165383);
#define CTNODE_NO_0078 82
DEF_STATIC_CONST_VAL_FLOAT(val_0068,-0.516332);
#define CTNODE_NO_0077 83
DEF_STATIC_CONST_VAL_FLOAT(val_0069,-0.779112);
#define CTNODE_NO_0084 86
DEF_STATIC_CONST_VAL_FLOAT(val_0070,-0.337611);
#define CTNODE_NO_0083 87
DEF_STATIC_CONST_VAL_FLOAT(val_0071,1.400000);
DEF_STATIC_CONST_VAL_FLOAT(val_0072,-0.745807);
#define CTNODE_NO_0087 89
DEF_STATIC_CONST_VAL_FLOAT(val_0073,-1.049070);
#define CTNODE_NO_0089 91
DEF_STATIC_CONST_VAL_FLOAT(val_0074,-0.914974);
#define CTNODE_NO_0008 92
DEF_STATIC_CONST_VAL_STRING(val_0075,"initial");
DEF_STATIC_CONST_VAL_FLOAT(val_0076,0.172658);
#define CTNODE_NO_0095 97
DEF_STATIC_CONST_VAL_FLOAT(val_0077,-0.101423);
#define CTNODE_NO_0094 98
DEF_STATIC_CONST_VAL_FLOAT(val_0078,-0.360092);
#define CTNODE_NO_0093 99
DEF_STATIC_CONST_VAL_FLOAT(val_0079,2.900000);
DEF_STATIC_CONST_VAL_FLOAT(val_0080,1.100000);
DEF_STATIC_CONST_VAL_FLOAT(val_0081,0.764189);
#define CTNODE_NO_0101 103
DEF_STATIC_CONST_VAL_FLOAT(val_0082,0.555132);
#define CTNODE_NO_0104 106
DEF_STATIC_CONST_VAL_FLOAT(val_0083,0.369882);
#define CTNODE_NO_0103 107
DEF_STATIC_CONST_VAL_FLOAT(val_0084,0.666966);
#define CTNODE_NO_0100 108
DEF_STATIC_CONST_VAL_FLOAT(val_0085,0.400000);
DEF_STATIC_CONST_VAL_FLOAT(val_0086,0.240634);
#define CTNODE_NO_0110 112
DEF_STATIC_CONST_VAL_FLOAT(val_0087,0.486176);
#define CTNODE_NO_0109 113
DEF_STATIC_CONST_VAL_FLOAT(val_0088,0.573811);
#define CTNODE_NO_0108 114
DEF_STATIC_CONST_VAL_FLOAT(val_0089,0.194468);
#define CTNODE_NO_0099 115
DEF_STATIC_CONST_VAL_FLOAT(val_0090,0.499383);
#define CTNODE_NO_0115 117
DEF_STATIC_CONST_VAL_FLOAT(val_0091,0.073732);
#define CTNODE_NO_0119 121
DEF_STATIC_CONST_VAL_FLOAT(val_0092,0.331014);
#define CTNODE_NO_0118 122
DEF_STATIC_CONST_VAL_FLOAT(val_0093,0.092930);
#define CTNODE_NO_0122 124
DEF_STATIC_CONST_VAL_FLOAT(val_0094,-0.044327);
#define CTNODE_NO_0117 125
DEF_STATIC_CONST_VAL_FLOAT(val_0095,0.517681);
#define CTNODE_NO_0126 128
DEF_STATIC_CONST_VAL_FLOAT(val_0096,0.128316);
#define CTNODE_NO_0128 130
DEF_STATIC_CONST_VAL_FLOAT(val_0097,0.361383);
#define CTNODE_NO_0125 131
DEF_STATIC_CONST_VAL_FLOAT(val_0098,0.054136);
#define CTNODE_NO_0131 133
DEF_STATIC_CONST_VAL_FLOAT(val_0099,0.246742);
#define CTNODE_NO_0092 134
DEF_STATIC_CONST_VAL_FLOAT(val_0100,0.621547);
#define CTNODE_NO_0135 137
DEF_STATIC_CONST_VAL_FLOAT(val_0101,0.501679);
#define CTNODE_NO_0137 139
DEF_STATIC_CONST_VAL_FLOAT(val_0102,3.300000);
DEF_STATIC_CONST_VAL_FLOAT(val_0103,-0.042049);
#define CTNODE_NO_0140 142
DEF_STATIC_CONST_VAL_FLOAT(val_0104,0.183226);
#define CTNODE_NO_0139 143
DEF_STATIC_CONST_VAL_FLOAT(val_0105,0.284799);
#define CTNODE_NO_0134 144
DEF_STATIC_CONST_VAL_FLOAT(val_0106,-0.820934);
#define CTNODE_NO_0145 147
DEF_STATIC_CONST_VAL_FLOAT(val_0107,-0.348735);
#define CTNODE_NO_0144 148
DEF_STATIC_CONST_VAL_FLOAT(val_0108,-0.400920);
#define CTNODE_NO_0149 151
DEF_STATIC_CONST_VAL_FLOAT(val_0109,-0.639366);
#define CTNODE_NO_0148 152
DEF_STATIC_CONST_VAL_FLOAT(val_0110,0.364857);
#define CTNODE_NO_0152 154
DEF_STATIC_CONST_VAL_FLOAT(val_0111,3.400000);
DEF_STATIC_CONST_VAL_FLOAT(val_0112,-0.007686);
#define CTNODE_NO_0155 157
DEF_STATIC_CONST_VAL_FLOAT(val_0113,-0.197753);
#define CTNODE_NO_0154 158
DEF_STATIC_CONST_VAL_FLOAT(val_0114,-0.394632);
#define CTNODE_NO_0007 159
DEF_STATIC_CONST_VAL_FLOAT(val_0115,0.100000);
DEF_STATIC_CONST_VAL_FLOAT(val_0116,0.938841);
#define CTNODE_NO_0162 164
DEF_STATIC_CONST_VAL_FLOAT(val_0117,-0.079664);
#define CTNODE_NO_0164 166
DEF_STATIC_CONST_VAL_FLOAT(val_0118,0.480026);
#define CTNODE_NO_0166 168
DEF_STATIC_CONST_VAL_FLOAT(val_0119,0.127175);
#define CTNODE_NO_0161 169
DEF_STATIC_CONST_VAL_FLOAT(val_0120,-0.708767);
#define CTNODE_NO_0170 172
DEF_STATIC_CONST_VAL_FLOAT(val_0121,-0.236212);
#define CTNODE_NO_0169 173
DEF_STATIC_CONST_VAL_FLOAT(val_0122,-0.273389);
#define CTNODE_NO_0174 176
DEF_STATIC_CONST_VAL_STRING(val_0123,"in");
DEF_STATIC_CONST_VAL_FLOAT(val_0124,0.058134);
#define CTNODE_NO_0176 178
DEF_STATIC_CONST_VAL_FLOAT(val_0125,0.721904);
#define CTNODE_NO_0178 180
DEF_STATIC_CONST_VAL_FLOAT(val_0126,2.200000);
DEF_STATIC_CONST_VAL_FLOAT(val_0127,0.016121);
#define CTNODE_NO_0181 183
DEF_STATIC_CONST_VAL_FLOAT(val_0128,0.227372);
#define CTNODE_NO_0180 184
DEF_STATIC_CONST_VAL_FLOAT(val_0129,0.445569);
#define CTNODE_NO_0173 185
DEF_STATIC_CONST_VAL_FLOAT(val_0130,-0.120097);
#define CTNODE_NO_0187 189
DEF_STATIC_CONST_VAL_FLOAT(val_0131,0.219042);
#define CTNODE_NO_0186 190
DEF_STATIC_CONST_VAL_FLOAT(val_0132,0.321245);
#define CTNODE_NO_0185 191
DEF_STATIC_CONST_VAL_FLOAT(val_0133,0.134075);
#define CTNODE_NO_0191 193
DEF_STATIC_CONST_VAL_FLOAT(val_0134,-0.466418);
#define CTNODE_NO_0193 195
DEF_STATIC_CONST_VAL_FLOAT(val_0135,-0.425925);
#define CTNODE_NO_0197 199
DEF_STATIC_CONST_VAL_FLOAT(val_0136,-0.542809);
#define CTNODE_NO_0196 200
DEF_STATIC_CONST_VAL_FLOAT(val_0137,-0.201899);
#define CTNODE_NO_0195 201
DEF_STATIC_CONST_VAL_FLOAT(val_0138,0.209018);
#define CTNODE_NO_0202 204
DEF_STATIC_CONST_VAL_FLOAT(val_0139,-0.178136);
#define CTNODE_NO_0201 205
DEF_STATIC_CONST_VAL_FLOAT(val_0140,-0.235593);
#define CTNODE_NO_0206 208
DEF_STATIC_CONST_VAL_FLOAT(val_0141,0.126118);
#define CTNODE_NO_0205 209
DEF_STATIC_CONST_VAL_FLOAT(val_0142,-0.174812);
#define CTNODE_NO_0209 211
DEF_STATIC_CONST_VAL_STRING(val_0143,"content");
DEF_STATIC_CONST_VAL_FLOAT(val_0144,-0.231509);
#define CTNODE_NO_0211 213
DEF_STATIC_CONST_VAL_FLOAT(val_0145,-0.536405);
#define CTNODE_NO_0160 214
DEF_STATIC_CONST_VAL_FLOAT(val_0146,0.163343);
#define CTNODE_NO_0214 216
DEF_STATIC_CONST_VAL_FLOAT(val_0147,-0.455280);
#define CTNODE_NO_0217 219
DEF_STATIC_CONST_VAL_FLOAT(val_0148,-0.099803);
#define CTNODE_NO_0216 220
DEF_STATIC_CONST_VAL_FLOAT(val_0149,-0.930547);
#define CTNODE_NO_0220 222
DEF_STATIC_CONST_VAL_FLOAT(val_0150,-0.634119);
#define CTNODE_NO_0223 225
DEF_STATIC_CONST_VAL_FLOAT(val_0151,-0.760176);
#define CTNODE_NO_0222 226
DEF_STATIC_CONST_VAL_FLOAT(val_0152,-0.121355);
#define CTNODE_NO_0226 228
DEF_STATIC_CONST_VAL_FLOAT(val_0153,-0.557509);
#define CTNODE_NO_0159 229
DEF_STATIC_CONST_VAL_FLOAT(val_0154,-0.402734);
#define CTNODE_NO_0231 233
DEF_STATIC_CONST_VAL_FLOAT(val_0155,-0.988478);
#define CTNODE_NO_0234 236
DEF_STATIC_CONST_VAL_FLOAT(val_0156,-0.802536);
#define CTNODE_NO_0236 238
DEF_STATIC_CONST_VAL_FLOAT(val_0157,-0.900628);
#define CTNODE_NO_0233 239
DEF_STATIC_CONST_VAL_FLOAT(val_0158,-0.768992);
#define CTNODE_NO_0240 242
DEF_STATIC_CONST_VAL_FLOAT(val_0159,-0.574918);
#define CTNODE_NO_0239 243
DEF_STATIC_CONST_VAL_FLOAT(val_0160,-0.756359);
#define CTNODE_NO_0243 245
DEF_STATIC_CONST_VAL_FLOAT(val_0161,-0.808937);
#define CTNODE_NO_0245 247
DEF_STATIC_CONST_VAL_FLOAT(val_0162,-0.933150);
#define CTNODE_NO_0230 248
DEF_STATIC_CONST_VAL_FLOAT(val_0163,-0.428493);
#define CTNODE_NO_0252 254
DEF_STATIC_CONST_VAL_FLOAT(val_0164,0.021107);
#define CTNODE_NO_0254 256
DEF_STATIC_CONST_VAL_FLOAT(val_0165,-0.254485);
#define CTNODE_NO_0251 257
DEF_STATIC_CONST_VAL_FLOAT(val_0166,-0.389966);
#define CTNODE_NO_0258 260
DEF_STATIC_CONST_VAL_FLOAT(val_0167,0.185781);
#define CTNODE_NO_0257 261
DEF_STATIC_CONST_VAL_FLOAT(val_0168,0.422551);
#define CTNODE_NO_0261 263
DEF_STATIC_CONST_VAL_FLOAT(val_0169,0.145576);
#define CTNODE_NO_0250 264
DEF_STATIC_CONST_VAL_FLOAT(val_0170,-0.623190);
#define CTNODE_NO_0266 268
DEF_STATIC_CONST_VAL_FLOAT(val_0171,-0.317324);
#define CTNODE_NO_0268 270
DEF_STATIC_CONST_VAL_FLOAT(val_0172,-0.591051);
#define CTNODE_NO_0265 271
DEF_STATIC_CONST_VAL_FLOAT(val_0173,-0.405607);
#define CTNODE_NO_0272 274
DEF_STATIC_CONST_VAL_FLOAT(val_0174,-0.313148);
#define CTNODE_NO_0271 275
DEF_STATIC_CONST_VAL_FLOAT(val_0175,0.159416);
#define CTNODE_NO_0275 277
DEF_STATIC_CONST_VAL_FLOAT(val_0176,-0.254651);
#define CTNODE_NO_0264 278
DEF_STATIC_CONST_VAL_FLOAT(val_0177,-0.799896);
#define CTNODE_NO_0278 280
DEF_STATIC_CONST_VAL_FLOAT(val_0178,-0.551309);
#define CTNODE_NO_0249 281
DEF_STATIC_CONST_VAL_STRING(val_0179,"final");
DEF_STATIC_CONST_VAL_FLOAT(val_0180,-0.707084);
#define CTNODE_NO_0281 283
DEF_STATIC_CONST_VAL_FLOAT(val_0181,-0.901874);
#define CTNODE_NO_0248 284
DEF_STATIC_CONST_VAL_FLOAT(val_0182,0.196466);
#define CTNODE_NO_0287 289
DEF_STATIC_CONST_VAL_FLOAT(val_0183,0.003824);
#define CTNODE_NO_0286 290
DEF_STATIC_CONST_VAL_FLOAT(val_0184,-0.128590);
#define CTNODE_NO_0285 291
DEF_STATIC_CONST_VAL_FLOAT(val_0185,-0.219339);
#define CTNODE_NO_0291 293
DEF_STATIC_CONST_VAL_FLOAT(val_0186,-0.516734);
#define CTNODE_NO_0284 294
DEF_STATIC_CONST_VAL_STRING(val_0187,"single");
DEF_STATIC_CONST_VAL_FLOAT(val_0188,0.159445);
#define CTNODE_NO_0297 299
DEF_STATIC_CONST_VAL_FLOAT(val_0189,3.500000);
DEF_STATIC_CONST_VAL_FLOAT(val_0190,-0.419103);
#define CTNODE_NO_0300 302
DEF_STATIC_CONST_VAL_FLOAT(val_0191,-0.092856);
#define CTNODE_NO_0299 303
DEF_STATIC_CONST_VAL_FLOAT(val_0192,-0.576116);
#define CTNODE_NO_0296 304
DEF_STATIC_CONST_VAL_STRING(val_0193,"3");
DEF_STATIC_CONST_VAL_FLOAT(val_0194,-0.645830);
#define CTNODE_NO_0304 306
DEF_STATIC_CONST_VAL_FLOAT(val_0195,-0.466500);
#define CTNODE_NO_0295 307
DEF_STATIC_CONST_VAL_FLOAT(val_0196,-0.217292);
#define CTNODE_NO_0307 309
DEF_STATIC_CONST_VAL_FLOAT(val_0197,-0.304382);
#define CTNODE_NO_0309 311
DEF_STATIC_CONST_VAL_FLOAT(val_0198,-0.572203);
#define CTNODE_NO_0313 315
DEF_STATIC_CONST_VAL_FLOAT(val_0199,-0.240338);
#define CTNODE_NO_0312 316
DEF_STATIC_CONST_VAL_FLOAT(val_0200,-0.588171);
#define CTNODE_NO_0316 318
DEF_STATIC_CONST_VAL_FLOAT(val_0201,-0.957970);
#define CTNODE_NO_0311 319
DEF_STATIC_CONST_VAL_FLOAT(val_0202,3.900000);
DEF_STATIC_CONST_VAL_FLOAT(val_0203,-0.959427);
#define CTNODE_NO_0319 321
DEF_STATIC_CONST_VAL_FLOAT(val_0204,-0.845747);
#define CTNODE_NO_0294 322
DEF_STATIC_CONST_VAL_FLOAT(val_0205,-0.482247);
#define CTNODE_NO_0323 325
DEF_STATIC_CONST_VAL_FLOAT(val_0206,-0.632362);
#define CTNODE_NO_0325 327
DEF_STATIC_CONST_VAL_FLOAT(val_0207,-0.713117);
#define CTNODE_NO_0328 330
DEF_STATIC_CONST_VAL_FLOAT(val_0208,-0.924308);
#define CTNODE_NO_0327 331
DEF_STATIC_CONST_VAL_FLOAT(val_0209,-0.891342);
#define CTNODE_NO_0331 333
DEF_STATIC_CONST_VAL_FLOAT(val_0210,-1.152520);
#define CTNODE_NO_0322 334
DEF_STATIC_CONST_VAL_FLOAT(val_0211,-0.599624);
#define CTNODE_NO_0335 337
DEF_STATIC_CONST_VAL_FLOAT(val_0212,-0.077191);
#define CTNODE_NO_0334 338
DEF_STATIC_CONST_VAL_FLOAT(val_0213,-1.032420);
#define CTNODE_NO_0340 342
DEF_STATIC_CONST_VAL_FLOAT(val_0214,-0.542799);
#define CTNODE_NO_0339 343
DEF_STATIC_CONST_VAL_FLOAT(val_0215,2.800000);
DEF_STATIC_CONST_VAL_FLOAT(val_0216,-0.423979);
#define CTNODE_NO_0343 345
DEF_STATIC_CONST_VAL_FLOAT(val_0217,-0.766379);
#define CTNODE_NO_0338 346
DEF_STATIC_CONST_VAL_STRING(val_0218,"to");
DEF_STATIC_CONST_VAL_FLOAT(val_0219,-0.792895);
#define CTNODE_NO_0347 349
DEF_STATIC_CONST_VAL_FLOAT(val_0220,-0.276816);
#define CTNODE_NO_0349 351
DEF_STATIC_CONST_VAL_FLOAT(val_0221,-0.523721);
#define CTNODE_NO_0346 352
DEF_STATIC_CONST_VAL_FLOAT(val_0222,-0.488102);
#define CTNODE_NO_0353 355
DEF_STATIC_CONST_VAL_FLOAT(val_0223,-0.731758);
#define CTNODE_NO_0352 356
DEF_STATIC_CONST_VAL_FLOAT(val_0224,-0.822229);
#define CTNODE_NO_0229 357
DEF_STATIC_CONST_VAL_FLOAT(val_0225,1.023340);
#define CTNODE_NO_0359 361
DEF_STATIC_CONST_VAL_FLOAT(val_0226,0.536277);
#define CTNODE_NO_0361 363
DEF_STATIC_CONST_VAL_FLOAT(val_0227,0.138201);
#define CTNODE_NO_0358 364
DEF_STATIC_CONST_VAL_FLOAT(val_0228,-0.234710);
#define CTNODE_NO_0365 367
DEF_STATIC_CONST_VAL_FLOAT(val_0229,-0.525292);
#define CTNODE_NO_0364 368
DEF_STATIC_CONST_VAL_FLOAT(val_0230,0.417485);
#define CTNODE_NO_0368 370
DEF_STATIC_CONST_VAL_FLOAT(val_0231,-0.078200);
#define CTNODE_NO_0357 371
DEF_STATIC_CONST_VAL_FLOAT(val_0232,-0.569410);
#define CTNODE_NO_0375 377
DEF_STATIC_CONST_VAL_FLOAT(val_0233,-0.289362);
#define CTNODE_NO_0374 378
DEF_STATIC_CONST_VAL_FLOAT(val_0234,-0.092104);
#define CTNODE_NO_0373 379
DEF_STATIC_CONST_VAL_FLOAT(val_0235,0.139463);
#define CTNODE_NO_0379 381
DEF_STATIC_CONST_VAL_FLOAT(val_0236,-0.070872);
#define CTNODE_NO_0372 382
DEF_STATIC_CONST_VAL_FLOAT(val_0237,-0.618971);
#define CTNODE_NO_0383 385
DEF_STATIC_CONST_VAL_FLOAT(val_0238,-0.840495);
#define CTNODE_NO_0382 386
DEF_STATIC_CONST_VAL_FLOAT(val_0239,0.009134);
#define CTNODE_NO_0388 390
DEF_STATIC_CONST_VAL_FLOAT(val_0240,-0.512523);
#define CTNODE_NO_0391 393
DEF_STATIC_CONST_VAL_FLOAT(val_0241,0.121704);
#define CTNODE_NO_0393 395
DEF_STATIC_CONST_VAL_FLOAT(val_0242,-0.256370);
#define CTNODE_NO_0390 396
DEF_STATIC_CONST_VAL_FLOAT(val_0243,3.100000);
DEF_STATIC_CONST_VAL_FLOAT(val_0244,-0.474522);
#define CTNODE_NO_0397 399
DEF_STATIC_CONST_VAL_FLOAT(val_0245,-0.247206);
#define CTNODE_NO_0396 400
DEF_STATIC_CONST_VAL_FLOAT(val_0246,-0.597866);
#define CTNODE_NO_0401 403
DEF_STATIC_CONST_VAL_FLOAT(val_0247,-0.407765);
#define CTNODE_NO_0400 404
DEF_STATIC_CONST_VAL_FLOAT(val_0248,-0.741256);
#define CTNODE_NO_0387 405
DEF_STATIC_CONST_VAL_FLOAT(val_0249,-1.084260);
#define CTNODE_NO_0406 408
DEF_STATIC_CONST_VAL_FLOAT(val_0250,-0.397890);
#define CTNODE_NO_0405 409
DEF_STATIC_CONST_VAL_FLOAT(val_0251,2.600000);
DEF_STATIC_CONST_VAL_FLOAT(val_0252,-0.666011);
#define CTNODE_NO_0410 412
DEF_STATIC_CONST_VAL_FLOAT(val_0253,-0.499492);
#define CTNODE_NO_0409 413
DEF_STATIC_CONST_VAL_FLOAT(val_0254,-0.253186);
#define CTNODE_NO_0413 415
DEF_STATIC_CONST_VAL_FLOAT(val_0255,-0.372832);
#define CTNODE_NO_0386 416
DEF_STATIC_CONST_VAL_FLOAT(val_0256,-0.093649);
#define CTNODE_NO_0371 417
DEF_STATIC_CONST_VAL_FLOAT(val_0257,-0.249982);
#define CTNODE_NO_0417 419
DEF_STATIC_CONST_VAL_FLOAT(val_0258,3.200000);
DEF_STATIC_CONST_VAL_FLOAT(val_0259,0.180860);
#define CTNODE_NO_0419 421
DEF_STATIC_CONST_VAL_FLOAT(val_0260,-0.040291);
#define CTNODE_NO_0006 422
DEF_STATIC_CONST_VAL_STRING(val_0261,"4");
DEF_STATIC_CONST_VAL_FLOAT(val_0262,1.632030);
#define CTNODE_NO_0427 429
DEF_STATIC_CONST_VAL_FLOAT(val_0263,0.994933);
#define CTNODE_NO_0426 430
DEF_STATIC_CONST_VAL_FLOAT(val_0264,0.214457);
#define CTNODE_NO_0431 433
DEF_STATIC_CONST_VAL_FLOAT(val_0265,0.730381);
#define CTNODE_NO_0430 434
DEF_STATIC_CONST_VAL_FLOAT(val_0266,-0.336221);
#define CTNODE_NO_0435 437
DEF_STATIC_CONST_VAL_FLOAT(val_0267,0.468302);
#define CTNODE_NO_0434 438
DEF_STATIC_CONST_VAL_FLOAT(val_0268,-0.799121);
#define CTNODE_NO_0425 439
DEF_STATIC_CONST_VAL_FLOAT(val_0269,0.030061);
#define CTNODE_NO_0440 442
DEF_STATIC_CONST_VAL_STRING(val_0270,"d");
DEF_STATIC_CONST_VAL_FLOAT(val_0271,1.164900);
#define CTNODE_NO_0442 444
DEF_STATIC_CONST_VAL_FLOAT(val_0272,2.266800);
#define CTNODE_NO_0445 447
DEF_STATIC_CONST_VAL_FLOAT(val_0273,1.503750);
#define CTNODE_NO_0447 449
DEF_STATIC_CONST_VAL_FLOAT(val_0274,2.079270);
#define CTNODE_NO_0444 450
DEF_STATIC_CONST_VAL_FLOAT(val_0275,1.102430);
#define CTNODE_NO_0450 452
DEF_STATIC_CONST_VAL_FLOAT(val_0276,1.843200);
#define CTNODE_NO_0452 454
DEF_STATIC_CONST_VAL_FLOAT(val_0277,1.598530);
#define CTNODE_NO_0439 455
DEF_STATIC_CONST_VAL_FLOAT(val_0278,1.129270);
#define CTNODE_NO_0457 459
DEF_STATIC_CONST_VAL_FLOAT(val_0279,0.442376);
#define CTNODE_NO_0456 460
DEF_STATIC_CONST_VAL_FLOAT(val_0280,1.765080);
#define CTNODE_NO_0462 464
DEF_STATIC_CONST_VAL_FLOAT(val_0281,0.748600);
#define CTNODE_NO_0461 465
DEF_STATIC_CONST_VAL_FLOAT(val_0282,2.308260);
#define CTNODE_NO_0460 466
DEF_STATIC_CONST_VAL_FLOAT(val_0283,1.699170);
#define CTNODE_NO_0466 468
DEF_STATIC_CONST_VAL_FLOAT(val_0284,1.311280);
#define CTNODE_NO_0468 470
DEF_STATIC_CONST_VAL_FLOAT(val_0285,0.212421);
#define CTNODE_NO_0471 473
DEF_STATIC_CONST_VAL_FLOAT(val_0286,0.653094);
#define CTNODE_NO_0470 474
DEF_STATIC_CONST_VAL_FLOAT(val_0287,1.258020);
#define CTNODE_NO_0474 476
DEF_STATIC_CONST_VAL_FLOAT(val_0288,0.777568);
#define CTNODE_NO_0455 477
DEF_STATIC_CONST_VAL_FLOAT(val_0289,0.163941);
#define CTNODE_NO_0478 480
DEF_STATIC_CONST_VAL_FLOAT(val_0290,-0.167063);
#define CTNODE_NO_0477 481
DEF_STATIC_CONST_VAL_FLOAT(val_0291,-0.000859);
#define CTNODE_NO_0484 486
DEF_STATIC_CONST_VAL_FLOAT(val_0292,0.273433);
#define CTNODE_NO_0483 487
DEF_STATIC_CONST_VAL_FLOAT(val_0293,1.056940);
#define CTNODE_NO_0487 489
DEF_STATIC_CONST_VAL_FLOAT(val_0294,0.244916);
#define CTNODE_NO_0482 490
DEF_STATIC_CONST_VAL_FLOAT(val_0295,1.211870);
#define CTNODE_NO_0491 493
DEF_STATIC_CONST_VAL_FLOAT(val_0296,0.598650);
#define CTNODE_NO_0493 495
DEF_STATIC_CONST_VAL_FLOAT(val_0297,1.163400);
#define CTNODE_NO_0490 496
DEF_STATIC_CONST_VAL_FLOAT(val_0298,0.292935);
#define CTNODE_NO_0496 498
DEF_STATIC_CONST_VAL_FLOAT(val_0299,0.925740);
#define CTNODE_NO_0481 499
DEF_STATIC_CONST_VAL_FLOAT(val_0300,1.234840);
#define CTNODE_NO_0500 502
DEF_STATIC_CONST_VAL_FLOAT(val_0301,2.020080);
#define CTNODE_NO_0499 503
DEF_STATIC_CONST_VAL_FLOAT(val_0302,0.697089);
#define CTNODE_NO_0504 506
DEF_STATIC_CONST_VAL_FLOAT(val_0303,0.992197);
#define CTNODE_NO_0506 508
DEF_STATIC_CONST_VAL_FLOAT(val_0304,1.510930);
#define CTNODE_NO_0503 509
DEF_STATIC_CONST_VAL_FLOAT(val_0305,0.520952);
#define CTNODE_NO_0424 510
DEF_STATIC_CONST_VAL_FLOAT(val_0306,0.185827);
#define CTNODE_NO_0513 515
DEF_STATIC_CONST_VAL_FLOAT(val_0307,0.033230);
#define CTNODE_NO_0515 517
DEF_STATIC_CONST_VAL_FLOAT(val_0308,-0.534917);
#define CTNODE_NO_0512 518
DEF_STATIC_CONST_VAL_FLOAT(val_0309,0.575107);
#define CTNODE_NO_0518 520
DEF_STATIC_CONST_VAL_FLOAT(val_0310,-0.111275);
#define CTNODE_NO_0520 522
DEF_STATIC_CONST_VAL_FLOAT(val_0311,0.094470);
#define CTNODE_NO_0522 524
DEF_STATIC_CONST_VAL_FLOAT(val_0312,0.381947);
#define CTNODE_NO_0511 525
DEF_STATIC_CONST_VAL_FLOAT(val_0313,-0.490108);
#define CTNODE_NO_0525 527
DEF_STATIC_CONST_VAL_FLOAT(val_0314,-0.201268);
#define CTNODE_NO_0510 528
DEF_STATIC_CONST_VAL_FLOAT(val_0315,1.203970);
#define CTNODE_NO_0528 530
DEF_STATIC_CONST_VAL_FLOAT(val_0316,0.636568);
#define CTNODE_NO_0530 532
DEF_STATIC_CONST_VAL_FLOAT(val_0317,1.077630);
#define CTNODE_NO_0423 533
DEF_STATIC_CONST_VAL_FLOAT(val_0318,-0.016336);
#define CTNODE_NO_0537 539
DEF_STATIC_CONST_VAL_FLOAT(val_0319,1.072530);
#define CTNODE_NO_0540 542
DEF_STATIC_CONST_VAL_FLOAT(val_0320,0.525806);
#define CTNODE_NO_0542 544
DEF_STATIC_CONST_VAL_FLOAT(val_0321,0.952792);
#define CTNODE_NO_0539 545
DEF_STATIC_CONST_VAL_FLOAT(val_0322,0.469117);
#define CTNODE_NO_0546 548
DEF_STATIC_CONST_VAL_FLOAT(val_0323,-0.071645);
#define CTNODE_NO_0548 550
DEF_STATIC_CONST_VAL_FLOAT(val_0324,0.457137);
#define CTNODE_NO_0550 552
DEF_STATIC_CONST_VAL_FLOAT(val_0325,0.102492);
#define CTNODE_NO_0545 553
DEF_STATIC_CONST_VAL_FLOAT(val_0326,0.697337);
#define CTNODE_NO_0553 555
DEF_STATIC_CONST_VAL_FLOAT(val_0327,0.375114);
#define CTNODE_NO_0536 556
DEF_STATIC_CONST_VAL_FLOAT(val_0328,0.410671);
#define CTNODE_NO_0556 558
DEF_STATIC_CONST_VAL_FLOAT(val_0329,0.800000);
DEF_STATIC_CONST_VAL_FLOAT(val_0330,-0.331055);
#define CTNODE_NO_0558 560
DEF_STATIC_CONST_VAL_FLOAT(val_0331,-0.240616);
#define CTNODE_NO_0560 562
DEF_STATIC_CONST_VAL_FLOAT(val_0332,-0.019127);
#define CTNODE_NO_0535 563
DEF_STATIC_CONST_VAL_FLOAT(val_0333,0.556537);
#define CTNODE_NO_0564 566
DEF_STATIC_CONST_VAL_FLOAT(val_0334,0.153892);
#define CTNODE_NO_0563 567
DEF_STATIC_CONST_VAL_FLOAT(val_0335,0.123242);
#define CTNODE_NO_0569 571
DEF_STATIC_CONST_VAL_FLOAT(val_0336,0.295753);
#define CTNODE_NO_0568 572
DEF_STATIC_CONST_VAL_FLOAT(val_0337,-0.341018);
#define CTNODE_NO_0574 576
DEF_STATIC_CONST_VAL_FLOAT(val_0338,-0.008931);
#define CTNODE_NO_0573 577
DEF_STATIC_CONST_VAL_FLOAT(val_0339,-0.744625);
#define CTNODE_NO_0577 579
DEF_STATIC_CONST_VAL_FLOAT(val_0340,-0.302803);
#define CTNODE_NO_0572 580
DEF_STATIC_CONST_VAL_FLOAT(val_0341,0.113815);
#define CTNODE_NO_0580 582
DEF_STATIC_CONST_VAL_FLOAT(val_0342,-0.128733);
#define CTNODE_NO_0567 583
DEF_STATIC_CONST_VAL_FLOAT(val_0343,-0.854509);
#define CTNODE_NO_0583 585
DEF_STATIC_CONST_VAL_FLOAT(val_0344,-0.216179);
#define CTNODE_NO_0534 586
DEF_STATIC_CONST_VAL_FLOAT(val_0345,0.461950);
#define CTNODE_NO_0587 589
DEF_STATIC_CONST_VAL_FLOAT(val_0346,0.657169);
#define CTNODE_NO_0589 591
DEF_STATIC_CONST_VAL_FLOAT(val_0347,1.082220);
#define CTNODE_NO_0591 593
DEF_STATIC_CONST_VAL_FLOAT(val_0348,1.462570);
#define CTNODE_NO_0586 594
DEF_STATIC_CONST_VAL_FLOAT(val_0349,0.785204);
#define CTNODE_NO_0594 596
DEF_STATIC_CONST_VAL_FLOAT(val_0350,0.321168);
#define CTNODE_NO_0597 599
DEF_STATIC_CONST_VAL_FLOAT(val_0351,0.950834);
#define CTNODE_NO_0596 600
DEF_STATIC_CONST_VAL_FLOAT(val_0352,-0.167374);
#define CTNODE_NO_0602 604
DEF_STATIC_CONST_VAL_FLOAT(val_0353,-0.003744);
#define CTNODE_NO_0605 607
DEF_STATIC_CONST_VAL_FLOAT(val_0354,0.228448);
#define CTNODE_NO_0607 609
DEF_STATIC_CONST_VAL_FLOAT(val_0355,0.504252);
#define CTNODE_NO_0604 610
DEF_STATIC_CONST_VAL_FLOAT(val_0356,0.736476);
#define CTNODE_NO_0601 611
DEF_STATIC_CONST_VAL_FLOAT(val_0357,0.059097);
#define CTNODE_NO_0611 613
DEF_STATIC_CONST_VAL_FLOAT(val_0358,-0.431535);
#define CTNODE_NO_0600 614
DEF_STATIC_CONST_VAL_FLOAT(val_0359,1.006420);
#define CTNODE_NO_0614 616
DEF_STATIC_CONST_VAL_FLOAT(val_0360,0.481652);
#define CTNODE_NO_0617 619
DEF_STATIC_CONST_VAL_FLOAT(val_0361,0.749861);
#define CTNODE_NO_0616 620
DEF_STATIC_CONST_VAL_FLOAT(val_0362,0.069631);
#define CTNODE_NO_0621 623
DEF_STATIC_CONST_VAL_FLOAT(val_0363,0.552212);
#define CTNODE_NO_0620 624
DEF_STATIC_CONST_VAL_FLOAT(val_0364,-0.047922);
#define CTNODE_NO_0533 625
DEF_STATIC_CONST_VAL_FLOAT(val_0365,-1.060900);
#define CTNODE_NO_0626 628
DEF_STATIC_CONST_VAL_FLOAT(val_0366,-0.599330);
#define CTNODE_NO_0628 630
DEF_STATIC_CONST_VAL_FLOAT(val_0367,0.006987);
#define CTNODE_NO_0630 632
DEF_STATIC_CONST_VAL_FLOAT(val_0368,-0.064904);
#define CTNODE_NO_0633 635
DEF_STATIC_CONST_VAL_FLOAT(val_0369,-0.248899);
#define CTNODE_NO_0632 636
DEF_STATIC_CONST_VAL_FLOAT(val_0370,-0.601987);
#define CTNODE_NO_0636 638
DEF_STATIC_CONST_VAL_FLOAT(val_0371,-0.302401);
#define CTNODE_NO_0625 639
DEF_STATIC_CONST_VAL_FLOAT(val_0372,0.164636);
#define CTNODE_NO_0642 644
DEF_STATIC_CONST_VAL_FLOAT(val_0373,-0.098905);
#define CTNODE_NO_0641 645
DEF_STATIC_CONST_VAL_FLOAT(val_0374,-0.316836);
#define CTNODE_NO_0645 647
DEF_STATIC_CONST_VAL_FLOAT(val_0375,-0.096119);
#define CTNODE_NO_0640 648
DEF_STATIC_CONST_VAL_FLOAT(val_0376,-0.429437);
#define CTNODE_NO_0649 651
DEF_STATIC_CONST_VAL_FLOAT(val_0377,-0.065274);
#define CTNODE_NO_0648 652
DEF_STATIC_CONST_VAL_FLOAT(val_0378,-0.635089);
#define CTNODE_NO_0639 653
DEF_STATIC_CONST_VAL_FLOAT(val_0379,0.013936);
#define CTNODE_NO_0654 656
DEF_STATIC_CONST_VAL_FLOAT(val_0380,-0.454845);
#define CTNODE_NO_0653 657
DEF_STATIC_CONST_VAL_FLOAT(val_0381,0.809091);
#define CTNODE_NO_0658 660
DEF_STATIC_CONST_VAL_FLOAT(val_0382,-0.277092);
#define CTNODE_NO_0661 663
DEF_STATIC_CONST_VAL_FLOAT(val_0383,0.281001);
#define CTNODE_NO_0660 664
DEF_STATIC_CONST_VAL_FLOAT(val_0384,0.699145);
#define CTNODE_NO_0664 666
DEF_STATIC_CONST_VAL_FLOAT(val_0385,0.241873);
#define CTNODE_NO_0657 667
DEF_STATIC_CONST_VAL_FLOAT(val_0386,-0.470784);
#define CTNODE_NO_0668 670
DEF_STATIC_CONST_VAL_FLOAT(val_0387,-0.072112);
#define CTNODE_NO_0667 671
DEF_STATIC_CONST_VAL_FLOAT(val_0388,0.073349);
#define CTNODE_NO_0671 673
DEF_STATIC_CONST_VAL_FLOAT(val_0389,0.608371);
#define CTNODE_NO_0422 674
DEF_STATIC_CONST_VAL_FLOAT(val_0390,-0.293282);
#define CTNODE_NO_0678 680
DEF_STATIC_CONST_VAL_FLOAT(val_0391,-0.081611);
#define CTNODE_NO_0677 681
DEF_STATIC_CONST_VAL_FLOAT(val_0392,-0.504024);
#define CTNODE_NO_0676 682
DEF_STATIC_CONST_VAL_FLOAT(val_0393,0.983950);
#define CTNODE_NO_0683 685
DEF_STATIC_CONST_VAL_FLOAT(val_0394,0.634789);
#define CTNODE_NO_0685 687
DEF_STATIC_CONST_VAL_FLOAT(val_0395,4.400000);
DEF_STATIC_CONST_VAL_FLOAT(val_0396,0.479029);
#define CTNODE_NO_0687 689
DEF_STATIC_CONST_VAL_FLOAT(val_0397,0.143214);
#define CTNODE_NO_0689 691
DEF_STATIC_CONST_VAL_FLOAT(val_0398,0.406834);
#define CTNODE_NO_0682 692
DEF_STATIC_CONST_VAL_FLOAT(val_0399,0.600000);
DEF_STATIC_CONST_VAL_FLOAT(val_0400,-0.415599);
#define CTNODE_NO_0693 695
DEF_STATIC_CONST_VAL_FLOAT(val_0401,0.110288);
#define CTNODE_NO_0692 696
DEF_STATIC_CONST_VAL_FLOAT(val_0402,0.031419);
#define CTNODE_NO_0697 699
DEF_STATIC_CONST_VAL_FLOAT(val_0403,0.693893);
#define CTNODE_NO_0699 701
DEF_STATIC_CONST_VAL_FLOAT(val_0404,6.000000);
DEF_STATIC_CONST_VAL_FLOAT(val_0405,0.215675);
#define CTNODE_NO_0701 703
DEF_STATIC_CONST_VAL_FLOAT(val_0406,0.574068);
#define CTNODE_NO_0696 704
DEF_STATIC_CONST_VAL_FLOAT(val_0407,-0.458142);
#define CTNODE_NO_0704 706
DEF_STATIC_CONST_VAL_FLOAT(val_0408,0.304628);
#define CTNODE_NO_0707 709
DEF_STATIC_CONST_VAL_FLOAT(val_0409,-0.230940);
#define CTNODE_NO_0709 711
DEF_STATIC_CONST_VAL_FLOAT(val_0410,0.326954);
#define CTNODE_NO_0713 715
DEF_STATIC_CONST_VAL_FLOAT(val_0411,-0.100616);
#define CTNODE_NO_0712 716
DEF_STATIC_CONST_VAL_FLOAT(val_0412,-0.091913);
#define CTNODE_NO_0711 717
DEF_STATIC_CONST_VAL_FLOAT(val_0413,0.219053);
#define CTNODE_NO_0706 718
DEF_STATIC_CONST_VAL_FLOAT(val_0414,0.216118);
#define CTNODE_NO_0719 721
DEF_STATIC_CONST_VAL_FLOAT(val_0415,-0.008341);
#define CTNODE_NO_0718 722
DEF_STATIC_CONST_VAL_FLOAT(val_0416,0.761763);
#define CTNODE_NO_0722 724
DEF_STATIC_CONST_VAL_FLOAT(val_0417,0.332721);
#define CTNODE_NO_0675 725
DEF_STATIC_CONST_VAL_FLOAT(val_0418,0.217178);
#define CTNODE_NO_0726 728
DEF_STATIC_CONST_VAL_FLOAT(val_0419,1.294510);
#define CTNODE_NO_0728 730
DEF_STATIC_CONST_VAL_FLOAT(val_0420,4.000000);
DEF_STATIC_CONST_VAL_FLOAT(val_0421,0.945261);
#define CTNODE_NO_0734 736
DEF_STATIC_CONST_VAL_FLOAT(val_0422,0.687498);
#define CTNODE_NO_0733 737
DEF_STATIC_CONST_VAL_FLOAT(val_0423,0.403076);
#define CTNODE_NO_0732 738
DEF_STATIC_CONST_VAL_FLOAT(val_0424,1.002550);
#define CTNODE_NO_0731 739
DEF_STATIC_CONST_VAL_FLOAT(val_0425,1.091130);
#define CTNODE_NO_0730 740
DEF_STATIC_CONST_VAL_FLOAT(val_0426,0.209045);
#define CTNODE_NO_0725 741
DEF_STATIC_CONST_VAL_FLOAT(val_0427,-0.054407);
#define CTNODE_NO_0741 743
DEF_STATIC_CONST_VAL_FLOAT(val_0428,0.256045);
#define CTNODE_NO_0674 744
DEF_STATIC_CONST_VAL_FLOAT(val_0429,-0.967300);
#define CTNODE_NO_0746 748
DEF_STATIC_CONST_VAL_FLOAT(val_0430,-0.351397);
#define CTNODE_NO_0745 749
DEF_STATIC_CONST_VAL_FLOAT(val_0431,-0.623300);
#define CTNODE_NO_0749 751
DEF_STATIC_CONST_VAL_FLOAT(val_0432,0.266234);
#define CTNODE_NO_0752 754
DEF_STATIC_CONST_VAL_FLOAT(val_0433,-0.302281);
#define CTNODE_NO_0754 756
DEF_STATIC_CONST_VAL_FLOAT(val_0434,-0.010244);
#define CTNODE_NO_0757 759
DEF_STATIC_CONST_VAL_FLOAT(val_0435,-0.274514);
#define CTNODE_NO_0756 760
DEF_STATIC_CONST_VAL_FLOAT(val_0436,0.048218);
#define CTNODE_NO_0751 761
DEF_STATIC_CONST_VAL_FLOAT(val_0437,0.888495);
#define CTNODE_NO_0761 763
DEF_STATIC_CONST_VAL_FLOAT(val_0438,0.653018);
#define CTNODE_NO_0763 765
DEF_STATIC_CONST_VAL_FLOAT(val_0439,0.061289);
#define CTNODE_NO_0765 767
DEF_STATIC_CONST_VAL_FLOAT(val_0440,0.346637);
#define CTNODE_NO_0744 768
DEF_STATIC_CONST_VAL_FLOAT(val_0441,0.041181);
#define CTNODE_NO_0770 772
DEF_STATIC_CONST_VAL_FLOAT(val_0442,0.613305);
#define CTNODE_NO_0769 773
DEF_STATIC_CONST_VAL_FLOAT(val_0443,0.175467);
#define CTNODE_NO_0774 776
DEF_STATIC_CONST_VAL_FLOAT(val_0444,-0.276407);
#define CTNODE_NO_0773 777
DEF_STATIC_CONST_VAL_FLOAT(val_0445,-0.550878);
#define CTNODE_NO_0768 778
DEF_STATIC_CONST_VAL_FLOAT(val_0446,-0.240328);
#define CTNODE_NO_0781 783
DEF_STATIC_CONST_VAL_FLOAT(val_0447,0.330352);
#define CTNODE_NO_0783 785
DEF_STATIC_CONST_VAL_FLOAT(val_0448,-0.081668);
#define CTNODE_NO_0780 786
DEF_STATIC_CONST_VAL_FLOAT(val_0449,0.383533);
#define CTNODE_NO_0787 789
DEF_STATIC_CONST_VAL_FLOAT(val_0450,-0.324515);
#define CTNODE_NO_0786 790
DEF_STATIC_CONST_VAL_FLOAT(val_0451,-0.624870);
#define CTNODE_NO_0791 793
DEF_STATIC_CONST_VAL_FLOAT(val_0452,-0.513869);
#define CTNODE_NO_0793 795
DEF_STATIC_CONST_VAL_FLOAT(val_0453,0.207874);
#define CTNODE_NO_0796 798
DEF_STATIC_CONST_VAL_FLOAT(val_0454,-0.020471);
#define CTNODE_NO_0795 799
DEF_STATIC_CONST_VAL_FLOAT(val_0455,0.397372);
#define CTNODE_NO_0790 800
DEF_STATIC_CONST_VAL_FLOAT(val_0456,0.271734);
#define CTNODE_NO_0802 804
DEF_STATIC_CONST_VAL_FLOAT(val_0457,-0.261466);
#define CTNODE_NO_0804 806
DEF_STATIC_CONST_VAL_FLOAT(val_0458,-0.009566);
#define CTNODE_NO_0801 807
DEF_STATIC_CONST_VAL_FLOAT(val_0459,-0.381895);
#define CTNODE_NO_0807 809
DEF_STATIC_CONST_VAL_FLOAT(val_0460,-0.089877);
#define CTNODE_NO_0800 810
DEF_STATIC_CONST_VAL_FLOAT(val_0461,-1.126260);
#define CTNODE_NO_0811 813
DEF_STATIC_CONST_VAL_FLOAT(val_0462,-0.906926);
#define CTNODE_NO_0815 817
DEF_STATIC_CONST_VAL_FLOAT(val_0463,-0.625651);
#define CTNODE_NO_0814 818
DEF_STATIC_CONST_VAL_FLOAT(val_0464,-0.385089);
#define CTNODE_NO_0813 819
DEF_STATIC_CONST_VAL_FLOAT(val_0465,-0.359702);
#define CTNODE_NO_0810 820
DEF_STATIC_CONST_VAL_FLOAT(val_0466,0.216904);
#define CTNODE_NO_0821 823
DEF_STATIC_CONST_VAL_FLOAT(val_0467,-0.394349);
#define CTNODE_NO_0820 824
DEF_STATIC_CONST_VAL_FLOAT(val_0468,-0.860573);
#define CTNODE_NO_0825 827
DEF_STATIC_CONST_VAL_FLOAT(val_0469,-0.510488);
#define CTNODE_NO_0824 828
DEF_STATIC_CONST_VAL_FLOAT(val_0470,0.010843);
#define CTNODE_NO_0832 834
DEF_STATIC_CONST_VAL_FLOAT(val_0471,-0.035054);
#define CTNODE_NO_0834 836
DEF_STATIC_CONST_VAL_FLOAT(val_0472,-0.179727);
#define CTNODE_NO_0837 839
DEF_STATIC_CONST_VAL_FLOAT(val_0473,-0.297341);
#define CTNODE_NO_0836 840
DEF_STATIC_CONST_VAL_FLOAT(val_0474,-0.542602);
#define CTNODE_NO_0831 841
DEF_STATIC_CONST_VAL_FLOAT(val_0475,-0.604960);
#define CTNODE_NO_0841 843
DEF_STATIC_CONST_VAL_FLOAT(val_0476,-0.432058);
#define CTNODE_NO_0830 844
DEF_STATIC_CONST_VAL_FLOAT(val_0477,-0.389079);
#define CTNODE_NO_0844 846
DEF_STATIC_CONST_VAL_FLOAT(val_0478,-0.735640);
#define CTNODE_NO_0846 848
DEF_STATIC_CONST_VAL_FLOAT(val_0479,-0.605444);
#define CTNODE_NO_0829 849
DEF_STATIC_CONST_VAL_FLOAT(val_0480,-0.827377);
#define CTNODE_NO_0849 851
DEF_STATIC_CONST_VAL_FLOAT(val_0481,-0.275338);
#define CTNODE_NO_0851 853
DEF_STATIC_CONST_VAL_FLOAT(val_0482,-0.802801);
#define CTNODE_NO_0853 855
DEF_STATIC_CONST_VAL_FLOAT(val_0483,-0.371234);
#define CTNODE_NO_0855 857
DEF_STATIC_CONST_VAL_FLOAT(val_0484,-0.772883);
#define CTNODE_NO_0828 858
DEF_STATIC_CONST_VAL_FLOAT(val_0485,-0.655006);
#define CTNODE_NO_0860 862
DEF_STATIC_CONST_VAL_FLOAT(val_0486,-0.303751);
#define CTNODE_NO_0862 864
DEF_STATIC_CONST_VAL_FLOAT(val_0487,-0.456882);
#define CTNODE_NO_0859 865
DEF_STATIC_CONST_VAL_FLOAT(val_0488,-0.133182);
#define CTNODE_NO_0858 866
DEF_STATIC_CONST_VAL_FLOAT(val_0489,0.114442);
#define CTNODE_NO_0866 868
DEF_STATIC_CONST_VAL_FLOAT(val_0490,-0.167545);
#define CTNODE_NO_0779 869
DEF_STATIC_CONST_VAL_FLOAT(val_0491,-0.876950);
#define CTNODE_NO_0869 871
DEF_STATIC_CONST_VAL_FLOAT(val_0492,-0.640572);
#define CTNODE_NO_0871 873
DEF_STATIC_CONST_VAL_FLOAT(val_0493,-0.321322);
#define CTNODE_NO_0778 874
DEF_STATIC_CONST_VAL_FLOAT(val_0494,-0.925472);
