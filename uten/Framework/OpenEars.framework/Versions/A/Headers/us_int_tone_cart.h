/*************************************************************************/
/*                                                                       */
/*                   Carnegie Mellon University and                      */
/*                Centre for Speech Technology Research                  */
/*                     University of Edinburgh, UK                       */
/*                       Copyright (c) 1998-2001                         */
/*                        All Rights Reserved.                           */
/*                                                                       */
/*  Permission is hereby granted, free of charge, to use and distribute  */
/*  this software and its documentation without restriction, including   */
/*  without limitation the rights to use, copy, modify, merge, publish,  */
/*  distribute, sublicense, and/or sell copies of this work, and to      */
/*  permit persons to whom this work is furnished to do so, subject to   */
/*  the following conditions:                                            */
/*   1. The code must retain the above copyright notice, this list of    */
/*      conditions and the following disclaimer.                         */
/*   2. Any modifications must be clearly marked as such.                */
/*   3. Original authors' names are not deleted.                         */
/*   4. The authors' names are not used to endorse or promote products   */
/*      derived from this software without specific prior written        */
/*      permission.                                                      */
/*                                                                       */
/*  THE UNIVERSITY OF EDINBURGH, <PERSON><PERSON><PERSON><PERSON><PERSON> MELLON UNIVERSITY AND THE      */
/*  CONTRIBUTORS TO THIS WORK DISCLAIM ALL WARRANTIES WITH REGARD TO     */
/*  THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY   */
/*  AND FITNESS, IN NO EVENT SHALL THE UNIVERSITY OF EDINBURGH, CARNEGIE */
/*  MELLON UNIVERSITY NOR THE CONTRIBUTORS BE LIABLE FOR ANY SPECIAL,    */
/*  INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER          */
/*  RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN  AN ACTION   */
/*  OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF     */
/*  OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.       */
/*                                                                       */
/*************************************************************************/
/*             Author:  Alan W Black (<EMAIL>)                    */
/*               Date:  January 2001                                     */
/*************************************************************************/
/*                                                                       */
/*  Derived directly from the tone model cart tree in University of      */
/*  Edinburgh's Festival Speech Synthesis Systems                        */
/*    file:  festival/lib/tobi.scm:f2b_int_tone_cart_tree                */
/*  which was in turn was trained from Boston University FM Radio Data   */
/*  Corpus                                                               */
/*                                                                       */
/*************************************************************************/
/*******************************************************/
/**  Autogenerated cart tree for us_int_tone    */
/**  from .    */
/*******************************************************/

DEF_STATIC_CONST_VAL_STRING(val_0000,"1");
DEF_STATIC_CONST_VAL_STRING(val_0001,"H-H%");
#define CTNODE_NO_0000 2
DEF_STATIC_CONST_VAL_STRING(val_0002,"cc");
DEF_STATIC_CONST_VAL_STRING(val_0003,"NONE");
#define CTNODE_NO_0002 4
DEF_STATIC_CONST_VAL_STRING(val_0004,"10");
#define CTNODE_NO_0004 6
DEF_STATIC_CONST_VAL_STRING(val_0005,"md");
#define CTNODE_NO_0006 8
DEF_STATIC_CONST_VAL_STRING(val_0006,"4");
#define CTNODE_NO_0008 10
DEF_STATIC_CONST_VAL_STRING(val_0007,"det");
#define CTNODE_NO_0010 12
DEF_STATIC_CONST_VAL_STRING(val_0008,"3");
#define CTNODE_NO_0012 14
#define CTNODE_NO_0014 16
DEF_STATIC_CONST_VAL_STRING(val_0009,"in");
#define CTNODE_NO_0016 18
DEF_STATIC_CONST_VAL_STRING(val_0010,"0");
DEF_STATIC_CONST_VAL_STRING(val_0011,"L-L%");
#define CTNODE_NO_0019 21
DEF_STATIC_CONST_VAL_STRING(val_0012,"aux");
#define CTNODE_NO_0021 23
#define CTNODE_NO_0023 25
#define CTNODE_NO_0025 27
DEF_STATIC_CONST_VAL_STRING(val_0013,"6");
#define CTNODE_NO_0027 29
DEF_STATIC_CONST_VAL_STRING(val_0014,"L-H%");
#define CTNODE_NO_0029 31
DEF_STATIC_CONST_VAL_STRING(val_0015,"5");
#define CTNODE_NO_0031 33
DEF_STATIC_CONST_VAL_STRING(val_0016,"2");
#define CTNODE_NO_0033 35
#define CTNODE_NO_0035 37
#define CTNODE_NO_0039 41
#define CTNODE_NO_0038 42
#define CTNODE_NO_0042 44
#define CTNODE_NO_0037 45
#define CTNODE_NO_0018 46
DEF_STATIC_CONST_VAL_STRING(val_0017,"pps");
#define CTNODE_NO_0046 48
#define CTNODE_NO_0048 50
DEF_STATIC_CONST_VAL_STRING(val_0018,"content");
#define CTNODE_NO_0051 53
#define CTNODE_NO_0053 55
#define CTNODE_NO_0055 57
#define CTNODE_NO_0057 59
#define CTNODE_NO_0059 61
#define CTNODE_NO_0061 63
#define CTNODE_NO_0063 65
#define CTNODE_NO_0065 67
#define CTNODE_NO_0067 69
DEF_STATIC_CONST_VAL_STRING(val_0019,"to");
#define CTNODE_NO_0069 71
#define CTNODE_NO_0071 73
#define CTNODE_NO_0073 75
#define CTNODE_NO_0075 77
#define CTNODE_NO_0077 79
#define CTNODE_NO_0079 81
#define CTNODE_NO_0081 83
#define CTNODE_NO_0083 85
#define CTNODE_NO_0085 87
#define CTNODE_NO_0087 89
#define CTNODE_NO_0089 91
DEF_STATIC_CONST_VAL_STRING(val_0020,"H-");
#define CTNODE_NO_0091 93
#define CTNODE_NO_0050 94
