/*******************************************************/
/**  Autogenerated cart tree for us_nums    */
/**  from .    */
/*******************************************************/

DEF_STATIC_CONST_VAL_FLOAT(val_0000,3.800000);
DEF_STATIC_CONST_VAL_STRING(val_0001,"month");
DEF_STATIC_CONST_VAL_STRING(val_0002,"0");
DEF_STATIC_CONST_VAL_STRING(val_0003,"year");
#define CTNODE_us_nums_NO_0002 4
DEF_STATIC_CONST_VAL_STRING(val_0004,"ordinal");
#define CTNODE_us_nums_NO_0001 5
DEF_STATIC_CONST_VAL_STRING(val_0005,"cardinal");
#define CTNODE_us_nums_NO_0006 8
#define CTNODE_us_nums_NO_0005 9
DEF_STATIC_CONST_VAL_STRING(val_0006,"numeric");
DEF_STATIC_CONST_VAL_FLOAT(val_0007,2.000000);
DEF_STATIC_CONST_VAL_STRING(val_0008,"sym");
DEF_STATIC_CONST_VAL_STRING(val_0009,"digits");
#define CTNODE_us_nums_NO_0012 14
#define CTNODE_us_nums_NO_0011 15
#define CTNODE_us_nums_NO_0010 16
#define CTNODE_us_nums_NO_0016 18
#define CTNODE_us_nums_NO_0009 19
#define CTNODE_us_nums_NO_0022 24
#define CTNODE_us_nums_NO_0021 25
#define CTNODE_us_nums_NO_0020 26
#define CTNODE_us_nums_NO_0019 27
DEF_STATIC_CONST_VAL_FLOAT(val_0010,302.299988);
DEF_STATIC_CONST_VAL_STRING(val_0011,"flight");
#define CTNODE_us_nums_NO_0028 30
#define CTNODE_us_nums_NO_0031 33
#define CTNODE_us_nums_NO_0030 34
#define CTNODE_us_nums_NO_0027 35
DEF_STATIC_CONST_VAL_STRING(val_0012,"a");
#define CTNODE_us_nums_NO_0035 37
DEF_STATIC_CONST_VAL_FLOAT(val_0013,669.200012);
#define CTNODE_us_nums_NO_0039 41
#define CTNODE_us_nums_NO_0038 42
#define CTNODE_us_nums_NO_0037 43
DEF_STATIC_CONST_VAL_FLOAT(val_0014,373.200012);
#define CTNODE_us_nums_NO_0043 45
DEF_STATIC_CONST_VAL_FLOAT(val_0015,436.200012);
DEF_STATIC_CONST_VAL_FLOAT(val_0016,392.600006);
#define CTNODE_us_nums_NO_0046 48
#define CTNODE_us_nums_NO_0045 49
DEF_STATIC_CONST_VAL_FLOAT(val_0017,716.500000);
#define CTNODE_us_nums_NO_0049 51
DEF_STATIC_CONST_VAL_FLOAT(val_0018,773.599976);
DEF_STATIC_CONST_VAL_STRING(val_0019,"_other_");
#define CTNODE_us_nums_NO_0052 54
#define CTNODE_us_nums_NO_0051 55
#define CTNODE_us_nums_NO_0000 56
#define CTNODE_us_nums_NO_0057 59
#define CTNODE_us_nums_NO_0059 61
#define CTNODE_us_nums_NO_0056 62
#define CTNODE_us_nums_NO_0063 65
#define CTNODE_us_nums_NO_0065 67
#define CTNODE_us_nums_NO_0067 69
#define CTNODE_us_nums_NO_0062 70
DEF_STATIC_CONST_VAL_FLOAT(val_0020,4.400000);
DEF_STATIC_CONST_VAL_FLOAT(val_0021,2959.600098);
DEF_STATIC_CONST_VAL_FLOAT(val_0022,1773.400024);
#define CTNODE_us_nums_NO_0073 75
#define CTNODE_us_nums_NO_0072 76
#define CTNODE_us_nums_NO_0071 77
#define CTNODE_us_nums_NO_0077 79
#define CTNODE_us_nums_NO_0070 80
DEF_STATIC_CONST_VAL_STRING(val_0023,"to");
#define CTNODE_us_nums_NO_0080 82
#define CTNODE_us_nums_NO_0083 85
DEF_STATIC_CONST_VAL_FLOAT(val_0024,4.600000);
#define CTNODE_us_nums_NO_0085 87
#define CTNODE_us_nums_NO_0082 88
DEF_STATIC_CONST_VAL_FLOAT(val_0025,4.800000);
DEF_STATIC_CONST_VAL_FLOAT(val_0026,2880.000000);
DEF_STATIC_CONST_VAL_FLOAT(val_0027,1633.199951);
DEF_STATIC_CONST_VAL_FLOAT(val_0028,1306.400024);
#define CTNODE_us_nums_NO_0091 93
#define CTNODE_us_nums_NO_0090 94
#define CTNODE_us_nums_NO_0089 95
#define CTNODE_us_nums_NO_0088 96
