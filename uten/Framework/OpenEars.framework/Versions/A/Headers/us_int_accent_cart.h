/*************************************************************************/
/*                                                                       */
/*                   Carnegie Mellon University and                      */
/*                Centre for Speech Technology Research                  */
/*                     University of Edinburgh, UK                       */
/*                       Copyright (c) 1998-2001                         */
/*                        All Rights Reserved.                           */
/*                                                                       */
/*  Permission is hereby granted, free of charge, to use and distribute  */
/*  this software and its documentation without restriction, including   */
/*  without limitation the rights to use, copy, modify, merge, publish,  */
/*  distribute, sublicense, and/or sell copies of this work, and to      */
/*  permit persons to whom this work is furnished to do so, subject to   */
/*  the following conditions:                                            */
/*   1. The code must retain the above copyright notice, this list of    */
/*      conditions and the following disclaimer.                         */
/*   2. Any modifications must be clearly marked as such.                */
/*   3. Original authors' names are not deleted.                         */
/*   4. The authors' names are not used to endorse or promote products   */
/*      derived from this software without specific prior written        */
/*      permission.                                                      */
/*                                                                       */
/*  THE UNIVERSITY OF EDINBURGH, <PERSON><PERSON><PERSON><PERSON><PERSON> MELLON UNIVERSITY AND THE      */
/*  CONTRIBUTORS TO THIS WORK DISCLAIM ALL WARRANTIES WITH REGARD TO     */
/*  THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY   */
/*  AND FITNESS, IN NO EVENT SHALL THE UNIVERSITY OF EDINBURGH, CARNEGIE */
/*  MELLON UNIVERSITY NOR THE CONTRIBUTORS BE LIABLE FOR ANY SPECIAL,    */
/*  INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER          */
/*  RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN  AN ACTION   */
/*  OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF     */
/*  OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.       */
/*                                                                       */
/*************************************************************************/
/*             Author:  Alan W Black (<EMAIL>)                    */
/*               Date:  January 2001                                     */
/*************************************************************************/
/*                                                                       */
/*  Derived directly from the accent model cart tree in University of    */
/*  Edinburgh's Festival Speech Synthesis Systems                        */
/*    file:  festival/lib/tobi.scm:f2b_int_accent_cart_tree              */
/*  which was in turn was trained from Boston University FM Radio Data   */
/*  Corpus                                                               */
/*                                                                       */
/*************************************************************************/
/*******************************************************/
/**  Autogenerated cart tree for us_int_accent    */
/**  from .    */
/*******************************************************/

DEF_STATIC_CONST_VAL_STRING(val_0000,"1");
DEF_STATIC_CONST_VAL_STRING(val_0001,"H*");
#define CTNODE_NO_0000 2
DEF_STATIC_CONST_VAL_STRING(val_0002,"NONE");
#define CTNODE_NO_0002 4
#define CTNODE_NO_0004 6
DEF_STATIC_CONST_VAL_STRING(val_0003,"10");
#define CTNODE_NO_0006 8
DEF_STATIC_CONST_VAL_STRING(val_0004,"to");
#define CTNODE_NO_0008 10
DEF_STATIC_CONST_VAL_STRING(val_0005,"cc");
#define CTNODE_NO_0010 12
#define CTNODE_NO_0012 14
DEF_STATIC_CONST_VAL_STRING(val_0006,"in");
#define CTNODE_NO_0014 16
DEF_STATIC_CONST_VAL_STRING(val_0007,"wp");
#define CTNODE_NO_0016 18
DEF_STATIC_CONST_VAL_STRING(val_0008,"aux");
#define CTNODE_NO_0018 20
DEF_STATIC_CONST_VAL_STRING(val_0009,"det");
#define CTNODE_NO_0020 22
DEF_STATIC_CONST_VAL_STRING(val_0010,"0");
#define CTNODE_NO_0022 24
#define CTNODE_NO_0024 26
DEF_STATIC_CONST_VAL_STRING(val_0011,"md");
#define CTNODE_NO_0026 28
DEF_STATIC_CONST_VAL_STRING(val_0012,"3");
#define CTNODE_NO_0029 31
#define CTNODE_NO_0028 32
DEF_STATIC_CONST_VAL_STRING(val_0013,"4");
#define CTNODE_NO_0033 35
#define CTNODE_NO_0035 37
#define CTNODE_NO_0037 39
#define CTNODE_NO_0039 41
#define CTNODE_NO_0041 43
#define CTNODE_NO_0043 45
#define CTNODE_NO_0032 46
#define CTNODE_NO_0046 48
#define CTNODE_NO_0048 50
DEF_STATIC_CONST_VAL_STRING(val_0014,"content");
#define CTNODE_NO_0051 53
#define CTNODE_NO_0050 54
DEF_STATIC_CONST_VAL_STRING(val_0015,"2");
#define CTNODE_NO_0056 58
#define CTNODE_NO_0058 60
#define CTNODE_NO_0061 63
#define CTNODE_NO_0060 64
#define CTNODE_NO_0055 65
#define CTNODE_NO_0065 67
#define CTNODE_NO_0054 68
DEF_STATIC_CONST_VAL_STRING(val_0016,"L+H*");
#define CTNODE_NO_0069 71
#define CTNODE_NO_0068 72
#define CTNODE_NO_0073 75
#define CTNODE_NO_0075 77
#define CTNODE_NO_0078 80
#define CTNODE_NO_0077 81
#define CTNODE_NO_0072 82
#define CTNODE_NO_0084 86
#define CTNODE_NO_0083 87
#define CTNODE_NO_0082 88
DEF_STATIC_CONST_VAL_STRING(val_0017,"5");
#define CTNODE_NO_0089 91
#define CTNODE_NO_0088 92
#define CTNODE_NO_0093 95
#define CTNODE_NO_0095 97
#define CTNODE_NO_0097 99
#define CTNODE_NO_0099 101
DEF_STATIC_CONST_VAL_STRING(val_0018,"!H*");
#define CTNODE_NO_0092 102
#define CTNODE_NO_0103 105
#define CTNODE_NO_0105 107
#define CTNODE_NO_0102 108
#define CTNODE_NO_0108 110
#define CTNODE_NO_0111 113
#define CTNODE_NO_0110 114
DEF_STATIC_CONST_VAL_STRING(val_0019,"7");
#define CTNODE_NO_0116 118
#define CTNODE_NO_0115 119
#define CTNODE_NO_0114 120
#define CTNODE_NO_0120 122
#define CTNODE_NO_0122 124
#define CTNODE_NO_0124 126
#define CTNODE_NO_0127 129
#define CTNODE_NO_0129 131
#define CTNODE_NO_0131 133
#define CTNODE_NO_0133 135
#define CTNODE_NO_0126 136
#define CTNODE_NO_0136 138
DEF_STATIC_CONST_VAL_STRING(val_0020,"6");
#define CTNODE_NO_0138 140
#define CTNODE_NO_0140 142
#define CTNODE_NO_0142 144
#define CTNODE_NO_0145 147
#define CTNODE_NO_0144 148
