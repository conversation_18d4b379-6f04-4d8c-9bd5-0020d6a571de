//
//  OEGrammarDefinitions.h
//  OpenEars
//
//  Created by Halle on 9/30/13.
//  Copyright (c) 2013 Politepix. All rights reserved.
//

#define ThisWillBeSaidOnce @"ThisWillBeSaidOnce"
#define ThisCanBeSaidOnce @"ThisCanBeSaidOnce"
#define ThisWillBeSaidWithOptionalRepetitions @"ThisWillBeSaidWithOptionalRepetitions"
#define ThisCanBeSaidWithOptionalRepetitions @"ThisCanBeSaidWithOptionalRepetitions"
#define OneOfTheseWillBeSaidOnce @"OneOfTheseWillBeSaidOnce"
#define OneOfTheseCanBeSaidOnce @"OneOfTheseCanBeSaidOnce"
#define OneOfTheseWillBeSaidWithOptionalRepetitions @"OneOfTheseWillBeSaidWithOptionalRepetitions"
#define OneOfTheseCanBeSaidWithOptionalRepetitions @"OneOfTheseCanBeSaidWithOptionalRepetitions"
