//
//  MainViewController.m
//  uten
//
//  Created by 簡大翔 on 2019/6/19.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "MainViewController.h"
#import "GRRequestsManager.h"
#import "GRListingRequest.h"
#import "SSZipArchive.h"
#import "JHUD.h"
#import "MQTTClient.h"
#import "Game01Controller.h"
#import "eStudentViewController.h"
#import "eClassViewController.h"
#import "eTeacherViewController.h"
#import "eMaterialViewController.h"
#import "ABCViewController.h"
#import "ClassGradeViewController.h"
#import "ClassScheduleViewController.h"
#import "StudentGradeViewController.h"
#import "CourseViewController.h"
#import "PlayViewController.h"
#import "PureSayViewController.h"
#import "RankingListViewController.h"
#import "SoundCheckViewController.h"
#import "RetestViewController.h"
#import "selector410ViewController.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "UTENEnum.h"

@interface MainViewController () <GRRequestsManagerDelegate,MQTTSessionDelegate> {
    MQTTSession *session;
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    IBOutlet UILabel *LoginLink;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    
    UIActivityIndicatorView *indicator;
    int downcount;
    NSString *classContent;
    NSString *upgradeContent;
    JHUD *hudView;
    int messageid;
    NSString *ServerIP;
    NSString *uten_class,*r_uten_class;
}
@property (nonatomic, strong) GRRequestsManager *requestsManager;
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation MainViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    
    
//    NSString *GetCNAME = [defaults objectForKey:@"CNAME"];
//    [[NSUserDefaults standardUserDefaults] setObject : GetCNAME forKey:@" CNAME "];
//    [[NSUserDefaults standardUserDefaults] synchronize];

    NSString *ReturnCNAME = [defaults objectForKey:@"CNAME"];
    [[NSUserDefaults standardUserDefaults] setObject: ReturnCNAME forKey:@"CNAME2"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    //LoginLink.text=@"1234";
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    //
    // for download
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"class.csv"]];
    classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    
    NSString *ugdFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"upgrade.csv"]];
    upgradeContent= [NSString stringWithContentsOfFile:ugdFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"upgrade FIle:%@\n%@",DirFile,upgradeContent);
    /*
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:@"phototracq.com" user:@"<EMAIL>" password:@"4rfvCDE#2wsxZAQ!"];
     */
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:ServerIP user:@"uten" password:@"zZ54775178"];
    self.requestsManager.delegate=self;
    //
    
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
    transport.host = ServerIP;
    transport.port = 1883;
    
    session = [[MQTTSession alloc] init];
    session.transport = transport;
    session.delegate=self;
    [session connectWithConnectHandler:^(NSError *error) {
        // Do some work
        
        [self publishCommandLogin];
        
        NSLog(@"Subscription %@",uten_class);
        [session subscribeToTopic:r_uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                NSLog(@"Subscription failed %@", error.localizedDescription);
            } else {
                NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
            }
        }];
    }];
    messageid=0;

}

- (void)publishCommandLogin {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdLogin;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
    }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

- (IBAction)btPlay:(id)sender {
 //   [self dismissViewControllerAnimated:NO completion:nil];
    /*
    [session publishData:[[[NSString alloc] initWithFormat:(@"$uten_class01,START,0001,lbu1u4,0003,0004,end~"),ID] dataUsingEncoding:NSUTF8StringEncoding] onTopic:@"uten_class01" retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
    }];
     */
    
    
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    CourseViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"CourseViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
     

    
}
- (IBAction)btRollCall:(id)sender {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdRollCall;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
    }];
}
- (IBAction)btLogout:(id)sender {
    //   [self dismissViewControllerAnimated:NO completion:nil];
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdLogout;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
    }];
    session.delegate = nil;
    [session disconnect];
    session = nil;
    exit(-1);
}
- (IBAction)btPureSayPage:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    PureSayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"PureSayViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btABCPage:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    ABCViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"ABCViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btEditStudent:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    eStudentViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"eStudentViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btEditTeacher:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    eTeacherViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"eTeacherViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btEditClass:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    eClassViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"eClassViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btEditMaterial:(id)sender {
    /*
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    eMaterialViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"eMaterialViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
     */
}
- (IBAction)btClassSchedule:(id)sender {
    /*
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    ClassScheduleViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"ClassScheduleViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
    */
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    PlayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"PlayViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btClassGrade:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    ClassGradeViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"ClassGradeViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btStudentGrade:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    StudentGradeViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"StudentGradeViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}

- (IBAction)btRetest:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    RetestViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"RetestViewController"];
    
    // 檢查是否在導航堆疊中
    if (self.navigationController) {
        [self.navigationController pushViewController:myViewController animated:YES];
    } else {
        // 如果沒有導航控制器，創建一個並設置為根視圖控制器
        UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:self];
        [navController pushViewController:myViewController animated:YES];
        // 顯示新的導航控制器
        [self presentViewController:navController animated:YES completion:nil];
    }
}
- (IBAction)btselector41:(id)sender {
    [AppRoutes push:AppRoutes.selector410];
}



-(NSArray *)listFileAtPath:(NSString *)path
{
    //-----> LIST ALL FILES <-----//
    NSLog(@"LISTING ALL FILES FOUND");
    int count;
    NSArray *directoryContent = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:path error:NULL];
    for (count = 0; count < (int)[directoryContent count]; count++)
    {
        NSLog(@"File %d: %@", (count + 1), [directoryContent objectAtIndex:count]);
    }
    return directoryContent;
}
- (IBAction)btUpgrade:(id)sender {
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSArray *splitLine = [upgradeContent componentsSeparatedByString:@"\n"];
    for(int i=0;i<[splitLine count];i++) {
        //for(int i=0;i<2;i++) {
        NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
        if([split1 count]>=4) {
            NSString *locZFile = [documentsDirectoryPath stringByAppendingPathComponent:split1[2]];
            NSString *remZFile=[@"/uten/class/" stringByAppendingPathComponent:[NSString stringWithFormat:split1[2]]];
            NSLog(@"ZIP:%@",remZFile);
            [self.requestsManager addRequestForDownloadFileAtRemotePath:remZFile toLocalPath:locZFile];
            [self.requestsManager startProcessingRequests];
        }
    }
    /*
    indicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
    indicator.frame = CGRectMake(0.0, 0.0, 200.0, 200.0);
    indicator.center = self.view.center;
    [self.view addSubview:indicator];
    [indicator bringSubviewToFront:self.view];
    [UIApplication sharedApplication].networkActivityIndicatorVisible = TRUE;
    [indicator startAnimating];
*/
    hudView = [[JHUD alloc]initWithFrame:self.view.bounds];
    hudView.frame= CGRectMake(352.0f, 234.0f, 300.0f, 300.0f);
    //hudView.alpha=0.8f;
    hudView.messageLabel.text = @"題庫更新中,請稍候!";
    [hudView showAtView:self.view hudType:JHUDLoadingTypeCircleJoin];
}



- (IBAction)btRankingList:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    RankingListViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"RankingListViewController"];
    myViewController.modalPresentationStyle = UIModalPresentationFullScreen;//加一行.改全螢幕
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btSoundCheck:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    SoundCheckViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SoundCheckViewController"];
    myViewController.modalPresentationStyle = UIModalPresentationFullScreen;//加一行.改全螢幕
    [self presentViewController:myViewController animated:YES completion:nil];
}
- (IBAction)btGame01:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    Game01Controller *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"Game01Controller"];
    [self presentViewController:myViewController animated:YES completion:nil];
}

#pragma mark - GRRequestsManagerDelegate

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didScheduleRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didScheduleRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteListingRequest:(id<GRRequestProtocol>)request listing:(NSArray *)listing
{
    NSLog(@"requestsManager:didCompleteListingRequest:listing: \n%@", listing);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteCreateDirectoryRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteCreateDirectoryRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDeleteRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDeleteRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompletePercent:(float)percent forRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompletePercent:forRequest: %f", percent);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteUploadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteUploadRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDownloadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDownloadRequest:");
    GRListingRequest *req = (GRListingRequest *)request;
    NSLog(@"RETEMO FILE:%@",req.path);
    
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailWritingFileAtPath:(NSString *)path forRequest:(id<GRDataExchangeRequestProtocol>)request error:(NSError *)error
{
    NSLog(@"requestsManager:didFailWritingFileAtPath:forRequest:error: \n %@", error);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailRequest:(id<GRRequestProtocol>)request withError:(NSError *)error
{
    NSLog(@"requestsManager:didFailRequest:withError: \n %@", error);
}

-(void)requestsManagerDidCompleteQueue:(id<GRRequestsManagerProtocol>)requestsManager{
    NSLog(@"整个文件夹下载完成+++++++++++++++++");
    
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *unclassPath=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//"]];
    //NSString *zipFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l1u1u4.zip"];
    //NSString *unzipPath=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//l1u1u4"]];
    NSArray *splitLine = [upgradeContent componentsSeparatedByString:@"\n"];
    
    for(int i=0;i<[splitLine count];i++) {
        NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
        if([split1 count]>=4) {
            NSString *zipZFile = [documentsDirectoryPath stringByAppendingPathComponent:split1[2]];
            NSString *unzipPath=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@",split1[1]]];
            NSLog(@"UNZIP:%@->%@",zipZFile,unzipPath);
            [[NSFileManager defaultManager] removeItemAtPath:unzipPath error:nil];
            [SSZipArchive unzipFileAtPath:zipZFile toDestination:unzipPath];
        }
    }
    NSLog(@"After FLIST:%@",[[NSFileManager defaultManager] contentsOfDirectoryAtPath:unclassPath error:NULL]);
    //remove dir
    //[[NSFileManager defaultManager] removeItemAtPath:unzipPath error:nil];
    //NSLog(@"Before FLIST:%@",[[NSFileManager defaultManager] contentsOfDirectoryAtPath:unclassPath error:NULL]);
    //[indicator stopAnimating];
    [JHUD hideForView:self.view];
    messageid=0;
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:@"題庫下載完成,請重新登入才會套用新題庫" delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
    [alert show];
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    if(messageid==0) exit(0);
}


- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    NSString *str = [command toJSON:NSUTF8StringEncoding error:&error];
    if (command.isRollCall) {
        messageid=1;
        UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:str delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
        [alert show];
    }
}
@end
