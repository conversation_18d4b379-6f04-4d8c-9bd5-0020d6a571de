//
//  eTeacherViewController.m
//  uten
//
//  Created by <PERSON> on 2019/8/1.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "eTeacherViewController.h"
#import "OHMySQL.h"
@interface eTeacherViewController () {
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    int state;
    int messageid;
    __weak IBOutlet UITextField *pName;
    __weak IBOutlet UIButton *pStartDate;
    __weak IBOutlet UITextField *pTel1;
    __weak IBOutlet UITextField *pTel2;
    __weak IBOutlet UITextField *pCell1;
    __weak IBOutlet UITextField *pCell2;
    __weak IBOutlet UITextField *pAddr1;
    __weak IBOutlet UITextField *pAddr2;
    __weak IBOutlet UITextField *pContact1;
    __weak IBOutlet UITextField *pct_tel1;
    __weak IBOutlet UITextField *pContact2;
    __weak IBOutlet UITextField *pct_tel2;
    __weak IBOutlet UITextField *pRemark1;
    __weak IBOutlet UITextField *pRemark2;
    __weak IBOutlet UIButton *pButton;
    __weak IBOutlet UIButton *pButtonDelete;
}
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation eTeacherViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    state=1;
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    [self ClearEditItem];
    //
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {
    
    [self dismissViewControllerAnimated:NO completion:nil];
}
- (IBAction)btDelete:(id)sender {
    messageid=2;
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否要刪除紀錄還是要退出編輯" delegate:self cancelButtonTitle:@"退出編輯" otherButtonTitles:@"刪除本記錄",@"取消",nil];
    [alertView show];
}
-(void) ClearEditItem {
    pName.text=@"";
    [pStartDate setTitle:@"" forState:UIControlStateNormal];
    pTel1.text=@"";
    pTel2.text=@"";
    pCell1.text=@"";
    pCell2.text=@"";
    pAddr1.text=@"";
    pAddr2.text=@"";
    pContact1.text=@"";
    pContact2.text=@"";
    pct_tel1.text=@"";
    pct_tel2.text=@"";
    pRemark1.text=@"";
    pRemark2.text=@"";
}
- (IBAction)btButton:(id)sender {
    switch(state)
    {
        case 1:
        {
            [self ClearEditItem];
            pButtonDelete.hidden=false;
            [pButton setImage:[UIImage imageNamed:@"btokr.png"] forState:UIControlStateNormal];
            state=2;
        }
            break;
        case 2:
        {
            messageid=1;
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"是否存檔" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"存檔",nil];
            [alertView show];
        }
            break;
    }
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    
    switch (buttonIndex) {
        case 0:
            switch(messageid) {
                case 2:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btadd.png"] forState:UIControlStateNormal];
                    state=1;
                }
                break;
            }
            break;
        case 1:
            switch(messageid) {
                case 1:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btadd.png"] forState:UIControlStateNormal];
                    state=1;
                    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"存檔完成!!" delegate:self cancelButtonTitle:@"確定" otherButtonTitles:nil];
                    [alertView show];
                }
                break;
                case 2:
                {
                    pButtonDelete.hidden=true;
                    [pButton setImage:[UIImage imageNamed:@"btadd.png"] forState:UIControlStateNormal];
                    state=1;
                    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"刪除成功!!" delegate:self cancelButtonTitle:@"確定" otherButtonTitles:nil];
                    [alertView show];
                }
                    break;
            }
            break;
        default:
            break;
    }
    
}
@end
