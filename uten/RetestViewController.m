//
//  RetestViewController.m
//  uten
//
//  Created by 蔡駿寓 on 2024/11/25.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "RetestViewController.h"

@interface RetestViewController ()
@property (weak, nonatomic) IBOutlet UIImageView *imageView1;
@property (weak, nonatomic) IBOutlet UIImageView *imageView2;
@property (weak, nonatomic) IBOutlet UIImageView *imageView3;
@property (weak, nonatomic) IBOutlet UIImageView *imageView4;
@property (weak, nonatomic) IBOutlet UIImageView *imageView5;
@property (weak, nonatomic) IBOutlet UIImageView *imageView6;
@end
@implementation RetestViewController
- (void)viewDidLoad {
    [super viewDidLoad];
    // (旋轉背景圖) 設置每個圖片視圖的圖片
    self.imageView1.image = [UIImage imageNamed:@"Retest_004.png"];//(旋轉)
    self.imageView2.image = [UIImage imageNamed:@"Retest_006.png"];//(旋轉)
    self.imageView3.image = [UIImage imageNamed:@"Retest_005.png"];//(旋轉)
    self.imageView4.image = [UIImage imageNamed:@"Retest_003.png"];//(旋轉)
    self.imageView5.image = [UIImage imageNamed:@"Retest_007.png"];//(旋轉)
    self.imageView6.image = [UIImage imageNamed:@"Retest_002.png"];//(旋轉)
//    self.imageView7.image = [UIImage imageNamed:@"SoundCheck 14.png"];//(旋轉)
    
    // (旋轉背景圖) 啟動每個圖片視圖的旋轉動畫  //20秒1圈，或10秒1圈
    [self startRotatingImageView:self.imageView1 withDuration:40.0];    //(旋轉)
    [self startRotatingImageView:self.imageView2 withDuration:30.0];    //(旋轉)
    [self startRotatingImageView:self.imageView3 withDuration:35.0];    //(旋轉)
    [self startRotatingImageView:self.imageView4 withDuration:30.0];    //(旋轉)
    [self startRotatingImageView:self.imageView5 withDuration:35.0];    //(旋轉)
    [self startRotatingImageView:self.imageView6 withDuration:40.0];    //(旋轉)
//    [self startRotatingImageView:self.imageView7 withDuration:35.0];    //(旋轉)
//        //(搖.手指) 初始化圖片名稱，導入2張圖片
//        self.imageNames = @[@"SoundCheck011.png", @"SoundCheck012.png"];
//        self.currentImageIndex = 0;
//
//        //(搖.手指) 設置初始圖片
//        self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];
//
//        //(搖.手指) 設置定時器以自動切換圖片，0.5秒切換1張，repeat重複
//        self.timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(nextImage) userInfo:nil repeats:YES];
//    //-----
//
//    [self setupLayout];
//    [self setupViewModel];
//    [self setupFtp];
}
- (void)startRotatingImageView:(UIImageView *)imageView withDuration:(CGFloat)duration {
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];//創建基本動畫  //(旋轉)
    rotationAnimation.fromValue = @(0);  //旋轉的起點為 0度。       //(旋轉)
    rotationAnimation.toValue = @(M_PI * 2);//表示轉360度         //(旋轉)
    //rotationAnimation.toValue = @(M_PI * 2 * 120 / 360);//旋轉弧長度.轉1/3圈
    rotationAnimation.fillMode = kCAFillModeForwards;//確定重新來 //(旋轉)
    rotationAnimation.removedOnCompletion = NO;//確定重新來       //(旋轉)
    rotationAnimation.duration = duration;//持續時間.旋轉速度      //(旋轉)
    rotationAnimation.repeatCount = INFINITY;//無限次重復         //(旋轉)
    [imageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];// 將動畫添加到圖片的layer中   //(旋轉)
}
- (IBAction)btback:(id)sender {
    if (self.navigationController) {
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}
@end


