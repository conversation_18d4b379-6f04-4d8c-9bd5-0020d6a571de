//
//  Game01Controller.m
//  uten
//
//  Created by <PERSON> on 2024/4/17.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "Game01Controller.h"
#import "SignatureDrawView.h"
@interface Game01Controller () {
    SignatureDrawView *dW00[200];
    IBOutlet UIImageView *uiv;
    NSTimer *cTimer;
    NSTimer *cTimer100ms;
    NSTimer *cTimer300ms;
    int showmode;
    int showinx;
    int treeinx;
    UIImageView *tree01;
    UIImageView *tree02;
    UIImageView *tree03;
    UIImageView *tree04;
    
    UIImageView *tree11;
    UIImageView *tree12;
    UIImageView *tree13;
    UIImageView *tree14;
    
    UIImageView *car01;
    UIImageView *car02;
    UIImageView *car03;
    
    UIImageView *car11;
    UIImageView *car12;
    UIImageView *car13;
    
    UIImageView *test;
    
    int picinx,picinx1,picinx2,picinx3;
    int pi1,pi2,pi3,pi4;
    int lt;
    float a1,b1;
    float a2,b2;
    float a3,b3;

    float ta1,tb1;
    float ta2,tb2;
    float ta3,tb3;
    float ta4,tb4;
    
    float l1x,l1y;
    float l1w,l1h;
    float x1s,x1e;
    float y1s,y1e;
    
    float l2x,l2y;
    float l2w,l2h;
    float x2s,x2e;
    float y2s,y2e;
    
    float l3x,l3y;
    float l3w,l3h;
    float x3s,x3e;
    float y3s,y3e;
    
    float tl1x,tl1y;
    float tl1w,tl1h;
    float tx1s,tx1e;
    float ty1s,ty1e;
    
    float tl2x,tl2y;
    float tl2w,tl2h;
    float tx2s,tx2e;
    float ty2s,ty2e;
    
    float tl3x,tl3y;
    float tl3w,tl3h;
    float tx3s,tx3e;
    float ty3s,ty3e;
    
    float tl4x,tl4y;
    float tl4w,tl4h;
    float tx4s,tx4e;
    float ty4s,ty4e;
    UIImage *im1,*im2,*im3;
    UIImage *tim1,*tim2,*tim3,*tim4;
    UIImage *sim1,*sim2,*sim3;
    float speed;
    NSArray *cda;
    int dlen[3];
    NSString *dstr[3];
    
}

@end

@implementation Game01Controller

- (void)viewDidLoad {
    [super viewDidLoad];
    float xd=2; //1180; //1.7355;
    float yd=2; //1.8731;
    NSLog(@"INDATA:%@_%@", _seltag,_uclass);
    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
    cda = [_seltag componentsSeparatedByCharactersInSet:set];
    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *ssrc=cda[5+x*2];
        NSLog(@"INDATA2:%@", ssrc);
        switch(x) {
            case 0:
            case 1:
            case 2: dlen[x]=ssrc.length;
                dstr[x]=ssrc;
                break;
        }
    }
    tree01 = [[UIImageView alloc] initWithFrame:CGRectMake(768/xd,236/yd,32,32)];
    [self.view addSubview:tree01];
    tree02 = [[UIImageView alloc] initWithFrame:CGRectMake(925/xd,236/yd,32,32)];
    [self.view addSubview:tree02];
    tree03 = [[UIImageView alloc] initWithFrame:CGRectMake(1078/xd,236/yd,32,32)];
    [self.view addSubview:tree03];
    tree04 = [[UIImageView alloc] initWithFrame:CGRectMake(1230/xd,236/yd,32,32)];
    [self.view addSubview:tree04];

    int cw=136/2;
    car01 = [[UIImageView alloc] initWithFrame:CGRectMake(785/xd,227/yd,cw,cw/2.27)];
    [self.view addSubview:car01];
    car02 = [[UIImageView alloc] initWithFrame:CGRectMake(943/xd,227/yd,cw,cw/2.27)];
    [self.view addSubview:car02];
    car03 = [[UIImageView alloc] initWithFrame:CGRectMake(1101/xd,227/yd,cw,cw/2.27)];
    [self.view addSubview:car03];
    speed=0.1;
/* 0.0266
    test = [[UIImageView alloc] initWithFrame:CGRectMake(0,0,25,75)];
    test.image=[UIImage imageNamed:@"a1g.png"];
    [self.view addSubview:test];
 */
    sim1=[UIImage imageNamed:@"roll01.png"];
    sim2=[UIImage imageNamed:@"roll02.png"];
    sim3=[UIImage imageNamed:@"roll03.png"];
    lt=0;
   // int x=498; //520;
    int x=320+54+54;
    int y=618; //720;
    for(int i=0;i<dlen[0];i++) {
        dW00[10+i] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(x+27*i,y,25,75)];
        [self.view addSubview:dW00[10+i]];
        dW00[10+i].hidden=YES;
    }
    x=15+54+54+81+54;
    for(int i=0;i<dlen[1];i++) {
        dW00[i] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(x+27*i,y,25,75)];
        [self.view addSubview:dW00[i]];
        dW00[i].hidden=YES;
    }
    
    x=745-27*4; //+54+54;;
    for(int i=0;i<dlen[2];i++) {
        dW00[20+i] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(x+27*i,y,25,75)];
        [self.view addSubview:dW00[20+i]];
        dW00[20+i].hidden=YES;
    }
    /*
    dW00[0] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(x+27,y,25,75)];
    [self.view addSubview:dW00[0]];
    dW00[0] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(x+27*2,y,25,75)];
    [self.view addSubview:dW00[0]];
     */
    // Do any additional setup after loading the view.

    
    
    showmode=0;
    showinx=0;
    treeinx=0;
    /*
    cTimer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    cTimer100ms = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp100ms:) userInfo:nil repeats:YES];
    cTimer300ms = [NSTimer scheduledTimerWithTimeInterval:0.3 target:self selector:@selector(timesUp300ms:) userInfo:nil repeats:YES];
    */
    NSThread *newThread = [[NSThread alloc]initWithTarget:self selector:@selector(tUp:) object:@"Thread"];
    NSThread *newThread100ms = [[NSThread alloc]initWithTarget:self selector:@selector(tUp100ms:) object:@"Thread100ms"];
    NSThread *newThread300ms = [[NSThread alloc]initWithTarget:self selector:@selector(tUp300ms:) object:@"Thread300ms"];
    [newThread start];
    [newThread300ms start];
    [newThread100ms start];
       
}
//3w 4s /5 * 4
//6w
- (void) tUp:(NSString *)params {
    while (true)
    {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            switch(showinx) {
                case 0: uiv.image=sim1; break;
                case 1: uiv.image=sim2; break;
                case 2: uiv.image=sim3; break;
            }
            showinx++;
            showinx%=3;
        });

        // thread loop
        [NSThread sleepForTimeInterval:0.5]; //等同于sleep(1);
    }
}
- (NSString *)getpicname:(NSString *)d
{
    if([d isEqual:@"a"]) return @"a1g.png";
    if([d isEqual:@"b"]) return @"b1g.png";
    if([d isEqual:@"c"]) return @"c1g.png";
    if([d isEqual:@"d"]) return @"d1g.png";
    if([d isEqual:@"e"]) return @"e1g.png";
    if([d isEqual:@"f"]) return @"f1g.png";
    if([d isEqual:@"g"]) return @"g1g.png";
    if([d isEqual:@"h"]) return @"h1g.png";
    if([d isEqual:@"i"]) return @"i1g.png";
    if([d isEqual:@"j"]) return @"j1g.png";
    if([d isEqual:@"k"]) return @"k1g.png";
    if([d isEqual:@"l"]) return @"l1g.png";
    if([d isEqual:@"m"]) return @"m1g.png";
    if([d isEqual:@"n"]) return @"n1g.png";
    if([d isEqual:@"o"]) return @"o1g.png";
    if([d isEqual:@"p"]) return @"p1g.png";
    if([d isEqual:@"q"]) return @"q1g.png";
    if([d isEqual:@"r"]) return @"r1g.png";
    if([d isEqual:@"s"]) return @"s1g.png";
    if([d isEqual:@"t"]) return @"t1g.png";
    if([d isEqual:@"u"]) return @"u1g.png";
    if([d isEqual:@"v"]) return @"v1g.png";
    if([d isEqual:@"w"]) return @"w1g.png";
    if([d isEqual:@"x"]) return @"x1g.png";
    if([d isEqual:@"y"]) return @"y1g.png";
    if([d isEqual:@"z"]) return @"z1g.png";
    return @"a1g.png";
}
- (void) tUp100ms:(NSString *)params {
    while (true)
    {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            // use weakSelf here
            
            int xp1=180;
            int xp2=180;
            int xp3=120;
            switch(picinx) {
                    //[dWb0 isWord:Wb0.image]
                case 0: //im1=[UIImage imageNamed:@"car_1_1.png"];
                    /*
                    im1=[self addImage:[UIImage imageNamed:@"car_1_1.png"] dstImage:[self getpicname:[dstr[0] componentsSeparatedByString:@":"][0]] x:xp1 y:10];
                    for(int i=1;i<dlen[0];i++) im1=[self addImage:im1 dstImage:[self getpicname:[dstr[0] componentsSeparatedByString:@":"][i]] x:xp1+i*50 y:10];
                    im2=[self addImage:[UIImage imageNamed:@"car_5_1.png"] dstImage:[self getpicname:[dstr[1] componentsSeparatedByString:@":"][0]] x:xp1 y:10];
                    for(int i=1;i<dlen[0];i++) im2=[self addImage:im2 dstImage:[self getpicname:[dstr[1] componentsSeparatedByString:@":"][i]] x:xp1+i*50 y:10];
                    im3=[self addImage:[UIImage imageNamed:@"car_3_1.png"] dstImage:[self getpicname:[dstr[2] componentsSeparatedByString:@":"][0]] x:xp1 y:10];
                    for(int i=1;i<dlen[0];i++) im3=[self addImage:im3 dstImage:[self getpicname:[dstr[2] componentsSeparatedByString:@":"][i]] x:xp1+i*50 y:10];
                    */
                    
                        im1=[self addImage:[UIImage imageNamed:@"car_1_1.png"] dstImage:@"c1g.png" x:xp1 y:10];
                        im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
                        im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
                        
                    
                        im2=[self addImage:[UIImage imageNamed:@"car_5_1.png"] dstImage:@"p1g.png" x:xp2 y:10];
                        im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
                        im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
                        im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
                        im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
                        im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                        im3=[self addImage:[UIImage imageNamed:@"car_3_1.png"] dstImage:@"g1g.png" x:xp3 y:10];
                        im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
                        im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
                        im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
                        im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];
                     
                        picinx++;
                        break;
                    /*
                case 1: //im1=[UIImage imageNamed:@"car_1_2.png"];
                    im1=[self addImage:[UIImage imageNamed:@"car_1_2.png"] dstImage:@"c1g.png" x:xp1 y:10];
                    im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
                    im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
                        im2=[self addImage:[UIImage imageNamed:@"car_5_2.png"] dstImage:@"p1g.png" x:xp2 y:10];
                    im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
                    im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
                    im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
                    im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
                    im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                        im3=[self addImage:[UIImage imageNamed:@"car_3_2.png"] dstImage:@"g1g.png" x:xp3 y:10];
                    im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
                    im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
                    im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
                    im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];
                    break;
                case 2: //im1=[UIImage imageNamed:@"car_1_3.png"];
                    im1=[self addImage:[UIImage imageNamed:@"car_1_3.png"] dstImage:@"c1g.png" x:xp1 y:10];
                    im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
                    im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
                    im2=[self addImage:[UIImage imageNamed:@"car_5_3.png"] dstImage:@"p1g.png" x:xp2 y:10];
                    im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
                    im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
                    im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
                    im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
                    im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                        im3=[self addImage:[UIImage imageNamed:@"car_3_3.png"] dstImage:@"g1g.png" x:xp3 y:10];
                    im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
                    im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
                    im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
                    im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];
                    break;
                    
                case 4:
                case 3: //im1=[UIImage imageNamed:@"car_1_4.png"];
                    im1=[self addImage:[UIImage imageNamed:@"car_1_4.png"] dstImage:@"c1g.png" x:xp1 y:10];
                    im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
                    im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
                    im2=[self addImage:[UIImage imageNamed:@"car_5_4.png"] dstImage:@"p1g.png" x:xp2 y:10];
                    im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
                    im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
                    im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
                    im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
                    im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                        im3=[self addImage:[UIImage imageNamed:@"car_3_4.png"] dstImage:@"g1g.png" x:xp3 y:10];
                    im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
                    im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
                    im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
                    im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];

                    break;
                     */
            }
            float x=0;
            float y=0;
            //
            if(picinx1==0) {
                l1x=783; l1y=227;
                l1w=682; l1h=300;
                x1s=783; x1e=0;
                y1s=227; y1e=936;
                b1=(x1s*y1e-x1e*y1s)/(y1e-y1s);
                a1=(x1s-x1e)/(y1s-y1e);
                for(int i=0;i<3;i++) {
                    dW00[i].hidden=YES;
                    [dW00[i] erase];
                }
            }
            CGRect r1 = car01.frame;
            y=l1y+(picinx1*2)*0.0305;
            x=a1*y+b1;
            r1.origin.x=x/2;
            r1.origin.y=y/2;
            r1.size.width=l1w/2*(0.2+(1-0.2)/(y1e-y1s)*(y-y1s));
            r1.size.height=l1h/2*(0.2+(1-0.2)/(y1e-y1s)*(y-y1s));
            l1y=y;
            car01.frame = r1;
            car01.image=im1;
            picinx1++;
            if(picinx1==100) {
                for(int i=0;i<3;i++) {
                    dW00[i].hidden=NO;
                    [dW00[i] erase];
                }
                speed=0.026*3;
            }
            picinx1%=152;
            if(lt > 100) {
                if(picinx2==0) {
                    x2s=943; x2e=683;
                    y2s=227; y2e=936;
                    
                    l2x=x2s; l2y=y2s;
                    l2w=682; l2h=300;
                    b2=(x2s*y2e-x2e*y2s)/(y2e-y2s);
                    a2=(x2s-x2e)/(y2s-y2e);
                    for(int i=0;i<6;i++) {
                        dW00[10+i].hidden=YES;
                        [dW00[10+i] erase];
                    }
                    
                }
                CGRect r2 = car02.frame;
                y=l2y+(picinx2*2)*0.0305;
                x=a2*y+b2;
                r2.origin.x=x/2;
                r2.origin.y=y/2;
                r2.size.width=l2w/2*(0.2+(1-0.2)/(y2e-y2s)*(y-y2s));
                r2.size.height=l2h/2*(0.2+(1-0.2)/(y2e-y2s)*(y-y2s));
                l2y=y;
                car02.frame = r2;
                car02.image=im2;
                picinx2++;
                if(picinx2==100) {
                    for(int i=0;i<6;i++) {
                        dW00[10+i].hidden=NO;
                        [dW00[10+i] erase];
                    }
                    speed=0.026*6;
                }
                picinx2%=152;
                
                //Center
                if(picinx2==151) {
                    [self dismissViewControllerAnimated:NO completion:nil];
                }
                
            }
            if(lt > 50) {
                if(picinx3==0) {
                    x3s=1103; x3e=1365;
                    y3s=227; y3e=936;
                    
                    l3x=x3s; l3y=y3s;
                    l3w=682; l3h=300;
                    b3=(x3s*y3e-x3e*y3s)/(y3e-y3s);
                    a3=(x3s-x3e)/(y3s-y3e);
                    for(int i=0;i<5;i++) {
                        dW00[20+i].hidden=YES;
                        [dW00[20+i] erase];
                        
                    }
                }
                CGRect r3 = car03.frame;
                y=l3y+(picinx3*2)*0.0305;
                x=a3*y+b3;
                r3.origin.x=x/2;
                r3.origin.y=y/2;
                r3.size.width=l3w/2*(0.2+(1-0.2)/(y3e-y3s)*(y-y3s));
                r3.size.height=l3h/2*(0.2+(1-0.2)/(y3e-y3s)*(y-y3s));
                l3y=y;
                car03.frame = r3;
                car03.image=im3;
                picinx3++;
                if(picinx3==100) {
                    for(int i=0;i<5;i++) {
                        dW00[20+i].hidden=NO;
                        [dW00[20+i] erase];
                        
                    }
                    speed=0.026*5;
                }
                picinx3%=152;

            }
            lt++;
           // picinx++;
           // picinx%=5;
        });

        // thread loop
        [NSThread sleepForTimeInterval:speed]; //等同于sleep(1);
    }
}
- (void) tUp300ms:(NSString *)params {
    while (true)
    {
        __weak typeof(self) weakSelf = self;
        dispatch_async(dispatch_get_main_queue(), ^{
            // use weakSelf here
            
            switch(treeinx) {
                case 0: //im1=[self addImage:[UIImage imageNamed:@"tree_1_1.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];
                        tim1=[UIImage imageNamed:@"tree_1_2.png"];
                        tim2=[UIImage imageNamed:@"tree_2_1.png"];
                        tim3=[UIImage imageNamed:@"tree_3_1.png"];
                        tim4=[UIImage imageNamed:@"tree_4_1.png"];
                    treeinx++;
                        break;
                    /*
                case 1: im1=[UIImage imageNamed:@"tree_1_2.png"];
                        //im1=[self addImage:[UIImage imageNamed:@"tree_1_2.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];

                        im2=[UIImage imageNamed:@"tree_2_2.png"];
                        im3=[UIImage imageNamed:@"tree_3_2.png"];
                        im4=[UIImage imageNamed:@"tree_4_2.png"];
                        break;
                case 2: im1=[UIImage imageNamed:@"tree_1_3.png"];
                        //im1=[self addImage:[UIImage imageNamed:@"tree_1_3.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];

                        im2=[UIImage imageNamed:@"tree_2_3.png"];
                        im3=[UIImage imageNamed:@"tree_3_3.png"];
                        im4=[UIImage imageNamed:@"tree_4_3.png"];
                        break;
                    
                case 4:
                case 3: im1=[UIImage imageNamed:@"tree_1_4.png"];
                        //im1=[self addImage:[UIImage imageNamed:@"tree_1_4.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];

                        im2=[UIImage imageNamed:@"tree_2_4.png"];
                        im3=[UIImage imageNamed:@"tree_3_4.png"];
                        im4=[UIImage imageNamed:@"tree_4_4.png"];
*/
                    break;
            }
            //tree01.image=im1;
            //tree02.image=im2;
            //tree03.image=im3;
            //tree04.image=im4;
            float x=0;
            float y=0;
            if(lt > 50) {
                if(pi1==0) {
                    tx1s=770; tx1e=0;
                    ty1s=236; ty1e=716;
                    tl1x=tx1s; tl1y=ty1s;
                    tl1w=144; tl1h=144;
                    tb1=(tx1s*ty1e-tx1e*ty1s)/(ty1e-ty1s);
                    ta1=(tx1s-tx1e)/(ty1s-ty1e);
                }
                CGRect r1 = tree01.frame;
                y=tl1y+(pi1*2)*0.0305;
                x=ta1*y+tb1;
                r1.origin.x=x/2;
                r1.origin.y=y/2;
                r1.size.width=tl1w/2*(0.2+(1-0.2)/(ty1e-ty1s)*(y-ty1s));
                r1.size.height=tl1h/2*(0.2+(1-0.2)/(ty1e-ty1s)*(y-ty1s));
                tl1y=y;
                tree01.frame = r1;
                tree01.image=tim1;
                pi1++;
                pi1%=152;
            }

            if(lt > 80) {
                if(pi2==0) {
                    tx2s=935; tx2e=464;
                    ty2s=236; ty2e=936;
                    tl2x=tx2s; tl2y=ty2s;
                    tl2w=144; tl2h=144;
                    tb2=(tx2s*ty2e-tx2e*ty2s)/(ty2e-ty2s);
                    ta2=(tx2s-tx2e)/(ty2s-ty2e);
                }
                CGRect r2 = tree02.frame;
                y=tl2y+(pi2*2)*0.0305;
                x=ta2*y+tb2;
                r2.origin.x=x/2;
                r2.origin.y=y/2;
                r2.size.width=tl2w/2*(0.2+(1-0.2)/(ty2e-ty2s)*(y-ty2s));
                r2.size.height=tl2h/2*(0.2+(1-0.2)/(ty2e-ty2s)*(y-ty2s));
                tl2y=y;
                tree02.frame = r2;
                tree02.image=tim2;
                pi2++;
                pi2%=152;
            }

            if(lt > 20) {
                if(pi3==0) {
                    tx3s=1078; tx3e=1294;
                    ty3s=236; ty3e=936;
                    tl3x=tx3s; tl3y=ty3s;
                    tl3w=144; tl3h=144;
                    tb3=(tx3s*ty3e-tx3e*ty3s)/(ty3e-ty3s);
                    ta3=(tx3s-tx3e)/(ty3s-ty3e);
                }
                CGRect r3 = tree03.frame;
                y=tl3y+(pi3*2)*0.0305;
                x=ta3*y+tb3;
                r3.origin.x=x/2;
                r3.origin.y=y/2;
                r3.size.width=tl3w/2*(0.2+(1-0.2)/(ty3e-ty3s)*(y-ty3s));
                r3.size.height=tl3h/2*(0.2+(1-0.2)/(ty3e-ty3s)*(y-ty3s));
                tl3y=y;
                tree03.frame = r3;
                tree03.image=tim3;
                pi3++;
                pi3%=152;
            }
            if(lt > 120) {
                if(pi4==0) {
                    tx4s=1230; tx4e=1828;
                    ty4s=236; ty4e=718;
                    tl4x=tx4s; tl4y=ty4s;
                    tl4w=144; tl4h=144;
                    tb4=(tx4s*ty4e-tx4e*ty4s)/(ty4e-ty4s);
                    ta4=(tx4s-tx4e)/(ty4s-ty4e);
                }
                CGRect r4 = tree04.frame;
                y=tl4y+(pi4*2)*0.0305;
                x=ta4*y+tb4;
                r4.origin.x=x/2;
                r4.origin.y=y/2;
                r4.size.width=tl4w/2*(0.2+(1-0.2)/(ty4e-ty4s)*(y-ty4s));
                r4.size.height=tl4h/2*(0.2+(1-0.2)/(ty4e-ty4s)*(y-ty4s));
                tl4y=y;
                tree04.frame = r4;
                tree04.image=tim4;
                pi4++;
                pi4%=152;
            }
           // treeinx++;
           // treeinx%=5;
        });

        // thread loop
        [NSThread sleepForTimeInterval:0.3]; //等同于sleep(1);
    }
}
-(void)timesUp300ms:(NSTimer *)timer{
    UIImage *im1,*im2,*im3,*im4;
    switch(treeinx) {
        case 0: //im1=[self addImage:[UIImage imageNamed:@"tree_1_1.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];
                im1=[UIImage imageNamed:@"tree_1_2.png"];
                im2=[UIImage imageNamed:@"tree_2_1.png"];
                im3=[UIImage imageNamed:@"tree_3_1.png"];
                im4=[UIImage imageNamed:@"tree_4_1.png"];
                break;
        case 1: im1=[UIImage imageNamed:@"tree_1_2.png"];
                //im1=[self addImage:[UIImage imageNamed:@"tree_1_2.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];

                im2=[UIImage imageNamed:@"tree_2_2.png"];
                im3=[UIImage imageNamed:@"tree_3_2.png"];
                im4=[UIImage imageNamed:@"tree_4_2.png"];
                break;
        case 2: im1=[UIImage imageNamed:@"tree_1_3.png"];
                //im1=[self addImage:[UIImage imageNamed:@"tree_1_3.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];

                im2=[UIImage imageNamed:@"tree_2_3.png"];
                im3=[UIImage imageNamed:@"tree_3_3.png"];
                im4=[UIImage imageNamed:@"tree_4_3.png"];
                break;
            
        case 4:
        case 3: im1=[UIImage imageNamed:@"tree_1_4.png"];
                //im1=[self addImage:[UIImage imageNamed:@"tree_1_4.png"] dstImage:[UIImage imageNamed:@"a1g.png"] x:0 y:0];

                im2=[UIImage imageNamed:@"tree_2_4.png"];
                im3=[UIImage imageNamed:@"tree_3_4.png"];
                im4=[UIImage imageNamed:@"tree_4_4.png"];

            break;
    }
    //tree01.image=im1;
    //tree02.image=im2;
    //tree03.image=im3;
    //tree04.image=im4;
    float x=0;
    float y=0;
    if(lt > 50) {
        if(pi1==0) {
            tx1s=770; tx1e=0;
            ty1s=236; ty1e=716;
            tl1x=tx1s; tl1y=ty1s;
            tl1w=144; tl1h=144;
            tb1=(tx1s*ty1e-tx1e*ty1s)/(ty1e-ty1s);
            ta1=(tx1s-tx1e)/(ty1s-ty1e);
        }
        CGRect r1 = tree01.frame;
        y=tl1y+(pi1*2)*0.0305;
        x=ta1*y+tb1;
        r1.origin.x=x/2;
        r1.origin.y=y/2;
        r1.size.width=tl1w/2*(0.2+(1-0.2)/(ty1e-ty1s)*(y-ty1s));
        r1.size.height=tl1h/2*(0.2+(1-0.2)/(ty1e-ty1s)*(y-ty1s));
        tl1y=y;
        tree01.frame = r1;
        tree01.image=im1;
        pi1++;
        pi1%=152;
    }

    if(lt > 80) {
        if(pi2==0) {
            tx2s=935; tx2e=464;
            ty2s=236; ty2e=936;
            tl2x=tx2s; tl2y=ty2s;
            tl2w=144; tl2h=144;
            tb2=(tx2s*ty2e-tx2e*ty2s)/(ty2e-ty2s);
            ta2=(tx2s-tx2e)/(ty2s-ty2e);
        }
        CGRect r2 = tree02.frame;
        y=tl2y+(pi2*2)*0.0305;
        x=ta2*y+tb2;
        r2.origin.x=x/2;
        r2.origin.y=y/2;
        r2.size.width=tl2w/2*(0.2+(1-0.2)/(ty2e-ty2s)*(y-ty2s));
        r2.size.height=tl2h/2*(0.2+(1-0.2)/(ty2e-ty2s)*(y-ty2s));
        tl2y=y;
        tree02.frame = r2;
        tree02.image=im2;
        pi2++;
        pi2%=152;
    }

    if(lt > 20) {
        if(pi3==0) {
            tx3s=1078; tx3e=1294;
            ty3s=236; ty3e=936;
            tl3x=tx3s; tl3y=ty3s;
            tl3w=144; tl3h=144;
            tb3=(tx3s*ty3e-tx3e*ty3s)/(ty3e-ty3s);
            ta3=(tx3s-tx3e)/(ty3s-ty3e);
        }
        CGRect r3 = tree03.frame;
        y=tl3y+(pi3*2)*0.0305;
        x=ta3*y+tb3;
        r3.origin.x=x/2;
        r3.origin.y=y/2;
        r3.size.width=tl3w/2*(0.2+(1-0.2)/(ty3e-ty3s)*(y-ty3s));
        r3.size.height=tl3h/2*(0.2+(1-0.2)/(ty3e-ty3s)*(y-ty3s));
        tl3y=y;
        tree03.frame = r3;
        tree03.image=im3;
        pi3++;
        pi3%=152;
    }
    if(lt > 120) {
        if(pi4==0) {
            tx4s=1230; tx4e=1828;
            ty4s=236; ty4e=718;
            tl4x=tx4s; tl4y=ty4s;
            tl4w=144; tl4h=144;
            tb4=(tx4s*ty4e-tx4e*ty4s)/(ty4e-ty4s);
            ta4=(tx4s-tx4e)/(ty4s-ty4e);
        }
        CGRect r4 = tree04.frame;
        y=tl4y+(pi4*2)*0.0305;
        x=ta4*y+tb4;
        r4.origin.x=x/2;
        r4.origin.y=y/2;
        r4.size.width=tl4w/2*(0.2+(1-0.2)/(ty4e-ty4s)*(y-ty4s));
        r4.size.height=tl4h/2*(0.2+(1-0.2)/(ty4e-ty4s)*(y-ty4s));
        tl4y=y;
        tree04.frame = r4;
        tree04.image=im4;
        pi4++;
        pi4%=152;
    }
    treeinx++;
    treeinx%=5;
}
-(void)timesUp100ms:(NSTimer *)timer{
/*
    switch(treeinx) {
        case 0: tree01.image=[UIImage imageNamed:@"tree_1_1.png"];
                tree02.image=[UIImage imageNamed:@"tree_2_1.png"];
                tree03.image=[UIImage imageNamed:@"tree_3_1.png"];
                tree04.image=[UIImage imageNamed:@"tree_4_1.png"];
                break;
        case 1: tree01.image=[UIImage imageNamed:@"tree_1_2.png"];
            tree02.image=[UIImage imageNamed:@"tree_2_2.png"];
            tree03.image=[UIImage imageNamed:@"tree_3_2.png"];
            tree04.image=[UIImage imageNamed:@"tree_4_2.png"];
            break;
        case 2: tree01.image=[UIImage imageNamed:@"tree_1_3.png"];
            tree02.image=[UIImage imageNamed:@"tree_2_3.png"];
            tree03.image=[UIImage imageNamed:@"tree_3_3.png"];
            tree04.image=[UIImage imageNamed:@"tree_4_3.png"];
            break;
        case 4:
        case 3: tree01.image=[UIImage imageNamed:@"tree_1_4.png"];
            tree02.image=[UIImage imageNamed:@"tree_2_4.png"];
            tree03.image=[UIImage imageNamed:@"tree_3_4.png"];
            tree04.image=[UIImage imageNamed:@"tree_4_4.png"];
            break;

    }
    switch(treeinx) {
        case 0: tree11.image=[UIImage imageNamed:@"tree_1_1.png"];
                tree12.image=[UIImage imageNamed:@"tree_2_1.png"];
                tree13.image=[UIImage imageNamed:@"tree_3_1.png"];
                tree14.image=[UIImage imageNamed:@"tree_4_1.png"];
                break;
        case 1: tree11.image=[UIImage imageNamed:@"tree_1_2.png"];
            tree12.image=[UIImage imageNamed:@"tree_2_2.png"];
            tree13.image=[UIImage imageNamed:@"tree_3_2.png"];
            tree14.image=[UIImage imageNamed:@"tree_4_2.png"];
            break;
        case 2: tree11.image=[UIImage imageNamed:@"tree_1_3.png"];
            tree12.image=[UIImage imageNamed:@"tree_2_3.png"];
            tree13.image=[UIImage imageNamed:@"tree_3_3.png"];
            tree14.image=[UIImage imageNamed:@"tree_4_3.png"];
            break;
        case 4:
        case 3: tree11.image=[UIImage imageNamed:@"tree_1_4.png"];
            tree12.image=[UIImage imageNamed:@"tree_2_4.png"];
            tree13.image=[UIImage imageNamed:@"tree_3_4.png"];
            tree14.image=[UIImage imageNamed:@"tree_4_4.png"];
            break;
    }

    switch(treeinx) {
        case 0:
            
            
            car01.image=[UIImage imageNamed:@"car_2_1.png"];
            car02.image=[UIImage imageNamed:@"car_2_1.png"];
            car03.image=[UIImage imageNamed:@"car_2_1.png"];
            break;
        case 1: car01.image=[UIImage imageNamed:@"car_2_2.png"];
            car02.image=[UIImage imageNamed:@"car_2_2.png"];
            car03.image=[UIImage imageNamed:@"car_2_2.png"];
            break;
        case 2: car01.image=[UIImage imageNamed:@"car_2_3.png"];
            car02.image=[UIImage imageNamed:@"car_2_3.png"];
            car03.image=[UIImage imageNamed:@"car_2_3.png"];
            break;
        case 4:
        case 3: car01.image=[UIImage imageNamed:@"car_2_4.png"];
            car02.image=[UIImage imageNamed:@"car_2_4.png"];
            car03.image=[UIImage imageNamed:@"car_2_4.png"];
            break;
    }
    switch(treeinx) {
        case 0: {
                CGRect r = car11.frame;
                r.origin.x += 10;
                r.origin.y += 10;
                car11.frame = r;
            }
            
            car11.image=[UIImage imageNamed:@"car_5_1.png"];
            car12.image=[UIImage imageNamed:@"car_5_1.png"];
            car13.image=[UIImage imageNamed:@"car_5_1.png"];
                break;
        case 1: car11.image=[UIImage imageNamed:@"car_5_2.png"];
            car12.image=[UIImage imageNamed:@"car_5_2.png"];
            car13.image=[UIImage imageNamed:@"car_5_2.png"];
            break;
        case 2: car11.image=[UIImage imageNamed:@"car_5_3.png"];
            car12.image=[UIImage imageNamed:@"car_5_3.png"];
            car13.image=[UIImage imageNamed:@"car_5_3.png"];
            break;
        case 4:
        case 3: car11.image=[UIImage imageNamed:@"car_5_4.png"];
            car12.image=[UIImage imageNamed:@"car_5_4.png"];
            car13.image=[UIImage imageNamed:@"car_5_4.png"];
            break;
    }
 */
    UIImage *im1,*im2,*im3;
    int xp1=180;
    int xp2=180;
    int xp3=120;
    switch(picinx) {
        case 0: //im1=[UIImage imageNamed:@"car_1_1.png"];
            
                im1=[self addImage:[UIImage imageNamed:@"car_1_1.png"] dstImage:@"c1g.png" x:xp1 y:10];
                im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
                im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
                im2=[self addImage:[UIImage imageNamed:@"car_5_1.png"] dstImage:@"p1g.png" x:xp2 y:10];
                im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
                im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
                im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
                im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
                im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                im3=[self addImage:[UIImage imageNamed:@"car_3_1.png"] dstImage:@"g1g.png" x:xp3 y:10];
            im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
            im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];
                break;
        case 1: //im1=[UIImage imageNamed:@"car_1_2.png"];
            im1=[self addImage:[UIImage imageNamed:@"car_1_2.png"] dstImage:@"c1g.png" x:xp1 y:10];
            im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
            im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
                im2=[self addImage:[UIImage imageNamed:@"car_5_2.png"] dstImage:@"p1g.png" x:xp2 y:10];
            im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
            im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
            im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
            im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
            im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                im3=[self addImage:[UIImage imageNamed:@"car_3_2.png"] dstImage:@"g1g.png" x:xp3 y:10];
            im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
            im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];
            break;
        case 2: //im1=[UIImage imageNamed:@"car_1_3.png"];
            im1=[self addImage:[UIImage imageNamed:@"car_1_3.png"] dstImage:@"c1g.png" x:xp1 y:10];
            im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
            im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
            im2=[self addImage:[UIImage imageNamed:@"car_5_3.png"] dstImage:@"p1g.png" x:xp2 y:10];
            im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
            im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
            im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
            im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
            im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                im3=[self addImage:[UIImage imageNamed:@"car_3_3.png"] dstImage:@"g1g.png" x:xp3 y:10];
            im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
            im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];
            break;
            
        case 4:
        case 3: //im1=[UIImage imageNamed:@"car_1_4.png"];
            im1=[self addImage:[UIImage imageNamed:@"car_1_4.png"] dstImage:@"c1g.png" x:xp1 y:10];
            im1=[self addImage:im1 dstImage:@"a1g.png" x:xp1+50 y:10];
            im1=[self addImage:im1 dstImage:@"t1g.png" x:xp1+100 y:10];
            im2=[self addImage:[UIImage imageNamed:@"car_5_4.png"] dstImage:@"p1g.png" x:xp2 y:10];
            im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+50 y:10];
            im2=[self addImage:im2 dstImage:@"n1g.png" x:xp2+100 y:10];
            im2=[self addImage:im2 dstImage:@"c1g.png" x:xp2+150 y:10];
            im2=[self addImage:im2 dstImage:@"e1g.png" x:xp2+200 y:10];
            im2=[self addImage:im2 dstImage:@"l1g.png" x:xp2+250 y:10];
                im3=[self addImage:[UIImage imageNamed:@"car_3_4.png"] dstImage:@"g1g.png" x:xp3 y:10];
            im3=[self addImage:im3 dstImage:@"l1g.png" x:xp3+50 y:10];
            im3=[self addImage:im3 dstImage:@"a1g.png" x:xp3+100 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+150 y:10];
            im3=[self addImage:im3 dstImage:@"s1g.png" x:xp3+200 y:10];

            break;
    }
    float x=0;
    float y=0;
    //
    if(picinx1==0) {
        l1x=783; l1y=227;
        l1w=682; l1h=300;
        x1s=783; x1e=0;
        y1s=227; y1e=936;
        b1=(x1s*y1e-x1e*y1s)/(y1e-y1s);
        a1=(x1s-x1e)/(y1s-y1e);
        for(int i=0;i<3;i++) {
            dW00[i].hidden=YES;
            [dW00[i] erase];
        }
    }
    CGRect r1 = car01.frame;
    y=l1y+(picinx1*2)*0.0305;
    x=a1*y+b1;
    r1.origin.x=x/2;
    r1.origin.y=y/2;
    r1.size.width=l1w/2*(0.2+(1-0.2)/(y1e-y1s)*(y-y1s));
    r1.size.height=l1h/2*(0.2+(1-0.2)/(y1e-y1s)*(y-y1s));
    l1y=y;
    car01.frame = r1;
    car01.image=im1;
    picinx1++;
    if(picinx1==100) {
        for(int i=0;i<3;i++) {
            dW00[i].hidden=NO;
            [dW00[i] erase];
        }
    }
    picinx1%=152;
    if(lt > 100) {
        if(picinx2==0) {
            x2s=943; x2e=683;
            y2s=227; y2e=936;
            
            l2x=x2s; l2y=y2s;
            l2w=682; l2h=300;
            b2=(x2s*y2e-x2e*y2s)/(y2e-y2s);
            a2=(x2s-x2e)/(y2s-y2e);
            for(int i=0;i<6;i++) {
                dW00[10+i].hidden=YES;
                [dW00[10+i] erase];
            }
        }
        CGRect r2 = car02.frame;
        y=l2y+(picinx2*2)*0.0305;
        x=a2*y+b2;
        r2.origin.x=x/2;
        r2.origin.y=y/2;
        r2.size.width=l2w/2*(0.2+(1-0.2)/(y2e-y2s)*(y-y2s));
        r2.size.height=l2h/2*(0.2+(1-0.2)/(y2e-y2s)*(y-y2s));
        l2y=y;
        car02.frame = r2;
        car02.image=im2;
        picinx2++;
        if(picinx2==100) {
            for(int i=0;i<6;i++) {
                dW00[10+i].hidden=NO;
                [dW00[10+i] erase];
            }
        }
        picinx2%=152;
    }
    if(lt > 50) {
        if(picinx3==0) {
            x3s=1103; x3e=1365;
            y3s=227; y3e=936;
            
            l3x=x3s; l3y=y3s;
            l3w=682; l3h=300;
            b3=(x3s*y3e-x3e*y3s)/(y3e-y3s);
            a3=(x3s-x3e)/(y3s-y3e);
            for(int i=0;i<5;i++) {
                dW00[20+i].hidden=YES;
                [dW00[20+i] erase];
            }
        }
        CGRect r3 = car03.frame;
        y=l3y+(picinx3*2)*0.0305;
        x=a3*y+b3;
        r3.origin.x=x/2;
        r3.origin.y=y/2;
        r3.size.width=l3w/2*(0.2+(1-0.2)/(y3e-y3s)*(y-y3s));
        r3.size.height=l3h/2*(0.2+(1-0.2)/(y3e-y3s)*(y-y3s));
        l3y=y;
        car03.frame = r3;
        car03.image=im3;
        picinx3++;
        if(picinx3==100) {
            for(int i=0;i<5;i++) {
                dW00[20+i].hidden=NO;
                [dW00[20+i] erase];
            }
        }
        picinx3%=152;
    }
    lt++;
    picinx++;
    picinx%=5;
}
-(UIImage *) addImage:(UIImage *) src dstImage:(NSString *) dst x:(int) x y:(int) y  {
    UIImage *im1=src;
    UIImage *im2=[UIImage imageNamed:dst];
    UIGraphicsBeginImageContext(im1.size);
    [im1 drawInRect:CGRectMake(0,0,im1.size.width,im1.size.height)];
    [im2 drawInRect:CGRectMake(x,y,50,150)];
    UIImage *resImage=UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resImage;
    
}
+ (UIImage *)imageWithImage:(UIImage *)image scaledToSize:(CGSize)newSize {
    //UIGraphicsBeginImageContext(newSize);
    // In next line, pass 0.0 to use the current device's pixel scaling factor (and thus account for Retina resolution).
    // Pass 1.0 to force exact pixel size.
    UIGraphicsBeginImageContextWithOptions(newSize, NO, 0.0);
    [image drawInRect:CGRectMake(0, 0, newSize.width, newSize.height)];
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImage;
}
-(void)timesUp:(NSTimer *)timer{
    switch(showinx) {
        case 0: uiv.image=[UIImage imageNamed:@"roll01.png"]; break;
        case 1: uiv.image=[UIImage imageNamed:@"roll02.png"]; break;
        case 2: uiv.image=[UIImage imageNamed:@"roll03.png"]; break;
    }
    showinx++;
    showinx%=3;
    
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
