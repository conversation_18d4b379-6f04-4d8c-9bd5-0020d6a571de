//
//  selector410ViewController.m
//  uten
//
//  Created by 蔡駿寓 on 2025/1/12.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import "selector410ViewController.h"
#import "selector41ViewController.h"
@interface selector410ViewController ()
@property (nonatomic, strong) UIScrollView *scrollView; // UIScrollView 作為按鈕容器
@property (nonatomic, strong) NSArray *buttonData; // 按鈕資料陣列

@end

@implementation selector410ViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    //穎.1放在viewdidload最上面        // 初始化按鈕資料陣列
    //目前：120顆,發音3:1~33,發音4:34~60,發音5:61~90,發音6:91~120
    self.buttonData = @[//{image:圖片名稱   ,title:顯示文字,  action:動作代碼}
            @{@"image": @"selector41075", @"title": @"ph3 (a~z)", @"action": @1},
            @{@"image": @"selector41071", @"title": @"ph3複合音",  @"action": @2},
            @{@"image": @"selector41077", @"title": @"ph3唸一遍",  @"action": @3},

            @{@"image": @"selector41063", @"title": @"ph3 01", @"action":@4},
            @{@"image": @"selector41063", @"title": @"ph3 02", @"action":@5},
            @{@"image": @"selector41063", @"title": @"ph3 03", @"action":@6},
            @{@"image": @"selector41063", @"title": @"ph3 04", @"action":@7},
            @{@"image": @"selector41063", @"title": @"ph3 05", @"action":@8},
            @{@"image": @"selector41063", @"title": @"ph3 06", @"action":@9},
            @{@"image": @"selector41063", @"title": @"ph3 07", @"action": @10},
            @{@"image": @"selector41063", @"title": @"ph3 08", @"action": @11},
            @{@"image": @"selector41063", @"title": @"ph3 09", @"action": @12},
            @{@"image": @"selector41063", @"title": @"ph3 10", @"action": @13},
            @{@"image": @"selector41063", @"title": @"ph3 11", @"action": @14},
            @{@"image": @"selector41063", @"title": @"ph3 12", @"action": @15},
            @{@"image": @"selector41063", @"title": @"ph3 13", @"action": @16},
            @{@"image": @"selector41067", @"title": @"ph3 01", @"action": @17},
            @{@"image": @"selector41067", @"title": @"ph3 02", @"action": @18},
            @{@"image": @"selector41067", @"title": @"ph3 03", @"action": @19},
            @{@"image": @"selector41067", @"title": @"ph3 04", @"action": @20},
            @{@"image": @"selector41067", @"title": @"ph3 05", @"action": @21},
            @{@"image": @"selector41067", @"title": @"ph3 06", @"action": @22},
            @{@"image": @"selector41067", @"title": @"ph3 07", @"action": @23},
            @{@"image": @"selector41067", @"title": @"ph3 08", @"action": @24},
            @{@"image": @"selector41067", @"title": @"ph3 09", @"action": @25},
            @{@"image": @"selector41067", @"title": @"ph3 10", @"action": @26},
            @{@"image": @"selector41067", @"title": @"ph3 11", @"action": @27},
            @{@"image": @"selector41067", @"title": @"ph3 12", @"action": @28},
            @{@"image": @"selector41067", @"title": @"ph3 13", @"action": @29},

            @{@"image": @"selector41081", @"title": @"ph3 (1-4)",  @"action": @30},
            @{@"image": @"selector41081", @"title": @"ph3 (5-8)",  @"action": @31},
            @{@"image": @"selector41081", @"title": @"ph3 (9-13)", @"action": @32},
            @{@"image": @"selector41081", @"title": @"ph3 (1-13)", @"action": @33},


            @{@"image": @"selector41072", @"title": @"ph4複合音", @"action": @34},
            @{@"image": @"selector41078", @"title": @"ph4唸一遍", @"action": @35},

            @{@"image": @"selector41064", @"title": @"ph4 01", @"action": @36},
            @{@"image": @"selector41064", @"title": @"ph4 02", @"action": @37},
            @{@"image": @"selector41064", @"title": @"ph4 03", @"action": @38},
            @{@"image": @"selector41064", @"title": @"ph4 04", @"action": @39},
            @{@"image": @"selector41064", @"title": @"ph4 05", @"action": @40},
            @{@"image": @"selector41064", @"title": @"ph4 06", @"action": @41},
            @{@"image": @"selector41064", @"title": @"ph4 07", @"action": @42},
            @{@"image": @"selector41064", @"title": @"ph4 08", @"action": @43},
            @{@"image": @"selector41064", @"title": @"ph4 09", @"action": @44},
            @{@"image": @"selector41064", @"title": @"ph4 10", @"action": @45},
            @{@"image": @"selector41064", @"title": @"ph4 11", @"action": @46},
            @{@"image": @"selector41068", @"title": @"ph4 01", @"action": @47},
            @{@"image": @"selector41068", @"title": @"ph4 02", @"action": @48},
            @{@"image": @"selector41068", @"title": @"ph4 03", @"action": @49},
            @{@"image": @"selector41068", @"title": @"ph4 04", @"action": @50},
            @{@"image": @"selector41068", @"title": @"ph4 05", @"action": @51},
            @{@"image": @"selector41068", @"title": @"ph4 06", @"action": @52},
            @{@"image": @"selector41068", @"title": @"ph4 07", @"action": @53},
            @{@"image": @"selector41068", @"title": @"ph4 08", @"action": @54},
            @{@"image": @"selector41068", @"title": @"ph4 09", @"action": @55},
            @{@"image": @"selector41068", @"title": @"ph4 10", @"action": @56},
            @{@"image": @"selector41068", @"title": @"ph4 11", @"action": @57},

            @{@"image": @"selector41082", @"title": @"ph4 (1-5)",  @"action": @58},
            @{@"image": @"selector41082", @"title": @"ph4 (6-11)", @"action": @59},
            @{@"image": @"selector41082", @"title": @"ph4 (1-11)", @"action": @60},
            
            
            @{@"image": @"selector41073", @"title": @"ph5複合音", @"action": @61},
            @{@"image": @"selector41079", @"title": @"ph5唸一遍", @"action": @62},

            @{@"image": @"selector41065", @"title": @"ph5 01", @"action": @63},
            @{@"image": @"selector41065", @"title": @"ph5 02", @"action": @64},
            @{@"image": @"selector41065", @"title": @"ph5 03", @"action": @65},
            @{@"image": @"selector41065", @"title": @"ph5 04", @"action": @66},
            @{@"image": @"selector41065", @"title": @"ph5 05", @"action": @67},
            @{@"image": @"selector41065", @"title": @"ph5 06", @"action": @68},
            @{@"image": @"selector41065", @"title": @"ph5 07", @"action": @69},
            @{@"image": @"selector41065", @"title": @"ph5 08", @"action": @70},
            @{@"image": @"selector41065", @"title": @"ph5 09", @"action": @71},
            @{@"image": @"selector41065", @"title": @"ph5 10", @"action": @72},
            @{@"image": @"selector41065", @"title": @"ph5 11", @"action": @73},
            @{@"image": @"selector41065", @"title": @"ph5 12", @"action": @74},
            @{@"image": @"selector41065", @"title": @"ph5 13", @"action": @75},
            @{@"image": @"selector41065", @"title": @"ph5 14", @"action": @76},
            @{@"image": @"selector41065", @"title": @"ph5 15", @"action": @77},
            @{@"image": @"selector41065", @"title": @"ph5 16", @"action": @78},
            @{@"image": @"selector41065", @"title": @"ph5 17", @"action": @79},
            @{@"image": @"selector41065", @"title": @"ph5 18", @"action": @80},
            @{@"image": @"selector41065", @"title": @"ph5 19", @"action": @81},
            @{@"image": @"selector41065", @"title": @"ph5 20", @"action": @82},
            @{@"image": @"selector41065", @"title": @"ph5 21", @"action": @83},
            @{@"image": @"selector41065", @"title": @"ph5 22", @"action": @84},
            @{@"image": @"selector41065", @"title": @"ph5 23", @"action": @85},
            @{@"image": @"selector41065", @"title": @"ph5 24", @"action": @86},
            @{@"image": @"selector41065", @"title": @"ph5 25", @"action": @87},
            @{@"image": @"selector41065", @"title": @"ph5 26", @"action": @88},
            @{@"image": @"selector41065", @"title": @"ph5 27", @"action": @89},
            @{@"image": @"selector41069", @"title": @"ph5 01", @"action": @90},
            @{@"image": @"selector41069", @"title": @"ph5 02", @"action": @91},
            @{@"image": @"selector41069", @"title": @"ph5 03", @"action": @92},
            @{@"image": @"selector41069", @"title": @"ph5 04", @"action": @93},
            @{@"image": @"selector41069", @"title": @"ph5 05", @"action": @94},
            @{@"image": @"selector41069", @"title": @"ph5 06", @"action": @95},
            @{@"image": @"selector41069", @"title": @"ph5 07", @"action": @96},
            @{@"image": @"selector41069", @"title": @"ph5 08", @"action": @97},
            @{@"image": @"selector41069", @"title": @"ph5 09", @"action": @98},
            @{@"image": @"selector41069", @"title": @"ph5 10", @"action": @99},
            @{@"image": @"selector41069", @"title": @"ph5 11", @"action": @100},
            @{@"image": @"selector41069", @"title": @"ph5 12", @"action": @101},
            @{@"image": @"selector41069", @"title": @"ph5 13", @"action": @102},
            @{@"image": @"selector41069", @"title": @"ph5 14", @"action": @103},
            @{@"image": @"selector41069", @"title": @"ph5 15", @"action": @104},
            @{@"image": @"selector41069", @"title": @"ph5 16", @"action": @105},
            @{@"image": @"selector41069", @"title": @"ph5 17", @"action": @106},
            @{@"image": @"selector41069", @"title": @"ph5 18", @"action": @107},
            @{@"image": @"selector41069", @"title": @"ph5 19", @"action": @108},
            @{@"image": @"selector41069", @"title": @"ph5 20", @"action": @109},
            @{@"image": @"selector41069", @"title": @"ph5 21", @"action": @110},
            @{@"image": @"selector41069", @"title": @"ph5 22", @"action": @111},
            @{@"image": @"selector41069", @"title": @"ph5 23", @"action": @112},
            @{@"image": @"selector41069", @"title": @"ph5 24", @"action": @113},
            @{@"image": @"selector41069", @"title": @"ph5 25", @"action": @114},
            @{@"image": @"selector41069", @"title": @"ph5 26", @"action": @115},
            @{@"image": @"selector41069", @"title": @"ph5 27", @"action": @116},

            @{@"image": @"selector41083", @"title": @"ph5 (1-4)",  @"action": @117},
            @{@"image": @"selector41083", @"title": @"ph5 (5-8)",  @"action": @118},
            @{@"image": @"selector41083", @"title": @"ph5 (9-12)", @"action": @119},
            @{@"image": @"selector41083", @"title": @"ph5(13-16)", @"action": @120},
            @{@"image": @"selector41083", @"title": @"ph5(17-20)", @"action": @121},
            @{@"image": @"selector41083", @"title": @"ph5.21-24",  @"action": @122},
            @{@"image": @"selector41083", @"title": @"ph5.25-27",  @"action": @123},
            @{@"image": @"selector41083", @"title": @"ph5 (1-14)", @"action": @124},
            @{@"image": @"selector41083", @"title": @"ph5(15-27)", @"action": @125},
            @{@"image": @"selector41083", @"title": @"ph5 (1-27)", @"action": @126},


            @{@"image": @"selector41074", @"title": @"ph6複合音", @"action": @127},
            @{@"image": @"selector41080", @"title": @"ph6唸一遍", @"action": @128},

            @{@"image": @"selector41066", @"title": @"ph6 01", @"action": @129},
            @{@"image": @"selector41066", @"title": @"ph6 02", @"action": @130},
            @{@"image": @"selector41066", @"title": @"ph6 03", @"action": @131},
            @{@"image": @"selector41066", @"title": @"ph6 04", @"action": @132},
            @{@"image": @"selector41066", @"title": @"ph6 05", @"action": @133},
            @{@"image": @"selector41066", @"title": @"ph6 06", @"action": @134},
            @{@"image": @"selector41066", @"title": @"ph6 07", @"action": @135},
            @{@"image": @"selector41066", @"title": @"ph6 08", @"action": @136},
            @{@"image": @"selector41066", @"title": @"ph6 09", @"action": @137},
            @{@"image": @"selector41066", @"title": @"ph6 10", @"action": @138},
            @{@"image": @"selector41066", @"title": @"ph6 11", @"action": @139},
            @{@"image": @"selector41066", @"title": @"ph6 12", @"action": @140},
            @{@"image": @"selector41066", @"title": @"ph6 13", @"action": @141},
            @{@"image": @"selector41066", @"title": @"ph6 14", @"action": @142},
            @{@"image": @"selector41066", @"title": @"ph6 15", @"action": @143},
            @{@"image": @"selector41066", @"title": @"ph6 16", @"action": @144},
            @{@"image": @"selector41066", @"title": @"ph6 17", @"action": @145},
            @{@"image": @"selector41066", @"title": @"ph6 18", @"action": @146},
            @{@"image": @"selector41066", @"title": @"ph6 19", @"action": @147},
            @{@"image": @"selector41070", @"title": @"ph6 01", @"action": @148},
            @{@"image": @"selector41070", @"title": @"ph6 02", @"action": @149},
            @{@"image": @"selector41070", @"title": @"ph6 03", @"action": @150},
            @{@"image": @"selector41070", @"title": @"ph6 04", @"action": @151},
            @{@"image": @"selector41070", @"title": @"ph6 05", @"action": @152},
            @{@"image": @"selector41070", @"title": @"ph6 06", @"action": @153},
            @{@"image": @"selector41070", @"title": @"ph6 07", @"action": @154},
            @{@"image": @"selector41070", @"title": @"ph6 08", @"action": @155},
            @{@"image": @"selector41070", @"title": @"ph6 09", @"action": @156},
            @{@"image": @"selector41070", @"title": @"ph6 10", @"action": @157},
            @{@"image": @"selector41070", @"title": @"ph6 11", @"action": @158},
            @{@"image": @"selector41070", @"title": @"ph6 12", @"action": @159},
            @{@"image": @"selector41070", @"title": @"ph6 13", @"action": @160},
            @{@"image": @"selector41070", @"title": @"ph6 14", @"action": @161},
            @{@"image": @"selector41070", @"title": @"ph6 15", @"action": @162},
            @{@"image": @"selector41070", @"title": @"ph6 16", @"action": @163},
            @{@"image": @"selector41070", @"title": @"ph6 17", @"action": @164},
            @{@"image": @"selector41070", @"title": @"ph6 18", @"action": @165},
            @{@"image": @"selector41070", @"title": @"ph6 19", @"action": @166},

            @{@"image": @"selector41084", @"title": @"ph6 (1-4)",  @"action": @167},
            @{@"image": @"selector41084", @"title": @"ph6 (5-8)",  @"action": @168},
            @{@"image": @"selector41084", @"title": @"ph6 (9-12)", @"action": @169},
            @{@"image": @"selector41084", @"title": @"ph6(13-16)", @"action": @170},
            @{@"image": @"selector41084", @"title": @"ph6(17-19)", @"action": @171},
            @{@"image": @"selector41084", @"title": @"ph6 (1-10)", @"action": @172},
            @{@"image": @"selector41084", @"title": @"ph6(11-19)", @"action": @173},
            @{@"image": @"selector41084", @"title": @"ph6 (1-19)", @"action": @174},  ];

// 設置 UIScrollView  //加載到畫面
self.scrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
[self.view addSubview:self.scrollView];

// 佈局按鈕 //執行函式
[self setupButtons01];
//穎.1放在viewdidload最上面
}


//穎.2放在任何地方
// 動態生成按鈕
- (void)setupButtons01 {
    int buttonsPerRow = 7; // 每行顯示的按鈕數量
    CGFloat buttonWidth = 100; // 按鈕寬度
    CGFloat buttonHeight = 100; // 按鈕高度
    CGFloat spacing = 30; // 按鈕之間的間距

    CGFloat startX = 50; // 起始位置的 x 座標
    CGFloat startY = 60; // 起始位置的 y 座標

    for (int i = 0; i < self.buttonData.count; i++) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem]; // 建立按鈕
        int row = i / buttonsPerRow; // 計算按鈕所在的行數
        int col = i % buttonsPerRow; // 計算按鈕所在的列數
        CGFloat x = startX + (buttonWidth + spacing) * col; // 計算按鈕的 x 座標
        CGFloat y = startY + (buttonHeight + spacing) * row; // 計算按鈕的 y 座標

        button.frame = CGRectMake(x, y, buttonWidth, buttonHeight); // 設置按鈕的框架

        // 取得按鈕資料
        NSDictionary *data = self.buttonData[i];
        NSString *imageName = data[@"image"];
        NSString *title = data[@"title"];

        // 設置圖片
        UIImage *image = [[UIImage imageNamed:imageName] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        if (!image) {
            NSLog(@"Warning: Image not found for %@", imageName);
        } else {
            button.imageView.contentMode = UIViewContentModeScaleAspectFit;
            [button setImage:image forState:UIControlStateNormal];
        }

        // 設定按鈕的 tintColor 為透明（可選）
        button.tintColor = [UIColor clearColor];

        // 調整圖片和文字的間距
        CGFloat spacingBetweenImageAndTitle = 0; // 圖片與文字的間距
        button.titleEdgeInsets = UIEdgeInsetsMake(button.imageView.frame.size.height + spacingBetweenImageAndTitle, -button.imageView.frame.size.width, 0, 0);
        button.imageEdgeInsets = UIEdgeInsetsMake(0, 0, button.titleLabel.intrinsicContentSize.height + spacingBetweenImageAndTitle, -button.titleLabel.intrinsicContentSize.width);

        [button setTitle:title forState:UIControlStateNormal]; // 設置按鈕文字
        button.titleLabel.font = [UIFont systemFontOfSize:20]; //設定文字大小
        button.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
        button.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
        [button setTitleColor:[UIColor blackColor] forState:UIControlStateNormal]; //設定顏色blackColor

        button.tag = i; // 使用 tag 儲存按鈕索引
        [button addTarget:self action:@selector(buttonTapped:) forControlEvents:UIControlEventTouchUpInside];

        [self.scrollView addSubview:button]; // 將按鈕加入 UIScrollView
    }

    CGFloat contentHeight = ((self.buttonData.count + buttonsPerRow - 1) / buttonsPerRow) * (buttonHeight + spacing);
    self.scrollView.contentSize = CGSizeMake(self.view.bounds.size.width, contentHeight); // 設置 UIScrollView 的滾動範圍
}
//穎.2放在任何地方

//穎.3放在任何地方
//(簡化)1.使用統一的按鈕操作方法 (按鈕點擊事件處理)
- (void)buttonTapped:(UIButton *)sender {
    NSInteger index = sender.tag;
    if (index < self.buttonData.count) {
        NSDictionary *data = self.buttonData[index];
        NSNumber *actionNumber = data[@"action"];
        [self commonHandlerForAction:actionNumber.integerValue];
    }
}
//    // 清空 UIScrollView 的所有子視圖
//    for (UIView *subview in self.scrollView.subviews) {
//        [subview removeFromSuperview];
//    }
//    // 清空 buttonData 陣列
//    self.buttonData = @[];
//    // 更新 UIScrollView 的內容範圍
//    self.scrollView.contentSize = CGSizeZero;
//    // 如果需要執行其他動作
//穎.3放在任何地方


//穎.4放在任何地方
- (void)commonHandlerForAction:(NSInteger)actionNumber {
    DDLogDebugTag(@"4pick1", @"Action %ld executed!", (long)actionNumber);
    [[NSUserDefaults standardUserDefaults] setInteger:actionNumber forKey:@"myInteger"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    NSLog(@"Action %ld executed!", (long)actionNumber);
    [AppRoutes push:AppRoutes.selector41];
}


//穎.4放在任何地方
    @end
