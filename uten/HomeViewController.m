//
//  HomeViewController.m
//  uten
//
//  Created by 簡大翔 on 2018/6/21.
//  Copyright © 2018年 bekubee. All rights reserved.
//

#import "HomeViewController.h"
#import "WriterViewController.h"
#import "SayViewController.h"
#import "GRRequestsManager.h"
#import "GRListingRequest.h"

@interface HomeViewController () <GRRequestsManagerDelegate> {
    IBOutlet UIView *alertClass;
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    IBOutlet UIButton *btUpdate;
    IBOutlet UIButton *class00;
    IBOutlet UIButton *class01;
    IBOutlet UIButton *class02;
    IBOutlet UIButton *class03;
    IBOutlet UIButton *class04;
    IBOutlet UIButton *class05;
    IBOutlet UIButton *class06;
    
    IBOutlet UILabel *lt00;
    IBOutlet UILabel *lt01;
    IBOutlet UILabel *lt02;
    IBOutlet UILabel *lt03;
    IBOutlet UILabel *lt04;
    IBOutlet UILabel *lt05;
    IBOutlet UILabel *lt06;
    
    IBOutlet UILabel *ld00;
    IBOutlet UILabel *ld01;
    IBOutlet UILabel *ld02;
    IBOutlet UILabel *ld03;
    IBOutlet UILabel *ld04;
    IBOutlet UILabel *ld05;
    IBOutlet UILabel *ld06;
//
    IBOutlet UIButton *class10;
    IBOutlet UIButton *class11;
    IBOutlet UIButton *class12;
    IBOutlet UIButton *class13;
    IBOutlet UIButton *class14;
    IBOutlet UIButton *class15;
    IBOutlet UIButton *class16;
    IBOutlet UIButton *class17;
    IBOutlet UIButton *class18;
    IBOutlet UIButton *class19;
    IBOutlet UILabel *lm10;
    IBOutlet UILabel *lm11;
    IBOutlet UILabel *lm12;
    IBOutlet UILabel *lm13;
    IBOutlet UILabel *lm14;
    IBOutlet UILabel *lm15;
    IBOutlet UILabel *lm16;
    IBOutlet UILabel *lm17;
    IBOutlet UILabel *lm18;
    IBOutlet UILabel *lm19;
    IBOutlet UILabel *lw10;
    IBOutlet UILabel *lw11;
    IBOutlet UILabel *lw12;
    IBOutlet UILabel *lw13;
    IBOutlet UILabel *lw14;
    IBOutlet UILabel *lw15;
    IBOutlet UILabel *lw16;
    IBOutlet UILabel *lw17;
    IBOutlet UILabel *lw18;
    IBOutlet UILabel *lw19;
    IBOutlet UILabel *ld10;
    IBOutlet UILabel *ld11;
    IBOutlet UILabel *ld12;
    IBOutlet UILabel *ld13;
    IBOutlet UILabel *ld14;
    IBOutlet UILabel *ld15;
    IBOutlet UILabel *ld16;
    IBOutlet UILabel *ld17;
    IBOutlet UILabel *ld18;
    IBOutlet UILabel *ld19;
//
    IBOutlet UIButton *class20;
    IBOutlet UIButton *class21;
    IBOutlet UIButton *class22;
    IBOutlet UIButton *class23;
    IBOutlet UIButton *class24;
    IBOutlet UIButton *class25;
    IBOutlet UIButton *class26;
    IBOutlet UIButton *class27;
    IBOutlet UIButton *class28;
    IBOutlet UIButton *class29;
    IBOutlet UILabel *lm20;
    IBOutlet UILabel *lm21;
    IBOutlet UILabel *lm22;
    IBOutlet UILabel *lm23;
    IBOutlet UILabel *lm24;
    IBOutlet UILabel *lm25;
    IBOutlet UILabel *lm26;
    IBOutlet UILabel *lm27;
    IBOutlet UILabel *lm28;
    IBOutlet UILabel *lm29;
    IBOutlet UILabel *lw20;
    IBOutlet UILabel *lw21;
    IBOutlet UILabel *lw22;
    IBOutlet UILabel *lw23;
    IBOutlet UILabel *lw24;
    IBOutlet UILabel *lw25;
    IBOutlet UILabel *lw26;
    IBOutlet UILabel *lw27;
    IBOutlet UILabel *lw28;
    IBOutlet UILabel *lw29;
    IBOutlet UILabel *ld20;
    IBOutlet UILabel *ld21;
    IBOutlet UILabel *ld22;
    IBOutlet UILabel *ld23;
    IBOutlet UILabel *ld24;
    IBOutlet UILabel *ld25;
    IBOutlet UILabel *ld26;
    IBOutlet UILabel *ld27;
    IBOutlet UILabel *ld28;
    IBOutlet UILabel *ld29;
    //
    IBOutlet UIButton *class30;
    IBOutlet UIButton *class31;
    IBOutlet UIButton *class32;
    IBOutlet UIButton *class33;
    IBOutlet UIButton *class34;
    IBOutlet UIButton *class35;
    IBOutlet UIButton *class36;
    IBOutlet UIButton *class37;
    IBOutlet UIButton *class38;
    IBOutlet UIButton *class39;
    IBOutlet UILabel *lm30;
    IBOutlet UILabel *lm31;
    IBOutlet UILabel *lm32;
    IBOutlet UILabel *lm33;
    IBOutlet UILabel *lm34;
    IBOutlet UILabel *lm35;
    IBOutlet UILabel *lm36;
    IBOutlet UILabel *lm37;
    IBOutlet UILabel *lm38;
    IBOutlet UILabel *lm39;
    IBOutlet UILabel *lw30;
    IBOutlet UILabel *lw31;
    IBOutlet UILabel *lw32;
    IBOutlet UILabel *lw33;
    IBOutlet UILabel *lw34;
    IBOutlet UILabel *lw35;
    IBOutlet UILabel *lw36;
    IBOutlet UILabel *lw37;
    IBOutlet UILabel *lw38;
    IBOutlet UILabel *lw39;
    IBOutlet UILabel *ld30;
    IBOutlet UILabel *ld31;
    IBOutlet UILabel *ld32;
    IBOutlet UILabel *ld33;
    IBOutlet UILabel *ld34;
    IBOutlet UILabel *ld35;
    IBOutlet UILabel *ld36;
    IBOutlet UILabel *ld37;
    IBOutlet UILabel *ld38;
    IBOutlet UILabel *ld39;
 //
    
    UIButton *btClass0[7];
    UILabel  *lvt0[7];
    UILabel  *lvd0[7];
    UIButton *btClass1[10];
    UILabel  *lvm1[10];
    UILabel  *lvw1[10];
    UILabel  *lvd1[10];
    UIButton *btClass2[10];
    UILabel  *lvm2[10];
    UILabel  *lvw2[10];
    UILabel  *lvd2[10];
    UIButton *btClass3[10];
    UILabel  *lvm3[10];
    UILabel  *lvw3[10];
    UILabel  *lvd3[10];
    int mode;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    UIActivityIndicatorView *indicator;
    int downcount;
    
}
@property (nonatomic, strong) GRRequestsManager *requestsManager;
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation HomeViewController
-(void) BuildButton {
    btClass0[0]=class00; btClass0[1]=class01; btClass0[2]=class02; btClass0[3]=class03; btClass0[4]=class04; btClass0[5]=class05; btClass0[6]=class06;
    lvt0[0]=lt00; lvt0[1]=lt01; lvt0[2]=lt02; lvt0[3]=lt03; lvt0[4]=lt04; lvt0[5]=lt05; lvt0[6]=lt06;
    lvd0[0]=ld00; lvd0[1]=ld01; lvd0[2]=ld02; lvd0[3]=ld03; lvd0[4]=ld04; lvd0[5]=ld05; lvd0[6]=ld06;
    btClass1[0]=class10; btClass1[1]=class11; btClass1[2]=class12; btClass1[3]=class13; btClass1[4]=class14;
    btClass1[5]=class15; btClass1[6]=class16; btClass1[7]=class17; btClass1[8]=class18; btClass1[9]=class19;
    lvm1[0]=lm10; lvm1[1]=lm11; lvm1[2]=lm12; lvm1[3]=lm13; lvm1[4]=lm14; lvm1[5]=lm15; lvm1[6]=lm16; lvm1[7]=lm17; lvm1[8]=lm18; lvm1[9]=lm19;
    lvw1[0]=lw10; lvw1[1]=lw11; lvw1[2]=lw12; lvw1[3]=lw13; lvw1[4]=lw14; lvw1[5]=lw15; lvw1[6]=lw16; lvw1[7]=lw17; lvw1[8]=lw18; lvw1[9]=lw19;
    lvd1[0]=ld10; lvd1[1]=ld11; lvd1[2]=ld12; lvd1[3]=ld13; lvd1[4]=ld14; lvd1[5]=ld15; lvd1[6]=ld16; lvd1[7]=ld17; lvd1[8]=ld18; lvd1[9]=ld19;
    btClass2[0]=class20; btClass2[1]=class21; btClass2[2]=class22; btClass2[3]=class23; btClass2[4]=class24;
    btClass2[5]=class25; btClass2[6]=class26; btClass2[7]=class27; btClass2[8]=class28; btClass2[9]=class29;
    lvm2[0]=lm20; lvm2[1]=lm21; lvm2[2]=lm22; lvm2[3]=lm23; lvm2[4]=lm24; lvm2[5]=lm25; lvm2[6]=lm26; lvm2[7]=lm27; lvm2[8]=lm28; lvm2[9]=lm29;
    lvw2[0]=lw20; lvw2[1]=lw21; lvw2[2]=lw22; lvw2[3]=lw23; lvw2[4]=lw24; lvw2[5]=lw25; lvw2[6]=lw26; lvw2[7]=lw27; lvw2[8]=lw28; lvw2[9]=lw29;
    lvd2[0]=ld20; lvd2[1]=ld21; lvd2[2]=ld22; lvd2[3]=ld23; lvd2[4]=ld24; lvd2[5]=ld25; lvd2[6]=ld26; lvd2[7]=ld27; lvd2[8]=ld28; lvd2[9]=ld29;
    btClass3[0]=class30; btClass3[1]=class31; btClass3[2]=class32; btClass3[3]=class33; btClass3[4]=class34;
    btClass3[5]=class35; btClass3[6]=class36; btClass3[7]=class37; btClass3[8]=class38; btClass3[9]=class39;
    lvm3[0]=lm30; lvm3[1]=lm31; lvm3[2]=lm32; lvm3[3]=lm33; lvm3[4]=lm34; lvm3[5]=lm35; lvm3[6]=lm36; lvm3[7]=lm37; lvm3[8]=lm38; lvm3[9]=lm39;
    lvw3[0]=lw30; lvw3[1]=lw31; lvw3[2]=lw32; lvw3[3]=lw33; lvw3[4]=lw34; lvw3[5]=lw35; lvw3[6]=lw36; lvw3[7]=lw37; lvw3[8]=lw38; lvw3[9]=lw39;
    lvd3[0]=ld30; lvd3[1]=ld31; lvd3[2]=ld32; lvd3[3]=ld33; lvd3[4]=ld34; lvd3[5]=ld35; lvd3[6]=ld36; lvd3[7]=ld37; lvd3[8]=ld38; lvd3[9]=ld39;
    
    mode=0;
}
-(void) BuildClass06_08 {
    int DA0[10]={1,2,3,4,5,6,7,8,9,10};
    NSString *WA0[10]={@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write"};
    NSString *MA0[10]={@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class"};
    
    int DA1[10]={11,12,13,1,2,3,4,5,6,7};
    NSString *WA1[10]={@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write"};
    NSString *MA1[10]={@"Class",@"Class",@"Class",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test"};
    
    int DA2[10]={8,9,10,11,12,13,0,0,0,0};
    NSString *WA2[10]={@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write",@"Write"};
    NSString *MA2[10]={@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test"};

    for(int i=0;i<10;i++) {
        [btClass1[i] setImage:[UIImage imageNamed: @"ispeak-day_purple.png"] forState:UIControlStateNormal];
        if(DA0[i]==0) {
            btClass1[i].hidden=YES;
            lvm1[i].hidden=YES;
            lvw1[i].hidden=YES;
            lvd1[i].hidden=YES;
        } else {
            btClass1[i].hidden=NO;
            lvm1[i].hidden=NO;
            lvw1[i].hidden=NO;
            lvd1[i].hidden=NO;
            lvm1[i].text=MA0[i]; //[NSString stringWithFormat:@"%i月", MA0[i]];
            lvw1[i].text=WA0[i];
            lvd1[i].text=[NSString stringWithFormat:@"%02i", DA0[i]];
        }
        
        [btClass2[i] setImage:[UIImage imageNamed: @"ispeak-day_purple.png"] forState:UIControlStateNormal];
        if(DA1[i]==0) {
            btClass2[i].hidden=YES;
            lvm2[i].hidden=YES;
            lvw2[i].hidden=YES;
            lvd2[i].hidden=YES;
        } else {
            btClass2[i].hidden=NO;
            lvm2[i].hidden=NO;
            lvw2[i].hidden=NO;
            lvd2[i].hidden=NO;
            lvm2[i].text=MA1[i]; //[NSString stringWithFormat:@"%i月", MA1[i]];
            lvw2[i].text=WA1[i];
            lvd2[i].text=[NSString stringWithFormat:@"%02i", DA1[i]];
        }
        [btClass3[i] setImage:[UIImage imageNamed: @"ispeak-day_purple.png"] forState:UIControlStateNormal];
        if(DA2[i]==0) {
            btClass3[i].hidden=YES;
            lvm3[i].hidden=YES;
            lvw3[i].hidden=YES;
            lvd3[i].hidden=YES;
        } else {
            btClass3[i].hidden=NO;
            lvm3[i].hidden=NO;
            lvw3[i].hidden=NO;
            lvd3[i].hidden=NO;
            lvm3[i].text=MA2[i]; //[NSString stringWithFormat:@"%i月", MA2[i]];
            lvw3[i].text=WA2[i];
            lvd3[i].text=[NSString stringWithFormat:@"%02i", DA2[i]];
        }
    }
}
- (IBAction)btM01Action:(id)sender {
    [self BuildClass06_08];
    mode=0;
}
- (IBAction)btM02Action:(id)sender {
    [self BuildClass09_11];
    mode=1;
}
-(void) BuildClass09_11 {
    int DA0[10]={1,2,3,4,5,6,7,8,9,10};
    NSString *WA0[10]={@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say"};
    NSString *MA0[10]={@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class",@"Class"};
    
    int DA1[10]={11,12,13,1,2,3,4,5,6,7};
    NSString *WA1[10]={@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say"};
    NSString *MA1[10]={@"Class",@"Class",@"Class",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test"};
    
    int DA2[10]={8,9,10,11,12,13,0,0,0,0};
    NSString *WA2[10]={@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say",@"Say"};
    NSString *MA2[10]={@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test",@"Test"};
    
    for(int i=0;i<10;i++) {
        [btClass1[i] setImage:[UIImage imageNamed: @"ispeak-day_green.png"] forState:UIControlStateNormal];
        if(DA0[i]==0) {
            btClass1[i].hidden=YES;
            lvm1[i].hidden=YES;
            lvw1[i].hidden=YES;
            lvd1[i].hidden=YES;
        } else {
            btClass1[i].hidden=NO;
            lvm1[i].hidden=NO;
            lvw1[i].hidden=NO;
            lvd1[i].hidden=NO;
            lvm1[i].text=MA0[i]; //[NSString stringWithFormat:@"%i月", MA0[i]];
            lvw1[i].text=WA0[i];
            lvd1[i].text=[NSString stringWithFormat:@"%02i", DA0[i]];
        }
        
        [btClass2[i] setImage:[UIImage imageNamed: @"ispeak-day_green.png"] forState:UIControlStateNormal];
        if(DA1[i]==0) {
            btClass2[i].hidden=YES;
            lvm2[i].hidden=YES;
            lvw2[i].hidden=YES;
            lvd2[i].hidden=YES;
        } else {
            btClass2[i].hidden=NO;
            lvm2[i].hidden=NO;
            lvw2[i].hidden=NO;
            lvd2[i].hidden=NO;
            lvm2[i].text=MA1[i]; //[NSString stringWithFormat:@"%i月", MA1[i]];
            lvw2[i].text=WA1[i];
            lvd2[i].text=[NSString stringWithFormat:@"%02i", DA1[i]];
        }
        [btClass3[i] setImage:[UIImage imageNamed: @"ispeak-day_green.png"] forState:UIControlStateNormal];
        if(DA2[i]==0) {
            btClass3[i].hidden=YES;
            lvm3[i].hidden=YES;
            lvw3[i].hidden=YES;
            lvd3[i].hidden=YES;
        } else {
            btClass3[i].hidden=NO;
            lvm3[i].hidden=NO;
            lvw3[i].hidden=NO;
            lvd3[i].hidden=NO;
            lvm3[i].text=MA2[i];  //[NSString stringWithFormat:@"%i月", MA2[i]];
            lvw3[i].text=WA2[i];
            lvd3[i].text=[NSString stringWithFormat:@"%02i", DA2[i]];
        }
    }
}
- (void)viewDidLoad {

    [super viewDidLoad];
    
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    if([TYPE intValue] < 10) btUpdate.hidden=NO;
    else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    // Do any additional setup after loading the view.
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    alertClass.hidden=YES;
    [self BuildButton];
    for(int i=0;i<7;i++) {
        [btClass0[i] setImage:[UIImage imageNamed: @"ispeak-day_red1.png"] forState:UIControlStateNormal];
        btClass0[i].hidden=YES;
        lvt0[i].hidden=YES;
        lvd0[i].hidden=YES;
    }
    [btClass0[0] setImage:[UIImage imageNamed: @"ispeak-day_purple.png"] forState:UIControlStateNormal];
    [btClass0[1] setImage:[UIImage imageNamed: @"ispeak-day_green.png"] forState:UIControlStateNormal];
    btClass0[0].hidden=NO;
    btClass0[1].hidden=NO;
    lvd0[0].hidden=NO;
    lvd0[1].hidden=NO;
    lvt0[0].hidden=NO;
    lvt0[1].hidden=NO;
    [self BuildClass06_08];
//Debug
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"%@",@"/class/base/test"]];
    [self listFileAtPath:base];
    downcount=0;

}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btGotoClass:(id)sender {
    alertClass.hidden=NO;
}
- (IBAction)btBack:(id)sender {
    alertClass.hidden=YES;
}
- (IBAction)btNoSupport:(id)sender {
    
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"未輸入適當的課程" delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
        [alertView show];

}

- (IBAction)btGotoNext:(id)sender {
    UIButton *button = (UIButton *)sender;

    _seltag = [NSString stringWithFormat:[NSString stringWithFormat:@"%d", button.tag]];
    if(mode==0) {
        UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
        WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
        [myViewController setValue:_seltag forKey:@"seltag"];
        [self presentViewController:myViewController animated:YES completion:nil];
    } else {
        UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
        SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
        [myViewController setValue:_seltag forKey:@"seltag"];
        [self presentViewController:myViewController animated:YES completion:nil];
    }
}
-(NSArray *)listFileAtPath:(NSString *)path
{
    //-----> LIST ALL FILES <-----//
    NSLog(@"LISTING ALL FILES FOUND");
    
    int count;
    
    NSArray *directoryContent = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:path error:NULL];
    for (count = 0; count < (int)[directoryContent count]; count++)
    {
        NSLog(@"File %d: %@", (count + 1), [directoryContent objectAtIndex:count]);
    }
    return directoryContent;
}

- (IBAction)btUpgrade:(id)sender {

    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"/%@",@"class"]];
    [fileManager createDirectoryAtPath:base withIntermediateDirectories:YES attributes:nil error:nil];
    base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"/class/%@",@"l1u1u4"]];
    [fileManager createDirectoryAtPath:base withIntermediateDirectories:YES attributes:nil error:nil];
    base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"/class/l1u1u4/%@",@"sound"]];
    [fileManager createDirectoryAtPath:base withIntermediateDirectories:YES attributes:nil error:nil];
    base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"/class/l1u1u4/%@",@"word"]];
    [fileManager createDirectoryAtPath:base withIntermediateDirectories:YES attributes:nil error:nil];
    base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"/class/l1u1u4/%@",@"test"]];
    [fileManager createDirectoryAtPath:base withIntermediateDirectories:YES attributes:nil error:nil];
    
    base = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"%@",@"/l1u1u4/base/sound"]];
    NSLog(@"[JAMES]List Directory");
    [self listFileAtPath:base];
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:@"phototracq.com" user:@"<EMAIL>" password:@"4rfvCDE#2wsxZAQ!"];
    self.requestsManager.delegate=self;
    [self.requestsManager addRequestForListDirectoryAtPath:@"/class/l1u1u4/sound"];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForListDirectoryAtPath:@"/class/l1u1u4/word"];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForListDirectoryAtPath:@"/class/l1u1u4/test"];
    [self.requestsManager startProcessingRequests];
    //
    indicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
    indicator.frame = CGRectMake(0.0, 0.0, 200.0, 200.0);
    indicator.center = self.view.center;
    [self.view addSubview:indicator];
    [indicator bringSubviewToFront:self.view];
    [UIApplication sharedApplication].networkActivityIndicatorVisible = TRUE;
    [indicator startAnimating];
}


#pragma mark - GRRequestsManagerDelegate

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didScheduleRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didScheduleRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteListingRequest:(id<GRRequestProtocol>)request listing:(NSArray *)listing
{
    //NSLog(@"requestsManager:didCompleteListingRequest:listing: \n%@", listing);
    //沙盒目录
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    //远程文件夹列表
    GRListingRequest *req = (GRListingRequest *)request;
    NSLog(@"REMOTE:%@",listing);
    //打印创建的目录
    NSLog(@"DOC:%@",documentsDirectoryPath);
    NSFileManager *fileManager = [NSFileManager defaultManager];
    //文件夹列表枚举
    [listing enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([[obj pathExtension] isEqualToString:@""]) {
                NSString *zhenggedizhi = [documentsDirectoryPath stringByAppendingString:[NSString stringWithFormat:@"%@%@",req.path,obj]];
                if (![fileManager fileExistsAtPath:zhenggedizhi]) {
                    [fileManager createDirectoryAtPath:zhenggedizhi withIntermediateDirectories:YES attributes:nil error:nil];
                }
            }else{
                if(([[obj pathExtension] isEqualToString:@"csv"])|([[obj pathExtension] isEqualToString:@"mp3"])) {
               // if([[obj pathExtension] isEqualToString:@"txt"]) {
                    NSLog(@"[JAMES] DOWNLOAD:%@",[NSString stringWithFormat:@"%@%@",req.path,obj]);
                    [self.requestsManager addRequestForDownloadFileAtRemotePath:[NSString stringWithFormat:@"%@%@",req.path,obj] toLocalPath:[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@%@",req.path,obj]]];
                    [self.requestsManager startProcessingRequests];
                }
            }
        
    }];
    
    
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteCreateDirectoryRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteCreateDirectoryRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDeleteRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDeleteRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompletePercent:(float)percent forRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompletePercent:forRequest: %f", percent);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteUploadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteUploadRequest:");
}
- (void)requestsManagerDidCompleteQueue:(id<GRRequestsManagerProtocol>)requestsManager{
    NSLog(@"整个文件夹下载完成+++++++++++++++++");
    [indicator stopAnimating];
    
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:@"題庫下載完成,請重新登入才會套用新題庫" delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
    
    [alert show];
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    exit(0);
}
- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDownloadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDownloadRequest:");
 //   [indicator stopAnimating];
    NSLog(@"[JAMES DOWNLOAD]:%d",downcount++);
    /*
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *localFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"user.csv"];
    NSString* content = [NSString stringWithContentsOfFile:localFilePath
                                                  encoding:NSUTF8StringEncoding
                                                     error:NULL];
    NSLog(@"CSV FIle:\n%@",content);
    NSArray *splitLine = [content componentsSeparatedByString:@"\n"];
    UserMax=0;
    for(int i=1;i<[splitLine count];i++) {
        NSArray *split= [splitLine[i] componentsSeparatedByString:@","];
        ID[UserMax]=split[0];
        TYPE[UserMax]=split[1];
        CNAME[UserMax]=split[2];
        ENAME[UserMax]=split[3];
        QRCODE[UserMax]=split[6];
        UserMax++;
    }
    for(int i=0;i<UserMax;i++) {
        NSLog(@"NAME:%@ , QR:%@",CNAME[i],QRCODE[i]);
    }
     */
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailWritingFileAtPath:(NSString *)path forRequest:(id<GRDataExchangeRequestProtocol>)request error:(NSError *)error
{
    NSLog(@"requestsManager:didFailWritingFileAtPath:forRequest:error: \n %@", error);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailRequest:(id<GRRequestProtocol>)request withError:(NSError *)error
{
    NSLog(@"requestsManager:didFailRequest:withError: \n %@", error);
}
- (IBAction)btBackHome:(id)sender {
    exit(0);
}

@end
