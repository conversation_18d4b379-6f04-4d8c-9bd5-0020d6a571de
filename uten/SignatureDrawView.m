//
//  SignatureDrawView.m
//  uten
//
//  Created by 簡大翔 on 2018/8/13.
//  Copyright © 2018年 bekubee. All rights reserved.
//

#import "SignatureDrawView.h"
#import <OHMySQL/OHMySQL.h>

@implementation SignatureDrawView {
    UIImage *tempDrawImage;
    int orgimg[75][25];
    int drawimg[75][25];
    int WordPtr;
    int mx,my,mrang;
    int selrang;
    CGPoint FisrtPoint;
    int count;
    //James
    int LAMx[40000*4];   //James //Peter 0603 原本1000(1000/4=250筆)改10000
    int LAMc;           //James
    int UDi;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UDj;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UDk;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UpDownLMA3[400][4];//全域變數.(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)
    int UDsi;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UDLMAs[20][4];     //全域變數.(將標準點LMAs_a[~][~]放入,在btCheckUp.btCheckDwon使用)
    
    
    
}

@synthesize theSwipeGesture;
@synthesize drawImage;
@synthesize lastPoint;
@synthesize mouseSwiped;
@synthesize mouseMoved;
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */
#pragma mark - View lifecycle

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        // Initialization code
        drawImage = [[UIImageView alloc] initWithImage:nil];
        drawImage.frame = CGRectMake(0, 0, self.frame.size.width, self.frame.size.height);
        [self addSubview:drawImage];
        self.backgroundColor = [UIColor whiteColor];
        mouseMoved = 0;
        FisrtPoint.x=0;
        FisrtPoint.y=0;
        count=0;
    }
    
    return self;
}

- (id)initWithCoder:(NSCoder*)coder
{
    if ((self = [super initWithCoder:coder]))
    {
        drawImage = [[UIImageView alloc] initWithImage:nil];
        drawImage.frame = CGRectMake(0, 0, self.frame.size.width, self.frame.size.height);
        [self addSubview:drawImage];
        self.backgroundColor = [UIColor redColor];
        mouseMoved = 0;
    }
    return self;
}

#pragma mark touch handling

- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    for (UITouch *touch in touches)
    {
        NSArray *array = touch.gestureRecognizers;
        for (UIGestureRecognizer *gesture in array)
        {
            if (gesture.enabled & [gesture isMemberOfClass:[UISwipeGestureRecognizer class]])
            {
                gesture.enabled = NO;
                self.theSwipeGesture = gesture;
            }
        }
    }
    
    mouseSwiped = NO;
    UITouch *touch = [touches anyObject];
    
    lastPoint = [touch locationInView:self];
    if(count==0) {
        FisrtPoint=lastPoint;
    }
    // JAMES0417
    LAMx[LAMc*4+0]=(int) lastPoint.x;              //James
    LAMx[LAMc*4+1]=(int) lastPoint.y;              //James
    LAMx[LAMc*4+2]=0;                              //James
    LAMx[LAMc*4+3]=(count+1)*1000;                 //James
    LAMc++;
    count++;
}

- (void)touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event
{
    mouseSwiped = YES;
    
    UITouch *touch = [touches anyObject];
    CGPoint currentPoint = [touch locationInView:self];
    
    UIGraphicsBeginImageContext(self.frame.size);
    [drawImage.image drawInRect:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
    CGContextSetLineCap(UIGraphicsGetCurrentContext(), kCGLineCapRound);
    CGContextSetLineWidth(UIGraphicsGetCurrentContext(), 1.0);
    CGContextSetRGBStrokeColor(UIGraphicsGetCurrentContext(), 0.0, 0.0, 1.0, 1.0);
    CGContextBeginPath(UIGraphicsGetCurrentContext());
    CGContextMoveToPoint(UIGraphicsGetCurrentContext(), lastPoint.x, lastPoint.y);
    CGContextAddLineToPoint(UIGraphicsGetCurrentContext(), currentPoint.x, currentPoint.y);
    CGContextStrokePath(UIGraphicsGetCurrentContext());
    drawImage.image = UIGraphicsGetImageFromCurrentImageContext();
    
    // LMA1[~][0]=lastPoint.x       LMA1[~][1]=lastPoint.y
    LAMx[LAMc*4+0]=(int) lastPoint.x; //James
    LAMx[LAMc*4+1]=(int) lastPoint.y; //James
    LAMx[LAMc*4+2]=0;                 //James
    LAMx[LAMc*4+3]=0;                 //James
    LAMc++;                           //James
    UIGraphicsEndImageContext();
    
    lastPoint = currentPoint;
    
    mouseMoved++;
    NSLog(@"Count:%ld / Position:(%f,%f)",(long)mouseMoved,lastPoint.x,lastPoint.y);
    if (mouseMoved == 10) {
        mouseMoved = 0;
    }
}
/* JAMES0417 把點改成字串
 -(NSString *) packLAMx {
 NSString *s=@"";
 for(int i=0;i<LAMc;i++) {
 s=[NSString stringWithFormat:@"%@%d*%d*%d*%d#",s,LAMx[LAMc*4+0], LAMx[LAMc*4+1]
 , LAMx[LAMc*4+2],LAMx[LAMc*4+3]];
 }
 return s;
 }
 JAMES0417 把字串改成點
 -(void) unpackLAMx:(NSString *) lamx {
 NSArray *pointstr = [lamx componentsSeparatedByString:@"#"];
 for(int i=0;i< pointstr.count-1;i++) {
 NSArray *item=[pointstr[i] componentsSeparatedByString:@"*"];
 //Do something
 // item[0] = X
 // item[1] = Y
 }
 }
 */
- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event
{
    if(!mouseSwiped)
    {
        UIGraphicsBeginImageContext(self.frame.size);
        [drawImage.image drawInRect:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
        CGContextSetLineCap(UIGraphicsGetCurrentContext(), kCGLineCapRound);
        CGContextSetLineWidth(UIGraphicsGetCurrentContext(), 3.0);
        CGContextSetRGBStrokeColor(UIGraphicsGetCurrentContext(), 0.0, 0.0, 1.0, 1.0);
        CGContextMoveToPoint(UIGraphicsGetCurrentContext(), lastPoint.x, lastPoint.y);
        CGContextAddLineToPoint(UIGraphicsGetCurrentContext(), lastPoint.x, lastPoint.y);
        CGContextStrokePath(UIGraphicsGetCurrentContext());
        CGContextFlush(UIGraphicsGetCurrentContext());
        drawImage.image = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }
    self.theSwipeGesture.enabled = YES;
    mouseSwiped = YES;
}

#pragma mark Methods

- (void)erase
{
    mouseSwiped = NO;
    drawImage.image = nil;
    FisrtPoint.x=0;
    FisrtPoint.y=0;
    count=0;
    *LAMx = 0 ;//Peter
    LAMc=0;//James
    
}
-(int *) getLAMx {    //James
    return LAMx;      //James
    
}
-(int) getLAMc{      //James
    return LAMc;     //James
}
- (void)setSignature:(NSData *)theLastData
{
    UIImage *image = [UIImage imageWithData:theLastData];
    if (image != nil)
    {
        drawImage.image = [UIImage imageWithData:theLastData];
        mouseSwiped = YES;
    }
}












//=============================================
- (void) printImg
{
    NSString *s=@"";
    for(int yy=0;yy<75;yy++) {
        s=@"";
        for(int xx=0;xx<25;xx++) {
            s = [NSString stringWithFormat:@"%@%d", s, drawimg[yy][xx]];
        }
        NSLog(@"%@",s);
    }
}


-(int)getDrawImage:(UIImage*)image
{
    int count=0;
    //    NSMutableArray *resultColor = [NSMutableArray array];
    CGImageRef imageRef = [image CGImage];
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    unsigned char *rawData = (unsigned char*) calloc(height * width * 4, sizeof(unsigned char));
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                                 
                                                 bitsPerComponent, bytesPerRow, colorSpace,
                                                 
                                                 kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    for(int yy=0;yy<75;yy++) {
        for(int xx=0;xx<25;xx++) {
            int idx=(yy*25+xx)*4;
            unsigned long da=rawData[idx+3]; da<<=8;
            da|=rawData[idx]; da<<=8;
            da|=rawData[idx+1]; da<<=8;
            da|=rawData[idx+2];
            // if((rawData[idx+3]!=rawData[idx])&(rawData[idx]!=rawData[idx+1])&(rawData[idx+1]!=rawData[idx+2])&(rawData[idx+2]!=rawData[idx+3]))
            if(da > 0) {
                drawimg[yy][xx]=1;
                count++;
            }
            else drawimg[yy][xx]=0;
        }
    }
    
    // NSString *s=@"";
    // for(int yy=0;yy<75;yy++) {
    //     s=@"";
    //     for(int xx=0;xx<25;xx++) {
    //         s = [NSString stringWithFormat:@"%@%d", s, drawimg[yy][xx]];
    //     }
    //     NSLog(@"%@",s);
    // }
    // [self printImg];
    
    return count;
}






//=============================================


-(int)getOrgImage:(UIImage*)image
{
    int count=0;
    //    NSMutableArray *resultColor = [NSMutableArray array];
    CGImageRef imageRef = [image CGImage];
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    unsigned char *rawData = (unsigned char*) calloc(height * width * 4, sizeof(unsigned char));
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                                 
                                                 bitsPerComponent, bytesPerRow, colorSpace,
                                                 
                                                 kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    for(int yy=0;yy<75;yy++) {
        for(int xx=0;xx<25;xx++) {
            int idx=(yy*25+xx)*4;
            unsigned long da=rawData[idx+3]; da<<=8;
            da|=rawData[idx]; da<<=8;
            da|=rawData[idx+1]; da<<=8;
            da|=rawData[idx+2];
            if((rawData[idx+3]!=rawData[idx])&(rawData[idx]!=rawData[idx+1])&(rawData[idx+1]!=rawData[idx+2])&(rawData[idx+2]!=rawData[idx+3])) {
                orgimg[yy][xx]=1;
                count++;
            }
            else orgimg[yy][xx]=0;
        }
    }
    /*
     NSString *s=@"";
     for(int yy=0;yy<150;yy++) {
     s=@"";
     for(int xx=0;xx<50;xx++) {
     s = [NSString stringWithFormat:@"%@%d", s, orgimg[yy][xx]];
     }
     NSLog(@"%@",s);
     }
     */
    return count;
}
/*
 - (int) cmpMustPoint:(int) mx : (int) my : (int) mrang {
 for(int xx=mx-mrang;xx<mx+mrang;xx++)
 for(int yy=my-mrang;yy<my+mrang;yy++) {
 if(drawimg[yy][xx]==1) return 1;
 }
 return 0;
 }
 */
- (int) cmpMustPoint
{
    int rt = 0;
    for(int xx=mx-mrang;xx<mx+mrang;xx++){
        for(int yy=my-mrang;yy<my+mrang;yy++) {
            if(drawimg[yy][xx]==1) rt = 1;
            if(drawimg[yy][xx] == 0){
                drawimg[yy][xx] = 3;
            }
        }
    }
    
    
    return rt;
}
- (int) cmpMustFirstPoint
{
    NSString *s=@"";
    for(int yy=0;yy<75;yy++) {
        s=@"";
        for(int xx=0;xx<25;xx++) {
            s = [NSString stringWithFormat:@"%@%d", s, drawimg[yy][xx]];
            
        }
        NSLog(@"%@",s);
    }
    NSLog(@"\n\n\n\n");
    
    
    NSLog(@"First point x: %f", FisrtPoint.x);
    NSLog(@"First point y: %f", FisrtPoint.y);
    NSLog(@"Mrang: %d", mrang);
    NSLog(@"Mx: %d", mx);
    NSLog(@"My: %d", my);
    
    
    if((FisrtPoint.x >= (mx-mrang))&(FisrtPoint.x <= (mx+mrang))) {
        if((FisrtPoint.y >= (my-mrang))&(FisrtPoint.y <= (my+mrang))) {
            NSLog(@"dk point x: %f", FisrtPoint.x);
            NSLog(@"dk point y: %f", FisrtPoint.y);
            return 1;
        }
    }
    return 0;
}









- (int) cmpNgPoint:(int) x : (int) y : (int) rang {
    
    return 0;
}
- (int) cmpCountPoint:(int) x : (int) y : (int) rang {
    return 0;
}

/*
-(int) cmpWordA {

}
-(int) cmpWordB {

}
-(int) cmpWordC {

}
-(int) cmpWordD {

}
-(int) cmpWordE {

}
-(int) cmpWordF {

}
-(int) cmpWordG {

}
-(int) cmpWordH {

}
-(int) cmpWordI {

}
-(int) cmpWordJ {

}
-(int) cmpWordK {

}
-(int) cmpWordL {

}
-(int) cmpWordM {

}
-(int) cmpWordN {

}
-(int) cmpWordO {

}
-(int) cmpWordP {

}
-(int) cmpWordQ {

}
-(int) cmpWordR {

}
-(int) cmpWordS {

}
-(int) cmpWordT {

}
-(int) cmpWordU {

}
-(int) cmpWordV {

}
-(int) cmpWordW {

}
-(int) cmpWordX {

}
-(int) cmpWordY{

}
-(int) cmpWordZ {

}
*/





-(int) cmpWordA {
    //=============(Peter手寫轉折點辨識 turningPoint)=====================================
    int LMAs[10][4] = {{19,29,0,0},{5,30,0,0},{5,45,0,0},{19,44,0,0},{19,29,0,0},{19,34,0,0},{19,39,0,0},{19,44,0,0},{22,48,0,0}};//Standard-"a"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    //==========(Peter手寫轉折點辨識)==========================================================

    
//=============原始-(int) cmpWordA {================================
//    int total=0;
//    int cs=0;
//    int Func[53][3]={{1,24,35},{4,20,28},{3,14,26},{4,5,31},{3,3,38},{4,4,46},{3,12,50},{4,19,48},
//        {2,24,35},{2,22,43},{5,25,50},{6,3,3},{6,8,3},{6,13,3},{6,18,3},{6,23,3},
//        {6,3,8},{6,8,8},{6,13,8},{6,18,8},{6,23,8},{6,3,13},{6,8,13},{6,13,13},
//        {6,18,13},{6,23,13},{6,3,18},{6,8,18},{6,13,18},{6,18,18},{6,23,18},{6,3,58},
//        {6,8,58},{6,13,58},{6,18,58},{6,23,58},{6,3,63},{6,8,63},{6,13,63},{6,18,63},
//        {6,23,63},{6,3,68},{6,8,68},{6,13,68},{6,18,68},{6,23,68},{6,3,73},{6,8,73},
//        {6,13,73},{6,18,73},{6,23,73},{6,14,35},{6,13,40}
//    };
//    for(int i=0;i<53;i++) {
//        switch(Func[i][0]) {
//            case 6:
//                /*
//                mx=Func[i][1]; my=Func[i][2]; mrang=2;
//                if([self cmpMustPoint] > 0) {
//                    NSLog(@"NG Point");
//                    return 0;
//                }
//                 */
//                break;
//            case 1:
//                if(i==0) {
//                    mx=Func[i][1]; my=Func[i][2]; mrang=selrang;
//
//                    // NSLog(@"%@",selrang);
//                    if([self cmpMustFirstPoint] == 0) {
//                        NSLog(@"Lose Must First Point");
//                        return 0;
//                    } else NSLog(@"OK Must First Point");
//                }
//            case 2:
//            case 5:
//                mx=Func[i][1]; my=Func[i][2]; mrang=selrang;
//                if([self cmpMustPoint] == 0) {
//                    NSLog(@"Lose Must Point");
//                    return 0;
//                }
//                break;
//            default:
//                total++;
//                mx=Func[i][1]; my=Func[i][2]; mrang=selrang;
//                if([self cmpMustPoint] > 0) cs++;
//                break;
//        }
//    }
//    if(count!=1) return 0;
//    NSLog(@"total:%d / cs:%d ,count:%d",total,cs,count);
//    return 100*cs/total;


-(int) cmpWordB {
    //=============(Peter手寫轉折點辨識 turningPoint)=====================================
    int LMAs[10][4] = {{05,05,0,0},{05,16,0,0},{05,27,0,0},{05,38,0,0},{05,49,0,0},{06,28,0,0},{19,31,0,0},{18,46,0,0},{05,43,0,0}};//Standard-"b"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}


-(int) cmpWordC {
    //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[6][4] = {{19,33,0,0},{10,25,0,0},{05,37,0,0},{10,49,0,0},{19,41,0,0}};//"c"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordD {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{19,05,0,0},{19,16,0,0},{19,27,0,0},{19,38,0,0},{19,49,0,0},{19,32,0,0},{06,28,0,0},{05,45,0,0},{19,43,0,0}};//Standard-"d"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordE {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[5][4] = {{05,37,0,0},{19,33,0,0},{06,28,0,0},{06,46,0,0},{19,41,0,0}};//Standard-"e"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordF {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{20,9,0,0},{12,10,0,0},{12,23,0,0},{12,36,0,0},{12,49,0,0},{04,25,0,0},{8,25,0,0},{12,25,0,0},{16,25,0,0},{20,25,0,0}};//Standard-"f"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordG {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{19,31,0,0},{05,30,0,0},{06,46,0,0},{19,41,0,0},{19,25,0,0},{19,40,0,0},{19,54,0,0},{16,68,0,0},{05,62,0,0}};//Standard-"g"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordH {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{05,05,0,0},{05,16,0,0},{05,27,0,0},{05,38,0,0},{05,49,0,0},{05,35,0,0},{12,25,0,0},{19,35,0,0},{19,49,0,0}};//Standard-"h"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordI {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{12,25,0,0},{12,31,0,0},{12,37,0,0},{12,43,0,0},{12,49,0,0},{12,10,0,0},{12,11,0,0},{12,12,0,0},{12,13,0,0},{12,14,0,0}};//Standard-"i"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordJ {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{12,25,0,0},{12,38,0,0},{12,50,0,0},{12,63,0,0},{05,66,0,0},{12,10,0,0},{12,11,0,0},{12,12,0,0},{12,13,0,0},{12,14,0,0}};//Standard-"j"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordK {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[14][4] = {{05,05,0,0},{05,16,0,0},{05,27,0,0},{05,38,0,0},{05,49,0,0},{19,25,0,0},{15,28,0,0},{12,31,0,0},{8,34,0,0},{5,37,0,0},{8,40,0,0},{12,43,0,0},{15,46,0,0},{19,49,0,0}};//Standard-"k"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordL {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[5][4] = {{12,05,0,0},{12,16,0,0},{12,27,0,0},{12,38,0,0},{12,49,0,0}};//Standard-"l"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordM {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[13][4] = {{05,25,0,0},{05,31,0,0},{05,37,0,0},{05,43,0,0},{05,49,0,0},{05,36,0,0},{9,25,0,0},{12,37,0,0},{12,49,0,0},{12,36,0,0},{16,25,0,0},{19,37,0,0},{19,49,0,0}};//Standard-"m"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordN {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{05,25,0,0},{05,31,0,0},{05,37,0,0},{05,43,0,0},{05,49,0,0},{05,35,0,0},{12,25,0,0},{19,36,0,0},{19,49,0,0}};//Standard-"n"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordO {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[5][4] = {{12,25,0,0},{05,37,0,0},{12,49,0,0},{19,37,0,0},{12,25,0,0}};//Standard-"o"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordP {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{05,25,0,0},{05,36,0,0},{05,47,0,0},{05,58,0,0},{05,69,0,0},{05,30,0,0},{16,26,0,0},{19,37,0,0},{15,49,0,0},{05,44,0,0}};//Standard-"p"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordQ {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{19,25,0,0},{19,36,0,0},{19,47,0,0},{19,58,0,0},{19,69,0,0},{19,30,0,0},{8,26,0,0},{05,37,0,0},{9,49,0,0},{19,44,0,0}};//Standard-"q"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordR {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{8,25,0,0},{8,31,0,0},{8,37,0,0},{8,43,0,0},{8,49,0,0},{8,42,0,0},{8,35,0,0},{10,28,0,0},{17,25,0,0}};//Standard-"r"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordS {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[5][4] = {{19,31,0,0},{07,26,0,0},{12,37,0,0},{16,48,0,0},{05,43,0,0}};//Standard-"s"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordT {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[14][4] = {{12,05,0,0},{12,16,0,0},{12,27,0,0},{12,38,0,0},{12,49,0,0},{14,48,0,0},{15,47,0,0},{18,45,0,0},{19,45,0,0},{05,25,0,0},{9,25,0,0},{12,25,0,0},{16,25,0,0},{19,25,0,0}};//Standard-"t"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    

-(int) cmpWordU {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{19,29,0,0},{5,30,0,0},{5,45,0,0},{19,44,0,0},{19,29,0,0},{19,34,0,0},{19,39,0,0},{19,44,0,0},{22,48,0,0}};//Standard-"u"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordV {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{05,25,0,0},{07,31,0,0},{8,37,0,0},{10,43,0,0},{12,49,0,0},{14,43,0,0},{16,37,0,0},{17,31,0,0},{19,25,0,0}};//Standard-"v"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordW {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[17][4] = {{05,25,0,0},{06,31,0,0},{07,37,0,0},{8,43,0,0},{8,49,0,0},{9,43,0,0},{10,37,0,0},{11,31,0,0},{12,25,0,0},{13,31,0,0},{14,37,0,0},{15,43,0,0},{15,49,0,0},{16,43,0,0},{17,37,0,0},{18,31,0,0},{19,25,0,0}};//Standard-"w"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordX {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{05,25,0,0},{9,31,0,0},{12,37,0,0},{15,43,0,0},{19,49,0,0},{19,25,0,0},{15,31,0,0},{12,37,0,0},{9,43,0,0},{05,49,0,0}};//Standard-"x"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }

    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}

    
-(int) cmpWordY {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[10][4] = {{05,25,0,0},{07,31,0,0},{8,37,0,0},{10,43,0,0},{11,49,0,0},{19,25,0,0},{15,36,0,0},{12,47,0,0},{8,58,0,0},{05,69,0,0}};//Standard-"y"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
-(int) cmpWordZ {
        //=============(Peter手寫轉折點辨識 turningPoint)=====================================
int LMAs[13][4] = {{05,25,0,0},{9,25,0,0},{12,25,0,0},{16,25,0,0},{19,25,0,0},{15,31,0,0},{12,37,0,0},{9,43,0,0},{05,49,0,0},{9,49,0,0},{12,49,0,0},{16,49,0,0},{19,49,0,0}};//Standard-"z"
    int x0,y0,x1,y1,cx,cy,ds,n;   //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  conutLMA2=0, k=0,K=0,conutLMA1=0,countLMAs=0 ;       //,countLMA=0,(conutLMA2,count LMA2[~][] )(m.counter is lastnumber)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int LMA2i=0;            //(LMA2i.counter is turningPoint for LMA2)
    int LMAn[30000][4] =  {}; //new Array
    int LMA2[200][4] = {}; //correctPoints //LineMustArray -> LMA2
    int LMA1[1000][4];
    int LMA0[1000][4];           //James
    for(int i=0;i<LAMc;i++) {            //James
        LMA0[i][0]=LAMx[i*4+0];        //James
        LMA0[i][1]=LAMx[i*4+1];        //James
        LMA0[i][2]=LAMx[i*4+2];        //James
        LMA0[i][3]=LAMx[i*4+3];        //James
        //        countLMA = i;                 //we can know how many about LMA[~] into LMA1[~][-](=c/4)
//        NSLog(@"---%d,%d,%d,%d",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
    }
//    NSLog(@"<<<*LAMc>>>(%d)",LAMc);
    //--------(Delete the same points)------------
    LMA1[0][0]=LMA0[0][0];        //put first point
    LMA1[0][1]=LMA0[0][1];        //put first point
    LMA1[0][2]=0;                 //put first point
    LMA1[0][3]=0;                 //put first point
    
    for(int i=0; i< LAMc; i++ ){
        if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
            LMA1[(conutLMA1)][0] = LMA0[i][0];
            LMA1[(conutLMA1)][1] = LMA0[i][1];
                                               //James_0123
            LMA1[(conutLMA1)][2] = LMA0[i][2]; //James_0123
            LMA1[(conutLMA1)][3] = LMA0[i][3]; //James_0123
//  NSLog(@"===%d,%d,%d,%d",LMA1[(conutLMA1)][0],LMA1[(conutLMA1)][1],LMA1[(conutLMA1)][2],LMA1[(conutLMA1)][3]);
            conutLMA1++;
        }
    }
                              //James_0123
    LMA1[(conutLMA1)][0] = 0; //James_0123
    LMA1[(conutLMA1)][1] = 0; //James_0123
    LMA1[(conutLMA1)][2] = 0; //James_0123
    LMA1[(conutLMA1)][3] = 0; //James_0123
//    NSLog(@"<<<*LAMc>>>(%d)",(conutLMA1-1));
    //--------(Delete the same points)------------
    //-----------------------------put (-1) to LMA2[~][3]
    for (int i=0; i<200 ; i++){
        LMA2[i][3] = -1;
        // LAM[i*4+3]=-1;     //James
    }
    //-----------------------------put (-1) to LMA2[~][3]
    
    LMA2[0][0] = LMA1[0][0];
    LMA2[0][1] = LMA1[0][1];
    LMA2[0][3] = 0;
    LMAn[0][0] = LMA1[0][0];
    LMAn[0][1] = LMA1[0][1];
    
    while ( LMA1[(conutLMA1)][0] != 0 && LMA1[(conutLMA1)][1] != 0){
        conutLMA1++ ;  //(count LMA1n how many)
    }
    
    for (int p=0; p<(conutLMA1-1) ;p++){    //++( make continuousPoint )* P/times++
        
        x0 = LMA1[p][0];
        y0 = LMA1[p][1];
        x1 = LMA1[p+1][0];
        y1 = LMA1[p+1][1];
        
        ds = 0;      //(make continuousPoint)       //(ds is biger side，x-side or y-side)
        if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){
            ds = (abs(x0-x1)-1);
        }else{
            ds = (abs(y0-y1)-1);
        }
        
        cx=0;
        cy=0;      //firstPoint cx+1,cy+1 if x==y don't do anything
        if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
            cy=1 ;
        }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
            cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
        }
        
        int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
        pmx = pmx*(abs(x1-x0)/(x1-x0));
        pmy = pmy*(abs(y1-y0)/(y1-y0));
        
//        NSLog(@"%d", ds);
        for ( n=0; n < ds ; n++){
            LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
            LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
        }      //-------------------(make continuousPoint)
        
        for(int n=0; n< ds ; n++){               //check don't repeat the same
            LMA2[conutLMA2+1][0] = LMAn[n][0];
            LMA2[conutLMA2+1][1] = LMAn[n][1];
            conutLMA2++;
        }
        
        LMA2[conutLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
        LMA2[conutLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
        conutLMA2++;
        
        //(make continuousPoint)
        
    }//++( make continuousPoint )* P/times++
    
    //( make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way
    for(LMA2i=5; LMA2i< (conutLMA2+8) ;LMA2i++){        //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
        bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;//(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
        bool isYChange=(LMA2[(LMA2i+6)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+2][1] ) < 0 ;//change y way.(y4-y2)*(y2-y0)
        bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;//(y8-y0)=1or=0;(y8-y0<=1)
        bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        
        bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
        bool isXChange=(LMA2[(LMA2i+6)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+2][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
        bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
        bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
        if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5) ){
            
            LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
            LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //(LMA2i+4)is turningPoint
            LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ;
            LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //
            
            k = (LMA2i+4); // small-k is LMA2i counter for Previous turningPoint
            K++; // big-K is counter-turningPoint，K=new turningPoint
        }
    }
    
    LMA2[(k+((conutLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
    LMA2[(k+((conutLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
    LMA2[(k+((conutLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
    LMA2[(k+((conutLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
    
    for (int i=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
            int x = LMAs[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
            int y = LMAs[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
            LMA2[i][2] = round(sqrt( x*x + y*y ));       // count circleScore (√z^2 = x^2+y^2)， round(3.5)=4
        }
    }
    
    
    for (int i=0,j=0; i<(conutLMA2+1) ;i++){     //counter-m.last one
        if( LMA2[i][3] > -1 ){
//            NSLog(@"a<%d>2[%d]3[%d](%d,%d)", i, LMA2[i][2], LMA2[i][3], LMA2[i][0], LMA2[i][1]);// <change to object-c>
//            NSLog(@"a(%d)2[%d]3[%d](%d,%d)", j, LMAs[j][2], LMAs[j][3], LMAs[j][0], LMAs[j][1]);
            j++;
        }
    }
    
    //--------(count StandardPoint)------------------
    while ( !(LMAs[countLMAs][0] == 0 && LMAs[countLMAs][1] == 0) ){
        countLMAs++;//count input new "a"array how many
    }
    //=============(Peter手寫轉折點辨識)=====================================
    //----------------(依序顯示圈數分數)
    int badScore = -1;   //本次成績
    int AverageScore=0, TotalScore=0;//平均分數;總分數
    for(int i=0, k = 0 , minusLMA2 = 0; i < ( LMA2[conutLMA2][3]+1 ); i++){
//      次數=>countLMA2(LMA2總數)為最後1筆的[3]是計分的最後一個編號
//原始      self.helloLabel = [[UILabel alloc] initWithFrame:CGRectMake(371.5+39*j,194.5+24*i , 60, 20)];
            // 創建一個 UILabel 並設置文字

        while ( LMA2[k][3] == -1 ){        //只挑(0,1,2~最後筆/例8)
            k++;
        }
        minusLMA2 = LMA2[k][2]* (-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
        TotalScore = TotalScore + LMA2[k][2];

        if ( minusLMA2 < badScore ){
            badScore = minusLMA2;
        }//-------------(get score)------------

        k++;
    }

    AverageScore = round(TotalScore / (LMA2[conutLMA2][3]+1) )*(-1);//四捨五入      //(get score/get Average score)
    
    return AverageScore;
}
    
    
    
    
    
    
    
    

    
    
    
    
    
    
    
- (void) isWorNumber:(int) wn
{
    selrang=7;
    WordPtr=wn;
}


- (int) isWord:(UIImage *) cmpimg
{
    int orgcnt,drawcnt;
    int matchcnt=0;
  //  if(drawImage.image.size.width!=50) return 0;
  //  if(drawImage.image.size.height!=150) return 0;
    UIGraphicsBeginImageContext(CGSizeMake(25, 75));
    [cmpimg drawInRect:CGRectMake(0, 0, 25, 75)];
    UIImage *resizeImage =UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    orgcnt=[self getOrgImage:resizeImage];
    

    UIGraphicsBeginImageContext(CGSizeMake(25, 75));
    [drawImage.image drawInRect:CGRectMake(0, 0, 25, 75)];
    UIImage *ri=UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    drawcnt=[self getDrawImage:ri];
    int fraction=0;
/*    switch(WordPtr) {
        case 0: fraction=[self cmpWordA];
        case 1: fraction=[self cmpWordB];
        case 2: fraction=[self cmpWordC];
        case 3: fraction=[self cmpWordD];
        case 4: fraction=[self cmpWordE];
        case 5: fraction=[self cmpWordF];
        case 6: fraction=[self cmpWordG];
        case 7: fraction=[self cmpWordH];
        case 8: fraction=[self cmpWordI];
        case 9: fraction=[self cmpWordJ];
        case 10: fraction=[self cmpWordK];
        case 11: fraction=[self cmpWordL];
        case 12: fraction=[self cmpWordM];
        case 13: fraction=[self cmpWordN];
        case 14: fraction=[self cmpWordO];
        case 15: fraction=[self cmpWordP];
        case 16: fraction=[self cmpWordQ];
        case 17: fraction=[self cmpWordR];
        case 18: fraction=[self cmpWordS];
        case 19: fraction=[self cmpWordT];
        case 20: fraction=[self cmpWordU];
        case 21: fraction=[self cmpWordV];
        case 22: fraction=[self cmpWordW];
        case 23: fraction=[self cmpWordX];
        case 24: fraction=[self cmpWordY];
        case 25: fraction=[self cmpWordZ];
        
    }
    //JAMES0417 彈出計算分數  大於-10 小於0
    NSLog(@"Fraction:%d",fraction);
    if((fraction <= 0 )&(fraction >= -10)) return 100;
    return fraction;
 */
    
    /*
    for(int yy=0;yy<150;yy++)
        for(int xx=0;xx<50;xx++) {
            if((drawimg[yy][xx]==1)&(orgimg[yy][xx]==1)) {
                matchcnt++;
            }
        }
    if((orgcnt/2) < drawcnt) {
        NSLog(@"Org:%d / Draw:%d / Math:%d(%d)",orgcnt,drawcnt,matchcnt,matchcnt*100/drawcnt);
        return (matchcnt*100/drawcnt);
    } else {
        return (0);
    }
     */
    
    
    
//=====================================================================================================================================
    
//    - (IBAction)btSimOK:(id)sender {
//        if (self.btSimOKhasExecuted) {
//                NSLog(@"按钮已经被按下过，操作不会再次执行。");
//                return; // 直接返回，不执行下面的代码
//            }
//        self.btSimOKhasExecuted = YES;    // 设置hasExecuted为YES，表示按钮已经被按下执行过
        int mm; // Signatrue的分數mm
        int weight = 10; //圈數分數打開到 10 為通過
        
    
    
    
    
    
    
    
    
    //=====(Peter手寫轉折點辨識 turningPoint)======================================================
    
    int x0,y0,x1,y1,cx,cy,ds,n;  //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  countLMA1=0, countLMA2=0, k=0,K=0,countLMAs=0 ;       //countLMA=0,(countLMA2,count LMA2[~][] )(m.counter is lastnumber)(countLMAs為計數標準點比對數量)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int countK=0;                //Peter 0603.計數.有幾筆劃.//count stroke Number
    int countkk[6]={};           //countkk為count1k數完,加上count2k.數完,再加上count3k.數完,再加上count4k.數完,再加上count5k.數完的值
    int countkkk[5]={};          //countkkk[0]=count1k,countkkk[1]=count2k,countkkk[2]=count3k,countkkk[3]=count4k,countkkk[4]=count5k
    int count1k=0, count2k=0;    //Peter 0603.計數.LMA2[~][3]1000的有幾個;2000的有幾個
    int count3k=0, count4k=0;    //Peter 0603.計數.LMA2[~][3]3000的有幾個;4000的有幾個
    int count5k=0;               //Peter 0617.計數.LMA2[~][3]5000的有幾個;
    int LMA2i=0;                 //(LMA2i.counter is turningPoint for LMA2)
    int LMA0[10000][4] = {};     //James    //*LAM解碼後放入.LMA0[][]
    int LMA0A[10000][4] = {};    //         //暫時運算存放用，用來複製LMA0[~][~]拆成4筆劃
    int LMA1[2000][4] = {};      //         //刪去重複的點後,放入LMA1[][]中
    int LMAn[2000][4] = {}; //new Array     //暫時運算存放用
    int LMA2[400][4] = {};  //correctPoints //做完連續放入LMA2中+完成轉折點.計分點.全部完成//LineMustArray -> LMA2
    int LMA3[400][4] ;  //LMA2->LMA3.數次的LMA2合併進入LMA3    //danny0924 //把這一行移動到76行,變成全域變數
    
    int countLMA3=0 ;                       //計數.LMA0做完每筆畫後到多少.接下筆劃續存
    int countLMA3before=0 ;                 //計數.countLMA3before為countLMA3的上一筆
    int *LAM = LAMx;       //James //跨頁面傳送
    int c = LAMc;           //James //跨頁面傳送.c=getLAMc,可以知道數量,導入多少筆資料數量的計數器
    
    
    
    //碰到{08,25,0,0}和{09,25,0,0}出問題，需要消除0變成{8,25,0,0}{9,25,0,0}
    int LMAs[20][4] = {{19,29,0,1001},{5,30,700,1002},{5,45,560,1003},{19,44,0,0},{19,29,230,1005},{19,34,0,0},{19,39,0,0},{19,44,0,0},{22,48,530,1009}};
    //依序將LMAs[i][j]=LMAs_a[i][j]代入用。 LineMustArray Standard for "a"
    //標準點.的第2筆畫 -> {-,-,<1>,-}<1>為第2筆劃標示   LMAs_x[10][4]={{5,25,<0>,0}, ~ ,{-,-,<1>,-}, ~ };
    //4點筆順
    
        int LMAs_a[9][4] = {{19,29,0,1001},{5,30,700,1002},{5,45,560,1003},{19,44,0,0},{19,29,230,1005},{19,34,0,0},{19,39,0,0},{19,44,0,0},{22,48,530,1009}};//Standard-"a"  9
        int LMAs_b[9][4] = {{5,5,0,1001},{5,16,0,0},{5,27,0,0},{5,38,0,0},{5,49,500,1005},{6,28,0,0},{19,31,240,1007},{18,46,530,1008},{5,43,760,1009}};//Standard-"b"  9
        int LMAs_c[5][4] = {{19,33,0,1001},{10,25,700,1002},{5,37,0,0},{10,49,560,1004},{19,41,340,1005}};//Standard-"c" 5
        int LMAs_d[9][4] = {{19,5,0,1001},{19,16,0,0},{19,27,0,0},{19,38,0,0},{19,49,500,1005},{19,32,0,0},{6,28,860,1007},{5,45,570,1008},{19,43,340,1009}};//Standard-"d"   9
        int LMAs_e[5][4] = {{5,37,0,1001},{19,33,300,1002},{6,28,710,1003},{6,46,560,1004},{19,41,340,1005}};//Standard-"e" 5
        int LMAs_f[10][4] = {{20,9,0,1001},{12,10,700,1002},{12,23,0,0},{12,36,0,0},{12,49,560,1005},{4,25,861,2001},{8,25,1,0},{12,25,1,0},{16,25,1,0},{20,25,321,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"f"  10
        int LMAs_g[9][4] = {{18,28,0,1001},{5,30,700,1002},{6,46,560,1003},{19,44,0,0},{19,28,0,0},{19,42,0,0},{19,55,340,1007},{15,68,0,0},{5,62,750,1009}};//Standard-"g" 9
        int LMAs_h[9][4] = {{5,5,0,1001},{5,16,0,0},{5,27,0,0},{5,38,0,0},{5,49,500,1005},{5,35,0,0},{12,25,240,1007},{19,35,0,0},{19,49,430,1009}};//Standard-"h"  9
        int LMAs_i[10][4] = {{12,25,0,1001},{12,31,0,0},{12,37,0,0},{12,43,0,0},{12,49,500,1005},{12,10,1,0},{12,11,1,0},{12,12,1,0},{12,13,1,0},{12,14,111,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"i" 10
        int LMAs_j[10][4] = {{12,25,0,1001},{12,38,0,0},{12,50,0,0},{12,63,500,1004},{5,66,750,1005},{12,10,1,0},{12,11,1,0},{12,12,1,0},{12,13,1,0},{12,14,111,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"j" 10
        int LMAs_k[14][4] = {{5,5,0,1001},{5,16,0,0},{5,27,0,0},{5,38,0,0},{5,49,500,1005},{19,25,241,2001},{15,28,1,0},{12,31,1,0},{8,34,1,0},{5,37,611,2005},{8,40,1,0},{12,43,1,0},{15,46,1,0},{19,49,451,2009}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"k"  14
        int LMAs_l[5][4] = {{12,05,0,1001},{12,16,0,0},{12,27,500,1003},{12,38,0,0},{12,49,550,1005}};//Standard-"l"  5
        int LMAs_m[13][4] = {{5,25,0,1001},{5,31,0,0},{5,37,0,0},{5,43,0,0},{5,49,500,1005},{5,36,0,0},{9,25,130,1007},{12,37,0,0},{12,49,530,1009},{12,36,0,0},{16,25,130,1011},{19,37,0,0},{19,49,530,1013}};//Standard-"m"13
        int LMAs_n[9][4] = {{5,25,0,1001},{5,31,0,0},{5,37,0,0},{5,43,0,0},{5,49,500,1005},{5,35,0,0},{12,25,230,1007},{19,36,0,0},{19,49,430,1009}};//Standard-"n"  9
        int LMAs_o[5][4] = {{12,25,0,1001},{5,37,700,1002},{12,49,550,1003},{19,37,330,1004},{12,25,110,1005}};//Standard-"o"  5
        int LMAs_p[10][4] = {{5,25,0,1001},{5,36,0,0},{5,47,0,0},{5,58,0,0},{5,69,500,1005},{5,31,101,2001},{16,26,1,0},{19,37,421,2003},{15,49,1,0},{5,44,651,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"p"  10
        int LMAs_q[10][4] = {{18,28,0,1001},{7,27,0,0},{5,38,600,1003},{9,49,0,0},{19,44,350,1005},{19,28,121,2001},{19,39,1,0},{19,49,1,0},{19,59,1,0},{19,69,551,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"q"  10
        int LMAs_r[9][4] = {{8,25,0,1001},{8,31,0,0},{8,37,0,0},{8,43,0,0},{8,49,500,1005},{8,42,0,0},{8,35,0,0},{10,28,0,0},{17,25,230,1009}};//Standard-"r"  9
        int LMAs_s[5][4] = {{19,31,0,1001},{7,26,700,1002},{12,37,550,1003},{16,48,540,1004},{5,43,700,1005}};//Standard-"s"  5
        int LMAs_t[14][4] = {{12,5,0,1001},{12,18,0,0},{12,31,0,0},{12,44,500,1004},{19,45,350,1005},
            {5,25,811,2001},{9,25,1,0},{12,25,1,0},{16,25,1,0},{19,25,311,2005}};   //{-,-,<1>,-}<1>為第2筆劃標示 Standard-"t" 14
        int LMAs_u[9][4] = {{5,25,0,1001},{5,39,0,0},{12,49,500,1003},{19,38,0,0},{19,25,130,1005},{19,31,0,0},{19,37,0,0},{19,43,0,0},{22,48,530,1009}};  //Standard-"u"    9
        int LMAs_v[9][4] = {{5,25,0,1001},{7,31,0,0},{8,37,0,0},{10,43,0,0},{12,49,400,1005},{14,43,0,0},{16,37,0,0},{17,31,0,0},{19,25,230,1009}};//Standard-"v"    9
        int LMAs_w[17][4] = {{5,25,0,1001},{6,31,0,0},{7,37,0,0},{8,43,0,0},{8,49,400,1005},{9,43,0,0},{10,37,0,0},{11,31,0,0},{12,25,230,1009},{13,31,0,0},{14,37,0,0},{15,43,0,0},{15,49,430,1013},{16,43,0,0},{17,37,0,0},{18,31,0,0},{19,25,230,1017}};//Standard-"w"17
        int LMAs_x[10][4] = {{5,25,0,1001},{9,31,0,0},{12,37,0,0},{15,43,0,0},{19,49,400,1005},{19,25,131,2001},{15,31,1,0},{12,37,1,0},{9,43,1,0},{5,49,671,2005}};//Standard-"x"  10
        int LMAs_y[10][4] = {{5,25,0,1001},{7,31,0,0},{8,37,0,0},{10,43,0,0},{11,49,400,1005},{19,25,231,2001},{15,36,1,0},{12,47,1,0},{8,58,1,0},{5,69,661,2005}};//Standard-"y"  10
        int LMAs_z[13][4] = {{5,25,0,1001},{9,25,0,0},{12,25,0,0},{16,25,0,0},{19,25,300,1005},{15,31,0,0},{12,37,0,0},{9,43,0,0},{5,49,650,1009},{9,49,0,0},{12,49,0,0},{16,49,0,0},{19,49,350,1013}};//Standard-"z" 13
    
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) LMA3[i][j]=0;//清零
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) LMAs[i][j]=0;//清零
    
    switch(WordPtr) {
        case 0: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_a[i][j];  break;
        case 1: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_b[i][j];  break;
        case 2: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_c[i][j];  break;
        case 3: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_d[i][j];  break;
        case 4: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_e[i][j];  break;
        case 5: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_f[i][j];  break;
        case 6: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_g[i][j];  break;
        case 7: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_h[i][j];  break;
        case 8: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_i[i][j];  break;
        case 9: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_j[i][j];  break;
        case 10: countLMAs=14; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_k[i][j];  break;
        case 11: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_l[i][j];  break;
        case 12: countLMAs=13; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_m[i][j];  break;
        case 13: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_n[i][j];  break;
        case 14: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_o[i][j];  break;
        case 15: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_p[i][j];  break;
        case 16: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_q[i][j];  break;
        case 17: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_r[i][j];  break;
        case 18: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_s[i][j];  break;
        case 19: countLMAs=14; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_t[i][j];  break;
        case 20: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_u[i][j];  break;
        case 21: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_v[i][j];  break;
        case 22: countLMAs=17; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_w[i][j];  break;
        case 23: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_x[i][j];  break;
        case 24: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_y[i][j];  break;
        case 25: countLMAs=13; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_z[i][j];  break;
    }
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=LMAs[i][j];
    
    //--------(設定參數,將標準點的筆劃數,拆分成數筆畫)------------------
    int LMAs1[20][4]={};//將標準點StandardPoint拆成多筆劃
    int LMAs2[20][4]={};//準備放入第2筆
    int LMAs3[20][4]={};//準備放入第3筆
    int LMAs4[20][4]={};//準備放入第4筆
    for(int i=0,j0=0,j1=0,j2=0,j3=0; i<countLMAs; i++){   //LMAs[i][2]
        //        NSLog(@"標準點=LMAs[%d][]=(%d,%d,<%d>,%d)", i, LMAs[i][0], LMAs[i][1], LMAs[i][2], LMAs[i][3]);   //原始
        switch ( LMAs[i][2] % 10 ){
            case 0:LMAs1[j0][0]=LMAs[i][0]; LMAs1[j0][1]=LMAs[i][1]; j0++;
                NSLog(@"Sig:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs1[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j0-1,LMAs1[j0-1][0],LMAs1[j0-1][1],LMAs1[j0-1][2],LMAs1[j0-1][3]);//小寫最多2筆
                break;
            case 1:LMAs2[j1][0]=LMAs[i][0]; LMAs2[j1][1]=LMAs[i][1]; j1++;
                NSLog(@"Sig:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs2[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j1-1,LMAs2[j1-1][0],LMAs2[j1-1][1],LMAs2[j1-1][2],LMAs2[j1-1][3]);//小寫最多2筆
                break;
            case 2:LMAs3[j2][0]=LMAs[i][0]; LMAs3[j2][1]=LMAs[i][1]; j2++;
                NSLog(@"Sig:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs3[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j2-1,LMAs3[j2-1][0],LMAs3[j2-1][1],LMAs3[j2-1][2],LMAs3[j2-1][3]);//大寫最多3筆，只有Ｅ4筆
                break;
            case 3:LMAs4[j3][0]=LMAs[i][0]; LMAs4[j3][1]=LMAs[i][1]; j3++;
                NSLog(@"Sig:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs4[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j3-1,LMAs4[j3-1][0],LMAs4[j3-1][1],LMAs4[j3-1][2],LMAs4[j3-1][3]);//大寫最多3筆，只有Ｅ4筆
                break;
        }
    }
    //--------(設定參數,將標準點的筆劃數,拆分成數筆畫)------------------
    
    //--------(跨頁面導入/解碼.跨頁面傳輸資料 *LAM=[dWb0 getLAMx])------------------
    for(int i=0; i<10000; i++){
        for(int j=0; j<4 ; j++){
            LMA0[i][j] =0;
        }
    }//LMA0[i][j]，一開始進來，先清零


    //--------(筆畫數超過9筆畫，btSimOK直接跳出)------------------
    //顯示視窗變成在外部函式處理(不在isWord顯示)依據isword所return出來的值判斷要映出什麼警告視窗
 //    for(int i=0; (i<10000)&&(LAM[i*4+0]+LAM[i*4+1]>0) ; i++){
//        NSLog(@"Sig:<i=%d>< LAM[%d],[%d],[%d],[%d] >(%d,%d,%d,%d)",i,(i*4+0),(i*4+1),(i*4+2),(i*4+3),LAM[i*4+0],LAM[i*4+1],LAM[i*4+2],LAM[i*4+3]);
//        if(LAM[i*4+3] >= 9000 ){
//            NSLog(@"Sig:筆畫數超過9筆畫，btSimOK直接跳出。");
//            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"不要亂畫，老師會生氣！" message:
//                                      [NSString stringWithFormat:@"筆畫數超過9筆畫！"] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
//            [alertView show];
//
//            for(int i=0; i< 40000*4 ; i++){
//                LAM[i] =0;
//            }              //清除 LAM[~]值
//            return;        //直接結束 btSimOK
//        }
        //--------(筆畫數超過9筆畫，btSimOK直接跳出)------------------
        
//    }//印出LMA[i][j]
NSLog(@"Sig:<<<*LAMc>>>(%d)",c);
    //--------(5~8筆畫,合併成第5筆畫)------------------
    for(int i=0,j=0;i<c;i++) {            //James
        LMA0[i][0]=LAM[i*4+0];            //James//導入外部LAM[i*4+0]資料.併解碼,為x值
        LMA0[i][1]=LAM[i*4+1];            //James//導入外部LAM[i*4+1]資料.併解碼,為y值
        LMA0[i][2]=LAM[i*4+2];            //James
        
        if( (LAM[i*4+3])/1000>=1 ){       //Peter 0603.導入筆劃1-1000,2-2000,3-3000,4-4000
            if(LAM[i*4+3]<4001){          //控制(1000~4000以內)
                LMA0[i][3]=LAM[i*4+3];    //將LAM第1筆1000,2000,3000,4000放入第1筆LMA0中
                j = LAM[i*4+3];           //將LAM第1筆1000,2000,3000,4000放入加權數j=1000,2000,3000
            }else{
                LMA0[i][3]=5000;          //否則(超過4000以上)都歸入(LMA0[i][3]=5000)第5.6.7...筆劃都只在(LMA0[i][3]=5000)
                j = 5000;                 //否則(超過4000以上)都歸入(j=5000)         第5.6.7...筆劃都只在(j=5000)
            }
        }else{
            LMA0[i][3]=LAM[i*4+3]+j ;
        }                                 //Peter 0603.導入筆劃1-1000,2-2000,3-3000,4-4000
        countK = j/1000;                  //Peter 0603.總筆畫數(countK)
        NSLog(@"Sig:=<i=%d>LMA0(%d,%d,%d,%d)<countK=%d>",i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
        //        NSLog(@"==== LMA0=(%d,%d,%d,%d)<countK=%d>",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
    }
    //--------(5~8筆畫,合併成第5筆畫)------------------
    
    for(int i=0; i<c; i++){
        NSLog(@"Sig:1K.LMA0<i=%d>LMA0(%d,%d,%d,%d)<countK=%d>",i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
        switch( (LMA0[i][3])/1000 ){
            case 1: count1k += 1; break;  //Peter 0603.計算器count1k第1筆劃有幾點,做幾次.
            case 2: count2k += 1; break;  //Peter 0603.計算器count2k第2筆劃有幾點,做幾次.
            case 3: count3k += 1; break;  //Peter 0603.計算器count3k第3筆劃有幾點,做幾次.
            case 4: count4k += 1; break;  //Peter 0617.計算器count4k第4筆劃有幾點,做幾次.
            default:count5k += 1; break;  //Peter 0617.計算器count5k第5-n筆劃有幾點(全部放到最後筆)
        }
    }
    countkk[0]=0;
    countkk[1]=countkk[0]+count1k;
    countkk[2]=countkk[1]+count2k;
    countkk[3]=countkk[2]+count3k;
    countkk[4]=countkk[3]+count4k;
    countkk[5]=countkk[4]+count5k;
    countkkk[0]=count1k;
    countkkk[1]=count2k;
    countkkk[2]=count3k;
    countkkk[3]=count4k;
    countkkk[4]=count5k;
    NSLog(@"Sig:countkk[0]--(kk[0]=%d,kk[1]=%d,kk[2]=%d,kk[3]=%d,kk[4]=%d,kk[5]=%d,(countK=%d)",countkk[0],countkk[1],countkk[2],countkk[3],countkk[4],countkk[5],countK);
    NSLog(@"Sig:countkkk[0]--[kkk0]=%d,kkk[1]=%d,kkk[2]=%d,kkk[3]=%d,kkk[4]=%d",countkkk[0],countkkk[1],countkkk[2],countkkk[3],countkkk[4]);
    //--------(跨頁面導入/解碼.跨頁面傳輸資料 *LAM=[dWb0 getLAMx])------------------
    //--------( 準備切5段/LMA0A[~][~]=LMA0[~][~];LMA0[~][~]=LMA0A[~][~];)----------
    for (int i=0;i<(count1k+count2k+count3k+count4k+count5k); i++){  //i<(總筆畫數)
        for(int j=0;j<4; j++){
            LMA0A[i][j] = LMA0[i][j];  //LMA0 放到 LMA0A
            LMA0[i][j] = 0;            //放完，LMA0立刻清零，第一次的清零
        }
        NSLog(@"Sig:LMA0A<%d>(%d,%d,%d,%d)<%d>",i,LMA0A[i][0],LMA0A[i][1],LMA0A[i][2],LMA0A[i][3],countK);
        NSLog(@"Sig:LMA0<%d>(%d,%d,%d,%d)<%d>",i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
    }
    //--------( 準備切5段/LMA0A[~][~]=LMA0[~][~];LMA0[~][~]=LMA0A[~][~];)----------
    
    //========<做1~5筆劃迴圈>(4+1筆劃/1~4+1數次判斷4+1.times/ic)======================================================
    
    //--------(LMA0[i][j]清零.LMA2[i][j]清零/將上次筆劃殘留的，再次清零)------------------
    for(int ic=0,countLMA1=0,countLMA2=0; ic<countK; ic++){
        countLMA1=0;     //每執行１筆畫,要清零
        countLMA2=0;     //每執行１筆畫,要清零
        
        for(int i=0; (ic>0)&&(i<countkkk[ic-1]); i++){
            for(int j=0; j<4 ; j++){
                LMA0[i][j] =0;
            }
        }//LMA0[i][j]，上次筆畫殘留的，再次清零
        
        for(int i=0; i<400; i++){
            for(int j=0; j<4 ; j++){
                LMA2[i][j] =0;
            }
        }//LMA2[~][~]，上次筆畫殘留的，再次清零 //LMA2[400][4]
        //--------(LMA0[i][j]清零.LMA2[i][j]清零/將上次筆劃殘留的，再次清零)------------------
        
        //--------(LMA0倒入LMA1中/LMA1[~][~]=LMA0[~][~])------------------
        for(int i=0; i< countkkk[ic]; i++ ){
            for(int j=0; j<4 ; j++){
                LMA0[i][j] = LMA0A[(i+countkk[ic])][j];  //只放第1段(第1筆劃的點數量).
            }
            NSLog(@"Sig:=LMA0A->(LMA0)，第%d+1筆.(countkkk[%d]=%d)，LMA0[i=%d](%d,%d,%d,%d)",ic,ic,countkkk[ic],i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
        }//將LMA0A[i][j]放入LMA0[i][j]中，依序每筆畫執行一次，放在同一位置
        //--------(LMA0倒入LMA1中/LMA1[~][~]=LMA0[~][~])------------------
        //--------(刪重複的點/Delete the same points)----------------------
        LMA1[0][0]=LMA0[0][0];        //put first point
        LMA1[0][1]=LMA0[0][1];        //put first point
        LMA1[0][2]=LMA0[0][2];        //put first point,Peter 0603
        LMA1[0][3]=LMA0[0][3];        //put first point,Peter 0603 數字已改成1000,要導入
        
        for(int i=0 ; i< countkkk[ic]; i++ ){
            if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
                LMA1[(countLMA1)][0] = LMA0[i][0];
                LMA1[(countLMA1)][1] = LMA0[i][1];
                LMA1[(countLMA1)][2] = LMA0[i][2];     //Peter 0603.這行數字沒有變化,可以不用導入
                LMA1[(countLMA1)][3] = LMA0[i][3];     //Peter 0603.這行數字已經變成1000-2000-3000...要導入。
                NSLog(@"Sig:=LMA0-><LMA1>，第%d+1筆.(countkkk[%d]=%d)，LMA1[countLMA1=%d](%d,%d,%d,%d)",ic,ic,countkkk[ic],countLMA1,LMA1[(countLMA1)][0],LMA1[(countLMA1)][1],LMA1[(countLMA1)][2],LMA1[(countLMA1)][3]);
                NSLog(@"Sig:刪重複點(countkkk[ic=%d]=%d)，LMA1[(countLMA1=%d)][](%d,%d,%d,%d)",ic,countkkk[ic],countLMA1,LMA1[(countLMA1)][0],LMA1[(countLMA1)][1],LMA1[(countLMA1)][2],LMA1[(countLMA1)][3]);
                countLMA1++;
            }
        }
        NSLog(@"Sig:<<<*LAMc>>><ic=%d>(countLMA1-1)=%d",ic,(countLMA1-1));
        //--------(刪重複的點/Delete the same points)----------------------
        //--------(put (-1) to LMA2[~][3])--------
        for (int i=0; i< 400 ; i++){
            LMA2[i][3] = -1;
        }
        //--------(put (-1) to LMA2[~][3])--------
        LMA2[0][0] = LMA1[0][0];    //put first point
        LMA2[0][1] = LMA1[0][1];
        LMA2[0][3] = 0;
        LMAn[0][0] = LMA1[0][0];
        LMAn[0][1] = LMA1[0][1];
        //--------(做連續點/做<數次>連續點 make continuousPoint/P/times)------------
        for (int p=0; p<(countLMA1-1) ;p++){
            x0 = LMA1[p][0];
            y0 = LMA1[p][1];
            x1 = LMA1[p+1][0];
            y1 = LMA1[p+1][1];
            ds = 0;
            //--------(做連續點/單次)------------
            if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){      //(put biger side into ds，x-side or y-side)
                ds = (abs(x0-x1)-1);
            }else{
                ds = (abs(y0-y1)-1);
            }                                           //(put biger side into ds，x-side or y-side)
            cx=0;
            cy=0;                                      //firstPoint cx+1,cy+1 if x==y don't do anything
            if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
                cy=1 ;
            }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
                cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
            }
            int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
            pmx = pmx*(abs(x1-x0)/(x1-x0));
            pmy = pmy*(abs(y1-y0)/(y1-y0));
            
            for ( n=0; n < ds ; n++){
                LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
                LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
            }//--------(做連續點)
            for(int n=0; n< ds ; n++){               //check don't repeat the same
                LMA2[countLMA2+1][0] = LMAn[n][0];
                LMA2[countLMA2+1][1] = LMAn[n][1];
                countLMA2++;
            }
            LMA2[countLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
            LMA2[countLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
            countLMA2++;
            //--------(做連續點/單次)------------
        }
        //--------(做連續點/做<數次>連續點 make continuousPoint/P/times)------------
        
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 ; i++){
            NSLog(@"Sig:做連續點完.第%d+1筆(countLMA2=%d) LMA2(i=%d)(%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        //--------(筆畫點數超過300點，btSimOK直接跳出)------------------
//        NSLog(@"Sig:不要亂畫，筆畫點數超過300點 countLMA2= %d點",countLMA2);
//        if ( countLMA2 > 300 ){
//            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"不要亂畫，老師會生氣！" message:
//                                      [NSString stringWithFormat:@"筆畫點數超過300點，共%d個！",countLMA2 ] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
//            [alertView show];
//            return;
//        }


        //--------(筆畫點數超過300點，btSimOK直接跳出)------------------
        //--------(做轉折點/make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way)------------
        //--------(ekztmw鈍銳角處理)------------------
        int turnYway_X=4, turnYway_Y=1; //Y軸轉折尖鈍,含abc~大部分(原設定Y_X=4,Y_Y=1)   //上限<=1，上限<=( 4 ~ 8 )
        int turnXway_X=1, turnXway_Y=4; //X軸轉折尖鈍,只有(e.k.m)(原設定X_X=1,X_Y=4)  e k z t m w
        NSLog(@"Sig:turnYway(wordinx=%d)",WordPtr);
        switch(WordPtr) {
            case 4:  turnYway_X=4;turnYway_Y=1;turnXway_X=1;turnXway_Y=1;  break;//e,4降1變尖.避免有轉折<ekz改turnXway_Y>  //4114
            case 10: turnYway_X=4;turnYway_Y=1;turnXway_X=5;turnXway_Y=8;  break;//k,4升8變鈍。<ekz改turnXway_Y>
            case 12: turnYway_X=2;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;  break;//m,4降2變尖.避免有轉折
            case 13: turnYway_X=2;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;  break;//  n,4降2變尖.避免有轉折
            case 19: turnYway_X=1;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;  break;//t,4降1變尖.避免有轉折
            case 21: turnYway_X=8;turnYway_Y=3;turnXway_X=1;turnXway_Y=4;  break;//  v,4升8變鈍，盡量有轉折
            case 22: turnYway_X=8;turnYway_Y=3;turnXway_X=1;turnXway_Y=4;  break;//w,4升8變鈍，盡量有轉折
            case 25: turnYway_X=4;turnYway_Y=1;turnXway_X=5;turnXway_Y=8;  break;//z,4升8變鈍，90度1升5。<ekz改turnXway_Y>
            default: turnYway_X=4;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;
        }
        //--------(ekztmw鈍銳角處理)------------------
        K=0;//先歸零 //peter 0621
        k=0;//先歸零 //peter 1021
        for(LMA2i=5; LMA2i< (countLMA2+8) ;LMA2i++){        //LMA2i是避免手抖，直接從第5點(LMA2i=5)開始計算。
            //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
            bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;
            //y軸2數相隔壁，取第1個，第2個不用。(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
            bool isYChange=(LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+0][1] ) < 0 ;
            //y軸轉方向，(y8-y4)*(y4-y0)(差2.90度)(差1變弧120度,2-3個轉折)(差3變60度,不夠尖無轉折)
            bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= turnYway_Y ;
            //要形成適當的角度，(y8-y0)必須要(0或1)，(y8-y0)=1or=0;(y8-y0<=1)
            //(y8-y4/轉折點)(y4/轉折點-y0)需間隔(3-4)，間隔太近會形成弧非尖角
            bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= turnYway_X;
            //要形成適當的角度，(x8-x0)必須要(0,1,2,3,4)，//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
            //以下相對於x軸。
//            bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;
//            //要形成適當的角度，(y8-y0)必須要(0或1)，(y8-y0)=1or=0;(y8-y0<=1)
//            bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;
//            //要形成適當的角度，(x8-x0)必須要(0,1,2,3,4)，//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
//            //以下相對於x軸。
            
            bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
            bool isXChange=(LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+0][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
            bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= turnXway_X ;//(x8-x0)=1or=0;(x8-x0<=1)
            bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= turnXway_Y;//(y8-y0)=4or3,2,1,0;(y8-y0<=4)
//            bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
//            bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
            
            bool islastPointBig10 = k+10 < (LMA2i+4) && (LMA2i+4) < countLMA2-10;
            //上個轉折點需超過10個計數距離以上，避免手抖產生轉折點 // 最後10點手抖，不做轉折點
            
            NSLog(@"Sig:轉折點前.第%d+1筆 LMA2(LMA2i=%d)(%d,%d,%d,%d)(k=%d +6 =< (LMA2i+4)=%d)",ic,LMA2i,LMA2[LMA2i][0],LMA2[LMA2i][1],LMA2[LMA2i][2],LMA2[LMA2i][3],k,(LMA2i+4));
            if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5 && islastPointBig10)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5 && islastPointBig10) ){
                
    /*
                NSLog(@"Sig:turnYwayY方向(isYdistanceNoBig2=%d)(y8-y0=%d-%d=%d)(<=%d)",isYdistanceNoBig2,LMA2[(LMA2i+8)][1],LMA2[(LMA2i+0)][1],LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1],turnYway_X);
                NSLog(@"Sig:turnYwayY方向(isXdistanceNoBig5=%d)(x8-x0=%d-%d=%d)(<=%d)",isXdistanceNoBig5,LMA2[(LMA2i+8)][0],LMA2[(LMA2i+0)][0],LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0],turnYway_Y);
                NSLog(@"Sig:turnXwayX方向(isXdistanceNoBig2=%d)(x8-x0=%d-%d=%d)(<=%d)",isXdistanceNoBig2,LMA2[(LMA2i+8)][0],LMA2[(LMA2i+0)][0],LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0],turnXway_X);
                NSLog(@"Sig:turnXwayX方向(isYdistanceNoBig5=%d)(y8-y0=%d-%d=%d)(<=%d)",isYdistanceNoBig5,LMA2[(LMA2i+8)][1],LMA2[(LMA2i+0)][1],LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1],turnXway_Y);
     
                NSLog(@"Sig:turn轉折點.轉(i+4)=%d(%d,%d,%d,%d)",LMA2i+4,LMA2[(LMA2i+4)][0],LMA2[(LMA2i+4)][1],LMA2[(LMA2i+4)][2],LMA2[(LMA2i+4)][3] );
                NSLog(@"Sig:turn轉折點.x=1(i+0)=%d(%d,%d,%d,%d)",LMA2i+0,LMA2[(LMA2i+0)][0],LMA2[(LMA2i+0)][1],LMA2[(LMA2i+0)][2],LMA2[(LMA2i+0)][3] );
                NSLog(@"Sig:turn轉折點.y=4(i+8)=%d(%d,%d,%d,%d)",LMA2i+8,LMA2[(LMA2i+8)][0],LMA2[(LMA2i+8)][1],LMA2[(LMA2i+8)][2],LMA2[(LMA2i+8)][3] );
*/
                LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
                LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //假如上述條件成立(LMA2i+4)則是轉折點，is turningPoint
                LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ; //[( 上一個轉折點 + (新轉折點-上一個轉折點)*1/4 )] // <做1/4點>
                LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //這是轉折點
                
                NSLog(@"Sig:轉折點後1/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*1/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*1/4),LMA2[k+(((LMA2i+4)-k)*1/4)][0],LMA2[k+(((LMA2i+4)-k)*1/4)][1],LMA2[k+(((LMA2i+4)-k)*1/4)][2],LMA2[k+(((LMA2i+4)-k)*1/4)][3]);
                NSLog(@"Sig:轉折點後2/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*2/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*2/4),LMA2[k+(((LMA2i+4)-k)*2/4)][0],LMA2[k+(((LMA2i+4)-k)*2/4)][1],LMA2[k+(((LMA2i+4)-k)*2/4)][2],LMA2[k+(((LMA2i+4)-k)*2/4)][3]);
                NSLog(@"Sig:轉折點後3/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*3/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*3/4),LMA2[k+(((LMA2i+4)-k)*3/4)][0],LMA2[k+(((LMA2i+4)-k)*3/4)][1],LMA2[k+(((LMA2i+4)-k)*3/4)][2],LMA2[k+(((LMA2i+4)-k)*3/4)][3]);
                NSLog(@"Sig:轉折點後4/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*4/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*4/4),LMA2[k+(((LMA2i+4)-k)*4/4)][0],LMA2[k+(((LMA2i+4)-k)*4/4)][1],LMA2[k+(((LMA2i+4)-k)*4/4)][2],LMA2[k+(((LMA2i+4)-k)*4/4)][3]);
                
                k = (LMA2i+4); //小k是轉折點，是記錄上一次(LMA2i+4)的值 //small-k is LMA2i counter for Previous turningPoint
                K++; // big-K is counter-turningPoint，K=new turningPoint,1005,1009,1013
            }//if( (isYnoZero && isYChange &&...~
        }//for(LMA2i=5; LMA2i< (countLMA2+8)...~
        
        NSLog(@"Sig:結束點.k=%d,countLMA2=%d,(countLMA2-k)=%d,((countLMA2-k)*1/4)=%d,[k+((countLMA2-k)*1/4)]=%d",k,countLMA2,(countLMA2-k),(countLMA2-k)/4,k+((countLMA2-k)/4) );
        
        LMA2[(k+((countLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
        LMA2[(k+((countLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
        LMA2[(k+((countLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
        LMA2[(k+((countLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
        
        NSLog(@"Sig:結束點.1/4.第%d+1筆 LMA2[[k+((countLMA2-k)*1/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*1/4),LMA2[k+((countLMA2-k)*1/4)][0],LMA2[k+((countLMA2-k)*1/4)][1],LMA2[k+((countLMA2-k)*1/4)][2],LMA2[k+((countLMA2-k)*1/4)][3]);
        NSLog(@"Sig:結束點.2/4.第%d+1筆 LMA2[[k+((countLMA2-k)*2/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*2/4),LMA2[k+((countLMA2-k)*2/4)][0],LMA2[k+((countLMA2-k)*2/4)][1],LMA2[k+((countLMA2-k)*2/4)][2],LMA2[k+((countLMA2-k)*2/4)][3]);
        NSLog(@"Sig:結束點.3/4.第%d+1筆 LMA2[[k+((countLMA2-k)*3/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*3/4),LMA2[k+((countLMA2-k)*3/4)][0],LMA2[k+((countLMA2-k)*3/4)][1],LMA2[k+((countLMA2-k)*3/4)][2],LMA2[k+((countLMA2-k)*3/4)][3]);
        NSLog(@"Sig:結束點.4/4.第%d+1筆 LMA2[[k+((countLMA2-k)*4/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*4/4),LMA2[k+((countLMA2-k)*4/4)][0],LMA2[k+((countLMA2-k)*4/4)][1],LMA2[k+((countLMA2-k)*4/4)][2],LMA2[k+((countLMA2-k)*4/4)][3]);
        //--------(做轉折點/make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way)------------
        
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 ; i++){
            NSLog(@"Sig:做轉折點完.第%d+1筆(countLMA2=%d) LMA2(i=%d)(%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        
        //--------(將標準點拆分的筆劃數點,再依<筆劃ic迴圈>倒回去LMAs[][]中)------------
        
        int LMAsC[20][4]={};
        
        for(int i=0; i<countLMAs; i++){   //LMAs[i][2]
            switch (ic){
                case 0:LMAsC[i][0]=LMAs1[i][0]; LMAsC[i][1]=LMAs1[i][1];  break;
                case 1:LMAsC[i][0]=LMAs2[i][0]; LMAsC[i][1]=LMAs2[i][1];  break;
                case 2:LMAsC[i][0]=LMAs3[i][0]; LMAsC[i][1]=LMAs3[i][1];  break;
                case 3:LMAsC[i][0]=LMAs4[i][0]; LMAsC[i][1]=LMAs4[i][1];  break;
                default:LMAsC[i][0]=LMAs4[i][0]; LMAsC[i][1]=LMAs4[i][1];  break;
            }
            NSLog(@"Sig:分段.標準點 LMAsC[i][0].第%d+1筆(i=%d)(%d,%d,%d,%d)",ic ,i,LMAsC[i][0],LMAsC[i][1],LMAsC[i][2],LMAsC[i][3]);
        }
        //--------(將標準點拆分的筆劃數點,再依<筆劃ic迴圈>倒回去LMAs[][]中)------------
        
        //--------(不足5個計分點,補足5點，<ij專則>)------------
        //        NSLog(@"<countLMA2=%d>",countLMA2);
        
        //------印出LMA2[][]內容
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 ; i++){
            NSLog(@"Sig:全印.未補不足5點.第%d+1筆<countLMA2=%d>LMA2[i=%d](%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        
        for(int i=0, countLMA22=countLMA2 ; i<(4-countLMA22)&&(countLMA2<5); i++){
            for(int j=0; j<4; j++){
                if(j<3){
                    LMA2[countLMA22+i+1][j] = LMA2[countLMA22+i][j];
                }else{
                    LMA2[countLMA22+i+1][j] = LMA2[countLMA22+i][j]+1 ;
                }
            }    //將不足5點的最後1點，複製到下1點，(1點複製4次，2點複製3次，3點複製2次，4點複製1次)
            countLMA2++;
            NSLog(@"Sig:<補加第%d點><countLMA2=%d>",(i+1),countLMA2);
            NSLog(@"Sig:now<補點><i=%d>LMA2[countLMA2=%d](%d,%d,%d,%d)",i,countLMA2,LMA2[countLMA2][0],LMA2[countLMA2][1],LMA2[countLMA2][2],LMA2[countLMA2][3]);
        }
        //--------(不足5個計分點,補足5點，<ij專則>)------------
        
        //------再印一次，印出LMA2[][]內容
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 && countLMA2<5 ; i++){
            LMA2[i][3] = i;
            NSLog(@"Sig:全印.已補不足5點.第%d+1筆<countLMA2=%d>LMA2[i=%d](%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        
        //   LMA2[countLMA2][3]<17----1     @"分數 %d",mm
        int LMAsCount=0 ;
        switch (ic){
            case 0: LMAsCount = 17+5 ; break; //第1筆劃上限17防亂畫，加5預防手誤。
            case 1: LMAsCount = 13+5 ; break; //第2筆劃上限13防亂畫，加5預防手誤。
            case 2: LMAsCount = 5+5  ; break; //第3筆劃上限 5防亂畫，加5預防手誤。
            case 3: LMAsCount = 5+5  ; break; //第4筆劃上限 5防亂畫，加5預防手誤。
        }
        //--------(計分點數超過17點，btSimOK直接跳出)------------------
//        NSLog(@"Sig:不要亂畫 LMAsCount=%d LMA2[%d][3]= %d ",LMAsCount,countLMA2,LMA2[countLMA2][3]);
//        if ( (LMA2[countLMA2][3] > LMAsCount)&& ic<4 ){
//            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"不要亂畫，老師會生氣！" message:
//                                      [NSString stringWithFormat:@"第%d+1筆，計分點超過%d個！",(ic+1),(LMAsCount-5)] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
//            [alertView show];
//            return;
//        }
        //--------(計分點數超過17點，btSimOK直接跳出)------------------
        
        //        --------(算各點分數)------------
        for (int i=0, j=0; i<(countLMA2+1); i++){               //counter-m.last one
            if( LMA2[i][3] > -1 ){
                if( LMAsC[j][0]+LMAsC[j][1]>0  ){
                    int x = LMAsC[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
                    int y = LMAsC[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
                    LMA2[i][2] = round(sqrt( x*x + y*y ));     //count circleScore (√z^2 = √(x^2+y^2))， round(3.5)=4
                    NSLog(@"Sig:(計分)LMA2[i][2].第%d+1筆 i=(%d)(j=%d)(LMA2[%d][3]=%d)(%d,%d,<%d>,%d)",ic,i,j,i,LMA2[i][3],LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
                    j++;
                }else{
                    LMA2[i][2] = 99;
                    NSLog(@"Sig:(扣99)LMA2[i][2].第%d+1筆 i=(%d)(j=%d)(LMA2[%d][3]=%d)(%d,%d,<%d>,%d)",ic,i,j,i,LMA2[i][3],LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
                    j++;
                }
            }
            NSLog(@"Sig:全LMA2[i][2].<第%d+1筆><i=%d><j=%d><LMA2[%d][3]=%d>(%d,%d,<%d>,%d))",ic,i,j,i,LMA2[i][3],LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        //        --------(算各點分數)------------
        
        for (int i=0; i<(countLMA2+1) ;i++){     //counter-m.last one
            NSLog(@"Sig:=*第%d+1筆，LMA2<%d>(%d,%d,%d,%d)",ic,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }//印出來看看
        
        countLMA3 = countLMA3 + countLMA2; //依序第1次/筆的countLMA2加入countLMA3，第2筆依序加入...
        
        
        
        //--------(將已完成的LMA2放入LMA3中/將每段(依每筆劃),依序將LMA3[~][~]=LMA2[~][~]+(ic+1)*1000+1)放入)------------
        for (int i=0;i< (countLMA2+1); i++){
            for(int j=0; j<4; j++){
                if(j==3){
                    LMA3[(i+countLMA3before)][j] = (LMA2[i][j]+(ic+1)*1000+1);//第1筆畫1000.2000.3000.4000
                    NSLog(@"Sig:第%d+1筆，[3]加權*1000+1 LMA3[(i=%d+countLMA3before=%d)][j=%d] = (LMA2[i=%d][j=%d]+(ic=%d+1)*1000+1)",ic,i,countLMA3before,j,i,j,ic);
                }else{
                    LMA3[(i+countLMA3before)][j] = LMA2[i][j];
                }
            }
            NSLog(@"Sig:第%d+1筆=LMA2[%d](%d,%d,%d,%d)改後,放入 LMA3[%d](%d,%d,%d,%d)",ic,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3],i, LMA3[(i+countLMA3before)][0], LMA3[(i+countLMA3before)][1], LMA3[(i+countLMA3before)][2], LMA3[(i+countLMA3before)][3]);
        }
        NSLog(@"Sig:=第%d+1筆<LMA3> (countLMA2=%d)+1 (countLMA3before=%d),(countLMA3=%d)",ic,countLMA2,countLMA3before,countLMA3);
        countLMA3before = countLMA3before+countLMA2+1; //countLMA3A原為0,這樣使他為countLMA3的之前一筆資料做使用
        //--------(將已完成的LMA2放入LMA3中/將每段(依每筆劃),依序將LMA3[~][~]=LMA2[~][~]+(ic+1)*1000+1)放入)------------
    }
    //========<做1~5筆劃迴圈>(4+1筆劃/1~4+1數次判斷4+1.times/ic)======================================================
    
    //印出檢查LMA3全部內容//可刪除
    for(int i=0 ;(i<1000)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0);i++){
        NSLog(@"Sig:=LMA3.全<%d>(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
    }
    //NSLog(@"標準點=LMAs[%d][](%d,%d,%d,%d)LMAs1[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j0-1,LMAs1[j0-1][0],LMAs1[j0-1][1],LMAs1[j0-1][2],LMAs1[j0-1][3]);//小寫最多2筆
    //--------(手寫點缺少點，補點,補-98分)------------
    int countLMA3Point = 0;   //計數LMA3有計分點的數量
    int countLMA3LastOne = 0; //計數LMA3最後的位置
    for(int i=0 ;(i<1000)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0);i++){
        countLMA3LastOne++;
        if( LMA3[i][3]%1000!=0 ){
            countLMA3Point++;
            NSLog(@"Sig:=LMA3.全(countLMA3Point=%d)(countLMA3LastOne=%d)<%d>(%d,%d,%d,%d)",countLMA3Point,countLMA3LastOne,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        }
    }
    //印出檢查LMAs全部內容//可刪除
    for(int i=0 ; (LMAs[i][0]+LMAs[i][1])>0 ; i++){
        NSLog(@"Sig:=LMAs.標準點.全<%d>(%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3]);
        countLMAs = i;
    }
    for(int i=0; i<( countLMAs - countLMA3Point ); i++){      //假如標準點多於手寫點
        NSLog(@"Sig:=標準點>手寫點.(countLMAs=%d)-(countLMA3Point=%d) > (i=%d)",countLMAs,countLMA3Point,i );
        LMA3[countLMA3LastOne][0] = LMAs[countLMA3Point+i][0]; //將標準點LMAs[][0]的x值.放入手寫LMA3[][0]中
        LMA3[countLMA3LastOne][1] = LMAs[countLMA3Point+i][1]; //將標準點LMAs[][1]的y值.放入手寫LMA3[][1]中
        LMA3[countLMA3LastOne][2] = 98;                        //將 98 放入手寫LMA3[2]中
        LMA3[countLMA3LastOne][3] = (2000+1+i);  //放入2001,2002,2003...但是如果<第3.4筆>會錯誤！
        NSLog(@"Sig:=   標準點       <第%d個>(%d,%d,%d,%d)",i+1,LMAs[countLMA3Point+i][0],LMAs[countLMA3Point+i][1],LMAs[countLMA3Point+i][2],(2000+1+i) );
        NSLog(@"Sig:=補標準點到 LMA3中<第%d個>(countLMA3LastOne=%d)(%d,%d,%d,%d)",i+1,countLMA3LastOne,LMA3[countLMA3LastOne][0],LMA3[countLMA3LastOne][1],LMA3[countLMA3LastOne][2],LMA3[countLMA3LastOne][3]);
        countLMA3LastOne++;
    }
    for(int i=0 ;(i<1000)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0);i++){
        NSLog(@"Sig:=再印LMA3.全<%d>(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
    }
    //--------(手寫點缺少點，補點,補-98分)------------
    
    
    //--------(4點筆順，<NESW.8方位>，檢查軌跡方向是否正確)------------
    /*
    //--------(<NESW_way.8方位>內部函式)------------
    //(內部函式用法)(透過 block(代碼塊)來實現類似內部函式的功能) Peter 2024 1014
    //原始 int NESW_way( int X0, int Y0, int X1, int Y1, int Way ){~}
    //原始 int (^NESW_way)(int,int,int,int,int)= ^( int X0, int Y0, int X1, int Y1, int Way){}
    
    int (^NESW_way)(int,int,int,int,int,int,int)= ^( int X00, int Y00, int X0, int Y0, int X1, int Y1, int Way){
        switch( Way/100 ){
            case 1: if( (Y0-Y1)<0 ){ NSLog(@"往北^^ (O)"); return 1;} else { NSLog(@"往北^^ (X)"); return 11;} break;//往北N^^.是回傳1.否回傳0
            case 2: if( (Y0-Y1)<0 && (X0-X1)<0 ){ NSLog(@"往東北 >^ (O)"); return 1;} else { NSLog(@"往東北 >^ (X)"); return 12;} break;//往東北EN>^.是回傳1.否回傳0
            case 3: if( (X0-X1)<0 ){ NSLog(@"往東>> (O)"); return 1;} else { NSLog(@"往東>> (X)"); return 13;} break;//往東E>>.是回傳1.否回傳0
            case 4: if( (X0-X1)<0 && (Y0-Y1)>0 ){ NSLog(@"往東南 >v (O)"); return 1;} else { NSLog(@"往東南 >v (X)"); return 14;} break;//往東南ES>v.是回傳1.否回傳0
            case 5: if( (Y0-Y1)>0 ){ NSLog(@"往南vv (O)"); return 1;} else { NSLog(@"往南vv (X)"); return 15;} break;//往南Svv.是回傳1.否回傳0
            case 6: if( (Y0-Y1)>0 && (X0-X1)>0 ){ NSLog(@"往西南 <v (O)"); return 1;} else { NSLog(@"往西南 <v (X)"); return 16;} break;//往西南WS<v.是回傳1.否回傳0
            case 7: if( (X0-X1)>0 ){ NSLog(@"往西<< (O)"); return 1;} else { NSLog(@"往西<< (X)"); return 17;} break;//往西W<<.是回傳1.否回傳0
            case 8: if( (X0-X1)>0 && (Y0-Y1)<0 ){ NSLog(@"往西北 <^ (O)"); return 1;} else { NSLog(@"往西北 <^ (X)"); return 18;} break;//往西北NW<^.是回傳1.否回傳0
            default: return -1;
        }
        switch( (Way/10)%10 ){
            case 1: if( (Y00-Y1)<0 ){ NSLog(@"上上點.往北^^ (O)"); return 1;} else { NSLog(@"上上點.往北^^ (X)"); return 21;} break;//往北N^^.是回傳1.否回傳0
            case 2: if( (Y00-Y1)<0 && (X00-X1)<0 ){ NSLog(@"上上點.往東北 >^ (O)"); return 1;} else { NSLog(@"上上點.往東北 >^ (X)"); return 22;} break;//往東北EN>^.是回傳1.否回傳0
            case 3: if( (X00-X1)<0 ){ NSLog(@"上上點.往東>> (O)"); return 1;} else { NSLog(@"上上點.往東>> (X)"); return 23;} break;//往東E>>.是回傳1.否回傳0
            case 4: if( (X00-X1)<0 && (Y00-Y1)>0 ){ NSLog(@"上上點.往東南 >v (O)"); return 1;} else { NSLog(@"上上點.往東南 >v (X)"); return 24;} break;//往東南ES>v.是回傳1.否回傳0
            case 5: if( (Y00-Y1)>0 ){ NSLog(@"上上點.往南vv (O)"); return 1;} else { NSLog(@"上上點.往南vv (X)"); return 25;} break;//往南Svv.是回傳1.否回傳0
            case 6: if( (Y00-Y1)>0 && (X00-X1)>0 ){ NSLog(@"上上點.往西南 <v (O)"); return 1;} else { NSLog(@"上上點.往西南 <v (X)"); return 26;} break;//往西南WS<v.是回傳1.否回傳0
            case 7: if( (X00-X1)>0 ){ NSLog(@"上上點.往西<< (O)"); return 1;} else { NSLog(@"上上點.往西<< (X)"); return 27;} break;//往西W<<.是回傳1.否回傳0
            case 8: if( (X00-X1)>0 && (Y00-Y1)<0 ){ NSLog(@"上上點.往西北 <^ (O)"); return 1;} else { NSLog(@"上上點.往西北 <^ (X)"); return 28;} break;//往西北NW<^.是回傳1.否回傳0
            default: return -1;
        }
        
        return 99;
    };
    //--------(<NESW_way.8方位>內部函式)------------
    */
     
    int NESW_n1 = 1001; int NESW_n0 = 1001; int NESW_n00 = 1001; int NESW_2W = 0;//n判斷點,n0上一點,n00為上上點  //(已將LMAs[i][j]=LMAs_a[i][j]代入使用)，1001做完.查找LMAs[~][~]下一個為1003或1005，1005放入NESW_n中。
    int NESW_i1=0, NESW_i0=0, NESW_i00=0;  //n->i,n0->i0,n00->i00;(n判斷點在LMA3[][]中的第i位置)
    int LMA3_X1=0;int LMA3_Y1=0;  int LMA3_X0=0;int LMA3_Y0=0;  int LMA3_X00=0;int LMA3_Y00=0;//為上個點的上一點，即上上點
    
    //--------(取判斷點.NESW_n，即LMA3[~][3]的1001的下一點，即1005)------------
    for(int m=1; (m<20)&&(LMAs[m][0]+LMAs[m][1])>0; m++){
        if(NESW_n1 < LMAs[m][3]){
            NESW_n1 = LMAs[m][3];
            NESW_2W = LMAs[m][2];
            NSLog(@"標準s(NESW_n1=%d)(LMAs[%d][3]=%d)(NESW_2W=%d)",NESW_n1,m,LMAs[m][3],NESW_2W);
        }else{
            continue;
        }
        
    //--------(取手寫LMA3[~][3]的(X1,Y1)(NESW_i1))------------
    for(int i=0; (LMA3[i][3]>=1000)&&(i<400) ; i++){
        if(NESW_n1 == LMA3[i][3]){
            LMA3_X1 = LMA3[i][0];//此為1002，即1001的下一個
            LMA3_Y1 = LMA3[i][1];
            NESW_i1 = i;
            NSLog(@"手寫3(X1,Y1)(NESW_n1=%d)LMA3[%d][3]=(%d,%d,~,%d)",NESW_n1,i,LMA3[i][0],LMA3[i][1],LMA3[i][3]);
            //--------(取手寫LMA3[~][3]的(X0,Y0)(NESW_i0)------------
            for(int j=i; j>=0; j--){
                if(NESW_n0 == LMA3[j][3]){
                    LMA3_X0 = LMA3[j][0];
                    LMA3_Y0 = LMA3[j][1];
                    NESW_i0 = j;
                    NSLog(@"手寫3(X0,Y0)(NESW_n0=%d)LMA3[%d][3]=(%d,%d,~,%d)",NESW_n0,j,LMA3[j][0],LMA3[j][1],LMA3[j][3]);
                    //--------(取手寫LMA3[~][3]的(X00,Y00)(NESW_i00)------------
                    for(int k=j; k>=0; k--){
                        if(NESW_n00 == LMA3[k][3]){
                            LMA3_X00 = LMA3[k][0];
                            LMA3_Y00 = LMA3[k][1];
                            NESW_i00 = k;
                            NSLog(@"手寫3(X00,Y00)(NESW_n00=%d)LMA3[%d][3]=(%d,%d,~,%d)",NESW_n00,k,LMA3[k][0],LMA3[k][1],LMA3[k][3]);
                            break;
                        }//由1002倒退回去找1001的點。
                    }
                    break;
                }//由1002倒退回去找1001的點。
            }
            break;
        }
    }
    
    //--------(<NESW_way.8方位>函式)------------
    
        switch( NESW_2W/100 ){
            case 1: if( (LMA3_Y0-LMA3_Y1)>0 ){ NSLog(@"(Y0-Y1)往北1 ^^ (O)");} else { NSLog(@"(Y0-Y1)往北1 ^^ (X)"); LMA3[NESW_i1][2]=97;} break;//往北N^^.是(O).否改97
            case 2: if( (LMA3_Y0-LMA3_Y1)>0 && (LMA3_X0-LMA3_X1)<0 ){ NSLog(@"(XY0-XY1)往東北2 ^> (O)");} else { NSLog(@"(XY0-XY1)往東北2 ^> (X)"); LMA3[NESW_i1][2]=97;} break;//往東北EN>^.是(O).否改97
            case 3: if( (LMA3_X0-LMA3_X1)<0 ){ NSLog(@"(X0-X1)往東3 >> (O)");} else { NSLog(@"(X0-X1)往東3 >> (X)"); LMA3[NESW_i1][2]=97;} break;//往東E>>.是(O).否改97
            case 4: if( (LMA3_X0-LMA3_X1)<0 && (LMA3_Y0-LMA3_Y1)<0 ){ NSLog(@"(XY0-XY1)往東南4 v> (O)");} else { NSLog(@"(XY0-XY1)往東南4 v> (X)"); LMA3[NESW_i1][2]=97;} break;//往東南ES>v.是(O).否改97
            case 5: if( (LMA3_Y0-LMA3_Y1)<0 ){ NSLog(@"(Y0-Y1)往南5 vv (O)");} else { NSLog(@"(Y0-Y1)往南5 vv (X)"); LMA3[NESW_i1][2]=97;} break;//往南Svv.是(O).否改97
            case 6: if( (LMA3_Y0-LMA3_Y1)<0 && (LMA3_X0-LMA3_X1)>0 ){ NSLog(@"(XY0-XY1)往西南6 <v (O)");} else { NSLog(@"(XY0-XY1)往西南6 <v (X)"); LMA3[NESW_i1][2]=97;} break;//往西南WS<v.是(O).否改97
            case 7: if( (LMA3_X0-LMA3_X1)>0 ){ NSLog(@"(X0-X1)往西7 << (O)"); } else { NSLog(@"(X0-X1)往西7 << (X)"); LMA3[NESW_i1][2]=97;} break;//往西W<<.是(O).否改97
            case 8: if( (LMA3_X0-LMA3_X1)>0 && (LMA3_Y0-LMA3_Y1)>0 ){ NSLog(@"(XY0-XY1)往西北8 <^ (O)");} else { NSLog(@"(XY0-XY1)往西北8 <^ (X)"); LMA3[NESW_i1][2]=97;} break;//往西北NW<^.是(O).否改97
        }
        switch( (NESW_2W/10)%10 ){
            case 1: if( (LMA3_Y00-LMA3_Y1)>0 ){ NSLog(@"(Y00-Y1)往北1 ^^ (O)");} else { NSLog(@"(Y00-Y1)往北1 ^^ (X)"); LMA3[NESW_i1][2]=96;} break;//往北N^^.是(O).否改96
            case 2: if( (LMA3_Y00-LMA3_Y1)>0 && (LMA3_X00-LMA3_X1)<0 ){ NSLog(@"(XY00-XY1)往東北2 ^> (O)");} else { NSLog(@"(XY00-XY1)往東北2 ^> (X)"); LMA3[NESW_i1][2]=96;} break;//往東北EN>^.是(O).否改96
            case 3: if( (LMA3_X00-LMA3_X1)<0 ){ NSLog(@"(X00-X1)往東3 >> (O)");} else { NSLog(@"(X00-X1)往東3 >> (X)"); LMA3[NESW_i1][2]=96;} break;//往東E>>.是(O).否改96
            case 4: if( (LMA3_X00-LMA3_X1)<0 && (LMA3_Y00-LMA3_Y1)<0 ){ NSLog(@"(XY00-XY1)往東南4 v> (O)");} else { NSLog(@"(XY00-XY1)往東南4 v> (X)"); LMA3[NESW_i1][2]=96;} break;//往東南ES>v.是(O).否改96
            case 5: if( (LMA3_Y00-LMA3_Y1)<0 ){ NSLog(@"(Y00-Y1)往南5 vv (O)");} else { NSLog(@"(Y00-Y1)往南5 vv (X)"); LMA3[NESW_i1][2]=96;} break;//往南Svv.是(O).否改96
            case 6: if( (LMA3_Y00-LMA3_Y1)<0 && (LMA3_X00-LMA3_X1)>0 ){ NSLog(@"(XY00-XY1)往西南6 <v (O)");} else { NSLog(@"(XY00-XY1)往西南6 <v (X)"); LMA3[NESW_i1][2]=96;} break;//往西南WS<v.是(O).否改96
            case 7: if( (LMA3_X00-LMA3_X1)>0 ){ NSLog(@"(X00-X1)往西7 << (O)"); } else { NSLog(@"(X00-X1)往西7 << (X)"); LMA3[NESW_i1][2]=96;} break;//往西W<<.是(O).否改96
            case 8: if( (LMA3_X00-LMA3_X1)>0 && (LMA3_Y00-LMA3_Y1)>0 ){ NSLog(@"(XY00-XY1)往西北8 <^ (O)");} else { NSLog(@"(XY00-XY1)往西北8 <^ (X)"); LMA3[NESW_i1][2]=96;} break;//往西北NW<^.是(O).否改96
    }
    
    NSLog(@"LMA3的(i00=%d<%d,%d>)(i0=%d<%d,%d>)(i1=%d<%d,%d>)(NESW_n1=%d,NESW_2W=%d)(LMA3[NESW_i1][2]=%d)", NESW_i00,LMA3_X00,LMA3_Y00, NESW_i0,LMA3_X0,LMA3_Y0, NESW_i1,LMA3_X1,LMA3_Y1,NESW_n1,NESW_2W,LMA3[NESW_i1][2] );
    NESW_n00 = NESW_n0;//將(X0,Y0)1001.進入下一點為.(X0,Y0)1002
    NESW_n0 = NESW_n1;//將(X0,Y0)1001.進入下一點為.(X0,Y0)1002
        
    }//把 標準點的LMAs[m][3].從1檢查到最後，標準點不超過20點，第1點跳過
    //--------(4點筆順，<NESW.8方位>，檢查軌跡方向是否正確)------------
    
    //--------(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)------------
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//先清零
    for(int i=0; (i<400)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0); i++){
        for(int j=0; j<4; j++){
            UpDownLMA3[i][j] = LMA3[i][j];
        }
    }
    UDi = -1;  //預設值為起點-1(不存在)
    UDj = -1;  //預設值為起點-1(不存在)
    UDk = -1;  //預設值為起點-1(不存在)
    //--------(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)------------
//=====(Peter手寫轉折點辨識 turningPoint)======================================================
    
    
    
    
    
    
    //==danny.做圖1 =============================================================================
        
        //--------(印.標準計分點 print StandardPoint)------------------
//    for (int i=0; (i<20)&&(LMAs[i][0]+LMAs[i][1])!=0 ;i++){
//        NSLog(@"<redP1>.LMAs<i=%d>(%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3]);
//    }
//
//        CGRect frame;                           //James
//        for(int i = 0, j=0; i < countLMAs ; i++){            //j用來計算又增加1筆劃，將redP1多印1次
//            if( ((LMAs[i][2]%10-LMAs[i-1][2]%10)!=0)&&(j!=0) ){
//                hs[i]= [[UIImageView alloc] initWithFrame:CGRectMake(180+10*LMAs[i][0],12+10*LMAs[i][1] , weight, weight)];
//                hs[i].image=[UIImage imageNamed:@"redP1"];//標準計分點.(紅叉)點
//                [self.view addSubview:hs[i]];
//                NSLog(@"Sig:<redP1.標準點>(i=%d,j=%d)(LMAs[%d][2]除10餘=%d)-(LMAs[%d-1][2]=%d)!=0 ",i,j,i, LMAs[i][2]%10,i, LMAs[i-1][2]%10 );
//                continue;
//            }
//            if( j %4 == 0 ){
//                hs[i]= [[UIImageView alloc] initWithFrame:CGRectMake(180+10*LMAs[i][0],12+10*LMAs[i][1] , weight, weight)];
//                hs[i].image=[UIImage imageNamed:@"redP1"];//標準計分點.(紅叉)點
//                [self.view addSubview:hs[i]];
//                NSLog(@"Sig:<redP1.標準點.紅叉>(if) hs<i=%d>.<j=%d>", i, j);
//                j++;
//            }else {
//                hs[i]= [[UIImageView alloc] initWithFrame:CGRectMake(180+10*LMAs[i][0],12+10*LMAs[i][1] , weight, weight)];
//                hs[i].image=[UIImage imageNamed:@"blueP2"];//標準計分點.(藍叉)點
//                [self.view addSubview:hs[i]];
//                NSLog(@"Sig:<redP1.標準點.藍叉>(else) hs<i=%d>.<j=%d>", i, j);
//                j++;
//            }
//        }
        //--------(印.標準計分點 print StandardPoint)------------------
        
        //計算LMA3[][]有多少數量放入countLMA3中
        //    int LMA3a1=0, LMA3a2=0, LMA3a3=0, LMA3a4=0; //設LMA3每筆畫數的結尾數LMA3a1...
//        for(int i=0,countLMA3=-1; i<2000 && (LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3])>0; i++){
//            countLMA3 +=1 ;
//            NSLog(@"Sig:<countLMA3/%d><%d>(%d/%d/%d/%d)",countLMA3,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
//        }


        //計算LMA3[][]有多少數量放入countLMA3中
        
        //--------(印.手寫點 print StandardPoint)------------------
//        for (int i = 0; i<(countLMA3LastOne) && LMA3[i][3]>=1000 ; i++) { //peter 0621 改
//            NSString *imageName;  //imageName
//            NSLog(@"Sig:=<black80><LMA3[i][3]/1000=第%d筆<%d>(%d,%d,%d,%d)",LMA3[i][3]/1000,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
//            switch (LMA3[i][3]/1000) {     //a=(LMA3[~][3]/1000)，第1筆畫數1001/1000=1需要換成1，第2筆畫數2008/1000=2需要換成2
//                case 1: imageName = @"black80";  break;  //淡黑.透明度80.第1筆
//                case 2: imageName = @"blue80";   break;  //淡藍.透明度80.第2筆
//                case 3: imageName = @"green80";  break;  //淡綠.透明度80.第3筆
//                case 4: imageName = @"orange80"; break;  //淡橘.透明度80.第4筆
//                default:imageName = @"red80";    break;  //淡紅.透明度80.Ｘ超過第5筆
//            }
////i依序跑完直到最後筆(conutLMA2+1)(原有-1.0.1.2.3.4值，-1直接濾除)(現為1001.1000~1000.1002.1003.20001.2000~2000.2002.~)
//            switch ((LMA3[i][3]%1000-1) % 4) {  //先(LMA2[i][3]/1000-1)變回原值;
//                case 0:                 imageName = @"red50";  break;
//                case 1: case 2: case 3: imageName = @"blue50"; break;
//            }
//            hiv[i] = [[UIImageView alloc] initWithFrame:CGRectMake(180 + 10 * LMA3[i][0], 12 + 10 * LMA3[i][1], weight, weight)];
//            hiv[i].image = [UIImage imageNamed:imageName];
//            hiv[i].contentMode = UIViewContentModeScaleAspectFit;
//            [self.view addSubview:hiv[i]];
//        }
        //--------(印.手寫點 print StandardPoint)------------------
    
        //--------(印.分數紅藍字.落起轉(紅)計分點(藍) print )----------
       //                    badScore = -1;       //本次成績.咖啡色    //0613 peter


                    int AverageScore=0;      //平均分數.紅色      //0613 peter
                    int TotalScore=0;        //總分數            //0613 peter
                    int TotalWritePoints=0;  //總計分點數         //0613 peter


        //            //peter 0614 先產生實例，再設定屬性
        //            //Danny 0603.依筆畫數切換位置(落.1/4.2/4.3/4.轉.1/4.2/4.3/4.起)的位置
        //            //所有點的分數    //i依序播放上述的點的位置.
        //            //i依序跑完hl[i]直到最後筆.(LMA2[k][2]已經放入hl[i]).
        //            //(原有-1.0.1.2.3.4值，-1直接濾除)(現為1001.1000~1000.1002..20001.2000~2000.2002.~)
        //
        //            for(int j=0,i=0,k=0; ( j<(countLMA3LastOne) ) && (LMA3[j][3]>=1000) ; j++,i++,k++ ) { //peter 0621 改
        //                while ( LMA3[j][3]%1000 == 0 ){        //跳過非計分點,只挑(0,1,2~最後筆/例8)跳過()
        //                    j++;
        //                }
        //                //----------------
        //                if(LMA3[j][3]%1000==1){
        //                    k=0;                                                 //(k)點用來位移到(下一個字)
        //                    NSLog(@"=開頭為1,數字左移1行,k=0歸零(LMA3[%d][3]除餘1000)=(%d) <i=%d><j=%d>",j,LMA3[j][3]%1000,i,j);
        //                }
        //                switch (LMA3[j][3]/1000) {                               //(case:)用來切換(跳行)印出
        //                    case 1: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(550, 50+38*k, 60, 60)]; break;//分數紅.黑字(第1排/筆)//原始
        //                    case 2: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(610, 50+38*k, 60, 60)]; break;//分數紅.藍字(第2排/筆)//原始
        //                    case 3: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(670, 50+38*k, 60, 60)]; break;//分數紅.綠字(第3排/筆)//原始
        //                    case 4: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(730, 50+38*k, 60, 60)]; break;//分數紅.橘字(第4排/筆)//原始
        //                    default:hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(790, 50+38*k, 60, 60)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        //                }                                                       //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
    //                //peter 0614
    //                NSLog(@"=hl[i=%d],<k=%d>LMA3[%d](%d,%d,%d,%d)",i,k,j,LMA3[j][0],LMA3[j][1],LMA3[j][2],LMA3[j][3]);
    //                hl[i].font = [UIFont systemFontOfSize:30];
    //                [self.view addSubview:hl[i]];
   //                }
   //            for(int i=0, k=0 , minusLMA3=0; (i<=countLMA3+4)&&(k<=countLMA3+4) ; i++){ //原始peter 0625


            NSLog(@"Sig:countLMA3LastOne=%d",countLMA3LastOne);
            for(int i=0, k=0 , minusLMA3=0; (i<=countLMA3LastOne-1)&&(k<=countLMA3LastOne-1) ; i++){
                //      次數=>LMA3a1為第1筆的[3]是第1筆的計分點最後數量，上面為4個筆畫所有的計分點數量
                while ( LMA3[k][3]%1000 == 0 ){        //只挑(0,1,2~最後筆/例8)
                    k++;
                }
                TotalWritePoints++;
                NSLog(@"Sig:<TotalWritePoints>=<%d>",TotalWritePoints);
                minusLMA3 = LMA3[k][2]*(-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
                NSLog(@"<TotalScore.before>=<%d>，LMA3[%d][2]=%d，",TotalScore,k,LMA3[k][2]*(-1));
                TotalScore = TotalScore + LMA3[k][2]*(-1);
                NSLog(@"Sig:<TotalScore.after>=<%d>",TotalScore);




        //                    hl[i].text = [NSString stringWithFormat:@"%d", minusLMA3];//穎作字(4.)放入文字內容
        //                    if( ( (LMA3[k][3]%1000-1)%4 == 0) ){
        //                        hl[i].textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.5 alpha:0.7];
        //                        //分數紅藍字.依.hl[i].0/4/8 順序決定.(紅字)
        //                    }else{
        //                        //分數紅藍字.依.hl[i].123/567順序決定.(藍字換以下字)
        //                        switch (LMA3[k][3]/1000) {             //(case:)用來切換(跳行)印出
        //                            case 1: hl[i].textColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.5]; break;//分數淡黑字(第1排/筆)
        //                            case 2: hl[i].textColor = [UIColor colorWithRed:0.5 green:0.6 blue:0.8 alpha:0.7]; break;//分數淡藍字(第2排/筆)
        //                            case 3: hl[i].textColor = [UIColor colorWithRed:0.7 green:0.8 blue:0.1 alpha:0.7]; break;//分數淡綠字(第3排/筆)
        //                            case 4: hl[i].textColor = [UIColor colorWithRed:0.9 green:0.5 blue:0.1 alpha:0.7]; break;//分數淡橘字(第4排/筆)
        //                            default:hl[i].textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.5 alpha:0.5]; break;//分數紅字(第4排/筆)//0613 peter
        //                        }
        //                    }//(1.0不透明.alpha:0.7)(黑0/0/0.藍0.5/0.6/0.8綠0.7/0.8/0.1橘0.9/0.5/0.1紅0.9/0/0.5白1/1/1 //測試OK
        //                            if ( minusLMA3 < badScore ){
       //                                badScore = minusLMA3;        //放最差的成績，-4<-5把-4放入badScore記錄下來
       //                            }//-------------(get score)------------
                            k++;
        //        //            [self.view addSubview:hl[i]];//0612
                        }
        //--------(印.分數紅藍字.落起轉(紅)計分點(藍) print )----------
    
        //--------(印.98.99紅方框/圖)------------------
        //        for (int i = 0; i<(countLMA3LastOne) && LMA3[i][3]>=1000 ; i++) { //peter 0621 改
        //            if ( LMA3[i][2]==98 ){
        //                NSString *imageName01 = @"square03";
        //    //            NSLog(@"印方框 imageName=%@,(%d)(%d,%d,%d,%d)",imageName01,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        //                hivSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*LMA3[i][0],2+10*LMA3[i][1],30,30)];
        //                hivSQ[i].image = [UIImage imageNamed:imageName01];
        //            }else if( LMA3[i][2]==99 ){
        //                NSString *imageName02 = @"square05";
        //    //            NSLog(@"印方框 imageName=%@,(%d)(%d,%d,%d,%d)",imageName02,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        //                hivSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*LMA3[i][0],2+10*LMA3[i][1],30,30)];
        //                hivSQ[i].image = [UIImage imageNamed:imageName02];
        //            }
        //                hivSQ[i].contentMode = UIViewContentModeScaleAspectFit;
        //                [self.view addSubview:hivSQ[i]];
        //        }
        //--------(印.98.99紅方框/圖)------------------


        //--------(印.98.99紅長框/數字)------------------
        //Danny 0603.依筆畫數切換位置(落.1/4.2/4.3/4.轉.1/4.2/4.3/4.起)的位置
        //i依序跑完hl[i]直到最後筆.(LMA2[k][2]已經放入hl[i]).
        //(原有-1.0.1.2.3.4值，-1直接濾除)(現為1001.1000~1000.1002..20001.2000~2000.2002.~)
        //        for(int j=0,i=0,k=0; ( j<(countLMA3LastOne) ) && (LMA3[j][3]>=1000) ; j++,k++) { //peter 0621 改
        //            while ( LMA3[j][3]%1000 == 0 ){  j++;  } //跳過非計分點,只挑(0,1,2~最後筆/例8)跳過()
        //            if(LMA3[j][3]%1000==1){
        //                k=0;                                                 //(k)點用來位移到(下一個字)
        //    //            NSLog(@"=開頭為1,數字左移1行,k=0歸零(LMA3[%d][3]除餘1000)=(%d) <i=%d><j=%d>",j,LMA3[j][3]%1000,i,j);
        //            }
        //    //        NSLog(@"長方形99(%d)(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        //            if( LMA3[j][2]==98 ){
        //                NSString *imageName03 = @"rectangle03";
        //    //            NSLog(@"(有.長方形99)(%d)(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        //                switch (LMA3[j][3]/1000) {                               //(case:)用來切換(跳行)印出
        //                    case 1: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*k, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
        //                    case 2: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*k, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
        //                    case 3: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*k, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
        //                    case 4: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*k, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
        //                    default:hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*k, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        //                }    //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
        //                hlSQ[i].image = [UIImage imageNamed:imageName03];
        //            }else if( LMA3[j][2]==99 ){
        //                NSString *imageName04 = @"rectangle05";
        //    //            NSLog(@"(有.長方形99)(%d)(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        //                switch (LMA3[j][3]/1000) {                               //(case:)用來切換(跳行)印出
        //                    case 1: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*k, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
        //                    case 2: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*k, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
        //                    case 3: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*k, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
        //                    case 4: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*k, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
        //                    default:hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*k, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        //                }                                                       //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
        //                hlSQ[i].image = [UIImage imageNamed:imageName04];
        //            }
        //            hlSQ[i].contentMode = UIViewContentModeScaleAspectFit;
        //            [self.view addSubview:hlSQ[i]];
        //            i++;
        //        }
        //--------(印.98.99紅長框/數字)------------------


    
    //--------(印.總分紅咖啡字.(紅)通過(咖啡)最差 print verageScore/red badScore/brown)----------


    AverageScore = round(TotalScore/(TotalWritePoints-1));//四捨五入//(get score/get Average score)
    NSLog(@"Sig:AverageScore %d,TotalScore %d,TotalWritePoints %d)",AverageScore,TotalScore,TotalWritePoints);


         //        //James_0123
        //        //peter 0614 先產生實例，再設定屬性
        //        //----------------
        //        hL= [[UILabel alloc] initWithFrame:CGRectMake(850, 360, 200, 100)]; //分數紅/咖啡字.(紅字 /上)
        //        hL.font=[UIFont systemFontOfSize:70];
        //        hL.text=[NSString stringWithFormat:@"%d", AverageScore];
        //        hL.textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.0 alpha:1.0];
        //        //分數紅/咖啡字.(紅字 /上)(平均/有無通過)
        //        hR= [[UILabel alloc] initWithFrame:CGRectMake(850, 500, 150, 100)]; //分數紅/咖啡字.(咖啡字/下)
        //        hR.font=[UIFont systemFontOfSize:70];
        //        hR.text=[NSString stringWithFormat:@"%d", badScore];
        //        hR.textColor=[UIColor colorWithRed:0.5 green:0.6 blue:0.8 alpha:1.0];
        //        //分數紅/咖啡字.(咖啡字/下)(最差/看哪點被扣最多分)
        //        [self.view addSubview:hL];
        //        [self.view addSubview:hR];
        //--------(印.總分紅咖啡字.(紅)通過(咖啡)最差 print verageScore/red badScore/brown)----------
    
    //==danny.做圖1 =============================================================================
//    for(int i=0;i<400;i++)
//        for(int j=0;j<4;j++) JLMA3[i][j]=LMA3[i][j];
//    JcountLMA3=countLMA3;


//    [dWb0 isWorNumber:wordinx];// 告訴他什麼字
//    mm=[dWb0 isWord:Wb0.image];// 看709行
//    NSLog(@"ABC AverageScore %d",AverageScore);
//    NSLog(@"ABC讀Signatrue分數mm %d",mm);
//    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:
//                              [NSString stringWithFormat:@"分數 %d",mm] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
//    [alertView show];


//=====================================================================================================================================
    fraction = AverageScore;
        NSLog(@"Signature: AverageScore= %d",AverageScore);
        NSLog(@"Signature: fraction= %d",fraction);


//}//(IBAction)btSimOK:(id)sender(結束)==========================================================================
//    if( (fraction <= 0 )&(fraction >= -10) ) return 100;
    
    
    //(將每次字母LMA3資料上傳至mysql)=========================================
//    int Json_n=0;
//    for ( int i=0 ; (i<400) && (LMA3[i][0]+LMA3[i][1]) >0 ; Json_n++, i++){
//        NSLog(@"<i=%d>(%d,%d,%d,%d)", i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
//    }//Json_n計算LMA3長度計數
//    // 計數LMA3的數量=======
//    // 以雙重迴圈將LMA3壓製成字串型態jsonString=======
//    NSMutableArray *array = [[NSMutableArray alloc] init];
//    for (int i = 0; i < Json_n; i++) {
//        NSMutableArray *subArray = [[NSMutableArray alloc] init];
//        for (int j = 0; j < 4; j++) {
//            [subArray addObject:@(LMA3[i][j])];
//        }
//        [array addObject:subArray];
//    }
//    NSError *error;
//    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:array options:0 error:&error];
//    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
//    NSLog(@" JSON 格式的 jsonData : %@", jsonData);
//    NSLog(@" JSON 格式的 jsonString : %@", jsonString);
//    // 以雙重迴圈將LMA3壓製成字串型態jsonString=======
//    
//    NSString *GetCNAME = [[NSUserDefaults standardUserDefaults] objectForKey:@"CNAME2"];
//        
//    //設定上傳連線=======
//  OHMySQLUser *user;
//    user = [[OHMySQLUser alloc] initWithUserName:@"uten"
//                                         password:@"1qazXSW@3edcVFR$"
//                                       serverName:@"uten.synology.me"
//                                           dbName:@"uten"
//                                             port:3307
//                                           socket:@"/run/mysqld/mysqld10.sock"];
//    
//    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
//    [coordinator connect];
//    [coordinator setEncoding:CharsetEncodingUTF8MB4];
//    
//    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
//    queryContext.storeCoordinator = coordinator;
//    //設定上傳連線=======
//    //取得字母abc.待罰=======
//    // wordinx
//    
////    NSLog(@"SIGNATURE有跑");
//    
//    char Getabc = 'a';
//    for (int i=0; i<=25; i++){
//        if (i == WordPtr){
//            Getabc = Getabc + i;
//            NSLog(@"Getabc = %c",Getabc);
//        }
//    }
//    NSString *GetabcString = [NSString stringWithFormat:@"%c", Getabc];
//    int NoPass = 5;
//    if ( AverageScore <= 10 ){
//        NoPass = 0;
//    }
//    int abcscore;
//    switch(AverageScore){
//        case 0: case -1:
//            abcscore = 100;
//            break;
//        case -2:
//            abcscore = 90;
//            break;
//        case -3:
//            abcscore = 80;
//            break;
//        case -4: case -5:
//            abcscore = 70;
//            break;
//        case -6: case -7: case -8: case -9: case -10:
//            abcscore = 60;
//            break;
//        default:
//            abcscore = 0;
//            break;
//    }
//    
//    //取得字母abc.待罰=======
//    //設定上傳的資料=======
//    // 假設只插入不包含自動填充的日期欄位
//    NSDictionary *insertData = @{
//        @"CNAME": GetCNAME,            // 倒入CNAME
//        @"字母": GetabcString,          // 字母 未  @"test_word",
//        @"字母軌跡": jsonString,         // 導入字母軌跡
//        @"通過分數": @(abcscore),              // 導入 mm 是整數變數
//        @"待罰"   : @(NoPass),          // 導入 NoPass (及格0,不及格5次)
//    };
//  
//    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory INSERT:@"AllStudentABC" set:insertData];
//    //設定上傳的資料=======
//    //上傳完成=======
//    NSError *error1 = nil;
//    [queryContext executeQueryRequest:query error:&error];
//    
//    if (error1) {
//        NSLog(@"Error1: %@", error.localizedDescription);
//    } else {
//        NSLog(@"Data inserted successfully into dannytest.");
//    }
//    [coordinator disconnect];

    
    //(將每次字母LMA3資料上傳至mysql)=========================================
    
    
    switch (fraction) {
        case 0: case -1:
            return 100;
            break;
        case -2:
            return 90;
            break;
        case -3:
            return 80;
            break;
        case -4: case -5:
            return 70;
            break;
        case -6: case -7: case -8: case -9: case -10:
            return 60;
            break;
        default:
            return 0;
            break;
    }
//    if( (fraction <= 0 )&(fraction >= -10) ) return fraction;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"FractionNotification" object:nil userInfo:@{@"fraction": @(fraction)}];
        
    return fraction;
}//isWord結束
- (BOOL)isSignatureWrite
{
    return mouseSwiped;
}
@end

