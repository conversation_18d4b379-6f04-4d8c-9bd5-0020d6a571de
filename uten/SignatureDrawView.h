//
//  SignatureDrawView.h
//  uten
//
//  Created by 簡大翔 on 2018/8/13.
//  Copyright © 2018年 bekubee. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface SignatureDrawView : UIView

@property (nonatomic, retain) UIGestureRecognizer *theSwipeGesture;
@property (nonatomic, retain) UIImageView *drawImage;
@property (nonatomic, assign) CGPoint lastPoint;
@property (nonatomic, assign) BOOL mouseSwiped;
@property (nonatomic, assign) NSInteger mouseMoved;

- (int *) getLAMx; //James
- (int) getLAMc;   //James
- (void)erase;
- (void)setSignature:(NSData *)theLastData;
- (BOOL)isSignatureWrite;
- (int) isWord:(UIImage *) cmpimg;
- (int) cmpMustPoint;
- (void) isWorNumber:(int) wn;
-(int)returnfraction;
@end

