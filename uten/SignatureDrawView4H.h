//
//  SignatureDrawView4H.h
//  uten
//
//  Created by 簡大翔 on 2020/9/14.
//  Copyright © 2020 bekubee. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SignatureDrawView4H : UIView
@property (nonatomic, retain) UIGestureRecognizer *theSwipeGesture;
@property (nonatomic, retain) UIImageView *drawImage;
@property (nonatomic, assign) CGPoint lastPoint;
@property (nonatomic, assign) BOOL mouseSwiped;
@property (nonatomic, assign) NSInteger mouseMoved;

- (void)erase;
- (void)setSignature:(NSData *)theLastData;
- (BOOL)isSignatureWrite;
- (int) isWord:(UIImage *) cmpimg pens:(int) ispen;
- (int) draw_getcount;
- (void) draw_replay:(int )cnt;
- (void) draw_clear;
@end

NS_ASSUME_NONNULL_END
