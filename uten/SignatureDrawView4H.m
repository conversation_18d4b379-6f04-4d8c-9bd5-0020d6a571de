//
//  SignatureDrawView4H.m
//  uten
//
//  Created by 簡大翔 on 2020/9/14.
//  Copyright © 2020 bekubee. All rights reserved.
//

#import "SignatureDrawView4H.h"

@implementation SignatureDrawView4H {
    UIImage *tempDrawImage;
    int orgimg[300][100];
    int drawimg[300][100];
    int pen;
    CGPoint lpoint[1000];
    CGPoint cpoint[1000];
    int PCnt;
}
@synthesize theSwipeGesture;
@synthesize drawImage;
@synthesize lastPoint;
@synthesize mouseSwiped;
@synthesize mouseMoved;
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        // Initialization code
        drawImage = [[UIImageView alloc] initWithImage:nil];
        drawImage.frame = CGRectMake(0, 0, self.frame.size.width, self.frame.size.height);
        [self addSubview:drawImage];
        self.backgroundColor = [UIColor whiteColor];
        mouseMoved = 0;
        pen=0;
    }
    return self;
}

- (id)initWithCoder:(NSCoder*)coder
{
    if ((self = [super initWithCoder:coder]))
    {
        drawImage = [[UIImageView alloc] initWithImage:nil];
        drawImage.frame = CGRectMake(0, 0, self.frame.size.width, self.frame.size.height);
        [self addSubview:drawImage];
        self.backgroundColor = [UIColor redColor];
        mouseMoved = 0;
        pen=0;
    }
    return self;
}

#pragma mark touch handling

- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    for (UITouch *touch in touches)
    {
        NSArray *array = touch.gestureRecognizers;
        for (UIGestureRecognizer *gesture in array)
        {
            if (gesture.enabled & [gesture isMemberOfClass:[UISwipeGestureRecognizer class]])
            {
                gesture.enabled = NO;
                self.theSwipeGesture = gesture;
            }
        }
    }
    
    mouseSwiped = NO;
    UITouch *touch = [touches anyObject];
    
    lastPoint = [touch locationInView:self];
}

- (void)touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event
{
    mouseSwiped = YES;
    UITouch *touch = [touches anyObject];
    CGPoint currentPoint = [touch locationInView:self];
    UIGraphicsBeginImageContext(self.frame.size);
    [drawImage.image drawInRect:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
    CGContextSetLineCap(UIGraphicsGetCurrentContext(), kCGLineCapRound);
    CGContextSetLineWidth(UIGraphicsGetCurrentContext(), 2.0);
    CGContextSetRGBStrokeColor(UIGraphicsGetCurrentContext(), 0.0, 0.0, 1.0, 1.0);
    CGContextBeginPath(UIGraphicsGetCurrentContext());
    CGContextMoveToPoint(UIGraphicsGetCurrentContext(), lastPoint.x, lastPoint.y);
    CGContextAddLineToPoint(UIGraphicsGetCurrentContext(), currentPoint.x, currentPoint.y);
    CGContextStrokePath(UIGraphicsGetCurrentContext());
    drawImage.image = UIGraphicsGetImageFromCurrentImageContext();
    lpoint[PCnt]=lastPoint;
    cpoint[PCnt]=currentPoint;
    PCnt++;
    UIGraphicsEndImageContext();
    lastPoint = currentPoint;
    mouseMoved++;
    NSLog(@"Count:%d / Position:(%f,%f)",mouseMoved,lastPoint.x,lastPoint.y);
    if (mouseMoved == 10) {
        mouseMoved = 0;
    }
}

- (void)touchesEnded:(NSSet *)touches withEvent:(UIEvent *)event
{
    if(!mouseSwiped)
    {
        UIGraphicsBeginImageContext(self.frame.size);
        [drawImage.image drawInRect:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
        CGContextSetLineCap(UIGraphicsGetCurrentContext(), kCGLineCapRound);
        CGContextSetLineWidth(UIGraphicsGetCurrentContext(), 3.0); //3.0
        CGContextSetRGBStrokeColor(UIGraphicsGetCurrentContext(), 0.0, 0.0, 1.0, 1.0);
        CGContextMoveToPoint(UIGraphicsGetCurrentContext(), lastPoint.x, lastPoint.y);
        CGContextAddLineToPoint(UIGraphicsGetCurrentContext(), lastPoint.x, lastPoint.y);
        CGContextStrokePath(UIGraphicsGetCurrentContext());
        CGContextFlush(UIGraphicsGetCurrentContext());
        drawImage.image = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }
    self.theSwipeGesture.enabled = YES;
    mouseSwiped = YES;
    pen++;
}

#pragma mark Methods

- (void)erase
{
    mouseSwiped = NO;
    drawImage.image = nil;
    pen=0;
    PCnt=0;
}
-(int) draw_getcount {
    return PCnt;
}
-(void) draw_replay:(int )cnt {
    if(cnt < PCnt) {
        UIGraphicsBeginImageContext(self.frame.size);
        [drawImage.image drawInRect:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height)];
        CGContextSetLineCap(UIGraphicsGetCurrentContext(), kCGLineCapRound);
        CGContextSetLineWidth(UIGraphicsGetCurrentContext(), 2.0);
        CGContextSetRGBStrokeColor(UIGraphicsGetCurrentContext(), 0.0, 0.0, 1.0, 1.0);
        CGContextBeginPath(UIGraphicsGetCurrentContext());
        CGContextMoveToPoint(UIGraphicsGetCurrentContext(), lpoint[cnt].x, lpoint[cnt].y);
        CGContextAddLineToPoint(UIGraphicsGetCurrentContext(), cpoint[cnt].x, cpoint[cnt].y);
        CGContextStrokePath(UIGraphicsGetCurrentContext());
        drawImage.image = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }
}
-(void) draw_clear {
    mouseSwiped = NO;
    drawImage.image = nil;
    pen=0;
}
- (void)setSignature:(NSData *)theLastData
{
    UIImage *image = [UIImage imageWithData:theLastData];
    if (image != nil)
    {
        drawImage.image = [UIImage imageWithData:theLastData];
        mouseSwiped = YES;
    }
}
-(int)getDrawImage:(UIImage*)image
{
    int count=0;
    NSMutableArray *resultColor = [NSMutableArray array];
    CGImageRef imageRef = [image CGImage];
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    unsigned char *rawData = (unsigned char*) calloc(height * width * 4, sizeof(unsigned char));
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                                 
                                                 bitsPerComponent, bytesPerRow, colorSpace,
                                                 
                                                 kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    for(int yy=0;yy<300;yy++) {
        for(int xx=0;xx<100;xx++) {
            int idx=(yy*100+xx)*4;
            unsigned long da=rawData[idx+3]; da<<=8;
            da|=rawData[idx]; da<<=8;
            da|=rawData[idx+1]; da<<=8;
            da|=rawData[idx+2];
           // if((rawData[idx+3]!=rawData[idx])&(rawData[idx]!=rawData[idx+1])&(rawData[idx+1]!=rawData[idx+2])&(rawData[idx+2]!=rawData[idx+3]))
            if(da > 0) {
                drawimg[yy][xx]=1;
                count++;
            }
            else drawimg[yy][xx]=0;
        }
    }
    /*
    NSString *s=@"";
    for(int yy=0;yy<150;yy++) {
        s=@"";
        for(int xx=0;xx<50;xx++) {
            s = [NSString stringWithFormat:@"%@%d", s, drawimg[yy][xx]];
        }
        NSLog(@"%@",s);
    }
     */
    return count;
}
-(int)getOrgImage:(UIImage*)image
{
    int count=0;
    NSMutableArray *resultColor = [NSMutableArray array];
    CGImageRef imageRef = [image CGImage];
    NSUInteger width = CGImageGetWidth(imageRef);
    NSUInteger height = CGImageGetHeight(imageRef);
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    unsigned char *rawData = (unsigned char*) calloc(height * width * 4, sizeof(unsigned char));
    NSUInteger bytesPerPixel = 4;
    NSUInteger bytesPerRow = bytesPerPixel * width;
    NSUInteger bitsPerComponent = 8;
    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                                 
                                                 bitsPerComponent, bytesPerRow, colorSpace,
                                                 
                                                 kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);
    CGColorSpaceRelease(colorSpace);
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), imageRef);
    CGContextRelease(context);
    
    for(int yy=0;yy<300;yy++) {
        for(int xx=0;xx<100;xx++) {
            int idx=(yy*100+xx)*4;
            unsigned long da=rawData[idx+3]; da<<=8;
                          da|=rawData[idx]; da<<=8;
                          da|=rawData[idx+1]; da<<=8;
                          da|=rawData[idx+2];
            if((rawData[idx+3]!=rawData[idx])&(rawData[idx]!=rawData[idx+1])&(rawData[idx+1]!=rawData[idx+2])&(rawData[idx+2]!=rawData[idx+3])) {
                orgimg[yy][xx]=1;
                count++;
            }
            else orgimg[yy][xx]=0;
        }
    }
    /*
    NSString *s=@"";
    for(int yy=0;yy<150;yy++) {
        s=@"";
        for(int xx=0;xx<50;xx++) {
            s = [NSString stringWithFormat:@"%@%d", s, orgimg[yy][xx]];
        }
        NSLog(@"%@",s);
    }
     */
    return count;
}
- (int) isWord:(UIImage *) cmpimg pens:(int) ispen
{
    int orgcnt,drawcnt;
    int matchcnt=0;
    if(drawImage.image.size.width!=100) return 0;
    if(drawImage.image.size.height!=300) return 0;
    UIGraphicsBeginImageContext(CGSizeMake(100, 300));
    //將原始影像重繪在此範圍中
    [cmpimg drawInRect:CGRectMake(0, 0, 100, 300)];
    //以目前的ImageContext來製作新的UIImage
    UIImage *resizeImage =UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    orgcnt=[self getOrgImage:resizeImage];
    drawcnt=[self getDrawImage:drawImage.image];
    NSLog(@"pen:%d",pen);
    if(pen != ispen) return(-1);
    for(int yy=0;yy<300;yy++)
        for(int xx=0;xx<100;xx++) {
            int fg=0;
            if((drawimg[yy][xx]==1)&(orgimg[yy][xx]==1)) fg=1;
            if((drawimg[yy][xx]==1)&(orgimg[yy][xx+1]==1)) fg=1;
            if((drawimg[yy][xx]==1)&(orgimg[yy+1][xx]==1)) fg=1;
            if((drawimg[yy][xx]==1)&(orgimg[yy+1][xx+1]==1)) fg=1;
            if(fg==1) matchcnt++;
            
        }
    
    if((orgcnt/2) < drawcnt) {
        NSLog(@"Org:%d / Draw:%d / Math:%d(%d)",orgcnt,drawcnt,matchcnt,matchcnt*100/drawcnt);
        return (matchcnt*100/drawcnt);
    } else {
        return (0);
    }
}
- (BOOL)isSignatureWrite
{
    return mouseSwiped;
}
@end
