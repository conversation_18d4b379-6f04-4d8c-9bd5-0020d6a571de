//
//  WaitViewController.m
//  uten
//
//  Created by 簡大翔 on 2019/6/19.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "WaitViewController.h"
#import "MQTTClient.h"
#import "WriterViewController.h"
#import "WriteMutilViewController.h"
#import "WriteMutilWordViewController.h"
#import "WriteSentenceViewController.h"
#import "WriteWordViewController.h"
#import "SayViewController.h"
#import "eMaterialViewController.h"
#import "PureSayViewController.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "MQTTSession+X.h"
#import <ReactiveObjC/ReactiveObjC.h>

@interface WaitViewController () <MQTTSessionDelegate> {
    MQTTSession *session;
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    IBOutlet UILabel *LoginLink;
    IBOutlet UILabel *iTitle;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *CLASS;
    NSString *ENAME;
    NSString *ServerIP;
    int restcount;
    NSTimer *cTimer;
    NSString *uten_class,*r_uten_class;
}

@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation WaitViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP=[defaults objectForKey:@"ServerIP"];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    CLASS = [defaults objectForKey:@"CLASS"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text= [[NSString alloc] initWithFormat:(@"%@(%@)"),CNAME,CLASS];

    NSString* documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
    NSString* foofile = [documentsPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.model",CNAME]];
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:foofile];
    if(fileExists) {
        LoginLink.text=@"";
    } else {
        LoginLink.text=@"無語音模型,請先建立";
    }
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    //
    
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
    transport.host = ServerIP;
    transport.port = 1883;
    
    session = [[MQTTSession alloc] init];
    session.transport = transport;
    session.delegate=self;
    if (uten_class == nil) {
        uten_class = @"uten_";
    }
    if (r_uten_class == nil) {
        r_uten_class = @"r_uten_";
    }
    [[[[session rac_connect] flattenMap:^RACSignal *(id value) {
        // 訂閱主題
        return [session rac_subscribeToTopic:uten_class atLevel:MQTTQosLevelExactlyOnce];
    }] flattenMap:^RACSignal *(id value) {
        // 發佈 LOGIN 訊息
        return [self publishCommandLogin];
    }] subscribeNext:^(id x) {
        NSLog(@"Subscription sucessfull!");
    } error:^(NSError *error) {
        NSLog(@"Subscription failed %@", error.localizedDescription);
    }];
    
    restcount=0;
    cTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    [self performSelector:@selector(onInit) withObject:nil afterDelay:0];
}

- (void)onInit {
    [self setupObservable];
    [self onReady];
}

- (void)onReady {
}

- (void)setupObservable {
    // 訂閱 session
    {
        @weakify(self);
        [[[session rac_connectionHandler] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(NSNumber *value) {
            @strongify(self);
            NSLog(@"Connected: %@", value);
            MQTTSessionEvent event = value.integerValue;
            switch (event) {
                case MQTTSessionEventConnected:
                    NSLog(@"Connected");
                    break;
                case MQTTSessionEventConnectionClosed:
                    NSLog(@"Connection closed");
                    break;
                case MQTTSessionEventConnectionError:
                    NSLog(@"Connection error");
                    break;
                case MQTTSessionEventConnectionRefused:
                    NSLog(@"Connection refused");
                    break;
                case MQTTSessionEventProtocolError:
                    NSLog(@"Protocol error");
                    break;
                case MQTTSessionEventConnectionClosedByBroker:
                    NSLog(@"Connection closed by broker");
                    break;
                default:
                    break;
            }
        } error:^(NSError *error) {
            NSLog(@"Connection error: %@", error);
        }];
    }
    // session 訂閱 message handler
    {
        @weakify(self);
        [[[session rac_messageHandler] takeUntil:self.rac_willDeallocSignal] subscribeNext:^(RACTuple *tuple) {
            @strongify(self);
            NSData *data = tuple.first;
            NSString *topic = tuple.second;
            // log topic
            NSLog(@"Message received in topic: %@", topic);
        } error:^(NSError *error) {
            NSLog(@"Message error: %@", error);
        }];
    }
}

-(void)timesUp:(NSTimer *)timer{
    if(restcount > 0) {
        restcount--;
        if(restcount ==0 ) {
            iTitle.text=@"課程準備開始,請稍候";
        }
    }
}
- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    
    // UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
  //  NSString *str=[NSString stringWithUTF8String:[data bytes]];
    // NSString *str = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
  /*
    NSString *str2 = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSLog(@"\n data = %@\n ---------\n  ,str2 = %@ \n ",data,str2 );
    
    NSLog(@"New message:%@",str);
    */
    // NSArray *ss = [str componentsSeparatedByString:@","];
    //$uten_class01,START,type,lbu1u4,tag(lxxxx),00004~
    if (command.isBye) {
        restcount=1;
        [self publishCommandClassStop];
    }
    else if (command.isRollCall) {
        [self publishCommandRollCall];
    }
    else if (command.isStart) {
        // [self startCommand:command];
        // 從 timestamp 轉換成 NSDate
        NSTimeInterval triggerAt = command.triggerAt.doubleValue;
        NSLog(@"triggerAt: %f", triggerAt);
        NSDate *startAt = [NSDate dateWithTimeIntervalSince1970:triggerAt];
        NSLog(@"Start at: %@", startAt);
        // timestamp 與現在時間的差距
        NSTimeInterval diff = MAX(0.0, startAt.timeIntervalSinceNow);
        NSLog(@"Start after: %f sec", diff);
        // 延遲 diff 秒後執行 startCommand 方法
        dispatch_time_t delay = dispatch_time(DISPATCH_TIME_NOW, NSEC_PER_SEC * diff);
        @weakify(self);
        dispatch_after(delay, dispatch_get_main_queue(), ^{
            @strongify(self);
            [self startCommand:command];
        });
    }
}

-(void)startCommand:(UTENCommand *)command {
    NSString *selTag = command.selTag;
    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
    NSArray *da = [selTag componentsSeparatedByCharactersInSet:set];
    if(da.count > 3) {
        UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
        if (([da[3] isEqualToString:@"寫字3"])|([da[3] isEqualToString:@"寫字6"])|([da[3] isEqualToString:@"寫字9"])) {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            // 設定模組參數
            myViewController.writeModule = command.writeModule.integerValue;
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"寫字1順"])| ([da[3] isEqualToString:@"寫字1亂"])) {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"寫拼3"])|([da[3] isEqualToString:@"寫拼6"])|([da[3] isEqualToString:@"寫拼9"])) {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"寫拼1順"])| ([da[3] isEqualToString:@"寫拼1亂"])) {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        //Database
        if (([da[3] isEqualToString:@"寫字1順(計)"]) | ([da[3] isEqualToString:@"寫句1順(計)"])| ([da[3] isEqualToString:@"寫句1亂(計)"])) {
            WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        

        if (([da[3] isEqualToString:@"說字3"])|([da[3] isEqualToString:@"說字6"])|([da[3] isEqualToString:@"說字9"])) {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"說字1順"])|([da[3] isEqualToString:@"說字1亂"])) {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"說句1順"])|([da[3] isEqualToString:@"說句1亂"])) {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        //DataBase
        if (([da[3] isEqualToString:@"說字1順(計)"])|([da[3] isEqualToString:@"說拼1順(計)"])|([da[3] isEqualToString:@"說拼1亂(計)"])) {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"說拼3"])|([da[3] isEqualToString:@"說拼6"])|([da[3] isEqualToString:@"說拼9"])) {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        if (([da[3] isEqualToString:@"說拼1順"])|([da[3] isEqualToString:@"說拼1亂"])) {
            SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
        }
        
        if (([da[3] isEqualToString:@"說書1"])|([da[3] isEqualToString:@"說書3"])|([da[3] isEqualToString:@"說書6"])|([da[3] isEqualToString:@"說書9"]))  {
            eMaterialViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"eMaterialViewController"];
            [myViewController setValue:selTag forKey:@"seltag"];
            [myViewController setValue:da[1] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
            
        }
    } else {
        if ([da[1] isEqualToString:@"休息1分鐘"]) {
            restcount=60;
            iTitle.text=da[1];
            
        }
        if ([da[1] isEqualToString:@"休息3分鐘"]) {
            restcount=180;
            iTitle.text=da[1];

        }
        if ([da[1] isEqualToString:@"休息5分鐘"]) {
            restcount=300;
            iTitle.text=da[1];

        }
    }
    /*
    switch([ss[2] intValue]) {
        case 0: {
                WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
                [myViewController setValue:ss[4] forKey:@"seltag"];
                [myViewController setValue:ss[3] forKey:@"uclass"];
                [self presentViewController:myViewController animated:YES completion:nil];
                }
                break;
        case 1: {
            WriteWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteWordViewController"];
            [myViewController setValue:ss[4] forKey:@"seltag"];
            [myViewController setValue:ss[3] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
            }
            break;

        case 2: {
            WriteMutilWordViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilWordViewController"];
            [myViewController setValue:ss[4] forKey:@"seltag"];
            [myViewController setValue:ss[3] forKey:@"uclass"];
            [self presentViewController:myViewController animated:YES completion:nil];
            }
            break;
        case 3: {
            WriteMutilViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriteMutilViewController"];
                [myViewController setValue:ss[4] forKey:@"seltag"];
                [myViewController setValue:ss[3] forKey:@"uclass"];
                [self presentViewController:myViewController animated:YES completion:nil];
            }
            break;
        default:
            {
                SayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"SayViewController"];
                [myViewController setValue:ss[2] forKey:@"cType"];
                [myViewController setValue:ss[4] forKey:@"seltag"];
                [myViewController setValue:ss[3] forKey:@"uclass"];
                [self presentViewController:myViewController animated:YES completion:nil];
            }
            break;
    }
        */
}

- (void)publishCommandClassStop {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = uten_class;
    command.rUtenClass = r_uten_class;
    command.loginID = ID;
    NSError *error = nil;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce];
}

- (void)publishCommandRollCall {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdRollCall;
    command.utenClass = uten_class;
    command.rUtenClass = r_uten_class;
    command.loginID = ID;
    command.cname = CNAME;
    command.ename = ENAME;
    NSError *error = nil;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce];
}

// publish login command
- (RACSignal *)publishCommandLogin {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdLogin;
    command.utenClass = uten_class;
    command.rUtenClass = r_uten_class;
    command.loginID = ID;
    NSError *error = nil;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return [RACSignal error:error];
    }
    return [session rac_publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce];
}

// publish logout command
- (void)publishCommandLogout {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdLogout;
    command.utenClass = uten_class;
    command.rUtenClass = r_uten_class;
    command.loginID = ID;
    NSError *error = nil;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce];
}

- (IBAction)btPureSayPage:(id)sender {
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    PureSayViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"PureSayViewController"];
    [self presentViewController:myViewController animated:YES completion:nil];
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {
    [self publishCommandLogout];
    session.delegate = nil;
    [session disconnect];
    session = nil;
    exit(-1);
    
    //Debug
    /*
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    WriterViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"WriterViewController"];
    [myViewController setValue:@"[up3u5u8][寫3][tall][short][old][young]" forKey:@"seltag"];
    [myViewController setValue:@"up3u5u8" forKey:@"uclass"];
    [self presentViewController:myViewController animated:YES completion:nil];
    */
     //Debug End
    //[self dismissViewControllerAnimated:NO completion:nil];
}
@end
