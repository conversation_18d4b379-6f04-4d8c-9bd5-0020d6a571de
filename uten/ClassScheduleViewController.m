//
//  ClassScheduleViewController.m
//  uten
//
//  Created by <PERSON> on 2019/8/1.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "ClassScheduleViewController.h"

@interface ClassScheduleViewController () {
    IBOutlet UIImageView *faceView;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
}
@property (nonatomic, strong) UIImage *faceimg;

@end

@implementation ClassScheduleViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    //if([TYPE intValue] < 10) btUpdate.hidden=NO;
    //else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:
                      [NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    //
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btLogout:(id)sender {
    
    [self dismissViewControllerAnimated:NO completion:nil];
}
@end
