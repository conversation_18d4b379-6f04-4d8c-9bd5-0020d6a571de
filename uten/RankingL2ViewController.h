//
//  RankingL2ViewController.h
//  uten
//
//  Created by 蔡駿寓 on 2024/5/18.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface RankingL2ViewController : UIViewController

//(轉場傳送.文字+圖片)--------
//(聲明一個整型變量來接收按鈕tag標籤)//(宣告label1~9+imageView)
@property (nonatomic, assign) NSInteger buttonTag;
@property (weak, nonatomic) IBOutlet UILabel *label1;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label2;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label3;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label4;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label5;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label6;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label7;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label8;  //傳送文字---
@property (weak, nonatomic) IBOutlet UILabel *label9;  //傳送文字---
@property (weak, nonatomic) IBOutlet UIImageView *imageView;// 声明UIImageView属性
@property (nonatomic, strong) UIImageView *showEnbotton;
//(轉場傳送.文字+圖片)--------

@end

NS_ASSUME_NONNULL_END
