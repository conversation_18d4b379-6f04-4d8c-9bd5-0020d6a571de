//
//  ClassViewController.m
//  uten
//
//  Created by <PERSON> on 2018/12/1.
//  Copyright © 2018 bekubee. All rights reserved.
//

#import "ClassViewController.h"
#import "GRRequestsManager.h"
#import "GRListingRequest.h"
#import "SSZipArchive.h"
#import "Test01ViewController.h"
@interface ClassViewController () <GRRequestsManagerDelegate>  {
    IBOutlet UIView *alertClass;
    IBOutlet UIImageView *faceView;
    IBOutlet UIPageControl *UPC;
    IBOutlet UILabel *LoginDate;
    IBOutlet UILabel *LoginName;
    IBOutlet UIButton *btUpdate;
    IBOutlet UIButton *class00;
    IBOutlet UIButton *class01;
    IBOutlet UIButton *class02;
    IBOutlet UIButton *class03;
    IBOutlet UIButton *class04;
    IBOutlet UIButton *class05;
    IBOutlet UIButton *class06;
    IBOutlet UIButton *class07;
    IBOutlet UIButton *class08;
    IBOutlet UIButton *class09;
    IBOutlet UIButton *class10;
    IBOutlet UIButton *class11;
    IBOutlet UIButton *class12;
    IBOutlet UIButton *class13;
    IBOutlet UIButton *class14;
    IBOutlet UIButton *class15;
    IBOutlet UIButton *class16;
    IBOutlet UIButton *class17;
    IBOutlet UIButton *class18;
    IBOutlet UIButton *class19;
    IBOutlet UIButton *class20;
    IBOutlet UILabel *lt00;
    IBOutlet UILabel *lt01;
    IBOutlet UILabel *lt02;
    IBOutlet UILabel *lt03;
    IBOutlet UILabel *lt04;
    IBOutlet UILabel *lt05;
    IBOutlet UILabel *lt06;
    IBOutlet UILabel *lt07;
    IBOutlet UILabel *lt08;
    IBOutlet UILabel *lt09;
    IBOutlet UILabel *lt10;
    IBOutlet UILabel *lt11;
    IBOutlet UILabel *lt12;
    IBOutlet UILabel *lt13;
    IBOutlet UILabel *lt14;
    IBOutlet UILabel *lt15;
    IBOutlet UILabel *lt16;
    IBOutlet UILabel *lt17;
    IBOutlet UILabel *lt18;
    IBOutlet UILabel *lt19;
    IBOutlet UILabel *lt20;
    IBOutlet UILabel *ld00;
    IBOutlet UILabel *ld01;
    IBOutlet UILabel *ld02;
    IBOutlet UILabel *ld03;
    IBOutlet UILabel *ld04;
    IBOutlet UILabel *ld05;
    IBOutlet UILabel *ld06;
    IBOutlet UILabel *ld07;
    IBOutlet UILabel *ld08;
    IBOutlet UILabel *ld09;
    IBOutlet UILabel *ld10;
    IBOutlet UILabel *ld11;
    IBOutlet UILabel *ld12;
    IBOutlet UILabel *ld13;
    IBOutlet UILabel *ld14;
    IBOutlet UILabel *ld15;
    IBOutlet UILabel *ld16;
    IBOutlet UILabel *ld17;
    IBOutlet UILabel *ld18;
    IBOutlet UILabel *ld19;
    IBOutlet UILabel *ld20;
    
    //

    //
    
    UIButton *btClass0[21];
    UILabel  *lvt0[21];
    UILabel  *lvd0[21];
    int mode;
    NSString *ID;
    NSString *TYPE;
    NSString *CNAME;
    NSString *ENAME;
    UIActivityIndicatorView *indicator;
    int downcount;
    NSString *classContent;
    NSString *upgradeContent;
}
@property (nonatomic, strong) GRRequestsManager *requestsManager;
@property (nonatomic, strong) UIImage *faceimg;
@end

@implementation ClassViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ID = [defaults objectForKey:@"ID"];
    TYPE = [defaults objectForKey:@"TYPE"];
    CNAME = [defaults objectForKey:@"CNAME"];
    ENAME = [defaults objectForKey:@"ENAME"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    [formatter setDateFormat:@"yyyy.MM.dd"];
    LoginDate.text=[formatter stringFromDate:[NSDate date]];
    LoginName.text=CNAME;
    if([TYPE intValue] < 10) btUpdate.hidden=NO;
    else btUpdate.hidden=YES;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory,
                                                         NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString* path = [documentsDirectory stringByAppendingPathComponent:[NSString stringWithString: @"faceImage.jpg"] ];
    _faceimg = [UIImage imageWithContentsOfFile:path];
    [faceView setImage:_faceimg];
    faceView.transform = CGAffineTransformMakeRotation(M_PI_2);
    //
    classContent=@"";
    // Do any additional setup after loading the view.
    btClass0[0]=class00; btClass0[1]=class01; btClass0[2]=class02; btClass0[3]=class03;
    btClass0[4]=class04; btClass0[5]=class05; btClass0[6]=class06; btClass0[7]=class07;
    btClass0[8]=class08; btClass0[9]=class09; btClass0[10]=class10; btClass0[11]=class11;
    btClass0[12]=class12; btClass0[13]=class13; btClass0[14]=class14; btClass0[15]=class15;
    btClass0[16]=class16; btClass0[17]=class17; btClass0[18]=class18; btClass0[19]=class19;
    btClass0[20]=class20;
    lvt0[0]=lt00; lvt0[1]=lt01; lvt0[2]=lt02; lvt0[3]=lt03; lvt0[4]=lt04; lvt0[5]=lt05; lvt0[6]=lt06;
    lvt0[7]=lt07; lvt0[8]=lt08; lvt0[9]=lt09; lvt0[10]=lt10; lvt0[11]=lt11; lvt0[12]=lt12; lvt0[13]=lt13;
    lvt0[14]=lt14; lvt0[15]=lt15; lvt0[16]=lt16; lvt0[17]=lt17; lvt0[18]=lt18; lvt0[19]=lt19; lvt0[20]=lt20;
    lvd0[0]=ld00; lvd0[1]=ld01; lvd0[2]=ld02; lvd0[3]=ld03; lvd0[4]=ld04; lvd0[5]=ld05; lvd0[6]=ld06;
    lvd0[7]=ld07; lvd0[8]=ld08; lvd0[9]=ld09; lvd0[10]=ld10; lvd0[11]=ld11; lvd0[12]=ld12; lvd0[13]=ld13;
    lvd0[14]=ld14; lvd0[15]=ld15; lvd0[16]=ld16; lvd0[17]=ld17; lvd0[18]=ld18; lvd0[19]=ld19; lvd0[20]=ld20;
    for(int i=0;i<21;i++) {
        btClass0[i].hidden=TRUE;
        lvt0[i].hidden=TRUE;
        lvd0[i].hidden=TRUE;
    }
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"class.csv"]];
    classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"class FIle:%@\n%@",DirFile,classContent);
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    int len=(int)[splitLine count];
    
    if((len%21)==0)
        UPC.numberOfPages=(len/21);
    else UPC.numberOfPages=(len/21)+1;
    UPC.currentPage=0;
    [self updateTitle];
    /*
    if(len > 21) len=21;
    
    for(int i=0;i<len;i++) {
        NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
        btClass0[i].hidden=FALSE;
        //lvt0[i].hidden=TRUE;
        lvd0[i].hidden=FALSE;
        lvd0[i].text=split1[2];
        
        NSLog(@"class name :%d-%@",i,lvd0[i].text);
    }
    */
    NSString *ugdFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"upgrade.csv"]];
    upgradeContent= [NSString stringWithContentsOfFile:ugdFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"upgrade FIle:%@\n%@",DirFile,upgradeContent);
    
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:@"phototracq.com" user:@"<EMAIL>" password:@"4rfvCDE#2wsxZAQ!"];
    self.requestsManager.delegate=self;
    
    UISwipeGestureRecognizer *swipeLeft = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(didSwipe:)];
    swipeLeft.direction = UISwipeGestureRecognizerDirectionLeft;
    [self.view addGestureRecognizer:swipeLeft];
    
    UISwipeGestureRecognizer *swipeRight = [[UISwipeGestureRecognizer alloc] initWithTarget:self  action:@selector(didSwipe:)];
    swipeRight.direction = UISwipeGestureRecognizerDirectionRight;
    [self.view addGestureRecognizer:swipeRight];
    
}
-(void) updateTitle {
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *DirFile=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"class.csv"]];
    classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"class FIle:%@\n%@",DirFile,classContent);
    NSArray *splitLine = [classContent componentsSeparatedByString:@"\n"];
    for(int i=0;i<21;i++) {
        [btClass0[i] setImage:[UIImage imageNamed: @"ispeak-day_green.png"] forState:UIControlStateNormal];
        btClass0[i].hidden=TRUE;
        lvt0[i].hidden=TRUE;
        lvd0[i].hidden=TRUE;
    }
    for(int i=0;i<21;i++) {
        if([splitLine count] > (UPC.currentPage*21+i)) {
            NSArray *split1=[splitLine[UPC.currentPage*21+i] componentsSeparatedByString:@","];
            if([split1 count] > 2) {
                btClass0[i].hidden=FALSE;
                lvd0[i].hidden=FALSE;
                lvd0[i].text=split1[2];
                NSLog(@"class name :%d-%@",i,lvd0[i].text);
            }
        }
    }
}
- (void)didSwipe:(UISwipeGestureRecognizer*)swipe{
    
    if (swipe.direction == UISwipeGestureRecognizerDirectionLeft) {
        if(UPC.numberOfPages > UPC.currentPage) UPC.currentPage++;
        [self updateTitle];
        NSLog(@"Swipe Left");
    } else if (swipe.direction == UISwipeGestureRecognizerDirectionRight) {
        if(UPC.currentPage > 0) UPC.currentPage--;
        [self updateTitle];
        NSLog(@"Swipe Right");
    } else if (swipe.direction == UISwipeGestureRecognizerDirectionUp) {
        NSLog(@"Swipe Up");
    } else if (swipe.direction == UISwipeGestureRecognizerDirectionDown) {
        NSLog(@"Swipe Down");
    }
}
- (IBAction)btGotoNext:(id)sender {
    UIButton *button = (UIButton *)sender;
    _uclass = lvd0[button.tag].text;
    UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    Test01ViewController *myViewController = [storyboard instantiateViewControllerWithIdentifier:@"Test01ViewController"];
    [myViewController setValue:_uclass forKey:@"uclass"];
    [self presentViewController:myViewController animated:YES completion:nil];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
 */

- (IBAction)btUpgrade:(id)sender {
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    
    NSArray *splitLine = [upgradeContent componentsSeparatedByString:@"\n"];
    for(int i=0;i<[splitLine count];i++) {
    //for(int i=0;i<2;i++) {
        NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
        if([split1 count]>=4) {
            NSString *locZFile = [documentsDirectoryPath stringByAppendingPathComponent:split1[2]];
            NSString *remZFile=[@"/class/" stringByAppendingPathComponent:[NSString stringWithFormat:split1[2]]];
            NSLog(@"ZIP:%@",remZFile);
            [self.requestsManager addRequestForDownloadFileAtRemotePath:remZFile toLocalPath:locZFile];
            [self.requestsManager startProcessingRequests];
        }
    }
    indicator = [[UIActivityIndicatorView alloc]initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
    indicator.frame = CGRectMake(0.0, 0.0, 200.0, 200.0);
    indicator.center = self.view.center;
    [self.view addSubview:indicator];
    [indicator bringSubviewToFront:self.view];
    [UIApplication sharedApplication].networkActivityIndicatorVisible = TRUE;
    [indicator startAnimating];

}
- (IBAction)btBackHome:(id)sender {
    exit(0);
}
#pragma mark - GRRequestsManagerDelegate

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didScheduleRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didScheduleRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteListingRequest:(id<GRRequestProtocol>)request listing:(NSArray *)listing
{
    NSLog(@"requestsManager:didCompleteListingRequest:listing: \n%@", listing);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteCreateDirectoryRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteCreateDirectoryRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDeleteRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDeleteRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompletePercent:(float)percent forRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompletePercent:forRequest: %f", percent);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteUploadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteUploadRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDownloadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDownloadRequest:");
    GRListingRequest *req = (GRListingRequest *)request;
    NSLog(@"RETEMO FILE:%@",req.path);

}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailWritingFileAtPath:(NSString *)path forRequest:(id<GRDataExchangeRequestProtocol>)request error:(NSError *)error
{
    NSLog(@"requestsManager:didFailWritingFileAtPath:forRequest:error: \n %@", error);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailRequest:(id<GRRequestProtocol>)request withError:(NSError *)error
{
    NSLog(@"requestsManager:didFailRequest:withError: \n %@", error);
}

-(void)requestsManagerDidCompleteQueue:(id<GRRequestsManagerProtocol>)requestsManager{
    NSLog(@"整个文件夹下载完成+++++++++++++++++");
    
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *unclassPath=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//"]];
    //NSString *zipFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l1u1u4.zip"];
    //NSString *unzipPath=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//l1u1u4"]];
    NSArray *splitLine = [upgradeContent componentsSeparatedByString:@"\n"];
    
    for(int i=0;i<[splitLine count];i++) {
        NSArray *split1=[splitLine[i] componentsSeparatedByString:@","];
        if([split1 count]>=4) {
            NSString *zipZFile = [documentsDirectoryPath stringByAppendingPathComponent:split1[2]];
            NSString *unzipPath=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@",split1[1]]];
            NSLog(@"UNZIP:%@->%@",zipZFile,unzipPath);
            [[NSFileManager defaultManager] removeItemAtPath:unzipPath error:nil];
            [SSZipArchive unzipFileAtPath:zipZFile toDestination:unzipPath];
        }
    }
    NSLog(@"After FLIST:%@",[[NSFileManager defaultManager] contentsOfDirectoryAtPath:unclassPath error:NULL]);
    //remove dir
    //[[NSFileManager defaultManager] removeItemAtPath:unzipPath error:nil];
    //NSLog(@"Before FLIST:%@",[[NSFileManager defaultManager] contentsOfDirectoryAtPath:unclassPath error:NULL]);
    [indicator stopAnimating];
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"訊息" message:@"題庫下載完成,請重新登入才會套用新題庫" delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
    [alert show];
}
- (void) alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    exit(0);
}
@end
