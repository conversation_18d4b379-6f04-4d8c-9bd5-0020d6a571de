//
//  SayViewController.m
//  uten
//
//  Created by 簡大翔 on 2018/8/13.
//  Copyright © 2018年 bekubee. All rights reserved.
//

#import "SayViewController.h"
#import "ViewController.h"
#import "UIImage+Color.h"
#import "UIImage+Rotate.h"
#import "UIImage+SubImage.h"
#import "UIImage+Gif.h"

#import <OpenEars/OEPocketsphinxController.h>
#import <OpenEars/OEFliteController.h>
#import <OpenEars/OELanguageModelGenerator.h>
#import <OpenEars/OELogging.h>
#import <OpenEars/OEAcousticModel.h>
#import <Slt/Slt.h>
#import "MQTTClient.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "UTENEnum.h"

@interface SayViewController () <MQTTSessionDelegate> {
    MQTTSession *session;
        NSString *ID;
    IBOutlet UIButton *btStartTime;
    IBOutlet UIButton *btTimes;
    IBOutlet UIButton *btSingal;
    IBOutlet UILabel *laTimes;
    IBOutlet UILabel *laStatus;
    IBOutlet UILabel *lacWord;
    
    IBOutlet UILabel *laPass;
    IBOutlet UILabel *laNG;
    NSTimer *cTimer;
    IBOutlet UIImageView *lframe;
    IBOutlet UIImageView *mframe;
    IBOutlet UIImageView *rframe;


    int classvalue;
    int wcount;
    NSString *eword[4000];
    NSString *cword[4000];
    int song[4000];
    bool mark[4000];
    int timecnt;
    int test01;
    int nowIdx;
    AVAudioPlayer *_audioPlayer;
    int TotalPass;
    int TotalNG;
    BOOL Answer;
    
    NSString *ss[4000][16];
    int scnt[4000];
    
    UIImageView *W00[200];
    int SYSCount;
    int SYSType;  //1:SPLIT SAY 2:ALL SAY
    int SYSPARA1; //MIN LOOP (N);
    int SYSPARA2; //MAX LOOP (M)
    int SYSPARA3; //Random time(R)
    int SYSPARA4;
    NSString *DirTest;
    NSString *DirSound;
    NSString *DirWord;
    int SoundStep;
    int waitcnt;
    NSString *FL[4000];
    int FLCount;
    NSString *ServerIP;
    //
    //
    int pta[4000];
    int kpos;
    int kloop;
    int kstart;
    int kstop;
    Boolean kmark;
    Boolean krandom;
    NSString *ee[4000];
    NSString *cc[4000];
    NSString *uten_class,*r_uten_class;
    //
    int eecolor[200][20];
    NSArray *cda;
    int plen;
    int follow;
    int songdly;
}
// These three are the important OpenEars objects that this class demonstrates the use of.
@property (nonatomic, strong) Slt *slt;

@property (nonatomic, strong) OEEventsObserver *openEarsEventsObserver;
@property (nonatomic, strong) OEPocketsphinxController *pocketsphinxController;
@property (nonatomic, strong) OEFliteController *fliteController;
// Things which help us show off the dynamic language features.
@property (nonatomic, copy) NSString *pathToFirstDynamicallyGeneratedLanguageModel;
@property (nonatomic, copy) NSString *pathToFirstDynamicallyGeneratedDictionary;
@property (nonatomic, copy) NSString *pathToSecondDynamicallyGeneratedLanguageModel;
@property (nonatomic, copy) NSString *pathToSecondDynamicallyGeneratedDictionary;

@property (nonatomic, assign) BOOL usingStartingLanguageModel;
@property (nonatomic, assign) int restartAttemptsDueToPermissionRequests;
@property (nonatomic, assign) BOOL startupFailedDueToLackOfPermissions;
@end

@implementation SayViewController
-(void) MakeTest
{
    int rpos=kpos;
    for(int i=0;i<kloop;i++) {
        for(int j=kstart;j<kstop;j++) {
            eword[kpos]=ee[j];
            cword[kpos]=cc[j];
            song[kpos]=j;
            /*
            if(kmark) {
                if(i==0) mark[kpos]=true;
                else mark[kpos]=false;
            }
             */
            mark[kpos]=kmark;
            if(krandom) mark[kpos]=true;
            kpos++;
        }
    }
    if(krandom) {
        int rp,rc;
        rp=rpos;
        rc=kstop-kstart;
        // A Random x 2
        for(int i=0;i<kloop;i++)
            for(int j=kstart;j<kstop;j++) {
                NSString *tmp;
                int itmp;
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=rc-1) {
                        tmp=eword[rp+i*rc+j]; eword[rp+i*rc+j]=eword[rp+i*rc+j+1]; eword[rp+i*rc+j+1]=tmp;
                        tmp=cword[rp+i*rc+j]; cword[rp+i*rc+j]=cword[rp+i*rc+j+1]; cword[rp+i*rc+j+1]=tmp;
                        itmp=song[rp+i*rc+j]; song[rp+i*rc+j]=song[rp+i*rc+j+1];   song[rp+i*rc+j+1]=itmp;
                    } else {
                        tmp=eword[rp+i*rc+j]; eword[rp+i*rc+j]=eword[rp+i*rc]; eword[rp+i*rc]=tmp;
                        tmp=cword[rp+i*rc+j]; cword[rp+i*rc+j]=cword[rp+i*rc]; cword[rp+i*rc]=tmp;
                        itmp=song[rp+i*rc+j]; song[rp+i*rc+j]=song[rp+i*rc]; song[rp+i*rc]=itmp;
                    }
                }
            }
        
    }
}
-(NSArray *)listFileAtPath:(NSString *)path
{
    //-----> LIST ALL FILES <-----//
    NSLog(@"LISTING ALL FILES FOUND");
    int count;
    NSArray *directoryContent = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:path error:NULL];
    for (count = 0; count < (int)[directoryContent count]; count++)
    {
        
        NSNumber *fileSizeValue = nil;
        NSString *filePath=path;
        NSURL *fileURL = [NSURL fileURLWithPath:filePath];
        NSError *fileSizeError = nil;
        [fileURL getResourceValue:&fileSizeValue
                           forKey:NSURLFileSizeKey
                            error:&fileSizeError];
        NSLog(@"File %d: %@  %@", (count + 1),  [directoryContent objectAtIndex:count],fileSizeValue);
        /*
        if (fileSizeValue) {
        NSLog(@"File %d: %@  %@", (count + 1), [directoryContent objectAtIndex:count],fileSizeValue);
        } else {
            NSLog(@"File %d: %@", (count + 1), [directoryContent objectAtIndex:count]);
        }
        */
    }
    return directoryContent;
}
-(void) MakeWordDB
{
    int pt=0;
    NSArray *fda=[self listFileAtPath:[NSString stringWithFormat:@"%@",DirWord]];

    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *localFilePath=NULL;
        NSString *ssrc=cda[5+x*2];
        int cnt=0;
        NSString *xf[10];
        for(int i=0; i<[fda count]; ++i) {
            NSArray *cp = [fda[i] componentsSeparatedByString:@"_"];
            if([cp count] >=3) {
                NSString *cm=cp[0];
                if([cm isEqualToString:ssrc]) {
                    cnt++;
                     //   NSLog(@"CP File:%@:%@",fda[i],cp[2]);
                    if([cp[2] isEqualToString:@"1.csv"]) xf[0] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"2.csv"]) xf[1] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"3.csv"]) xf[2] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"4.csv"]) xf[3] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"5.csv"]) xf[4] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"6.csv"]) xf[5] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"7.csv"]) xf[6] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"8.csv"]) xf[7] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"9.csv"]) xf[8] = [[NSString alloc] initWithString:fda[i]];
                }
            }
        }
        for(int i=0;i<cnt;i++) NSLog(@"CP File:%@",xf[i]);
        for(int u=0;u<cnt;u++) {
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@",xf[u]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
 
            int sz=[split[4] intValue];
            int y=sz-1;
            cc[pt]=split[2];
            if([split[5+y*2] isEqualToString:@"*"]) ee[pt]=split[1];
            else ee[pt]=split[5+y*2];
            ss[pt][0]=split[6+y*2];
            scnt[pt]=0;
            int hc=0;
            int bc=0;
            for(int z=0;z<sz-1;z++) {
                NSString *eu=split[5+z*2];
                if([split[5+z*2] isEqualToString:@"*"]==false) {
                    for(int t=0;t<[eu length];t++)
                    eecolor[pt][hc++]=bc%2;
                    bc++;
                }
                ss[pt][z]=split[6+z*2];
                scnt[pt]++;
            }
            ss[pt][sz-1]=split[6+(sz-1)*2];
            scnt[pt]++;
            pt++;
        }
        pta[x]=pt;
    }

    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *localFilePath=NULL;
        NSString *ssrc=cda[5+x*2];
        int cnt=0;
        NSString *xf[10];
        for(int i=0; i<[fda count]; ++i) {
            NSArray *cp = [fda[i] componentsSeparatedByString:@"_"];
            if([cp count] >=3) {
                NSString *cm=cp[0];
                if([cm isEqualToString:ssrc]) {
                    cnt++;
                     //   NSLog(@"CP File:%@:%@",fda[i],cp[2]);
                    if([cp[2] isEqualToString:@"1.csv"]) xf[0] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"2.csv"]) xf[1] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"3.csv"]) xf[2] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"4.csv"]) xf[3] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"5.csv"]) xf[4] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"6.csv"]) xf[5] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"7.csv"]) xf[6] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"8.csv"]) xf[7] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"9.csv"]) xf[8] = [[NSString alloc] initWithString:fda[i]];
                }
            }
        }
        for(int i=0;i<cnt;i++) NSLog(@"CP File:%@",xf[i]);
        int u=cnt-1;
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@",xf[u]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
 
            int sz=[split[4] intValue];
            int y=sz-1;
            cc[pt]=split[2];
            if([split[5+y*2] isEqualToString:@"*"]) ee[pt]=split[1];
            else ee[pt]=split[5+y*2];
            ss[pt][0]=split[6+y*2];
            scnt[pt]=0;
            int hc=0;
            int bc=0;
            for(int z=0;z<sz-1;z++) {
                NSString *eu=split[5+z*2];
                if([split[5+z*2] isEqualToString:@"*"]==false) {
                    for(int t=0;t<[eu length];t++)
                    eecolor[pt][hc++]=bc%2;
                    bc++;
                }
                ss[pt][z]=split[6+z*2];
                scnt[pt]++;
            }
            ss[pt][sz-1]=split[6+(sz-1)*2];
            scnt[pt]++;
            pt++;
        //pta[x]=pt;
        pta[(cda.count-4)/2+x]=pt;
    }
}
-(void) MakeWordDB_01
{
    int pt=0;
    NSArray *fda=[self listFileAtPath:[NSString stringWithFormat:@"%@",DirWord]];


    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *localFilePath=NULL;
        NSString *ssrc=cda[5+x*2];
        int cnt=0;
        NSString *xf[10];
        for(int i=0; i<[fda count]; ++i) {
            NSArray *cp = [fda[i] componentsSeparatedByString:@"_"];
            if([cp count] >=3) {
                NSString *cm=cp[0];
                if([cm isEqualToString:ssrc]) {
                    cnt++;
                     //   NSLog(@"CP File:%@:%@",fda[i],cp[2]);
                    if([cp[2] isEqualToString:@"1.csv"]) xf[0] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"2.csv"]) xf[1] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"3.csv"]) xf[2] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"4.csv"]) xf[3] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"5.csv"]) xf[4] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"6.csv"]) xf[5] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"7.csv"]) xf[6] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"8.csv"]) xf[7] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"9.csv"]) xf[8] = [[NSString alloc] initWithString:fda[i]];
                }
            }
        }
        for(int i=0;i<cnt;i++) NSLog(@"CP File:%@",xf[i]);
        int u=cnt-1;
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@",xf[u]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
 
            int sz=[split[4] intValue];
            int y=sz-1;
            cc[pt]=split[2];
            if([split[5+y*2] isEqualToString:@"*"]) ee[pt]=split[1];
            else ee[pt]=split[5+y*2];
            ss[pt][0]=split[6+y*2];
            scnt[pt]=0;
            int hc=0;
            int bc=0;
            for(int z=0;z<sz-1;z++) {
                NSString *eu=split[5+z*2];
                if([split[5+z*2] isEqualToString:@"*"]==false) {
                    for(int t=0;t<[eu length];t++)
                    eecolor[pt][hc++]=bc%2;
                    bc++;
                }
                ss[pt][z]=split[6+z*2];
                scnt[pt]++;
            }
            ss[pt][sz-1]=split[6+(sz-1)*2];
            scnt[pt]++;
            pt++;
        pta[x]=pt;
       // pta[(da.count-4)/2+x]=pt;
    }
}
- (void)viewDidLoad {


    [super viewDidLoad];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    // Do any additional setup after loading the view.
    NSLog(@"%@_%@", _seltag,_uclass);
    NSLog(@"%@", _seltag);
    int cType=[_cType intValue];
    int index=[_seltag intValue];
    cType=11;
    int loop=100;
    int st=18;
    int se=20;
    NSString *tmp;
    int itmp;
    bool imark;
    follow=0;
    songdly=0;
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    ID = [defaults objectForKey:@"ID"];
    for(int i=0;i<200;i++) {
        W00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        W00[i].image=[UIImage imageNamed:@"a1.png"];
        W00[i].alpha=0.9f;
        [self.view addSubview:W00[i]];
        W00[i].hidden=YES;
    }
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
     transport.host = ServerIP;
     transport.port = 1883;
     
     session = [[MQTTSession alloc] init];
     session.transport = transport;
     session.delegate=self;
     [session connectWithConnectHandler:^(NSError *error) {
        // Do some work
         
        [self publishCommandClassStart];
         
        NSLog(@"Subscription %@",uten_class);
        [session subscribeToTopic:uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                NSLog(@"Subscription failed %@", error.localizedDescription);
            } else {
                NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
            }
        }];

     }];
    
    
     NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
     DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",_uclass,_uclass]];
     DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
     DirWord=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//word//",_uclass,_uclass]];
     
     //@"[up3u5u8][寫3][tall][short][old][young]"
     
     NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
     NSArray *da = [_seltag componentsSeparatedByCharactersInSet:set];
    cda = [_seltag componentsSeparatedByCharactersInSet:set];
    int pt=0;
    wcount=0;
     if ([da[3] isEqualToString:@"說字3"]) {
         [self MakeWordDB];
         kpos=0;
         int pall=(cda.count-4)/2;
         int stl=0;
         int jp,jpl;
         for(int j=0;j<pall;j++) {
             for(int i=stl;i<pta[j];i++) {
                 kloop=4; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
             }
             stl=pta[j];
             jp=j;
             jpl=stl;
         }
         for(int k=0;k<4;k++) {
             stl=jpl;
             for(int j=jp;j<pall*2;j++) {
                 for(int i=stl;i<pta[j];i++) {
                     kloop=1; kstart=i; kstop=i+1;
                     kmark=true;
                     /*
                     if(k==0) kmark=true;
                     else kmark=false;
                      */
                     krandom=false; [self MakeTest];
                 }
                 stl=pta[j];
             }
         }
         wcount=kpos;
     }
     if ([da[3] isEqualToString:@"說字6"]) {
         [self MakeWordDB];
         kpos=0;
         int pall=(cda.count-4)/2;
         int stl=0;
         int jp,jpl;
         for(int j=0;j<pall;j++) {
             for(int i=stl;i<pta[j];i++) {
                 kloop=7; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
             }
             stl=pta[j];
             jp=j;
             jpl=stl;
         }
         for(int k=0;k<7;k++) {
             stl=jpl;
             for(int j=jp;j<pall*2;j++) {
                 for(int i=stl;i<pta[j];i++) {
                     kloop=1; kstart=i; kstop=i+1;
                     kmark=true;
                     krandom=false; [self MakeTest];
                 }
                 stl=pta[j];
             }
         }
         wcount=kpos;
     }
     if ([da[3] isEqualToString:@"說字9"]) {
         [self MakeWordDB];
         kpos=0;
         int pall=(cda.count-4)/2;
         int stl=0;
         int jp,jpl;
         for(int j=0;j<pall;j++) {
             for(int i=stl;i<pta[j];i++) {
                 kloop=10; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
             }
             stl=pta[j];
             jp=j;
             jpl=stl;
         }
         for(int k=0;k<10;k++) {
             stl=jpl;
             for(int j=jp;j<pall*2;j++) {
                 for(int i=stl;i<pta[j];i++) {
                     kloop=1; kstart=i; kstop=i+1;
                     kmark=true;
                     krandom=false; [self MakeTest];
                 }
                 stl=pta[j];
             }
         }
         wcount=kpos;
     }
     if ([da[3] isEqualToString:@"說字1順"]) {
         for(int i=0;i<(da.count-4)/2;i++) {
             NSString *localFilePath=NULL;
             for(int j=0;j<10;j++) {
                 localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                 if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
             }
             NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
             NSArray *split = [str componentsSeparatedByString:@","];
             NSLog(@"ee:%@",str);
             ee[i]=split[1];
             cc[i]=split[2];
             scnt[i]=[split[4] intValue];
             for(int j=0;j<scnt[i];j++) {
                 ss[i][j]=split[6+j*2];
             }
         }
         int cnt=(da.count-4)/2;
         int pos=0;
         for(int j=0;j<cnt;j++) {
                 eword[pos]=ee[j];
                 cword[pos]=cc[j];
                 song[pos]=j;
                 mark[pos]=true;
                 //if(i==0) mark[pos]=true;
                 //else mark[pos]=false;
                 pos++;
         }
         wcount=cnt;
         follow=1;
         songdly=0;
     }
    if ([da[3] isEqualToString:@"說字1順(計)"]) {
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
        int cnt=(da.count-4)/2;
        int pos=0;
        for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=true;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
        }
        wcount=cnt;
        follow=1;
        songdly=0;
    }
    //
    if ([da[3] isEqualToString:@"說拼3"]) {
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=4; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<4;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    kmark=true;
                    /*
                    if(k==0) kmark=true;
                    else kmark=false;
                     */
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([da[3] isEqualToString:@"說拼6"]) {
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=7; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<7;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    kmark=true;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([da[3] isEqualToString:@"說拼9"]) {
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=10; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<10;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    kmark=true;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([da[3] isEqualToString:@"說拼1順"]) {
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
        int cnt=(da.count-4)/2;
        int pos=0;
        for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=true;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
        }
        wcount=cnt;
        follow=1;
        songdly=0;
    }
    if ([da[3] isEqualToString:@"說拼1順(計)"]) {
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
        int cnt=(da.count-4)/2;
        int pos=0;
        for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=true;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
        }
        wcount=cnt;
        follow=1;
        songdly=0;
    }
    
    
    if ([da[3] isEqualToString:@"說句1順"]) {
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
        int cnt=(da.count-4)/2;
        int pos=0;
        for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=true;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
        }
        wcount=cnt;
        follow=1;
        songdly=0;
    }
    
     if ([da[3] isEqualToString:@"說句1亂"]) {
         for(int i=0;i<(da.count-4)/2;i++) {
             NSString *localFilePath=NULL;
             for(int j=0;j<10;j++) {
                 localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                 if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
             }
             NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
             NSArray *split = [str componentsSeparatedByString:@","];
             NSLog(@"ee:%@",str);
             ee[i]=split[1];
             cc[i]=split[2];
             scnt[i]=[split[4] intValue];
             for(int j=0;j<scnt[i];j++) {
                 ss[i][j]=split[6+j*2];
             }
         }
         int cnt=(da.count-4)/2;
         int pos=0;
             for(int j=0;j<cnt;j++) {
                 eword[pos]=ee[j];
                 cword[pos]=cc[j];
                 song[pos]=j;
                 mark[pos]=true;
                 //if(i==0) mark[pos]=true;
                 //else mark[pos]=false;
                 pos++;
             }
         pos=0; int i=0;
         for(int j=0;j<cnt;j++) {
             NSString *tmp;
             int itmp;
                 if(arc4random_uniform(2)==1) { //swap
                     if(j!=cnt-1) {
                         tmp=eword[pos+i*cnt+j]; eword[pos+i*cnt+j]=eword[pos+i*cnt+j+1]; eword[pos+i*cnt+j+1]=tmp;
                         tmp=cword[pos+i*cnt+j]; cword[pos+i*cnt+j]=cword[pos+i*cnt+j+1]; cword[pos+i*cnt+j+1]=tmp;
                         itmp=song[pos+i*cnt+j]; song[pos+i*cnt+j]=song[pos+i*cnt+j+1]; song[pos+i*cnt+j+1]=itmp;
                     } else {
                         tmp=eword[pos+i*cnt+j]; eword[pos+i*cnt+j]=eword[pos+i*cnt]; eword[pos+i*cnt]=tmp;
                         tmp=cword[pos+i*cnt+j]; cword[pos+i*cnt+j]=cword[pos+i*cnt]; cword[pos+i*cnt]=tmp;
                         itmp=song[pos+i*cnt+j]; song[pos+i*cnt+j]=song[pos+i*cnt]; song[pos+i*cnt]=itmp;
                     }
                 }
         }
         wcount=cnt;
         follow=1;
         songdly=0;
     }
    //DB
    if ([da[3] isEqualToString:@"說拼1亂(計)"]) {
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
        int cnt=(da.count-4)/2;
        int pos=0;
            for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=true;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
            }
        pos=0; int i=0;
        for(int j=0;j<cnt;j++) {
            NSString *tmp;
            int itmp;
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=cnt-1) {
                        tmp=eword[pos+i*cnt+j]; eword[pos+i*cnt+j]=eword[pos+i*cnt+j+1]; eword[pos+i*cnt+j+1]=tmp;
                        tmp=cword[pos+i*cnt+j]; cword[pos+i*cnt+j]=cword[pos+i*cnt+j+1]; cword[pos+i*cnt+j+1]=tmp;
                        itmp=song[pos+i*cnt+j]; song[pos+i*cnt+j]=song[pos+i*cnt+j+1]; song[pos+i*cnt+j+1]=itmp;
                    } else {
                        tmp=eword[pos+i*cnt+j]; eword[pos+i*cnt+j]=eword[pos+i*cnt]; eword[pos+i*cnt]=tmp;
                        tmp=cword[pos+i*cnt+j]; cword[pos+i*cnt+j]=cword[pos+i*cnt]; cword[pos+i*cnt]=tmp;
                        itmp=song[pos+i*cnt+j]; song[pos+i*cnt+j]=song[pos+i*cnt]; song[pos+i*cnt]=itmp;
                    }
                }
        }
        wcount=cnt;
        follow=1;
        songdly=0;
    }

     cTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
     TotalPass=0;
     TotalNG=0;
     laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
    //laPass.text=[[NSString stringWithFormat:@"%04d",TotalPass];
                
    ///
    self.fliteController = [[OEFliteController alloc] init];
    self.openEarsEventsObserver = [[OEEventsObserver alloc] init];
    self.openEarsEventsObserver.delegate = self;
    self.slt = [[Slt alloc] init];
    [OELogging startOpenEarsLogging]; // Uncomment me for OELogging, which is verbose logging about internal OpenEars operations such as audio settings. If you have issues, show this logging in the forums.
    [OEPocketsphinxController sharedInstance].verbosePocketSphinx = TRUE; // Uncomment this for much more verbose speech recognition engine output. If you have issues, show this logging in the forums.
    
    [self.openEarsEventsObserver setDelegate:self]; // Make this class the delegate of OpenEarsObserver so we can get all of the messages about what OpenEars is doing.
    
    [[OEPocketsphinxController sharedInstance] setActive:TRUE error:nil]; // Call this before setting any OEPocketsphinxController characteristics
    /*
    NSArray *firstLanguageArray = @[eword[0], // There is no case requirement in OpenEars,
                                    eword[1], // so these can be uppercase, lowercase, or mixed case.
                                    eword[2],,
                                    eword[3]];
     */
    
    NSMutableArray *firstLanguageArray = [[NSMutableArray alloc] init];
    for(int i=0; i< (da.count-4)/2 ; i++){
        [firstLanguageArray addObject:ee[i]];
    }
    OELanguageModelGenerator *languageModelGenerator = [[OELanguageModelGenerator alloc] init];
    
    // languageModelGenerator.verboseLanguageModelGenerator = TRUE; // Uncomment me for verbose language model generator debug output.
    
    NSError *error = [languageModelGenerator generateLanguageModelFromArray:firstLanguageArray withFilesNamed:@"FirstOpenEarsDynamicLanguageModel" forAcousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"]]; // Change "AcousticModelEnglish" to "AcousticModelSpanish" in order to create a language model for Spanish recognition instead of English.
    if(error) {
        NSLog(@"Dynamic language generator reported error %@", [error description]);
    } else {
        self.pathToFirstDynamicallyGeneratedLanguageModel = [languageModelGenerator pathToSuccessfullyGeneratedLanguageModelWithRequestedName:@"FirstOpenEarsDynamicLanguageModel"];
        self.pathToFirstDynamicallyGeneratedDictionary = [languageModelGenerator pathToSuccessfullyGeneratedDictionaryWithRequestedName:@"FirstOpenEarsDynamicLanguageModel"];
    }
}
-(NSString *) getWordPng:(NSString *)str
{
    if([str isEqualToString:@"a"]) return @"a1.png";
    if([str isEqualToString:@"b"]) return @"b1.png";
    if([str isEqualToString:@"c"]) return @"c1.png";
    if([str isEqualToString:@"d"]) return @"d1.png";
    if([str isEqualToString:@"e"]) return @"e1.png";
    if([str isEqualToString:@"f"]) return @"f1.png";
    if([str isEqualToString:@"g"]) return @"g1.png";
    if([str isEqualToString:@"h"]) return @"h1.png";
    if([str isEqualToString:@"i"]) return @"i1.png";
    if([str isEqualToString:@"j"]) return @"j1.png";
    if([str isEqualToString:@"k"]) return @"k1.png";
    if([str isEqualToString:@"l"]) return @"l1.png";
    if([str isEqualToString:@"m"]) return @"m1.png";
    if([str isEqualToString:@"n"]) return @"n1.png";
    if([str isEqualToString:@"o"]) return @"o1.png";
    if([str isEqualToString:@"p"]) return @"p1.png";
    if([str isEqualToString:@"q"]) return @"q1.png";
    if([str isEqualToString:@"r"]) return @"r1.png";
    if([str isEqualToString:@"s"]) return @"s1.png";
    if([str isEqualToString:@"t"]) return @"t1.png";
    if([str isEqualToString:@"u"]) return @"u1.png";
    if([str isEqualToString:@"v"]) return @"v1.png";
    if([str isEqualToString:@"w"]) return @"w1.png";
    if([str isEqualToString:@"x"]) return @"x1.png";
    if([str isEqualToString:@"y"]) return @"y1.png";
    if([str isEqualToString:@"z"]) return @"z1.png";
    return @"";
}
- (void) UpdateFrame {
    CGRect frame;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    for(int i=0;i<20;i++) {
        W00[i].hidden=YES;
    }
    frame = lframe.frame; frame.origin.y=280;
    frame.origin.x= x;
    frame.size.width=24;
    frame.size.height=80;
    lframe.frame= frame;
    
    frame = mframe.frame; frame.origin.y=280;
    frame.origin.x= x+24;
    frame.size.width=30*len-10;
    
    //frame.size.width=24;
    frame.size.height=80;
    mframe.frame= frame;
    
    frame = rframe.frame; frame.origin.y=280;
    frame.origin.x= x+30*len;
    frame.size.width=24;
    frame.size.height=80;
    
    rframe.frame= frame;
    
    frame = laTimes.frame; frame.origin.y=220;
    frame.origin.x= x;
    laTimes.frame= frame;
    
    frame = btTimes.frame; frame.origin.y=220;
    frame.origin.x= x;
    btTimes.frame= frame;
    
    frame = laStatus.frame; frame.origin.y=420;
    frame.origin.x= x;
    laStatus.frame= frame;

    frame = lacWord.frame; frame.origin.y=360;
    frame.origin.x= x;
    lacWord.frame= frame;

    frame = btSingal.frame; frame.origin.y=220;
    frame.origin.x= x+30*len-20;
    btSingal.frame= frame;
    btSingal.hidden=YES;
    lframe.hidden=NO;
    mframe.hidden=NO;
    rframe.hidden=NO;
    laTimes.hidden=YES;
    laStatus.hidden=NO;
    lacWord.hidden=NO;
    btTimes.hidden=YES;
    /*
    NSString *path = [NSString stringWithFormat:@"%@/%@_2e.mp3", [[NSBundle mainBundle] resourcePath],eword[nowIdx]];
    NSURL *soundUrl = [NSURL fileURLWithPath:path];
    // Create audio player object and initialize with URL to sound
    _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
    [_audioPlayer play];
    */
    
}
- (void) UpdateWord {
    NSString *oneWord;
    CGRect frame;
    NSRange needleRange;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    NSLog(@"EEWORD:%@",eword[nowIdx]);
    for(int i=0;i<20;i++) {
        W00[i].hidden=YES;
        if(len >= (i+1)) {
            frame = W00[i].frame; frame.origin.y=283;
            frame.origin.x= x+12+i*30;
            frame.size.width=25;
            frame.size.height=75;
            W00[i].frame= frame;
            needleRange = NSMakeRange(i,1);
            oneWord=[eword[nowIdx] substringWithRange:needleRange];
            W00[i].image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
            W00[i].hidden=false; //!mark[nowIdx];

        }
    }
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
    
}
-(void)timesUp:(NSTimer *)timer{
    timecnt++;
    switch(timecnt) {
        case 5: //0.5
            Answer=false;
            laTimes.text=[NSString stringWithFormat:@"%d",test01];
            lacWord.text=cword[nowIdx];
            laStatus.text=@"";
            
            [self UpdateFrame];
            [self UpdateWord];
            lacWord.hidden=NO;
            self.usingStartingLanguageModel = TRUE;
            if(![OEPocketsphinxController sharedInstance].isListening) {
                [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't already listening.
            }
            //if(mark[nowIdx]==false) timecnt=7;
            SoundStep=0;
            break;
        case 6:
            {
                NSString *base = DirSound;
                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[song[nowIdx]][SoundStep]];
                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
                if(mark[nowIdx])
                    [_audioPlayer setVolume:1.0];
                else [_audioPlayer setVolume:0.0];
                //if(wcount == 76) [_audioPlayer setVolume:1.0];
                //else [_audioPlayer setVolume:0.0];
                [_audioPlayer play];
            }
            break;
        case 7:
            songdly++;
            if(_audioPlayer.isPlaying) timecnt=6;
            else {
                SoundStep++;
                if(SoundStep < scnt[song[nowIdx]]) timecnt=5;
                //if(SoundStep < scnt[nowIdx]) timecnt=5;
            }
            break;
        case 8:
            if(follow) {
                if(songdly > 0) {
                    songdly--;
                    timecnt=7;
                }
            }
            break;
        case 15:
            {
                NSError *error = nil;
                if([OEPocketsphinxController sharedInstance].isListening) { // Stop if we are currently listening.
                    error = [[OEPocketsphinxController sharedInstance] stopListening];
                    if(error)NSLog(@"Error stopping listening in stopButtonAction: %@", error);
                }
            }
            if(Answer) {
                [btSingal setImage:[UIImage imageNamed:@"O_Red.png"] forState:UIControlStateNormal];
                btSingal.hidden=NO;
                TotalPass++;
            } else {
                [btSingal setImage:[UIImage imageNamed:@"wait_red.png"] forState:UIControlStateNormal];
                btSingal.hidden=NO;
                TotalNG++;
            }
            laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
            laPass.text=[NSString stringWithFormat:@"%04d",TotalPass];
            waitcnt=SYSPARA4;
            timecnt=54;
            break;
        case 55:
            if(nowIdx==(wcount-1)) timecnt=99;
            else {
                if(waitcnt==0) {
                    nowIdx++;
                    timecnt=4;
                } else {
                    timecnt=54;
                    waitcnt--;
                }
            }
            break;

        case 100:
            {
                /*
                UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"本單元測試結束" delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
                [alertView show];
                */
                [cTimer invalidate];
                cTimer = nil;
                [self publishCommandClassStop];
                [self dismissViewControllerAnimated:NO completion:nil];
            }
            break;
        case 102:
            timecnt=101;
            break;
            
    }
    //NSLog(@"TimeUp");
}
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    [cTimer invalidate];
    cTimer = nil;
    [self dismissViewControllerAnimated:NO completion:nil];
    
}
- (IBAction)btBack:(id)sender {
    /*
    [cTimer invalidate];
    cTimer = nil;
    [session publishData:[[[NSString alloc] initWithFormat:(@"$%@,CLASS_STOP,%@,end~"),uten_class,ID] dataUsingEncoding:NSUTF8StringEncoding] onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
     }];
    [self dismissViewControllerAnimated:NO completion:nil];
     */
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
#pragma mark -
#pragma mark OEEventsObserver delegate methods

// What follows are all of the delegate methods you can optionally use once you've instantiated an OEEventsObserver and set its delegate to self.
// I've provided some pretty granular information about the exact phase of the Pocketsphinx listening loop, the Audio Session, and Flite, but I'd expect
// that the ones that will really be needed by most projects are the following:
//
//- (void) pocketsphinxDidReceiveHypothesis:(NSString *)hypothesis recognitionScore:(NSString *)recognitionScore utteranceID:(NSString *)utteranceID;
//- (void) audioSessionInterruptionDidBegin;
//- (void) audioSessionInterruptionDidEnd;
//- (void) audioRouteDidChangeToRoute:(NSString *)newRoute;
//- (void) pocketsphinxDidStartListening;
//- (void) pocketsphinxDidStopListening;
//
// It isn't necessary to have a OEPocketsphinxController or a OEFliteController instantiated in order to use these methods.  If there isn't anything instantiated that will
// send messages to an OEEventsObserver, all that will happen is that these methods will never fire.  You also do not have to create a OEEventsObserver in
// the same class or view controller in which you are doing things with a OEPocketsphinxController or OEFliteController; you can receive updates from those objects in
// any class in which you instantiate an OEEventsObserver and set its delegate to self.


// This is an optional delegate method of OEEventsObserver which delivers the text of speech that Pocketsphinx heard and analyzed, along with its accuracy score and utterance ID.
- (void) pocketsphinxDidReceiveHypothesis:(NSString *)hypothesis recognitionScore:(NSString *)recognitionScore utteranceID:(NSString *)utteranceID {
    
    NSLog(@"Local callback: The received hypothesis is %@ with a score of %@ and an ID of %@", hypothesis, recognitionScore, utteranceID); // Log it.
    if([hypothesis isEqualToString:@"change model"]) { // If the user says "change model", we will switch to the alternate model (which happens to be the dynamically generated model).
        
        // Here is an example of language model switching in OpenEars. Deciding on what logical basis to switch models is your responsibility.
        // For instance, when you call a customer service line and get a response tree that takes you through different options depending on what you say to it,
        // the models are being switched as you progress through it so that only relevant choices can be understood. The construction of that logical branching and
        // how to react to it is your job; OpenEars just lets you send the signal to switch the language model when you've decided it's the right time to do so.
        
        if(self.usingStartingLanguageModel) { // If we're on the starting model, switch to the dynamically generated one.
            
            [[OEPocketsphinxController sharedInstance] changeLanguageModelToFile:self.pathToSecondDynamicallyGeneratedLanguageModel withDictionary:self.pathToSecondDynamicallyGeneratedDictionary];
            self.usingStartingLanguageModel = FALSE;
            
        } else { // If we're on the dynamically generated model, switch to the start model (this is an example of a trigger and method for switching models).
            
            [[OEPocketsphinxController sharedInstance] changeLanguageModelToFile:self.pathToFirstDynamicallyGeneratedLanguageModel withDictionary:self.pathToFirstDynamicallyGeneratedDictionary];
            self.usingStartingLanguageModel = TRUE;
        }
    }
    
    ///self.heardTextView.text = [NSString stringWithFormat:@"Heard: \"%@\"", hypothesis]; // Show it in the status box.
    
    // This is how to use an available instance of OEFliteController. We're going to repeat back the command that we heard with the voice we've chosen.
    //[self.fliteController say:[NSString stringWithFormat:@"You said %@",hypothesis] withVoice:self.slt];
    NSLog(@"JAMES Say:%@",hypothesis);
    if([hypothesis isEqualToString:eword[nowIdx]]) Answer=true;
}

#ifdef kGetNbest
- (void) pocketsphinxDidReceiveNBestHypothesisArray:(NSArray *)hypothesisArray { // Pocketsphinx has an n-best hypothesis dictionary.
    NSLog(@"Local callback:  hypothesisArray is %@",hypothesisArray);
}
#endif
// An optional delegate method of OEEventsObserver which informs that there was an interruption to the audio session (e.g. an incoming phone call).
- (void) audioSessionInterruptionDidBegin {
    NSLog(@"Local callback:  AudioSession interruption began."); // Log it.
    ///self.statusTextView.text = @"Status: AudioSession interruption began."; // Show it in the status box.
    NSError *error = nil;
    if([OEPocketsphinxController sharedInstance].isListening) {
        error = [[OEPocketsphinxController sharedInstance] stopListening]; // React to it by telling Pocketsphinx to stop listening (if it is listening) since it will need to restart its loop after an interruption.
        if(error) NSLog(@"Error while stopping listening in audioSessionInterruptionDidBegin: %@", error);
    }
}

// An optional delegate method of OEEventsObserver which informs that the interruption to the audio session ended.
- (void) audioSessionInterruptionDidEnd {
    NSLog(@"Local callback:  AudioSession interruption ended."); // Log it.
    ///self.statusTextView.text = @"Status: AudioSession interruption ended."; // Show it in the status box.
    // We're restarting the previously-stopped listening loop.
    if(![OEPocketsphinxController sharedInstance].isListening){
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't currently listening.
    }
}

// An optional delegate method of OEEventsObserver which informs that the audio input became unavailable.
- (void) audioInputDidBecomeUnavailable {
    NSLog(@"Local callback:  The audio input has become unavailable"); // Log it.
    ///self.statusTextView.text = @"Status: The audio input has become unavailable"; // Show it in the status box.
    NSError *error = nil;
    if([OEPocketsphinxController sharedInstance].isListening){
        error = [[OEPocketsphinxController sharedInstance] stopListening]; // React to it by telling Pocketsphinx to stop listening since there is no available input (but only if we are listening).
        if(error) NSLog(@"Error while stopping listening in audioInputDidBecomeUnavailable: %@", error);
    }
}

// An optional delegate method of OEEventsObserver which informs that the unavailable audio input became available again.
- (void) audioInputDidBecomeAvailable {
    NSLog(@"Local callback: The audio input is available"); // Log it.
    ///self.statusTextView.text = @"Status: The audio input is available"; // Show it in the status box.
    if(![OEPocketsphinxController sharedInstance].isListening) {
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition, but only if we aren't already listening.
    }
}
// An optional delegate method of OEEventsObserver which informs that there was a change to the audio route (e.g. headphones were plugged in or unplugged).
- (void) audioRouteDidChangeToRoute:(NSString *)newRoute {
    NSLog(@"Local callback: Audio route change. The new audio route is %@", newRoute); // Log it.
    ///self.statusTextView.text = [NSString stringWithFormat:@"Status: Audio route change. The new audio route is %@",newRoute]; // Show it in the status box.
    
    NSError *error = [[OEPocketsphinxController sharedInstance] stopListening]; // React to it by telling the Pocketsphinx loop to shut down and then start listening again on the new route
    
    if(error)NSLog(@"Local callback: error while stopping listening in audioRouteDidChangeToRoute: %@",error);
    
    if(![OEPocketsphinxController sharedInstance].isListening) {
        [[OEPocketsphinxController sharedInstance] startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"] languageModelIsJSGF:FALSE]; // Start speech recognition if we aren't already listening.
    }
}

// An optional delegate method of OEEventsObserver which informs that the Pocketsphinx recognition loop has entered its actual loop.
// This might be useful in debugging a conflict between another sound class and Pocketsphinx.
- (void) pocketsphinxRecognitionLoopDidStart {
    
    NSLog(@"Local callback: Pocketsphinx started."); // Log it.
    ///self.statusTextView.text = @"Status: Pocketsphinx started."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx is now listening for speech.
- (void) pocketsphinxDidStartListening {
    
    NSLog(@"Local callback: Pocketsphinx is now listening."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx is now listening."; // Show it in the status box.
    
//    self.startButton.hidden = TRUE; // React to it with some UI changes.
//    self.stopButton.hidden = FALSE;
//    self.suspendListeningButton.hidden = FALSE;
//    self.resumeListeningButton.hidden = TRUE;
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx detected speech and is starting to process it.
- (void) pocketsphinxDidDetectSpeech {
    NSLog(@"Local callback: Pocketsphinx has detected speech."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has detected speech."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx detected a second of silence, indicating the end of an utterance.
// This was added because developers requested being able to time the recognition speed without the speech time. The processing time is the time between
// this method being called and the hypothesis being returned.
- (void) pocketsphinxDidDetectFinishedSpeech {
    NSLog(@"Local callback: Pocketsphinx has detected a second of silence, concluding an utterance."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has detected finished speech."; // Show it in the status box.
}


// An optional delegate method of OEEventsObserver which informs that Pocketsphinx has exited its recognition loop, most
// likely in response to the OEPocketsphinxController being told to stop listening via the stopListening method.
- (void) pocketsphinxDidStopListening {
    NSLog(@"Local callback: Pocketsphinx has stopped listening."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has stopped listening."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx is still in its listening loop but it is not
// Going to react to speech until listening is resumed.  This can happen as a result of Flite speech being
// in progress on an audio route that doesn't support simultaneous Flite speech and Pocketsphinx recognition,
// or as a result of the OEPocketsphinxController being told to suspend recognition via the suspendRecognition method.
- (void) pocketsphinxDidSuspendRecognition {
    NSLog(@"Local callback: Pocketsphinx has suspended recognition."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has suspended recognition."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Pocketsphinx is still in its listening loop and after recognition
// having been suspended it is now resuming.  This can happen as a result of Flite speech completing
// on an audio route that doesn't support simultaneous Flite speech and Pocketsphinx recognition,
// or as a result of the OEPocketsphinxController being told to resume recognition via the resumeRecognition method.
- (void) pocketsphinxDidResumeRecognition {
    NSLog(@"Local callback: Pocketsphinx has resumed recognition."); // Log it.
//    self.statusTextView.text = @"Status: Pocketsphinx has resumed recognition."; // Show it in the status box.
}

// An optional delegate method which informs that Pocketsphinx switched over to a new language model at the given URL in the course of
// recognition. This does not imply that it is a valid file or that recognition will be successful using the file.
- (void) pocketsphinxDidChangeLanguageModelToFile:(NSString *)newLanguageModelPathAsString andDictionary:(NSString *)newDictionaryPathAsString {
    NSLog(@"Local callback: Pocketsphinx is now using the following language model: \n%@ and the following dictionary: %@",newLanguageModelPathAsString,newDictionaryPathAsString);
}

// An optional delegate method of OEEventsObserver which informs that Flite is speaking, most likely to be useful if debugging a
// complex interaction between sound classes. You don't have to do anything yourself in order to prevent Pocketsphinx from listening to Flite talk and trying to recognize the speech.
- (void) fliteDidStartSpeaking {
    NSLog(@"Local callback: Flite has started speaking"); // Log it.
 //   self.statusTextView.text = @"Status: Flite has started speaking."; // Show it in the status box.
}

// An optional delegate method of OEEventsObserver which informs that Flite is finished speaking, most likely to be useful if debugging a
// complex interaction between sound classes.
- (void) fliteDidFinishSpeaking {
    NSLog(@"Local callback: Flite has finished speaking"); // Log it.
 //   self.statusTextView.text = @"Status: Flite has finished speaking."; // Show it in the status box.
}

- (void) pocketSphinxContinuousSetupDidFailWithReason:(NSString *)reasonForFailure { // This can let you know that something went wrong with the recognition loop startup. Turn on [OELogging startOpenEarsLogging] to learn why.
    NSLog(@"Local callback: Setting up the continuous recognition loop has failed for the reason %@, please turn on [OELogging startOpenEarsLogging] to learn more.", reasonForFailure); // Log it.
 //   self.statusTextView.text = @"Status: Not possible to start recognition loop."; // Show it in the status box.
}

- (void) pocketSphinxContinuousTeardownDidFailWithReason:(NSString *)reasonForFailure { // This can let you know that something went wrong with the recognition loop startup. Turn on [OELogging startOpenEarsLogging] to learn why.
    NSLog(@"Local callback: Tearing down the continuous recognition loop has failed for the reason %@, please turn on [OELogging startOpenEarsLogging] to learn more.", reasonForFailure); // Log it.
 //   self.statusTextView.text = @"Status: Not possible to cleanly end recognition loop."; // Show it in the status box.
}

- (void) testRecognitionCompleted { // A test file which was submitted for direct recognition via the audio driver is done.
    NSLog(@"Local callback: A test file which was submitted for direct recognition via the audio driver is done."); // Log it.
    NSError *error = nil;
    if([OEPocketsphinxController sharedInstance].isListening) { // If we're listening, stop listening.
        error = [[OEPocketsphinxController sharedInstance] stopListening];
        if(error) NSLog(@"Error while stopping listening in testRecognitionCompleted: %@", error);
    }
    
}
/** Pocketsphinx couldn't start because it has no mic permissions (will only be returned on iOS7 or later).*/
- (void) pocketsphinxFailedNoMicPermissions {
    NSLog(@"Local callback: The user has never set mic permissions or denied permission to this app's mic, so listening will not start.");
    self.startupFailedDueToLackOfPermissions = TRUE;
    if([OEPocketsphinxController sharedInstance].isListening){
        NSError *error = [[OEPocketsphinxController sharedInstance] stopListening]; // Stop listening if we are listening.
        if(error) NSLog(@"Error while stopping listening in micPermissionCheckCompleted: %@", error);
    }
}

/** The user prompt to get mic permissions, or a check of the mic permissions, has completed with a TRUE or a FALSE result  (will only be returned on iOS7 or later).*/
- (void) micPermissionCheckCompleted:(BOOL)result {
    if(result) {
        self.restartAttemptsDueToPermissionRequests++;
        if(self.restartAttemptsDueToPermissionRequests == 1 && self.startupFailedDueToLackOfPermissions) { // If we get here because there was an attempt to start which failed due to lack of permissions, and now permissions have been requested and they returned true, we restart exactly once with the new permissions.
            
            if(![OEPocketsphinxController sharedInstance].isListening) { // If there was no error and we aren't listening, start listening.
                [[OEPocketsphinxController sharedInstance]
                 startListeningWithLanguageModelAtPath:self.pathToFirstDynamicallyGeneratedLanguageModel
                 dictionaryAtPath:self.pathToFirstDynamicallyGeneratedDictionary
                 acousticModelAtPath:[OEAcousticModel pathToModel:@"AcousticModelEnglish"]
                 languageModelIsJSGF:FALSE]; // Start speech recognition.
                
                self.startupFailedDueToLackOfPermissions = FALSE;
            }
        }
    }
}
- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    // 收到中斷訊息
    if (command.isBye) {
        [cTimer invalidate];
        cTimer = nil;
        [self publishCommandClassStop];
        NSLog(@"PLAY END :[%d][%d] %.2f",0,0,0);
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

// publish class start
- (void)publishCommandClassStart {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStart;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

// publish class stop
- (void)publishCommandClassStop {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

@end
