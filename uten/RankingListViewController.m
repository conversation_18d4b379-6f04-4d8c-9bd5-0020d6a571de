//
//  RankingListViewController.m
//  uten
//
//  Created by 蔡駿寓 on 2024/4/28.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "RankingListViewController.h"
#import "RankingL2ViewController.h"  //公仔.跳轉頁面
#import "OHMySQL.h"                  //叫nas的資料下來.呂
#import <QuartzCore/QuartzCore.h>    //叫nas的資料下來.呂


@interface RankingListViewController ()


@property (weak, nonatomic) IBOutlet UIImageView *imageView1;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView2;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView3;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView4;//(旋轉)
@property (weak, nonatomic) IBOutlet UIImageView *imageView5;//(旋轉)
//(喵咪搖尾巴) 設定參數
@property (weak, nonatomic) IBOutlet UIImageView *imageView;//(喵咪)
@property (strong, nonatomic) NSArray *imageNames;          //(喵咪)
@property (nonatomic) NSInteger currentImageIndex;          //(喵咪)
@property (strong, nonatomic) NSTimer *timer;               //(喵咪)

@end

@implementation RankingListViewController


//(轉場傳送.文字+圖片)----------------------------------------
- (IBAction)buttonPressed:(UIButton *)sender {
    [self performSegueWithIdentifier:@"showDetailsSegue" sender:sender];
}
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    if ([segue.identifier isEqualToString:@"showDetailsSegue"]) {
        RankingL2ViewController *destinationVC = segue.destinationViewController;
        UIButton *button = (UIButton *)sender;
        NSLog(@"Button tag is: %ld", (long)button.tag);  // 添加這行代碼來檢查tag值
        destinationVC.buttonTag = button.tag;
    }
}
//說明：在故事版，為每個UIButton設置一個唯一的tag標籤，從1到15，並連接到同一個(IBAction)buttonPressed: 在這段中。
//實現按鈕的IBAction函數，並使用segue進行頁面跳轉，同時傳遞數據。
//(轉場傳送.文字+圖片)----------------------------------------






    
- (void)viewDidLoad {
    [super viewDidLoad];
    // (旋轉背景圖) 設置每個圖片視圖的圖片
    self.imageView1.image = [UIImage imageNamed:@"RankingList_011.png"];//(旋轉)
    self.imageView2.image = [UIImage imageNamed:@"RankingList_031.png"];//(旋轉)
    self.imageView3.image = [UIImage imageNamed:@"RankingList_041.png"];//(旋轉)
    self.imageView4.image = [UIImage imageNamed:@"RankingList_031.png"];//(旋轉)
    self.imageView5.image = [UIImage imageNamed:@"RankingList_041.png"];//(旋轉)

    // (旋轉背景圖) 啟動每個圖片視圖的旋轉動畫  //20秒1圈，或10秒1圈
    [self startRotatingImageView:self.imageView1 withDuration:40.0];    //(旋轉)
    [self startRotatingImageView:self.imageView2 withDuration:30.0];    //(旋轉)
    [self startRotatingImageView:self.imageView3 withDuration:25.0];    //(旋轉)
    [self startRotatingImageView:self.imageView4 withDuration:30.0];    //(旋轉)
    [self startRotatingImageView:self.imageView5 withDuration:35.0];    //(旋轉)
    
        //(喵咪搖尾巴) 初始化圖片名稱，導入4張圖片
        self.imageNames = @[@"RankingList_0211.png", @"RankingList_0212.png", @"RankingList_0213.png", @"RankingList_0214.png"];
        self.currentImageIndex = 0;

        //(喵咪搖尾巴) 設置初始化圖片
        self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];

        //(喵咪搖尾巴) 設置定時器以自動切換圖片，0.5秒切換1張，repeat重複
        self.timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(nextImage) userInfo:nil repeats:YES];
    
}


// (旋轉背景圖)  //(旋轉)
- (void)startRotatingImageView:(UIImageView *)imageView withDuration:(CGFloat)duration {
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];//創建基本動畫  //(旋轉)
    rotationAnimation.fromValue = @(0);  //旋轉的起點為 0度。       //(旋轉)
    rotationAnimation.toValue = @(M_PI * 2);//表示轉360度         //(旋轉)
    //rotationAnimation.toValue = @(M_PI * 2 * 120 / 360);//旋轉弧長度.轉1/3圈
    rotationAnimation.fillMode = kCAFillModeForwards;//確定重新來 //(旋轉)
    rotationAnimation.removedOnCompletion = NO;//確定重新來       //(旋轉)
    rotationAnimation.duration = duration;//持續時間.旋轉速度      //(旋轉)
    rotationAnimation.repeatCount = INFINITY;//無限次重復         //(旋轉)
    [imageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];// 將動畫添加到圖片的layer中   //(旋轉)
}//(旋轉)




//叫nas的資料下來.呂================{
-(IBAction)btTestMySQL:(id)sender {
    // 下載 nas的MYSQL 資料下來================
        // 1. 設定使用者----------
        OHMySQLUser *user = [[OHMySQLUser alloc] initWithUserName:@"uten"
                                                         password:@"1qazXSW@3edcVFR$"
                                                       serverName:@"uten.synology.me"
                                                           dbName:@"uten"
                                                             port:3307
                                                           socket:@"/run/mysqld/mysqld10.sock"];
        
        // 2. 設定資料庫協調器並連接----------
        OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
        [coordinator connect];
        [coordinator setEncoding:CharsetEncodingUTF8MB4];
        
        // 3. 建立查詢上下文----------
        OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
        queryContext.storeCoordinator = coordinator;
    
    
//        NSString *condition = @"通過分數 = 80 AND CNAME != 123 AND 字母順位 = 0 AND 待罰 = 0";
//        OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"AllStudentABC" condition:condition]; //可一次多種條件判斷式
        
        // 4. 使用 OHMySQLQueryRequestFactory 創建 SELECT 查詢請求----------
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"AllStudentABC" condition:nil];
        
        NSError *error = nil;
        NSArray *results = [queryContext executeQueryRequestAndFetchResult:query error:&error];
        
        if (error) {
            NSLog(@"Error: %@", error.localizedDescription);
        } else {
            
            // 5. 迭代查詢結果----------
            for (NSDictionary *row in results) {
                
                NSString *單字下載;
                NSData *data2 = row[@"字母"];
                單字下載 = [[NSString alloc] initWithData:data2 encoding:NSUTF8StringEncoding];
                
                NSString *字母軌跡下載;
                NSData *data7 = row[@"字母軌跡"];
                字母軌跡下載 = [[NSString alloc] initWithData:data7 encoding:NSUTF8StringEncoding]; //解碼data二進制轉string
                
                NSData *data = [字母軌跡下載 dataUsingEncoding:NSUTF8StringEncoding];
                NSError *error;
                NSArray *array = [NSJSONSerialization JSONObjectWithData:data options : 0 error : &error];
                if ( !error ) {
                    NSUInteger n = array.count;
                    int LMA4[n][4];     // 初始化二維陣列 LMA4
                    // 將 JSON 內容轉換為 int 型態並存入 LMA4
                    for (NSUInteger i = 0; i < n; i++) {
                        NSArray *subArray = array[i];
                        for (NSUInteger j = 0; j < 4; j++) {
                            LMA4[i][j] = [ subArray[ j ] intValue];
                        }   //將 LMA3[][] 下載下來後，存入 LMA4[][] 中。
                        NSLog(@"LMA4[%lu](%d,%d,%d,%d)",(unsigned long)i,LMA4[i][0],LMA4[i][1],LMA4[i][2],LMA4[i][3]);
                    } // 現在 LMA4 中已經存入 JSON 轉換過來的 int 型態數值
                } else {
                    NSLog(@"JSON 解析失敗: %@", error.localizedDescription);
                }
                NSNumber *data88 = row[@"通過分數"];
                int data8 = [data88 intValue];
                
                NSNumber *data99 = row[@"待罰"];
                int data9 = [data99 intValue];
                
                NSLog(@"CNAME: %@", row[@"CNAME"]);
                NSLog(@"字母: %@", 單字下載);
                NSLog(@"字母軌跡: %@", 字母軌跡下載);
                NSLog(@"通過分數: %d", data8);
                NSLog(@"待罰: %d", data9);
            }
        }
 
        // 6. 斷開連接----------
        [coordinator disconnect];
    // 下載 nas的MYSQL 資料下來================
    //叫nas的資料下來.呂================}
}

    //(喵咪搖尾巴)
- (void)nextImage {
    //(喵咪搖尾巴) 更新圖片索引
    self.currentImageIndex = (self.currentImageIndex + 1) % self.imageNames.count;
    
    //(喵咪搖尾巴) 設置圖片視圖為下一張圖片
    self.imageView.image = [UIImage imageNamed:self.imageNames[self.currentImageIndex]];
}//(喵咪搖尾巴)

- (void)dealloc {
    //(喵咪搖尾巴) 確保定時器被正確無誤地停止
    [self.timer invalidate];//(喵咪搖尾巴)
    self.timer = nil;       //(喵咪搖尾巴)
}                           //(喵咪搖尾巴)

//返回.上一層
- (IBAction)btBack:(id)sender {
    [self dismissViewControllerAnimated:NO completion:nil];
}//返回.上一層

@end
