//
//  ViewController.m
//  uten
//
//  Created by 簡大翔 on 2018/6/19.
//  Copyright 2018年 bekubee. All rights reserved.
//

#import "ViewController.h"
#import "HomeViewController.h"
#import "ClassViewController.h"
#import "MainViewController.h"
#import "WaitViewController.h"
#import <AVFoundation/AVFoundation.h>
#import "GRRequestsManager.h"
#import "GRListingRequest.h"
#import "MQTTClient.h"
#import "OHMySQL.h"
#import "NSUserDefaults+X.h"
#import "UTENEnum.h"
#import <MMKV/MMKV.h>
#import <ReactiveObjC/ReactiveObjC.h>
#import "UTENMediaModel.h"
#import "UTENConstants.h"
#import "UTENMySql.h"
#import "UTENConfig.h"
#import "AudioUploadViewModel.h"

@interface ViewController () <GRRequestsManagerDelegate>
{
    IBOutlet UILabel *titleLabel;
    IBOutlet UIImageView *iconView;
    IBOutlet UIView *scanView;
    NSString *ID[1000];
    NSString *TYPE[1000];
    NSString *CNAME[1000];
    NSString *ENAME[1000];
    NSString *QRCODE[1000];
    int UserMax;
    int UserPos;
    NSString *ServerIP;
    OHMySQLQueryContext *queryContext;
    
}
@property (nonatomic, strong) GRRequestsManager *requestsManager;
@property (nonatomic, strong) AVCaptureSession *captureSession;
@property (nonatomic, strong) AVCaptureStillImageOutput *imageOutput;
@property (nonatomic, strong) AVCaptureVideoPreviewLayer *videoPreviewLayer;
@property (nonatomic, strong) UIImage *faceimg;
@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic) BOOL isReading;
@property (nonatomic) BOOL faceMode;
@property (nonatomic) int faceCount;
@property (nonatomic, strong) RACDisposable *mmkvCheckDisposable;
// mmkv initialized flag
@property (nonatomic) BOOL mmkvInitialized;
// uploading flag
@property (nonatomic) BOOL uploading;
@property (nonatomic, strong) AudioUploadViewModel *audioUploadViewModel;
-(BOOL)startReading;
-(void)stopReading;
@end

@implementation ViewController
- (void)viewDidLoad {
    [super viewDidLoad];
    DDLogInfoTag(@"UI", @"%@ 進入前台", self.class);
    DDLogDebug(@"Performance", @"渲染耗時: %.2fms", 16.66f);
    // 設定 JLRoutes
    [AppRoutes setupRoutes:self.navigationController];
 //   ServerIP=@"**************";
 //   ServerIP=@"***********";
 //   ServerIP=@"**************";
    ServerIP=@"uten.synology.me";
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setObject:ServerIP forKey:@"ServerIP"];
    [defaults synchronize];
    //請掃描 QR code
    //拍照中.請保持微笑
    // Do any additional setup after loading the view, typically from a nib.
    // Initially make the captureSession object nil.
    _faceCount=0;
    _captureSession = nil;
    _faceMode=true;
    // Set the initial value of the flag to NO.
    _isReading = NO;
    [self startStopReading:nil];
    //Get FTP
    NSLog(@"GetFTP List");
    /*
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:@"phototracq.com" user:@"<EMAIL>" password:@"4rfvCDE#2wsxZAQ!"];
   */
    self.requestsManager = [[GRRequestsManager alloc] initWithHostname:ServerIP user:@"uten" password:@"zZ54775178"];
    self.requestsManager.delegate=self;
      
   // [self.requestsManager addRequestForListDirectoryAtPath:@"/"];
    //[self.requestsManager startProcessingRequests];
    
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *userFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"user.csv"];
    NSString *classFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"class.csv"];
    NSString *upgradeFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"upgrade.csv"];
    
    NSString *l1u1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l1u1u4_s.csv"];
    NSString *l1u5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l1u5u8_s.csv"];
    NSString *l2u1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l2u1u4_s.csv"];
    NSString *l2u5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l2u5u8_s.csv"];
    NSString *lbu1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"lbu1u4_s.csv"];
    NSString *lbu5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"lbu5u8_s.csv"];
    NSString *up1u1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"up1u1u4_s.csv"];
    NSString *up1u5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"up1u5u8_s.csv"];
    NSString *up2u1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"up2u1u4_s.csv"];
    NSString *up2u5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"up2u5u8_s.csv"];
    NSString *up3u1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"up3u1u4_s.csv"];
    NSString *up3u5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"up3u5u8_s.csv"];
    NSString *upsu1u4FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"upsu1u4_s.csv"];
    NSString *upsu5u8FilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"upsu5u8_s.csv"];
    /*
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/user.csv" toLocalPath:userFilePath];
    [self.requestsManager startProcessingRequests];
    */
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class.csv" toLocalPath:classFilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/upgrade.csv" toLocalPath:upgradeFilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/l1u1u4_s/l1u1u4_s.csv" toLocalPath:l1u1u4FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/l1u5u8_s/l1u5u8_s.csv" toLocalPath:l1u5u8FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/l2u1u4_s/l2u1u4_s.csv" toLocalPath:l2u1u4FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/l2u5u8_s/l2u5u8_s.csv" toLocalPath:l2u5u8FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/lbu1u4_s/lbu1u4_s.csv" toLocalPath:lbu1u4FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/lbu5u8_s/lbu5u8_s.csv" toLocalPath:lbu5u8FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/up1u1u4_s/up1u1u4_s.csv" toLocalPath:up1u1u4FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/up1u5u8_s/up1u5u8_s.csv" toLocalPath:up1u5u8FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/up2u1u4_s/up2u1u4_s.csv" toLocalPath:up2u1u4FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/up2u5u8_s/up2u5u8_s.csv" toLocalPath:up2u5u8FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/up3u1u4_s/up3u1u4_s.csv" toLocalPath:up3u1u4FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/up3u5u8_s/up3u5u8_s.csv" toLocalPath:up3u5u8FilePath];
    [self.requestsManager startProcessingRequests];
    [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/upsu1u4_s/upsu1u4_s.csv" toLocalPath:upsu1u4FilePath];
    [self.requestsManager startProcessingRequests];
    // [self.requestsManager addRequestForDownloadFileAtRemotePath:@"/uten/class/picbook/upsu5u8_s/upsu5u8_s.csv" toLocalPath:upsu5u8FilePath];
    // [self.requestsManager startProcessingRequests];
    //
        NSLog(@"LISTING ALL FILES FOUND");
    int count;
    NSLog(@"JFile DIR:%@",documentsDirectoryPath);
    NSArray *directoryContent = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:documentsDirectoryPath error:NULL];
    for (count = 0; count < (int)[directoryContent count]; count++)
    {
        NSLog(@"JFile %d: %@", (count + 1), [directoryContent objectAtIndex:count]);
    }

    NSString *DirFile1=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"l1u1u4_s"]];
    NSString *DirFile=[DirFile1 stringByAppendingString:@".csv"];
    NSString *classContent = [NSString stringWithContentsOfFile:DirFile encoding:NSUTF8StringEncoding error:NULL];
    NSLog(@"lbu1u4_s FIle:%@\n%@\n%@",DirFile1,DirFile,classContent);
    
    OHMySQLUser *user;
    user= [[OHMySQLUser alloc] initWithUserName:@"uten"
                                       password:@"1qazXSW@3edcVFR$"
                                     serverName:ServerIP
                                         dbName:@"uten"
                                           port:3307
                                         socket:@"/run/mysqld/mysqld10.sock"];
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    
    //OHMySQLQueryContext *
    queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"qrcode" condition:nil];
    NSError *error = nil;
    NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
    UserMax=0;
    for(int i=0;i<[tasks count];i++) {
        NSDictionary *dict = [tasks objectAtIndex:i];
        ID[UserMax]=[dict objectForKey:@"id"];
        TYPE[UserMax]=[dict objectForKey:@"type"];
        CNAME[UserMax]=[[NSString alloc] initWithData:[dict objectForKey:@"cname"]  encoding:NSUTF8StringEncoding];
        ENAME[UserMax]=[[NSString alloc] initWithData:[dict objectForKey:@"ename"]  encoding:NSUTF8StringEncoding];
        QRCODE[UserMax]=[[NSString alloc] initWithData:[dict objectForKey:@"qrcode"]  encoding:NSUTF8StringEncoding];
        UserMax++;
    }
    for(int i=0;i<UserMax;i++) {
        NSLog(@"NAME:%@ , QR:%@",CNAME[i],QRCODE[i]);
    }
    // 使用 ReactiveObjC 創建定時器，並使用 async map 處理
    self.mmkvCheckDisposable = [[[[RACSignal interval:10.0 onScheduler:[RACScheduler mainThreadScheduler]]
        map:^id(NSDate *date) {
            return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
                dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                    if (!self.uploading) {
                        self.uploading = YES;
                        [self uploadMMKVAudioToFTP];
                        self.uploading = NO;
                    } else {
                        DDLogWarnTag(@"audio", @"Already uploading, skipping upload");
                    }
                    [subscriber sendNext:nil];
                    [subscriber sendCompleted];
                });
                return nil;
            }];
        }]
        flatten]
        subscribeNext:^(id x) {
            // 完成處理
        }];

    // 初始化 AudioUploadViewModel
    self.audioUploadViewModel = [[AudioUploadViewModel alloc] init];
    // 取得登入角色(測試用)
    NSNumber *role = (NSNumber *)[[UTENConfig sharedInstance] valueForKey:@"Role"];
    if ([[UTENConstants masterGroup] containsObject:role]) {
        [self performSelector:@selector(toMasterViewController) withObject:nil afterDelay:1];
    }
    else if ([@(RoleStudent) isEqual:role]) {
        [self performSelector:@selector(toStudentViewController) withObject:nil afterDelay:1];
    }
}

- (void)dealloc {
    // 清理定時器
    [self.mmkvCheckDisposable dispose];
}

- (IBAction)startStopReading:(id)sender {
    if (!_isReading) {
        // This is the case where the app should read a QR code when the start button is tapped.
        if ([self startReading]) {
            // If the startReading methods returns YES and the capture session is successfully
            // running, then change the start button title and the status message.
        }
    }
    else{
        // In this case the app is currently reading a QR code and it should stop doing so.
        [self stopReading];
        // The bar button item's title should change again.
    }
    
    // Set to the flag the exact opposite value of the one that currently has.
    _isReading = !_isReading;
}

#pragma mark - Private method implementation
- (BOOL)startReading {
    NSError *error;
    
    // Get an instance of the AVCaptureDevice class to initialize a device object and provide the video
    // as the media type parameter.
    AVCaptureDevice *captureDevice = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    
    // Get an instance of the AVCaptureDeviceInput class using the previous device object.

    NSArray *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    for(AVCaptureDevice *camera in devices) {
        if (camera.position == AVCaptureDevicePositionFront)
        {
            captureDevice = camera;
            break;
        }
    }
    AVCaptureDeviceInput *input= [AVCaptureDeviceInput deviceInputWithDevice:captureDevice error:&error];
    if (!input) {
        // If any error occurs, simply log the description of it and don't continue any more.
        NSLog(@"%@", [error localizedDescription]);
        return NO;
    }
    
    // Initialize the captureSession object.
    _captureSession = [[AVCaptureSession alloc] init];
    // Set the input device on the capture session.
    [_captureSession addInput:input];
    
    // Initialize a AVCaptureMetadataOutput object and set it as the output device to the capture session.
    AVCaptureMetadataOutput *captureMetadataOutput = [[AVCaptureMetadataOutput alloc] init];
    [_captureSession addOutput:captureMetadataOutput];
    
    // Create a new serial dispatch queue.
    dispatch_queue_t dispatchQueue;
    dispatchQueue = dispatch_queue_create("myQueue", NULL);
    [captureMetadataOutput setMetadataObjectsDelegate:self queue:dispatchQueue];
    [captureMetadataOutput setMetadataObjectTypes:[captureMetadataOutput availableMetadataObjectTypes]];
    
    // Initialize the video preview layer and add it as a sublayer to the viewPreview view's layer.
    _videoPreviewLayer = [[AVCaptureVideoPreviewLayer alloc] initWithSession:_captureSession];
    
    _videoPreviewLayer.orientation = [[UIDevice currentDevice] orientation];
    [_videoPreviewLayer setVideoGravity:AVLayerVideoGravityResizeAspectFill];
    [_videoPreviewLayer setFrame:scanView.layer.bounds];
    [scanView.layer addSublayer:_videoPreviewLayer];
    // Start video capture.
    [_captureSession startRunning];
    
    return YES;
}
-(void)stopReading{
    // Stop video capture and make the capture session object nil.
    [_captureSession stopRunning];
    _captureSession = nil;
    // Remove the video preview layer from the viewPreview view's layer.
    [_videoPreviewLayer removeFromSuperlayer];
}

#pragma mark - AVCaptureMetadataOutputObjectsDelegate method implementation
-(void) TakePicture {
    AVCaptureStillImageOutput *imageOutput = [[AVCaptureStillImageOutput alloc] init];
    imageOutput.outputSettings = @{AVVideoCodecKey:AVVideoCodecJPEG};
    if ([_captureSession canAddOutput:imageOutput]) {
        [_captureSession addOutput:imageOutput];
        _imageOutput = imageOutput;
    }
    
    // 输出图片
    AVCaptureConnection *connection = [_imageOutput connectionWithMediaType:AVMediaTypeVideo];

    id takePictureSuccess = ^(CMSampleBufferRef sampleBuffer,NSError *error){
        if (sampleBuffer == NULL) {
            NSLog(error);
            return ;
        }
        NSData *imageData = [AVCaptureStillImageOutput jpegStillImageNSDataRepresentation:sampleBuffer];
        _faceimg = [[UIImage alloc]initWithData:imageData];
       // _faceimg= [[UIImage alloc]initWithCGImage:_faceimg.CGImage scale:2.0 orientation:UIImageOrientationUp];
    };
    [_imageOutput captureStillImageAsynchronouslyFromConnection:connection completionHandler:takePictureSuccess];
    
}
-(void) Beep {
    NSURL *url = [NSURL fileURLWithPath:[[NSBundle mainBundle]
                                         pathForResource:@"beep-07"
                                         ofType:@"mp3"]];
    AVAudioPlayer *audioPlayer = [[AVAudioPlayer alloc]
                                  initWithContentsOfURL:url
                                  error:nil];
    [audioPlayer play];
    [NSThread sleepForTimeInterval:1];
}
-(void)captureOutput:(AVCaptureOutput *)captureOutput didOutputMetadataObjects:(NSArray *)metadataObjects fromConnection:(AVCaptureConnection *)connection{
    // Check if the metadataObjects array is not nil and it contains at least one object.
    if (metadataObjects != nil && [metadataObjects count] > 0)  {
        NSLog(@"Result:%@", metadataObjects);
        
        if(_faceMode) {
            AVMetadataFaceObject *metadataObj = [metadataObjects objectAtIndex:0];
            if(metadataObj.type==AVMetadataObjectTypeFace) {
                if(metadataObj.faceID > 0) {
                    //[self Beep];
                    if(_faceCount++ > 50) {
                        [self TakePicture];
                        _faceMode=false;
                        dispatch_async(dispatch_get_main_queue(), ^{
                            // Your code to run on the main queue/thread
                           
                            titleLabel.text=@"請掃描 QR code";
                            titleLabel.textColor=UIColor.blackColor;
                            iconView.image=[UIImage imageNamed: @"scan_start.png"];
                        });
                    }
                }
            }
            
        } else {
        // Get the metadata object.
            AVMetadataMachineReadableCodeObject *metadataObj = [metadataObjects objectAtIndex:0];
        //NSLog(@"string:%@", metadataObj.stringValue);
            if ([[metadataObj type] isEqualToString:@"org.iso.QRCode"]) {
                for(int i=0;i<UserMax;i++) {
                    NSString *tmp=[metadataObj.stringValue stringByReplacingOccurrencesOfString:@"\n" withString:@""];
                    if ([tmp isEqualToString:QRCODE[i]])
                    {
                        // Do stuff...
                        [self Beep];
                        dispatch_async(dispatch_get_main_queue(), ^{
                            NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
                            NSString *filePath = [[paths objectAtIndex:0] stringByAppendingPathComponent:@"faceImage.jpg"];
                            // Save image.
                            [UIImageJPEGRepresentation(_faceimg,0.5) writeToFile:filePath atomically:YES];
                            NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
                            [formatter setDateFormat:@"yyyy-MM-dd-hh-mm"];
                            //NSString *remote= [NSString stringWithFormat:@"login/%@_%@_%@.jpg",[formatter stringFromDate:[NSDate date]],ID[i],CNAME[i]];
                            NSString *remote= [NSString stringWithFormat:@"login/%@_%@.jpg",[formatter stringFromDate:[NSDate date]],CNAME[i]];
                            NSLog(@"JAMES %@->%@",filePath,remote);
                            [self.requestsManager addRequestForUploadFileAtLocalPath:filePath toRemotePath:remote];
                            [self.requestsManager startProcessingRequests];
                            
                            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                            [defaults setObject:ID[i] forKey:@"ID"];
                            [defaults setObject:TYPE[i] forKey:@"TYPE"];
                            [defaults setObject:CNAME[i] forKey:@"CNAME"];
                            [defaults setObject:ENAME[i] forKey:@"ENAME"];
                            [defaults synchronize];
                            
                            UIStoryboard *storyboard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
                            
                            if([TYPE[i] intValue]==3) {
                                NSString *strcondition = [[NSString alloc] initWithFormat:(@"cname='%@'"),CNAME[i]];
                                OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory SELECT:@"nhistory" condition:strcondition];
                                NSError *error = nil;
                                NSArray *tasks = [queryContext executeQueryRequestAndFetchResult:query error:&error];
                                NSString *cname,*ename,*nclass;
                                NSLog(@"DBG count:%d",[tasks count]);
                                for(int i=0;i<[tasks count];i++) {
                                    NSDictionary *dict = [tasks objectAtIndex:i];
                                    cname=[[NSString alloc] initWithData:[dict objectForKey:@"cname"]  encoding:NSUTF8StringEncoding];
                                    ename=[[NSString alloc] initWithData:[dict objectForKey:@"ename"]  encoding:NSUTF8StringEncoding];
                                    nclass=[[NSString alloc] initWithData:[dict objectForKey:@"nclass"]  encoding:NSUTF8StringEncoding];
                                    NSLog(@"NAME:%@ , Class:%@",cname,nclass);
                                }
                                NSString *uten_class = [[NSString alloc] initWithFormat:(@"uten_%@"),nclass];
                                NSString *r_uten_class = [[NSString alloc] initWithFormat:(@"r_uten_%@"),nclass];
                                [defaults setObject:nclass forKey:@"CLASS"];
                                [defaults setObject:uten_class forKey:@"uten_class"];
                                [defaults setObject:r_uten_class forKey:@"r_uten_class"];

                                [self initializeMMKV];
                                [AppRoutes push:AppRoutes.main];
                            }

                            if([TYPE[i] intValue] < 3) {
                                [self initializeMMKV];
                                [AppRoutes push:AppRoutes.wait];
                            }
                    });
                        [self stopReading];
                        [self performSelectorOnMainThread:@selector(stopReading) withObject:nil waitUntilDone:NO];
                        _isReading = NO;
                    }
                }
            }
        }
    }
}

- (void)initializeMMKV {
    // 獲取 Documents 目錄
    NSString *documentsPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    // /Users/<USER>/Library/Developer/CoreSimulator/Devices/B801DC9F-49AE-471D-9EF4-984D8446F3A1/data/Containers/Data/Application/8F887177-5D8C-4141-8018-794925223BC8/Documents
    NSLog(@"Documents Path: %@", documentsPath);
    // 使用 Documents 目錄作為 MMKV 的 root directory
    NSNumber *identifier = [NSUserDefaults standardUserDefaults].identifier ?: @(0);
    // concat with rootDir
    NSString *rootDir = [NSString stringWithFormat:@"%@/%@", documentsPath, identifier];
    NSLog(@"MMKV initing rootDir:%@", rootDir);
    // 遍历所有 LocalTable 值
    for (LocalTable table = LocalTableUnknown + 1; table < LocalTableMax; table++) {
        NSString *groupId = [NSString stringWithFormat:@"%@", @(table)];
        // init table
        NSString *dir = [MMKV initializeMMKV:rootDir groupDir:groupId logLevel:MMKVLogDebug];
        NSLog(@"MMKV inited dir:%@", dir);
        // MMKV *mmkv = [MMKV defaultMMKV];
        // MMKV *mmkv = [MMKV mmkvWithID:groupId];
        // [mmkv setString:groupId forKey:@"string"];
        // NSString *str = [mmkv getStringForKey:@"string"];
        // NSLog(@"MMKV inited str:%@", str);
    }
    self.mmkvInitialized = YES;
}

// 在 viewWillAppear 中隱藏
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

// 在 viewWillDisappear 中恢復 (如果需要)
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

// usage: [self performSelector:@selector(toMasterViewController) withObject:nil afterDelay:1];
- (void)toMasterViewController {
    NSUserDefaults *defaults = NSUserDefaults.standardUserDefaults;
    defaults.identifier = @(1);
    defaults.type = @(RoleProgrammer);
    defaults.cname = @"簡先生程式人員";
    defaults.ename = @"簡先生程式人員";
    [self initializeMMKV];
    [AppRoutes push:AppRoutes.main];
}

// usage: [self performSelector:@selector(toStudentViewController) withObject:nil afterDelay:1];
- (void)toStudentViewController {
    NSUserDefaults *defaults = NSUserDefaults.standardUserDefaults;
    defaults.identifier = @(70);
    defaults.type = @(RoleStudent);
    defaults.cname = @"Uten2022Student001";
    defaults.ename = @"Uten2022Student001";
    [self initializeMMKV];
    [AppRoutes push:AppRoutes.wait];
}

- (void)uploadMMKVAudioToFTP {
    if (!self.mmkvInitialized) {
        DDLogWarnTag(@"audio", @"MMKV not initialized, skipping upload");
        return;
    }
    
    // 获取 MMKV
    NSString *groupId = [NSString stringWithFormat:@"%@", @(LocalTableMedia)];
    DDLogInfoTag(@"audio", @"Uploading audio files from MMKV with group ID: %@", groupId);
    MMKV *mmkv = [MMKV mmkvWithID:groupId];
    if (mmkv) {
        // 獲取音檔文件路径
        NSString *documentsPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        
        @weakify(self);
        // 遍历 MMKV
        [mmkv enumerateKeys:^(NSString *key, BOOL *stop) {
            @strongify(self);
            NSString *jsonString = [mmkv getStringForKey:key];
            DDLogInfoTag(@"audio", @"MMKV Key(%@), Value(%@)", key, jsonString);
            // 删除 MMKV 中的键值对
            // [mmkv removeValueForKey:key];
            
            // 解析 JSON 字符串
            NSError *error;
            UTENMediaModel *mediaModel = [UTENMediaModel fromJSON:jsonString encoding:NSUTF8StringEncoding error:&error];
            if (error) {
                DDLogErrorTag(@"audio", @"Failed to parse media model: %@", error.localizedDescription);
                return;
            }

            // 检查文件是否存在
            NSString *audioPath = [documentsPath stringByAppendingPathComponent:mediaModel.file];
            DDLogInfoTag(@"audio", @"Checking audio file: %@", audioPath);
            if (audioPath && [[NSFileManager defaultManager] fileExistsAtPath:audioPath]) {
                // 檢查音檔峰值，小於大音量則不上傳
                float peakDB = [self.audioUploadViewModel getAudioPeakLevel:audioPath];
                if (peakDB < -30.0f) { // 小於大音量
                    DDLogWarnTag(@"audio", @"Audio level too low (%.2f dB), skipping upload: %@", peakDB, audioPath);
                    // 刪除音檔
                    NSError *error;
                    [[NSFileManager defaultManager] removeItemAtPath:audioPath error:&error];
                    if (error) {
                        DDLogErrorTag(@"audio", @"Error deleting audio file: %@", error.localizedDescription);
                    } else {
                        DDLogInfoTag(@"audio", @"Deleted audio file: %@", audioPath);
                    }
                    return;
                }
                // 檢查音檔長度，如果小於 0.5 秒，則不上傳
                // 取得音檔長度
                AVURLAsset *asset = [AVURLAsset assetWithURL:[NSURL fileURLWithPath:audioPath]];
                Float64 duration = CMTimeGetSeconds(asset.duration);
                DDLogInfoTag(@"audio", @"Audio file duration: %f seconds", duration);
                // 如果小於 0.5 秒，則不上傳
                if (duration < 0.5) {
                    DDLogWarnTag(@"audio", @"Audio file duration less than 0.5 seconds, skipping upload: %@", audioPath);
                    // 删除音檔文件
                    NSError *error;
                    [[NSFileManager defaultManager] removeItemAtPath:audioPath error:&error];
                    if (error) {
                        DDLogErrorTag(@"audio", @"Error deleting audio file: %@", error.localizedDescription);
                    } else {
                        DDLogInfoTag(@"audio", @"Deleted audio file: %@", audioPath);
                    }
                    return;
                }
                // 构建远程路径，这里假设在FTP服务器上创建一个audio文件夹
                NSString *remotePath = [NSString stringWithFormat:@"%@/%@", UTENConstants.ftpUploadPath, mediaModel.file];
                DDLogInfoTag(@"audio", @"Uploading audio file: %@ to remote path: %@", audioPath, remotePath);
                // 添加上传请求
                [self.requestsManager addRequestForUploadFileAtLocalPath:audioPath 
                                                            toRemotePath:remotePath];
                // insert model into database
                [self insertMediaRecord:mediaModel];
            } else {
                // 文件不存在
                NSLog(@"File not found: %@", audioPath);
            }
        }];
        [mmkv clearAll];
        // 开始处理上传请求
        [self.requestsManager startProcessingRequests];
    }
}

- (void)insertMediaRecord:(UTENMediaModel *)media {
    [[[UTENMySql sharedInstance] putMedia:media] subscribeNext:^(NSNumber *mediaId) {
        NSLog(@"Successfully inserted media");
    } error:^(NSError *error) {
        NSLog(@"Error inserting media: %@", error);
    }];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
    [self.mmkvCheckDisposable dispose];
    self.mmkvCheckDisposable = nil;
}

#pragma mark - GRRequestsManagerDelegate

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didScheduleRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didScheduleRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteListingRequest:(id<GRRequestProtocol>)request listing:(NSArray *)listing
{
    NSLog(@"requestsManager:didCompleteListingRequest:listing: \n%@", listing);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteCreateDirectoryRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteCreateDirectoryRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDeleteRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDeleteRequest:");
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompletePercent:(float)percent forRequest:(id<GRRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompletePercent:forRequest: %f", percent);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteUploadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    DDLogInfoTag(@"audio", @"requestsManager:didCompleteUploadRequest:");
    
    // Get the local file path from the request
    NSString *localPath = request.localFilePath;
    DDLogInfoTag(@"audio", @"Uploaded localPath: %@", localPath);

    NSError *error;
    [[NSFileManager defaultManager] removeItemAtPath:localPath error:&error];
    if (error) {
        DDLogErrorTag(@"audio", @"Error deleting local file: %@", error.localizedDescription);
    } else {
        DDLogInfoTag(@"audio", @"Deleted local file: %@", localPath);
    }
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didCompleteDownloadRequest:(id<GRDataExchangeRequestProtocol>)request
{
    NSLog(@"requestsManager:didCompleteDownloadRequest:");
    GRListingRequest *req = (GRListingRequest *)request;
    NSLog(@"RETEMO FILE:%@",req.path);
    /*
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *localFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"l2u1u4_s.csv"];
    NSString* content = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
    GRListingRequest *req = (GRListingRequest *)request;
    NSLog(@"JRETEMO FILE:%@",req.path);
    NSLog(@"CSV FIle:\n%@",content);
     */
    /*
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    NSString *localFilePath = [documentsDirectoryPath stringByAppendingPathComponent:@"user.csv"];
    NSString* content = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
    GRListingRequest *req = (GRListingRequest *)request;
    NSLog(@"RETEMO FILE:%@",req.path);
    NSLog(@"CSV FIle:\n%@",content);
    if ([req.path isEqualToString:@"/user.csv"]) {
        NSArray *splitLine = [content componentsSeparatedByString:@"\n"];
        UserMax=0;
        for(int i=1;i<[splitLine count];i++) {
            NSArray *split= [splitLine[i] componentsSeparatedByString:@","];
            ID[UserMax]=split[0];
            TYPE[UserMax]=split[1];
            CNAME[UserMax]=split[2];
            ENAME[UserMax]=split[3];
            QRCODE[UserMax]=split[6];
            UserMax++;
        }
        for(int i=0;i<UserMax;i++) {
            NSLog(@"ｆ%@ , TYPE:%@ QR:%@",CNAME[i],TYPE[i],QRCODE[i]);
        }
    }
   */
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailWritingFileAtPath:(NSString *)path forRequest:(id<GRDataExchangeRequestProtocol>)request error:(NSError *)error
{
    NSLog(@"requestsManager:didFailWritingFileAtPath:forRequest:error: \n %@", error);
}

- (void)requestsManager:(id<GRRequestsManagerProtocol>)requestsManager didFailRequest:(id<GRRequestProtocol>)request withError:(NSError *)error
{
    NSLog(@"requestsManager:didFailRequest:withError: \n %@", error);
}
- (IBAction)brExit:(id)sender {
    exit(0);
}

@end
