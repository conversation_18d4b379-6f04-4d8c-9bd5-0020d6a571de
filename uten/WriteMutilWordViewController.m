//
//  WriteMutilWordViewController.m
//  uten
//
//  Created by <PERSON> on 2019/4/2.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "WriteMutilWordViewController.h"
#import "ViewController.h"
#import "UIImage+Color.h"
#import "UIImage+Rotate.h"
#import "UIImage+SubImage.h"
#import "UIImage+Gif.h"
#import "MQTTClient.h"
#import "UTENEnum.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"

@interface WriteMutilWordViewController () <MQTTSessionDelegate> {
    MQTTSession *session;
    NSString *ID;
    IBOutlet UIButton *btStartTime;
    IBOutlet UIButton *btTimes;
    IBOutlet UILabel *laTimes;
    IBOutlet UIButton *btSingal;
    IBOutlet UILabel *laStatus;
    IBOutlet UILabel *lacWord;
    
    IBOutlet UILabel *laPass;
    IBOutlet UILabel *laNG;
    NSTimer *cTimer;
    IBOutlet UIImageView *lframe;
    IBOutlet UIImageView *mframe;
    IBOutlet UIImageView *rframe;
    int classvalue;
    int wcount;
    NSString *eword[400];
    NSString *cword[400];
    int song[400];
    bool mark[400];
    NSString *ss[401][16];
    int scnt[401];
    int timecnt;
    int test01;
    int nowIdx;
    AVAudioPlayer *_audioPlayer;
    int TotalPass;
    int TotalNG;
    
    int SYSCount;
    int SYSType;  //1:SPLIT SAY 2:ALL SAY
    int SYSPARA1; //MIN LOOP (N);
    int SYSPARA2; //MAX LOOP (M)
    int SYSPARA3; //Random time(R)
    int SYSPARA4;
    
    UIImageView *W00[200];
    UIImageView *I00[200];
    int wordinx[200];
    SignatureDrawView *dW00[200];
    UIImageView *lf[10];
    UIImageView *mf[10];
    UIImageView *rf[10];
    
    NSString *DirTest;
    NSString *DirSound;
    NSString *DirWord;
    int SoundStep;
    int waitcnt;
    NSString *FL[100];
    int FLCount;
    NSString *ServerIP;
    NSString *uten_class,*r_uten_class;
}


@end

@implementation WriteMutilWordViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    NSString *ee[400][4]={
        {@"bat",@"cat",@"hat",@"rat"},{@"can",@"man",@"pan",@"van"},{@"bad",@"dad",@"mad",@"sad"},{@"back",@"pack",@"sack",@"snack"},
        {@"big",@"pig",@"wig",@"twig"},{@"bill",@"jill",@"hill",@"ill"},{@"king",@"ring",@"sing",@"wings"},{@"lick",@"nick",@"kick",@"chick"},
        {@"hop",@"mop",@"pop",@"top"},{@"not",@"dot",@"hot",@"pot"},{@"rock",@"sock",@"fox",@"ox"},{@"bun",@"nun",@"run",@"sun"},
        {@"bug",@"rug",@"duck",@"truck"},{@"bat",@"cat",@"hat",@"rat"},{@"can",@"man",@"pan",@"van"},{@"bad",@"dad",@"mad",@"sad"},
        {@"back",@"pack",@"sack",@"snack"},{@"big",@"pig",@"wig",@"twig"},{@"bill",@"jill",@"hill",@"ill"},{@"king",@"ring",@"sing",@"wings"},
        {@"lick",@"Nick",@"kick",@"chick"},{@"hop",@"mop",@"pop",@"top"},{@"not",@"dot",@"hot",@"pot"},{@"rock",@"sock",@"fox",@"ox"},
        {@"bun",@"nun",@"run",@"sun"},{@"bug",@"rug",@"duck",@"truck"}};
    NSString *cc[400][4]={
        {@"蝙蝠",@"貓",@"帽子",@"田鼠"},{@"罐頭",@"男人",@"平底鍋",@"廂型車"},{@"壞",@"爹",@"發瘋的",@"傷心的"},{@"背",@"背包",@"紙袋",@"小點心"},
        {@"大",@"豬",@"假髮",@"細樹枝"},{@"比爾",@"吉兒",@"山丘",@"生病"},{@"國王",@"戒指",@"唱歌",@"翅膀 複"},{@"舔",@"尼克",@"踢",@"小雞"},
        {@"單腳跳",@"拖把",@"碰",@"陀螺"},{@"不",@"點",@"熱",@"鍋子"},{@"岩石",@"襪子",@"狐狸",@"公牛"},{@"小麵包",@"修女",@"跑",@"太陽"},
        {@"蟲子",@"小地毯",@"鴨子",@"卡車"},{@"蝙蝠",@"貓",@"帽子",@"田鼠"},{@"罐頭",@"男人",@"平底鍋",@"廂型車"},{@"壞",@"爹",@"發瘋的",@"傷心的"},
        {@"背",@"背包",@"紙袋",@"小點心"},{@"大",@"豬",@"假髮",@"細樹枝"},{@"比爾",@"吉兒",@"山丘",@"生病"},{@"國王",@"戒指",@"唱歌",@"翅膀 複"},
        {@"舔",@"尼克",@"踢",@"小雞"},{@"單腳跳",@"拖把",@"碰",@"陀螺"},{@"不",@"點",@"熱",@"鍋子"},{@"岩石",@"襪子",@"狐狸",@"公牛"},
        {@"小麵包",@"修女",@"跑",@"太陽"},{@"蟲子",@"小地毯",@"鴨子",@"卡車"}};
    [super viewDidLoad];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    ID = [defaults objectForKey:@"ID"];
    int index=[_seltag intValue];
    // Do any additional setup after loading the view.
    for(int i=0;i<200;i++) {
        W00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        W00[i].image=[UIImage imageNamed:@"a1.png"];
        W00[i].alpha=0.3f;
        [self.view addSubview:W00[i]];
        W00[i].hidden=YES;
        I00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        I00[i].image=[UIImage imageNamed:@"bk.png"];
        I00[i].alpha=0.3f;
        [self.view addSubview:I00[i]];
        I00[i].hidden=YES;
        dW00[i] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(50,50,25,75)];
        [self.view addSubview:dW00[i]];
        dW00[i].hidden=YES;
    }
    for(int i=0;i<9;i++) {
        lf[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        lf[i].image=[UIImage imageNamed:@"left_blue.png"];
        lf[i].alpha=0.3f;
        [self.view addSubview:lf[i]];
        lf[i].hidden=NO;
        mf[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        mf[i].image=[UIImage imageNamed:@"mid_blue.png"];
        mf[i].alpha=0.3f;
        [self.view addSubview:mf[i]];
        mf[i].hidden=NO;
        rf[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        rf[i].image=[UIImage imageNamed:@"right_blue.png"];
        rf[i].alpha=0.3f;
        [self.view addSubview:rf[i]];
        rf[i].hidden=NO;
    }
    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",_uclass,_uclass]];
    DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
    DirWord=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//word//",_uclass,_uclass]];
    
    NSString *localFilePath = [DirTest stringByAppendingPathComponent:[NSString stringWithFormat:@"l%03d.csv",index+1]];
    NSString* content = [NSString stringWithContentsOfFile:localFilePath
                                                  encoding:NSUTF8StringEncoding
                                                     error:NULL];
    NSLog(@"CSV FIle:%@\n%@",localFilePath,content);
    NSArray *splitLine = [content componentsSeparatedByString:@"\n"];
    if([splitLine count] >= 4) {
        NSArray *split1=[splitLine[1] componentsSeparatedByString:@","];
        NSArray *split2=[splitLine[3] componentsSeparatedByString:@","];
        SYSCount=[split1[0] intValue];
        SYSType=[split1[1] intValue];
        SYSPARA1=[split1[2] intValue];
        SYSPARA2=[split1[3] intValue];
        SYSPARA3=[split1[4] intValue];
        SYSPARA4=[split1[5] intValue];
        NSLog(@"String Count:%d\n",SYSCount);
        NSArray *dirFiles = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:DirWord error:nil];
        FLCount=0;
        for(int i=0;i<SYSCount;i++) {
            NSString *arr=[@"" stringByAppendingPathComponent:[NSString stringWithFormat:@"self BEGINSWITH '%@_'",split2[i]]];
            NSArray *csvFiles = [dirFiles filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:arr]];
            for(int j=0;j<[csvFiles count];j++) {
                NSString *arrx=[@"" stringByAppendingPathComponent:[NSString stringWithFormat:@"self ENDSWITH '_%d.csv'",j+1]];
                NSArray *cFiles = [csvFiles filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:arrx]];
                if([cFiles count]==1) {
                    FL[FLCount]=cFiles[0];
                    FLCount++;
                }
            }
        }
        for(int i=0;i<FLCount;i++) {
            /*
             for(int j=1;j<10;j++) {
             localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"%@_%@_%d.csv",split2[i],split2[i],j]];
             if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
             }
             */
            //localFilePath=FL[i];
            //localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.csv",split2[i]]];
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"%@",FL[i]]];
            //localFilePath = [documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//base//word//%@.csv",split2[i]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[index][i]=split[1];
            cc[index][i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
    }
    //SYSPARA1=2;
    //SYSPARA2=3;
    //SYSPARA3=3;
    if(index <=100) {
        int pos=0;
        for(int k=0;k<SYSPARA2;k++) {
            for(int j=0;j<SYSCount;j++) {
                for(int i=0;i<9;i++) {
                    eword[pos]=ee[index][j];
                    cword[pos]=cc[index][j];
                    song[pos]=j;
                    if(i==0) mark[pos]=true;
                    else mark[pos]=false;
                    pos++;
                }
            }
        }
        wcount=pos;
        /*
         for(int k=0;k<SYSPARA3;k++) {
         for(int j=0;j<SYSCount;j++) {
         eword[pos]=ee[index][j];
         cword[pos]=cc[index][j];
         song[pos]=j;
         mark[pos]=false;
         pos++;
         
         }
         }
         
         wcount=pos;
         */
    }
    nowIdx=0;
    timecnt=0;
    test01=7;
    lacWord.text=@"";
    [self UpdateFrame];
    [self UpdateWord];
    cTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
     transport.host = ServerIP;
     transport.port = 1883;
     
     session = [[MQTTSession alloc] init];
     session.transport = transport;
     session.delegate=self;
     [session connectWithConnectHandler:^(NSError *error) {
         // Do some work
         
         [self publishCommandClassStart];
         
         NSLog(@"Subscription %@",uten_class);
         [session subscribeToTopic:uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
             if (error) {
                 NSLog(@"Subscription failed %@", error.localizedDescription);
             } else {
                 NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
             }
         }];

     }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/
- (IBAction)btBack:(id)sender {
    /*
    [cTimer invalidate];
    cTimer = nil;
    [session publishData:[[[NSString alloc] initWithFormat:(@"$%@,CLASS_STOP,%@,end~"),uten_class,ID] dataUsingEncoding:NSUTF8StringEncoding] onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
     }];
    [self dismissViewControllerAnimated:NO completion:nil];
     */
}
- (void) UpdateWord {
    NSString *oneWord;
    CGRect frame;
    NSRange needleRange;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    if(x < 0) x=0;
    NSLog(@"EEWORD:%@",eword[nowIdx]);
    for(int j=0;j<9;j++) {
        for(int i=0;i<100;i++) {
            int k=j*len+i;
            W00[k].hidden=YES; I00[k].hidden=YES;
            if(len >= (i+1)) {
                frame = W00[k].frame;
                frame.origin.y=20+80*j;
                frame.origin.x= x+12+i*30;
                frame.size.width=25;
                frame.size.height=75;
                W00[k].frame= frame;
                dW00[k].frame=frame;
                I00[k].frame=frame;
                needleRange = NSMakeRange(i,1);
                oneWord=[eword[nowIdx] substringWithRange:needleRange];
                W00[k].image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
                wordinx[i]=[self getWordInx:oneWord];
                if(j==0) W00[k].hidden=NO;
                else W00[k].hidden=YES;
                //W00[k].hidden=NO;
                I00[k].hidden=NO;
                [dW00[k] erase];
                dW00[k].hidden=NO;
            }
        }
    }
    
}
-(int ) getWordInx:(NSString *)str
{
    
        if([str isEqualToString:@"a"]) return 0;
        if([str isEqualToString:@"b"]) return 1;
        if([str isEqualToString:@"c"]) return 2;
        if([str isEqualToString:@"d"]) return 3;
        if([str isEqualToString:@"e"]) return 4;
        if([str isEqualToString:@"f"]) return 5;
        if([str isEqualToString:@"g"]) return 6;
        if([str isEqualToString:@"h"]) return 7;
        if([str isEqualToString:@"i"]) return 8;
        if([str isEqualToString:@"j"]) return 9;
        if([str isEqualToString:@"k"]) return 10;
        if([str isEqualToString:@"l"]) return 11;
        if([str isEqualToString:@"m"]) return 12;
        if([str isEqualToString:@"n"]) return 13;
        if([str isEqualToString:@"o"]) return 14;
        if([str isEqualToString:@"p"]) return 15;
        if([str isEqualToString:@"q"]) return 16;
        if([str isEqualToString:@"r"]) return 17;
        if([str isEqualToString:@"s"]) return 18;
        if([str isEqualToString:@"t"]) return 19;
        if([str isEqualToString:@"u"]) return 20;
        if([str isEqualToString:@"v"]) return 21;
        if([str isEqualToString:@"w"]) return 22;
        if([str isEqualToString:@"x"]) return 23;
        if([str isEqualToString:@"y"]) return 24;
        if([str isEqualToString:@"z"]) return 25;
     
    return 0;
}
-(NSString *) getWordPng:(NSString *)str
{
    if([str isEqualToString:@"a"]) return @"a1.png";
    if([str isEqualToString:@"b"]) return @"b1.png";
    if([str isEqualToString:@"c"]) return @"c1.png";
    if([str isEqualToString:@"d"]) return @"d1.png";
    if([str isEqualToString:@"e"]) return @"e1.png";
    if([str isEqualToString:@"f"]) return @"f1.png";
    if([str isEqualToString:@"g"]) return @"g1.png";
    if([str isEqualToString:@"h"]) return @"h1.png";
    if([str isEqualToString:@"i"]) return @"i1.png";
    if([str isEqualToString:@"j"]) return @"j1.png";
    if([str isEqualToString:@"k"]) return @"k1.png";
    if([str isEqualToString:@"l"]) return @"l1.png";
    if([str isEqualToString:@"m"]) return @"m1.png";
    if([str isEqualToString:@"n"]) return @"n1.png";
    if([str isEqualToString:@"o"]) return @"o1.png";
    if([str isEqualToString:@"p"]) return @"p1.png";
    if([str isEqualToString:@"q"]) return @"q1.png";
    if([str isEqualToString:@"r"]) return @"r1.png";
    if([str isEqualToString:@"s"]) return @"s1.png";
    if([str isEqualToString:@"t"]) return @"t1.png";
    if([str isEqualToString:@"u"]) return @"u1.png";
    if([str isEqualToString:@"v"]) return @"v1.png";
    if([str isEqualToString:@"w"]) return @"w1.png";
    if([str isEqualToString:@"x"]) return @"x1.png";
    if([str isEqualToString:@"y"]) return @"y1.png";
    if([str isEqualToString:@"z"]) return @"z1.png";
    return @"";
}
- (void) UpdateFrame {
    CGRect frame;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    for(int i=0;i<200;i++) {
        W00[i].hidden=YES;
        dW00[i].hidden=YES;
    }
    
    frame = lframe.frame; frame.origin.y=16;
    frame.origin.x= x;
    frame.size.width=24;
    frame.size.height=84;
    lframe.frame= frame;
    
    frame = mframe.frame; frame.origin.y=16;
    frame.origin.x= x+24;
    frame.size.width=30*len-10;
    
    //frame.size.width=24;
    frame.size.height=84;
    mframe.frame= frame;
    
    frame = rframe.frame; frame.origin.y=16;
    frame.origin.x= x+30*len;
    frame.size.width=24;
    frame.size.height=84;
    rframe.frame= frame;
    
    
    for(int i=0;i<9;i++) {
        
        frame = lf[i].frame; frame.origin.y=16+i*80;
        frame.origin.x= x;
        frame.size.width=24;
        frame.size.height=84;
        lf[i].frame= frame;
        
        frame = mf[i].frame; frame.origin.y=16+i*80;
        frame.origin.x= x+24;
        frame.size.width=30*len-10;
        
        //frame.size.width=24;
        frame.size.height=84;
        mf[i].frame= frame;
        
        frame = rf[i].frame; frame.origin.y=16+i*80;
        frame.origin.x= x+30*len;
        frame.size.width=24;
        frame.size.height=84;
        rf[i].frame= frame;
    }
    //   lframe.hidden=NO;
    //   mframe.hidden=NO;
    //   rframe.hidden=NO;
}
-(void)timesUp:(NSTimer *)timer{
    timecnt++;
    switch(timecnt) {
        case 5: //0.5
            laTimes.text=[NSString stringWithFormat:@"%d",test01];
            lacWord.text=cword[nowIdx];
            laStatus.text=@"Listen";
            [self UpdateFrame];
            [self UpdateWord];
            SoundStep=0;
            break;
        case 6:
        {
            NSString *base = DirSound;
            NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[song[nowIdx]][SoundStep]];
            NSURL *soundUrl = [NSURL fileURLWithPath:path];
            _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
            [_audioPlayer setVolume:1.0];
            //if(wcount == 76) [_audioPlayer setVolume:1.0];
            //else [_audioPlayer setVolume:0.0];
            [_audioPlayer play];
        }
            break;
        case 7:
            if(_audioPlayer.isPlaying) timecnt=6;
            else {
                SoundStep++;
                if(SoundStep < scnt[song[nowIdx]]) timecnt=5;
            }
            break;
        case 8:
        {
            int len=eword[nowIdx].length;
            int lcnt=0;
            for(int i=0;i<len;i++) {
                [dW00[i] isWorNumber:wordinx[i]];
                lcnt+=[dW00[i] isWord:W00[i].image];
            }
            NSLog(@"Total:%d,Len:%d",lcnt,len);
            lcnt/=len;
            if(lcnt >= 80) {
                [btSingal setImage:[UIImage imageNamed:@"O_Red.png"] forState:UIControlStateNormal];
                btSingal.hidden=NO;
                TotalPass++;
            } else {
                [btSingal setImage:[UIImage imageNamed:@"wait_red.png"] forState:UIControlStateNormal];
                btSingal.hidden=NO;
                TotalNG++;
            }
            laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
            laPass.text=[NSString stringWithFormat:@"%04d",TotalPass];
            laStatus.text=eword[nowIdx];
            laStatus.hidden=NO;
        }
            waitcnt=SYSPARA4;
            timecnt=54;
            break;
        case 55:
            if(nowIdx==(wcount-1)) timecnt=99;
            else {
                if(waitcnt==0) {
                    nowIdx++;
                    timecnt=4;
                } else {
                    timecnt=54;
                    waitcnt--;
                }
            }
            break;
        case 100:
        {
            /*
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"本單元測試結束" delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
            [alertView show];
             */
            [cTimer invalidate];
            cTimer = nil;
            [self publishCommandClassStop];
            [self dismissViewControllerAnimated:NO completion:nil];
        }
            break;
        case 102:
            timecnt=101;
            break;
            
    }
    //NSLog(@"TimeUp");
}
- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    // 收到中斷訊息
    if (command.isBye) {
        [cTimer invalidate];
        cTimer = nil;
        [self publishCommandClassStop];
        NSLog(@"PLAY END :[%d][%d] %.2f",0,0,0);
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

// publish class start
- (void)publishCommandClassStart {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStart;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

// publish class stop
- (void)publishCommandClassStop {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

@end
