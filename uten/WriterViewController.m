//
//  WriterViewController.m
//  uten
//
//  Created by 簡大翔 on 2018/8/13.
//  Copyright © 2018年 bekubee. All rights reserved.
//

#import "WriterViewController.h"
#import "ViewController.h"
#import "UIImage+Color.h"
#import "UIImage+Rotate.h"
#import "UIImage+SubImage.h"
#import "UIImage+Gif.h"
#import "MQTTClient.h"
#import <ReactiveObjC/ReactiveObjC.h>
#import <Masonry/Masonry.h>
#import "UIColor+X.h"
#import "UIScreen+X.h"
#import "UTENUtility.h"
#import "UTENEnum.h"
#import "UTENFile.h"
#import "UTENFile+X.h"
#import "FLAnimatedImage.h"
#import "ComboViewModel.h"
#import "UTENComboModel.h"
#import "BombViewModel.h"
#import "FourPickOneViewModel.h"
#import "UTENBombModel.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "UTENEnum.h"
#import <MMKV/MMKV.h>
#import "UTENScoreModel.h"

static const int MAX_WORD = 4000;

//寫字1順(計) 寫句1順(計) 寫句1亂(計)
@interface WriterViewController ()  <MQTTSessionDelegate> {
    MQTTSession *session;
    NSString *ID;
    IBOutlet UIButton *btStartTime;
    IBOutlet UIButton *btTimes;
    IBOutlet UILabel *laTimes;
    IBOutlet UIButton *btSingal;
    IBOutlet UILabel *laStatus;
    IBOutlet UILabel *lacWord;
    // 通過計數顯示器
    IBOutlet UILabel *laPass;
    // 失敗計數顯示器
    IBOutlet UILabel *laNG;
    NSTimer *cTimer;
    IBOutlet UIImageView *lframe;
    IBOutlet UIImageView *mframe;
    IBOutlet UIImageView *rframe;

    int classvalue;
    int wcount;
    NSString *eword[4000];
    NSString *cword[4000];
    int song[4000];
    int pta[4000];
    bool mark[4000];
    NSString *ss[4000][16];
    int scnt[4001];
    int timecnt;
    int test01;
    int nowIdx;
    AVAudioPlayer *_audioPlayer;
    // 正確計數器
    int TotalPass;
    // 錯誤計數器
    int TotalNG;
    int WordsId;

    int SYSCount;
    int SYSType;  //1:SPLIT SAY 2:ALL SAY
    int SYSPARA1; //MIN LOOP (N);
    int SYSPARA2; //MAX LOOP (M)
    int SYSPARA3; //Random time(R)
    int SYSPARA4;
    int wcolor;
    UIImageView *W00[200];
    UIImageView *W01[200];
    UIImageView *I00[200];
    int wordinx[200];
    SignatureDrawView *dW00[200];
    NSString *DirTest;
    NSString *DirSound;
    NSString *DirWord;
    int SoundStep;
    int waitcnt;
    NSString *ServerIP;
    Float64 mp3time;
    int mp3cnt;
    int musttime;
    //
    int kpos;
    int kloop;
    int kstart;
    int kstop;
    Boolean kmark;
    Boolean krandom;
    NSString *ee[4000];
    NSString *cc[4000];
    NSString *uten_class,*r_uten_class;
    int songdelay;
    int dlytype;
    int eecolor[200][20];
    NSArray *cda;
    int plen;
    int novoice;
}
// 回合結果的通知
@property (nonatomic, strong) RACSubject<NSNumber *> *resultSignal;
// duration
@property (nonatomic, assign, readonly) NSTimeInterval roundDuration;
// remaining time
@property (nonatomic, assign, readonly) NSTimeInterval remainingTime;
// current time
@property (nonatomic, assign, readonly) int timerData;
// UILabel property
@property (nonatomic, weak) IBOutlet UILabel *labelTimer;
// 記錄回合開始時間
@property (nonatomic, assign, readonly) NSTimeInterval roundStartTime;
// 連擊99的 view model
@property (nonatomic, strong) ComboViewModel *comboViewModel;
// 轟炸竹東的 view model
@property (nonatomic, strong) BombViewModel *bombViewModel;
// 四選一的 view model
@property (nonatomic, strong) FourPickOneViewModel *fourPickOneViewModel;
// 寫字區 offset
@property (nonatomic, assign) CGPoint offset;
// 寫字區背景View
@property (nonatomic, strong) UIView *writeBackgroundView;
@end

@implementation WriterViewController

- (NSTimeInterval)calculateRoundStep:(NSUInteger)index {
    NSTimeInterval step = 0.0;
    // base
    NSTimeInterval baseStep = index == 0 ? 24.0 : 20.0;
    step += baseStep;
    // mp3
    NSUInteger soundStep = 0;
    NSTimeInterval mp3Step = 0.0;
    NSUInteger songIndex = song[index];
    BOOL enableVoice = novoice == 0;
    while (enableVoice) {
        NSString *file = ss[songIndex][soundStep];
        NSString *path = [NSString stringWithFormat:@"%@/%@", DirSound, file];
        NSError *error;
        NSURL *soundUrl = [NSURL fileURLWithPath:path];
        AVAudioPlayer *audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:&error];
        // NSLog(@"時間 MP3: %@ Length: %.2f", file, audioPlayer.duration);
        NSTimeInterval audioStep = (ceil(audioPlayer.duration * 10.0) + 1.0);
        mp3Step += audioStep;
        // soundStep
        soundStep++;
        if (soundStep >= scnt[songIndex]) {
            break;
        }
        // divider
        mp3Step++;
    }
    step += mp3Step;
    // song delay
    NSTimeInterval songdelay = 0;
    switch (dlytype) {
        case 1: songdelay = eword[index].length * 5.0; break;
        case 2: songdelay = eword[index].length * 6.0; break;
        case 3: songdelay = eword[index].length * 7.0; break;
        case 4: songdelay = eword[index].length * 7.0; break;
        default: songdelay= eword[index].length * 7.0; break;
    }
    step += (songdelay + 1.0);

    NSTimeInterval back = 0.0;
    switch(dlytype) {
        case 1: back = 4.0; break; // 0.8
        case 2: back = 2.0; break; // 0.9
        case 3: back = 2.0; break; // 0.9
    }
    step -= back;

    NSLog(@"%@回合 step: %@", @(index), @(step));
    return step;
}

// 剩餘時間
- (NSTimeInterval)remainingTime {
    NSDate *currentDate = [NSDate date];
    // 計算經過時間
    NSTimeInterval elapsedTime = currentDate.timeIntervalSince1970 - self.roundStartTime;
    return self.roundDuration - elapsedTime;
}

- (void)setupBomb {
    self.bombViewModel = [[BombViewModel alloc] init];
    UILabel *label;
    [self.bombViewModel setupLayout:self.view withPassLabel:laPass andNGLabel:laNG andTimerLabel:&label];
    self.labelTimer = label;
}

- (void)setupCombo {
    self.comboViewModel = [[ComboViewModel alloc] init];
    UILabel *label;
    [self.comboViewModel setupLayout:self.view withPassLabel:laPass andNGLabel:laNG andTimerLabel:&label];
    self.labelTimer = label;
}

- (void)resetRoundStartTime {
    DDLogDebugTag(@"WriterViewController", @"resetRoundStartTime");
    DDLogDebugTag(@"WriterViewController", @"nowIdx: %d", nowIdx);
    DDLogDebugTag(@"WriterViewController", @"cword[nowIdx]: %@", cword[nowIdx]);
    DDLogDebugTag(@"WriterViewController", @"eword[nowIdx]: %@", eword[nowIdx]);
    _roundDuration = [self calculateRoundStep:nowIdx] * 0.1;
    _roundStartTime = [NSDate date].timeIntervalSince1970;
    if (self.bombViewModel != nil) {
        self.bombViewModel.text = cword[nowIdx];
    }
    if (self.fourPickOneViewModel != nil) {
        self.fourPickOneViewModel.currentPlayingText = eword[nowIdx];
    }
    // next frame update
    [self performSelector:@selector(onNextFrame) withObject:nil afterDelay:0];
}

- (void)onNextFrame {
    if (self.bombViewModel != nil) {
        [self.bombViewModel createTankAndEmit:self.view withDuration:self.roundDuration];
    }
}

- (void)setupObservable {
    // 計時器的 observable
    {
        static const NSTimeInterval kInterval = 0.1;
        RACSignal *timerSignal = [RACSignal interval:kInterval onScheduler:[RACScheduler mainThreadScheduler]];
        // take until
        timerSignal = [timerSignal takeUntil:self.rac_willDeallocSignal];
        @weakify(self);
        [timerSignal subscribeNext:^(id  _Nullable x) {
            @strongify(self);
            int max = MAX(0, self.remainingTime);
            if (self.timerData != max) {
                _timerData = max;
                [self updateTimer];
            }
        }];
    }
    // 結果的 signal
    {
        self.resultSignal = [RACSubject subject];
        // 訂閱 signalUntilDealloc，只要 self 沒有被釋放，就會接收到 signal 的值
        @weakify(self);
        [[self.resultSignal takeUntil:self.rac_willDeallocSignal] subscribeNext:^(id x) {
            @strongify(self);
            NSLog(@"接收到的值: %@", x);
            [self updateResult:x];
        }];
    }
}

- (void)updateResult:(NSNumber *)result {
    // 轟炸竹東更新結果
    if (self.bombViewModel != nil) {
        [self.bombViewModel createExplosion:self.view withResult:result.integerValue];
    }
    // 連擊99更新結果
    else if (self.comboViewModel != nil) {
        [self.comboViewModel onResult:result];
        int comboCounter = self.comboViewModel.comboCounter;
        // 更新結果
        // attack
        // if (self.comboCounter >= 2) {
        //     CGPoint point = self.comboModel.randomPoint;
        //     CGFloat x = UIScreen.width * point.x;
        //     CGFloat y = UIScreen.height * point.y;
        //     UTENFile *image = self.comboModel.randomAttackImage;
        //     [self showSpark:image.name withPoint:CGPointMake(x, y)];
        // }
        static const int MAX_SPARK = 6;
        for (int i = 0; i < MIN(comboCounter, MAX_SPARK); i++) {
            CGPoint point = self.comboViewModel.randomPoint;
            CGFloat x = UIScreen.width * point.x;
            CGFloat y = UIScreen.height * point.y;
            UTENFile *file = [self.comboViewModel randomAttackImageWithComboNumber:comboCounter];
            if (file != nil) {
                [self.comboViewModel showSpark:file withPoint:CGPointMake(x, y)];
            }
        }
        // combo
        if (comboCounter >= 1) {
            CGPoint point = self.comboViewModel.randomPoint;
            CGFloat x = UIScreen.width * point.x;
            CGFloat y = UIScreen.height * point.y;
            UTENFile *file = self.comboViewModel.randomComboImage;
            if (file != nil) {
                [self.comboViewModel showSpark:file withPoint:CGPointMake(x, y)];
            }
        }
    }
}

- (void)updateTimer {
    // 更新倒數時間
    self.labelTimer.text = [NSString stringWithFormat:@"%d", self.timerData];
    // 使用 animation 讓字體變大
    self.labelTimer.transform = CGAffineTransformIdentity;
    @weakify(self);
    [UIView animateWithDuration:0.2 animations:^{
        @strongify(self);
        self.labelTimer.transform = CGAffineTransformMakeScale(2.0, 2.0);
    } completion:^(BOOL finished) {
        // @strongify(self);
        // 恢復原狀
        // self.labelTimer.transform = CGAffineTransformIdentity;
    }];
}

-(void) MakeTest
{
    int rpos=kpos;
    for(int i=0;i<kloop;i++) {
        for(int j=kstart;j<kstop;j++) {
            eword[kpos]=ee[j];  //    NSString *eword[4000];
            cword[kpos]=cc[j];  //    NSString *cword[4000];
            song[kpos]=j;
            NSLog(@"kpos=%d,eword[%d]=%@,cword[%d]=%@,song[%d]=%d",kpos,kpos,eword[kpos],kpos,cword[kpos],kpos,song[kpos]);
            if(kmark) {
                if(i==0) mark[kpos]=true;
                else mark[kpos]=false;
            }
            kpos++;
        }
    }
    if(krandom) {
        int rp,rc;
        rp=rpos;
        rc=kstop-kstart;
        NSLog(@"rp=rpos=%d,rc=kstop(%d)-kstart(%d)=%d",rpos,kstop,kstart,rc);
        // A Random x 2
        for(int i=0;i<kloop;i++)
            for(int j=kstart;j<kstop;j++) {
                NSString *tmp;
                int itmp;
                if(arc4random_uniform(2)==1) { //swap //假如亂數,隨機交換，前後對調
                    if(j!=rc-1) {
                        NSLog(@"eword[%d]=%@,eword[%d]=%@",rp+i*rc+j,eword[rp+i*rc+j],rp+i*rc+j+1,eword[rp+i*rc+j+1]);
                        tmp=eword[rp+i*rc+j]; eword[rp+i*rc+j]=eword[rp+i*rc+j+1]; eword[rp+i*rc+j+1]=tmp;
                        NSLog(@"eword[%d]=%@,eword[%d]=%@",rp+i*rc+j,eword[rp+i*rc+j],rp+i*rc+j+1,eword[rp+i*rc+j+1]);
                        NSLog(@"cword[%d]=%@,cword[%d]=%@",rp+i*rc+j,cword[rp+i*rc+j],rp+i*rc+j+1,cword[rp+i*rc+j+1]);
                        tmp=cword[rp+i*rc+j]; cword[rp+i*rc+j]=cword[rp+i*rc+j+1]; cword[rp+i*rc+j+1]=tmp;
                        NSLog(@"cword[%d]=%@,cword[%d]=%@",rp+i*rc+j,cword[rp+i*rc+j],rp+i*rc+j+1,cword[rp+i*rc+j+1]);
                        NSLog(@"song[%d]=%d,song[%d]=%d",rp+i*rc+j,song[rp+i*rc+j],rp+i*rc+j+1,song[rp+i*rc+j+1]);
                        itmp=song[rp+i*rc+j]; song[rp+i*rc+j]=song[rp+i*rc+j+1];   song[rp+i*rc+j+1]=itmp;
                        NSLog(@"song[%d]=%d,song[%d]=%d",rp+i*rc+j,song[rp+i*rc+j],rp+i*rc+j+1,song[rp+i*rc+j+1]);
                    } else {
                        NSLog(@"else:eword[%d]=%@,eword[%d]=%@",rp+i*rc+j,eword[rp+i*rc+j],rp+i*rc,eword[rp+i*rc]);
                        tmp=eword[rp+i*rc+j]; eword[rp+i*rc+j]=eword[rp+i*rc]; eword[rp+i*rc]=tmp;
                        NSLog(@"else:eword[%d]=%@,eword[%d]=%@",rp+i*rc+j,eword[rp+i*rc+j],rp+i*rc,eword[rp+i*rc]);
                        NSLog(@"else:cword[%d]=%@,cword[%d]=%@",rp+i*rc+j,cword[rp+i*rc+j],rp+i*rc,cword[rp+i*rc]);
                        tmp=cword[rp+i*rc+j]; cword[rp+i*rc+j]=cword[rp+i*rc]; cword[rp+i*rc]=tmp;
                        NSLog(@"else:cword[%d]=%@,cword[%d]=%@",rp+i*rc+j,cword[rp+i*rc+j],rp+i*rc,cword[rp+i*rc]);
                        NSLog(@"else:song[%d]=%d,song[%d]=%d",rp+i*rc+j,song[rp+i*rc+j],rp+i*rc,song[rp+i*rc]);
                        itmp=song[rp+i*rc+j]; song[rp+i*rc+j]=song[rp+i*rc]; song[rp+i*rc]=itmp;
                        NSLog(@"else:song[%d]=%d,song[%d]=%d",rp+i*rc+j,song[rp+i*rc+j],rp+i*rc,song[rp+i*rc]);
                    }
                }
            }

    }
}
-(void) MakeWordDB
{
    int pt=0;
    NSArray *fda=[self listFileAtPath:[NSString stringWithFormat:@"%@",DirWord]];
    DDLogDebugTag(@"WriterViewController", @"MakeWordDB:DirWord=%@", DirWord);
    NSLog(@"MakeWordDB:DirWord=%@",DirWord);
    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *localFilePath=NULL;
        NSString *ssrc=cda[5+x*2];
        int cnt=0;
        NSString *xf[10];
        for(int i=0; i<[fda count]; ++i) {
            NSArray *cp = [fda[i] componentsSeparatedByString:@"_"];
            if([cp count] >=3) {
                NSLog(@"MakeWordDB:if[cp count]>=3, [cp count]=%lu",[cp count]);
                NSString *cm=cp[0];
                if([cm isEqualToString:ssrc]) {
                    cnt++;
                    //   NSLog(@"CP File:%@:%@",fda[i],cp[2]);
                    if([cp[2] isEqualToString:@"1.csv"]) xf[0] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"2.csv"]) xf[1] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"3.csv"]) xf[2] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"4.csv"]) xf[3] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"5.csv"]) xf[4] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"6.csv"]) xf[5] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"7.csv"]) xf[6] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"8.csv"]) xf[7] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"9.csv"]) xf[8] = [[NSString alloc] initWithString:fda[i]];
                }
            }
        }
        for(int i=0;i<cnt;i++) NSLog(@"MakeWordDB:CP[-]:xf[%d]%@",i,xf[i]);
        for(int u=0;u<cnt;u++) {
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@",xf[u]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"MakeWordDB:xf[%d]=%@,str=%@,split=%@",u,xf[u],str,split);

            int sz=[split[4] intValue];
            int y=sz-1;
            cc[pt]=split[2];
            NSLog(@"MakeWordDB:y=%d=sz-1,sz=%d=[split[4]=(%@)],cc[pt]=split[2]=%@",y,sz,split[4],cc[pt]);
            if([split[5+y*2] isEqualToString:@"*"]){
                ee[pt]=split[1];
                NSLog(@"MakeWordDB:if(*):ee[%d]=split[1]=%@",pt,ee[pt]);
            }
            else {
                ee[pt]=split[5+y*2];
                NSLog(@"MakeWordDB:else(!*):ee[%d]=split[5+%d*2]=%@",pt,y,ee[pt]);
            }
            ss[pt][0]=split[6+y*2];
            scnt[pt]=0;
            NSLog(@"MakeWordDB:ss[%d][0]=split[6+%d*2]=%@,scnt[%d]=0",pt,y,ss[pt][0],pt);
            int hc=0;
            int bc=0;
            for(int z=0;z<sz-1;z++) {
                NSString *eu=split[5+z*2];
                NSLog(@"MakeWordDB:eu=split[5+%d*2]=%@",z,split[5+z*2]);
                if([split[5+z*2] isEqualToString:@"*"]==false) {
                    NSLog(@"MakeWordDB:'*'!=split[5+%d*2]=%@,[eu length]=%lu",z,split[5+z*2],[eu length]);
                    for(int t=0;t<[eu length];t++)
                        NSLog(@"MakeWordDB:eecolor[%d][%d]=%d=bc％2,bc=%d",pt,hc+1,eecolor[pt][hc+1],bc);
                    eecolor[pt][hc++]=bc%2;
                    bc++;
                }
                NSLog(@"MakeWordDB:ss[%d][%d]=split[6+%d*2]=%@,scnt[%d]=%d,scnt[pt]++=%d",pt,z,z,split[6+z*2],pt,scnt[pt],scnt[pt]++);
                ss[pt][z]=split[6+z*2];
                scnt[pt]++;
            }
            NSLog(@"MakeWordDB:ss[%d][%d]=split[6+(%d-1)*2]=%@,scnt[%d]=%d,scnt[pt]++=%d",pt,sz-1,sz,split[6+(sz-1)*2],pt,scnt[pt],scnt[pt]++);
            ss[pt][sz-1]=split[6+(sz-1)*2];
            scnt[pt]++;
            pt++;
        }
        NSLog(@"MakeWordDB:pta[%d]=pt=%d",x,pt);
        pta[x]=pt;
    }
    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *localFilePath=NULL;
        NSString *ssrc=cda[5+x*2];
        int cnt=0;
        NSString *xf[10];
        for(int i=0; i<[fda count]; ++i) {
            NSArray *cp = [fda[i] componentsSeparatedByString:@"_"];
            if([cp count] >=3) {
                NSLog(@"MakeWordDB:if[cp count]>=3, [cp count]=%lu",[cp count]);
                NSString *cm=cp[0];
                if([cm isEqualToString:ssrc]) {
                    cnt++;
                     //   NSLog(@"CP File:%@:%@",fda[i],cp[2]);
                    if([cp[2] isEqualToString:@"1.csv"]) xf[0] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"2.csv"]) xf[1] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"3.csv"]) xf[2] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"4.csv"]) xf[3] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"5.csv"]) xf[4] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"6.csv"]) xf[5] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"7.csv"]) xf[6] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"8.csv"]) xf[7] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"9.csv"]) xf[8] = [[NSString alloc] initWithString:fda[i]];
                }
            }
        }
        for(int i=0;i<cnt;i++) NSLog(@"MakeWordDB:CP[-]:xf[%d]=%@",i,xf[i]);
        int u=cnt-1;
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@",xf[u]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"MakeWordDB:ee:%@",str);

            int sz=[split[4] intValue];
            int y=sz-1;
            cc[pt]=split[2];
            if([split[5+y*2] isEqualToString:@"*"]) ee[pt]=split[1];
            else ee[pt]=split[5+y*2];
            ss[pt][0]=split[6+y*2];
            scnt[pt]=0;
            int hc=0;
            int bc=0;
            for(int z=0;z<sz-1;z++) {
                NSString *eu=split[5+z*2];
                if([split[5+z*2] isEqualToString:@"*"]==false) {
                    for(int t=0;t<[eu length];t++)
                    eecolor[pt][hc++]=bc%2;
                    bc++;
                }
                ss[pt][z]=split[6+z*2];
                scnt[pt]++;
            }
            ss[pt][sz-1]=split[6+(sz-1)*2];
            scnt[pt]++;
            pt++;
        //pta[x]=pt;
        pta[(cda.count-4)/2+x]=pt;
    }
}
-(void) MakeWordDB_01
{
    int pt=0;
    NSArray *fda=[self listFileAtPath:[NSString stringWithFormat:@"%@",DirWord]];


    for(int x=0;x<(cda.count-4)/2;x++) {
        NSString *localFilePath=NULL;
        NSString *ssrc=cda[5+x*2];
        int cnt=0;
        NSString *xf[10];
        for(int i=0; i<[fda count]; ++i) {
            NSArray *cp = [fda[i] componentsSeparatedByString:@"_"];
            if([cp count] >=3) {
                NSString *cm=cp[0];
                if([cm isEqualToString:ssrc]) {
                    cnt++;
                     //   NSLog(@"CP File:%@:%@",fda[i],cp[2]);
                    if([cp[2] isEqualToString:@"1.csv"]) xf[0] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"2.csv"]) xf[1] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"3.csv"]) xf[2] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"4.csv"]) xf[3] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"5.csv"]) xf[4] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"6.csv"]) xf[5] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"7.csv"]) xf[6] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"8.csv"]) xf[7] = [[NSString alloc] initWithString:fda[i]];
                    if([cp[2] isEqualToString:@"9.csv"]) xf[8] = [[NSString alloc] initWithString:fda[i]];
                }
            }
        }
        for(int i=0;i<cnt;i++) NSLog(@"MakeWordDB:CP[-]:xf[%d]=%@",i,xf[i]);
        int u=cnt-1;
            localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@",xf[u]]];
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"MakeWordDB:ee:%@",str);

            int sz=[split[4] intValue];
            int y=sz-1;
            cc[pt]=split[2];
            if([split[5+y*2] isEqualToString:@"*"]) ee[pt]=split[1];
            else ee[pt]=split[5+y*2];
            ss[pt][0]=split[6+y*2];
            scnt[pt]=0;
            int hc=0;
            int bc=0;
            for(int z=0;z<sz-1;z++) {
                NSString *eu=split[5+z*2];
                if([split[5+z*2] isEqualToString:@"*"]==false) {
                    for(int t=0;t<[eu length];t++)
                    eecolor[pt][hc++]=bc%2;
                    bc++;
                }
                ss[pt][z]=split[6+z*2];
                scnt[pt]++;
            }
            ss[pt][sz-1]=split[6+(sz-1)*2];
            scnt[pt]++;
            pt++;
        pta[x]=pt;
       // pta[(da.count-4)/2+x]=pt;
    }
}
- (void)viewDidLoad {

    [super viewDidLoad];
    [self performSelector:@selector(onInit) withObject:nil afterDelay:0];
}

- (void)onInit {
    // @weakify(self);
    // NSArray<NSString *> *options = @[@"預設", @"連擊99", @"轟炸竹東"];
    // [[UTENUtility show:self withOptions:options] subscribeNext:(^(RACTwoTuple<NSNumber *, NSError *> *tuple) {
    //     @strongify(self);
    //     NSLog(@"選擇了第 %d 個選項", tuple.first.intValue);
    //     [self onReady:tuple.first.intValue];
    // })];
    [self onReady:self.writeModule];
}

- (void)onReady:(WriteModule)currentModule {
    // [super viewDidLoad];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    // Do any additional setup after loading the view.
    DDLogDebugTag(@"WriterViewController", @"seltag=%@", self.seltag);
    DDLogDebugTag(@"WriterViewController", @"uclass=%@", self.uclass);
    NSLog(@"%@_%@", _seltag,_uclass);
    int index=[_seltag intValue];
    int loop=100;
    int st=18;
    int se=20;
    NSString *tmp;
    int itmp;
    bool imark;
    NSString *NN[10];
    wcolor=0;
    novoice=0;
    dlytype=0;
    //Initial
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    uten_class = [defaults objectForKey:@"uten_class"];
    r_uten_class = [defaults objectForKey:@"r_uten_class"];
    DDLogDebugTag(@"WriterViewController", @"uten_class=%@", uten_class);
    DDLogDebugTag(@"WriterViewController", @"r_uten_class=%@", r_uten_class);
    songdelay=0;
    ID = [defaults objectForKey:@"ID"];
    for(int i=0;i<200;i++) {
        W00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        W00[i].image=[UIImage imageNamed:@"a1.png"];
        W00[i].alpha=0.3f;
        [self.view addSubview:W00[i]];
        W00[i].hidden=YES;

        W01[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        W01[i].image=[UIImage imageNamed:@"a1.png"];
        W01[i].alpha=1.0f;
        [self.view addSubview:W01[i]];
        W01[i].hidden=YES;

        I00[i] =[[UIImageView alloc] initWithFrame:CGRectMake(50,50,50,150)];
        I00[i].image=[UIImage imageNamed:@"bk.png"];
        I00[i].alpha=1.0f;
        [self.view addSubview:I00[i]];
        I00[i].hidden=YES;
        dW00[i] =[[SignatureDrawView alloc] initWithFrame:CGRectMake(50,50,25,75)];
        [self.view addSubview:dW00[i]];
        dW00[i].hidden=YES;
    }
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
     transport.host = ServerIP;
     transport.port = 1883;

     session = [[MQTTSession alloc] init];
     session.transport = transport;
     session.delegate=self;
     [session connectWithConnectHandler:^(NSError *error) {
         // Do some work

         [self publishCommandClassStart];

         NSLog(@"Subscription ％＠",uten_class);
         [session subscribeToTopic:uten_class atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
             if (error) {
                 NSLog(@"Subscription failed %@", error.localizedDescription);
             } else {
                 NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
             }
         }];

     }];
    //

    NSString *documentsDirectoryPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    DirTest=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//test//",_uclass,_uclass]];
    DirSound=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//sound//",_uclass,_uclass]];
    DirWord=[documentsDirectoryPath stringByAppendingPathComponent:[NSString stringWithFormat:@"//class//%@//%@//word//",_uclass,_uclass]];

    //@"[up3u5u8][寫3][tall][short][old][young]"

    NSCharacterSet *set = [NSCharacterSet characterSetWithCharactersInString:@"[]"];
    cda = [_seltag componentsSeparatedByCharactersInSet:set];

    wcount=0;
    if ([cda[3] isEqualToString:@"寫字3"]) {
        dlytype=1;
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=4; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<4;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    if(k==0) kmark=true;
                    else kmark=false;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫字6"]) {
        dlytype=1;
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=7; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<7;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    if(k==0) kmark=true;
                    else kmark=false;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫字9"]) {
        dlytype=3;
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall*2;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=10; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<10;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    if(k==0) kmark=true;
                    else kmark=false;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;

    }
    if ([cda[3] isEqualToString:@"寫字1順"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫字1亂"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        int pos=0; int i=0;
        for(int j=0;j<kpos;j++) {
            NSString *tmp;
            int itmp;
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=kpos-1) {
                        tmp=eword[pos+i*kpos+j]; eword[pos+i*kpos+j]=eword[pos+i*kpos+j+1]; eword[pos+i*kpos+j+1]=tmp;
                        tmp=cword[pos+i*kpos+j]; cword[pos+i*kpos+j]=cword[pos+i*kpos+j+1]; cword[pos+i*kpos+j+1]=tmp;
                        itmp=song[pos+i*kpos+j]; song[pos+i*kpos+j]=song[pos+i*kpos+j+1]; song[pos+i*kpos+j+1]=itmp;
                    } else {
                        tmp=eword[pos+i*kpos+j]; eword[pos+i*kpos+j]=eword[pos+i*kpos]; eword[pos+i*kpos]=tmp;
                        tmp=cword[pos+i*kpos+j]; cword[pos+i*kpos+j]=cword[pos+i*kpos]; cword[pos+i*kpos]=tmp;
                        itmp=song[pos+i*kpos+j]; song[pos+i*kpos+j]=song[pos+i*kpos]; song[pos+i*kpos]=itmp;
                    }
                }
        }
        wcount=kpos;
    }
    //
    if ([cda[3] isEqualToString:@"寫拼3"]) {
        dlytype=1;
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=4; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<4;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    if(k==0) kmark=true;
                    else kmark=false;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫拼6"]) {
        dlytype=1;
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=7; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<7;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    if(k==0) kmark=true;
                    else kmark=false;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫拼9"]) {
        dlytype=3;
        [self MakeWordDB];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        int jp,jpl;
        for(int j=0;j<pall*2;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=10; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
            jp=j;
            jpl=stl;
        }
        for(int k=0;k<10;k++) {
            stl=jpl;
            for(int j=jp;j<pall*2;j++) {
                for(int i=stl;i<pta[j];i++) {
                    kloop=1; kstart=i; kstop=i+1;
                    if(k==0) kmark=true;
                    else kmark=false;
                    krandom=false; [self MakeTest];
                }
                stl=pta[j];
            }
        }
        wcount=kpos;

    }
    if ([cda[3] isEqualToString:@"寫句1順"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        wcount=kpos;
    }
    //DB
    if ([cda[3] isEqualToString:@"寫句1順(計)"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        novoice=1;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=false; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫字1順(計)"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        novoice=1;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=false; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        wcount=kpos;
    }

    if ([cda[3] isEqualToString:@"寫拼1順"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        wcount=kpos;
    }
    if ([cda[3] isEqualToString:@"寫拼1亂"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=true; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        int pos=0; int i=0;
        for(int j=0;j<kpos;j++) {
            NSString *tmp;
            int itmp;
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=kpos-1) {
                        tmp=eword[pos+i*kpos+j]; eword[pos+i*kpos+j]=eword[pos+i*kpos+j+1]; eword[pos+i*kpos+j+1]=tmp;
                        tmp=cword[pos+i*kpos+j]; cword[pos+i*kpos+j]=cword[pos+i*kpos+j+1]; cword[pos+i*kpos+j+1]=tmp;
                        itmp=song[pos+i*kpos+j]; song[pos+i*kpos+j]=song[pos+i*kpos+j+1]; song[pos+i*kpos+j+1]=itmp;
                    } else {
                        tmp=eword[pos+i*kpos+j]; eword[pos+i*kpos+j]=eword[pos+i*kpos]; eword[pos+i*kpos]=tmp;
                        tmp=cword[pos+i*kpos+j]; cword[pos+i*kpos+j]=cword[pos+i*kpos]; cword[pos+i*kpos]=tmp;
                        itmp=song[pos+i*kpos+j]; song[pos+i*kpos+j]=song[pos+i*kpos]; song[pos+i*kpos]=itmp;
                    }
                }
        }
        wcount=kpos;
    }
    //DB
    if ([cda[3] isEqualToString:@"寫句1亂(計)"]) {
        dlytype=4;
        [self MakeWordDB_01];
        kpos=0;
        novoice=1;
        int pall=(cda.count-4)/2;
        int stl=0;
        for(int j=0;j<pall;j++) {
            for(int i=stl;i<pta[j];i++) {
                kloop=1; kstart=i; kstop=i+1; kmark=false; krandom=false; [self MakeTest];
            }
            stl=pta[j];
        }
        int pos=0; int i=0;
        for(int j=0;j<kpos;j++) {
            NSString *tmp;
            int itmp;
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=kpos-1) {
                        tmp=eword[pos+i*kpos+j]; eword[pos+i*kpos+j]=eword[pos+i*kpos+j+1]; eword[pos+i*kpos+j+1]=tmp;
                        tmp=cword[pos+i*kpos+j]; cword[pos+i*kpos+j]=cword[pos+i*kpos+j+1]; cword[pos+i*kpos+j+1]=tmp;
                        itmp=song[pos+i*kpos+j]; song[pos+i*kpos+j]=song[pos+i*kpos+j+1]; song[pos+i*kpos+j+1]=itmp;
                    } else {
                        tmp=eword[pos+i*kpos+j]; eword[pos+i*kpos+j]=eword[pos+i*kpos]; eword[pos+i*kpos]=tmp;
                        tmp=cword[pos+i*kpos+j]; cword[pos+i*kpos+j]=cword[pos+i*kpos]; cword[pos+i*kpos]=tmp;
                        itmp=song[pos+i*kpos+j]; song[pos+i*kpos+j]=song[pos+i*kpos]; song[pos+i*kpos]=itmp;
                    }
                }
        }
        wcount=kpos;
    }
/*
    if ([da[3] isEqualToString:@"寫1亂"]) {
        dlytype=4;
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                ss[i][j]=split[6+j*2];
            }
        }
        int cnt=(da.count-4)/2;
        int pos=0;
            for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=false;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
            }
        pos=0; int i=0;
        for(int j=0;j<cnt;j++) {
            NSString *tmp;
            int itmp;
                if(arc4random_uniform(2)==1) { //swap
                    if(j!=cnt-1) {
                        tmp=eword[pos+i*cnt+j]; eword[pos+i*cnt+j]=eword[pos+i*cnt+j+1]; eword[pos+i*cnt+j+1]=tmp;
                        tmp=cword[pos+i*cnt+j]; cword[pos+i*cnt+j]=cword[pos+i*cnt+j+1]; cword[pos+i*cnt+j+1]=tmp;
                        itmp=song[pos+i*cnt+j]; song[pos+i*cnt+j]=song[pos+i*cnt+j+1]; song[pos+i*cnt+j+1]=itmp;
                    } else {
                        tmp=eword[pos+i*cnt+j]; eword[pos+i*cnt+j]=eword[pos+i*cnt]; eword[pos+i*cnt]=tmp;
                        tmp=cword[pos+i*cnt+j]; cword[pos+i*cnt+j]=cword[pos+i*cnt]; cword[pos+i*cnt]=tmp;
                        itmp=song[pos+i*cnt+j]; song[pos+i*cnt+j]=song[pos+i*cnt]; song[pos+i*cnt]=itmp;
                    }
                }
        }
        wcount=cnt;
    }
    if ([da[3] isEqualToString:@"寫1順"]) {
        dlytype=4;
        for(int i=0;i<(da.count-4)/2;i++) {
            NSString *localFilePath=NULL;
            for(int j=0;j<10;j++) {
                localFilePath = [DirWord stringByAppendingPathComponent:[NSString stringWithFormat:@"//%@_%@_%d.csv",da[5+i*2],da[5+i*2],j]];
                if([[NSFileManager defaultManager] fileExistsAtPath:localFilePath]) break;
            }
            NSString *str = [NSString stringWithContentsOfFile:localFilePath encoding:NSUTF8StringEncoding error:NULL];
            NSArray *split = [str componentsSeparatedByString:@","];
            NSLog(@"ee:%@",str);
            ee[i]=split[1];
            cc[i]=split[2];
            scnt[i]=[split[4] intValue];
            for(int j=0;j<scnt[i];j++) {
                //ss[i][j]=split[6+j*2];
                ss[i][0]=split[6+j*2];
            }
            scnt[i]=1;
        }
        int cnt=(da.count-4)/2;
        int pos=0;
            for(int j=0;j<cnt;j++) {
                eword[pos]=ee[j];
                cword[pos]=cc[j];
                song[pos]=j;
                mark[pos]=false;
                //if(i==0) mark[pos]=true;
                //else mark[pos]=false;
                pos++;
            }
        wcount=cnt;
    }
 */
    cTimer = [NSTimer scheduledTimerWithTimeInterval:0.1 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    TotalPass=0;
    TotalNG=0;
    laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
    laPass.text=[NSString stringWithFormat:@"%04d",TotalPass];
    mp3time=0;
    mp3cnt=0;
    musttime=0;
    // 建立模組
    if (WriteModuleCombo == currentModule) {
        [self setupCombo];
    } else if (WriteModuleBomb == currentModule) {
        [self setupBomb];
    } else if (WriteModuleFourPickOne == currentModule) {
        [self setupFourPickOne];
    }
    // 寫字區背景
    self.writeBackgroundView = UIView.new;
    self.writeBackgroundView.backgroundColor = UIColor.whiteColor;
    // alpha 0.5
    self.writeBackgroundView.alpha = 0.5;
    [self.view insertSubview:self.writeBackgroundView atIndex:1];

    [self setupObservable];
    [self resetRoundStartTime];
}

- (void)setupFourPickOne {
    self.fourPickOneViewModel = [[FourPickOneViewModel alloc] initWithView:self.view];

    // 設置回調函數
    @weakify(self);
    self.fourPickOneViewModel.onBackPressed = ^() {
        @strongify(self);
        DDLogDebugTag(@"WriterViewController", @"onBackPressed");
    };

    // 初始化界面
    [self.fourPickOneViewModel setupLayout];
}

- (void)onClose {
    if (self.bombViewModel != nil) {
        [self.bombViewModel dispose];
        self.bombViewModel = nil;
    }
    if (self.comboViewModel != nil) {
        [self.comboViewModel dispose];
        self.comboViewModel = nil;
    }
    if (self.fourPickOneViewModel != nil) {
        [self.fourPickOneViewModel dispose];
        self.fourPickOneViewModel = nil;
    }
}

-(NSArray *)listFileAtPath:(NSString *)path
{
    //-----> LIST ALL FILES <-----//
    NSLog(@"LISTING ALL FILES FOUND");
    int count;
    NSArray *directoryContent = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:path error:NULL];
    for (count = 0; count < (int)[directoryContent count]; count++)
    {

        NSNumber *fileSizeValue = nil;
        NSString *filePath=path;
        NSURL *fileURL = [NSURL fileURLWithPath:filePath];
        NSError *fileSizeError = nil;
        [fileURL getResourceValue:&fileSizeValue
                           forKey:NSURLFileSizeKey
                            error:&fileSizeError];
        NSLog(@"WriterView:File %d: %@  %@", (count + 1),  [directoryContent objectAtIndex:count],fileSizeValue);
        /*
        if (fileSizeValue) {
        NSLog(@"File %d: %@  %@", (count + 1), [directoryContent objectAtIndex:count],fileSizeValue);
        } else {
            NSLog(@"File %d: %@", (count + 1), [directoryContent objectAtIndex:count]);
        }
        */
    }
    return directoryContent;
}
-(int ) getWordInx:(NSString *)str
{

        if([str isEqualToString:@"a"]) return 0;
        if([str isEqualToString:@"b"]) return 1;
        if([str isEqualToString:@"c"]) return 2;
        if([str isEqualToString:@"d"]) return 3;
        if([str isEqualToString:@"e"]) return 4;
        if([str isEqualToString:@"f"]) return 5;
        if([str isEqualToString:@"g"]) return 6;
        if([str isEqualToString:@"h"]) return 7;
        if([str isEqualToString:@"i"]) return 8;
        if([str isEqualToString:@"j"]) return 9;
        if([str isEqualToString:@"k"]) return 10;
        if([str isEqualToString:@"l"]) return 11;
        if([str isEqualToString:@"m"]) return 12;
        if([str isEqualToString:@"n"]) return 13;
        if([str isEqualToString:@"o"]) return 14;
        if([str isEqualToString:@"p"]) return 15;
        if([str isEqualToString:@"q"]) return 16;
        if([str isEqualToString:@"r"]) return 17;
        if([str isEqualToString:@"s"]) return 18;
        if([str isEqualToString:@"t"]) return 19;
        if([str isEqualToString:@"u"]) return 20;
        if([str isEqualToString:@"v"]) return 21;
        if([str isEqualToString:@"w"]) return 22;
        if([str isEqualToString:@"x"]) return 23;
        if([str isEqualToString:@"y"]) return 24;
        if([str isEqualToString:@"z"]) return 25;

    return 0;
}
-(NSString *) getWordPng:(NSString *)str
{
    if((wcolor%2)==0) {
        if([str isEqualToString:@"a"]) return @"a1.png";
        if([str isEqualToString:@"b"]) return @"b1.png";
        if([str isEqualToString:@"c"]) return @"c1.png";
        if([str isEqualToString:@"d"]) return @"d1.png";
        if([str isEqualToString:@"e"]) return @"e1.png";
        if([str isEqualToString:@"f"]) return @"f1.png";
        if([str isEqualToString:@"g"]) return @"g1.png";
        if([str isEqualToString:@"h"]) return @"h1.png";
        if([str isEqualToString:@"i"]) return @"i1.png";
        if([str isEqualToString:@"j"]) return @"j1.png";
        if([str isEqualToString:@"k"]) return @"k1.png";
        if([str isEqualToString:@"l"]) return @"l1.png";
        if([str isEqualToString:@"m"]) return @"m1.png";
        if([str isEqualToString:@"n"]) return @"n1.png";
        if([str isEqualToString:@"o"]) return @"o1.png";
        if([str isEqualToString:@"p"]) return @"p1.png";
        if([str isEqualToString:@"q"]) return @"q1.png";
        if([str isEqualToString:@"r"]) return @"r1.png";
        if([str isEqualToString:@"s"]) return @"s1.png";
        if([str isEqualToString:@"t"]) return @"t1.png";
        if([str isEqualToString:@"u"]) return @"u1.png";
        if([str isEqualToString:@"v"]) return @"v1.png";
        if([str isEqualToString:@"w"]) return @"w1.png";
        if([str isEqualToString:@"x"]) return @"x1.png";
        if([str isEqualToString:@"y"]) return @"y1.png";
        if([str isEqualToString:@"z"]) return @"z1.png";
       //
        if([str isEqualToString:@"A"]) return @"a2.png";
        if([str isEqualToString:@"B"]) return @"b2.png";
        if([str isEqualToString:@"C"]) return @"c2.png";
        if([str isEqualToString:@"D"]) return @"d2.png";
        if([str isEqualToString:@"E"]) return @"e2.png";
        if([str isEqualToString:@"F"]) return @"f2.png";
        if([str isEqualToString:@"G"]) return @"g2.png";
        if([str isEqualToString:@"H"]) return @"h2.png";
        if([str isEqualToString:@"I"]) return @"i2.png";
        if([str isEqualToString:@"J"]) return @"j2.png";
        if([str isEqualToString:@"K"]) return @"k2.png";
        if([str isEqualToString:@"L"]) return @"l2.png";
        if([str isEqualToString:@"M"]) return @"m2.png";
        if([str isEqualToString:@"N"]) return @"n2.png";
        if([str isEqualToString:@"O"]) return @"o2.png";
        if([str isEqualToString:@"P"]) return @"p2.png";
        if([str isEqualToString:@"Q"]) return @"q2.png";
        if([str isEqualToString:@"R"]) return @"r2.png";
        if([str isEqualToString:@"S"]) return @"s2.png";
        if([str isEqualToString:@"T"]) return @"t2.png";
        if([str isEqualToString:@"U"]) return @"u2.png";
        if([str isEqualToString:@"V"]) return @"v2.png";
        if([str isEqualToString:@"W"]) return @"w2.png";
        if([str isEqualToString:@"X"]) return @"x2.png";
        if([str isEqualToString:@"Y"]) return @"y2.png";
        if([str isEqualToString:@"Z"]) return @"z2.png";
    } else {
        if([str isEqualToString:@"a"]) return @"a1g.png";
        if([str isEqualToString:@"b"]) return @"b1g.png";
        if([str isEqualToString:@"c"]) return @"c1g.png";
        if([str isEqualToString:@"d"]) return @"d1g.png";
        if([str isEqualToString:@"e"]) return @"e1g.png";
        if([str isEqualToString:@"f"]) return @"f1g.png";
        if([str isEqualToString:@"g"]) return @"g1g.png";
        if([str isEqualToString:@"h"]) return @"h1g.png";
        if([str isEqualToString:@"i"]) return @"i1g.png";
        if([str isEqualToString:@"j"]) return @"j1g.png";
        if([str isEqualToString:@"k"]) return @"k1g.png";
        if([str isEqualToString:@"l"]) return @"l1g.png";
        if([str isEqualToString:@"m"]) return @"m1g.png";
        if([str isEqualToString:@"n"]) return @"n1g.png";
        if([str isEqualToString:@"o"]) return @"o1g.png";
        if([str isEqualToString:@"p"]) return @"p1g.png";
        if([str isEqualToString:@"q"]) return @"q1g.png";
        if([str isEqualToString:@"r"]) return @"r1g.png";
        if([str isEqualToString:@"s"]) return @"s1g.png";
        if([str isEqualToString:@"t"]) return @"t1g.png";
        if([str isEqualToString:@"u"]) return @"u1g.png";
        if([str isEqualToString:@"v"]) return @"v1g.png";
        if([str isEqualToString:@"w"]) return @"w1g.png";
        if([str isEqualToString:@"x"]) return @"x1g.png";
        if([str isEqualToString:@"y"]) return @"y1g.png";
        if([str isEqualToString:@"z"]) return @"z1g.png";
        if([str isEqualToString:@"A"]) return @"a1g.png";
        if([str isEqualToString:@"B"]) return @"b1g.png";
        if([str isEqualToString:@"C"]) return @"c1g.png";
        if([str isEqualToString:@"D"]) return @"d1g.png";
        if([str isEqualToString:@"E"]) return @"e1g.png";
        if([str isEqualToString:@"F"]) return @"f1g.png";
        if([str isEqualToString:@"G"]) return @"g1g.png";
        if([str isEqualToString:@"H"]) return @"h1g.png";
        if([str isEqualToString:@"I"]) return @"i1g.png";
        if([str isEqualToString:@"J"]) return @"j1g.png";
        if([str isEqualToString:@"K"]) return @"k1g.png";
        if([str isEqualToString:@"L"]) return @"l1g.png";
        if([str isEqualToString:@"M"]) return @"m1g.png";
        if([str isEqualToString:@"N"]) return @"n1g.png";
        if([str isEqualToString:@"O"]) return @"o1g.png";
        if([str isEqualToString:@"P"]) return @"p1g.png";
        if([str isEqualToString:@"Q"]) return @"q1g.png";
        if([str isEqualToString:@"R"]) return @"r1g.png";
        if([str isEqualToString:@"S"]) return @"s1g.png";
        if([str isEqualToString:@"T"]) return @"t1g.png";
        if([str isEqualToString:@"U"]) return @"u1g.png";
        if([str isEqualToString:@"V"]) return @"v1g.png";
        if([str isEqualToString:@"W"]) return @"w1g.png";
        if([str isEqualToString:@"X"]) return @"x1g.png";
        if([str isEqualToString:@"Y"]) return @"y1g.png";
        if([str isEqualToString:@"Z"]) return @"z1g.png";
    }
    return @"";
}
- (void) UpdateFrame {
    CGRect bgFrame;
    CGRect frame;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    for(int i=0;i<20;i++) {
        W00[i].hidden=YES;
        W01[i].hidden=YES;
        dW00[i].hidden=YES;
    }

    frame = lframe.frame; frame.origin.y=280;
    frame.origin.x= x;
    frame.size.width=24;
    frame.size.height=80;
    lframe.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 初始 rect
    bgFrame = lframe.frame;

    frame = mframe.frame; frame.origin.y=280;
    frame.origin.x= x+24;
    frame.size.width=30*len-10;

    //frame.size.width=24;
    frame.size.height=80;
    mframe.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    bgFrame = CGRectUnion(bgFrame, mframe.frame);

    frame = rframe.frame; frame.origin.y=280;
    frame.origin.x= x+30*len;
    frame.size.width=24;
    frame.size.height=80;

    rframe.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    bgFrame = CGRectUnion(bgFrame, rframe.frame);

    frame = laTimes.frame; frame.origin.y=220;
    frame.origin.x= x;
    laTimes.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    // bgFrame = CGRectUnion(bgFrame, laTimes.frame);

    frame = btTimes.frame; frame.origin.y=220;
    frame.origin.x= x;
    btTimes.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    // bgFrame = CGRectUnion(bgFrame, btTimes.frame);

    frame = laStatus.frame; frame.origin.y=140; //420
    frame.origin.x= x;
    laStatus.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    // bgFrame = CGRectUnion(bgFrame, laStatus.frame);

    frame = lacWord.frame; frame.origin.y=20; //360;
    frame.origin.x= x;
    lacWord.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    // bgFrame = CGRectUnion(bgFrame, lacWord.frame);

    frame = btSingal.frame; frame.origin.y=280;
    frame.origin.x= x-64; // x+30*len-20;
    btSingal.frame= CGRectOffset(frame, self.offset.x, self.offset.y);
    // 合併 rect
    // bgFrame = CGRectUnion(bgFrame, btSingal.frame);
    self.writeBackgroundView.frame = CGRectInset(bgFrame, -12, -12);

    btSingal.hidden=YES;
    lframe.hidden=NO;
    mframe.hidden=NO;
    rframe.hidden=NO;
    laTimes.hidden=YES;
    laStatus.hidden=YES;
    lacWord.hidden=NO;
    btTimes.hidden=YES;
}
- (void) UpdateWord {
    NSString *oneWord;
    CGRect bgFrame = CGRectZero;
    CGRect frame;
    NSRange needleRange;
    int len=eword[nowIdx].length;
    int y=280;
    int x=(1024-len*30)/2-12;
    NSLog(@"EEWORD:%@",eword[nowIdx]);
    for(int i=0;i<20;i++) {
        W00[i].hidden=YES; I00[i].hidden=YES;
        W01[i].hidden=YES;
        if(len >= (i+1)) {
            frame = W00[i].frame; frame.origin.y=283;
            frame.origin.x= x+12+i*30;
            frame.size.width=25;
            frame.size.height=75;
            W00[i].frame= CGRectOffset(frame, self.offset.x, self.offset.y);
            dW00[i].frame=CGRectOffset(frame, self.offset.x, self.offset.y);
            I00[i].frame=CGRectOffset(frame, self.offset.x, self.offset.y);
            // 合併 rect
            if (CGRectIsEmpty(bgFrame)) {
                bgFrame = W00[i].frame;
            } else {
                bgFrame = CGRectUnion(bgFrame, W00[i].frame);
            }
            //
            frame.origin.y=203;
            // 有位移則加上新的位移(需求)
            BOOL hasOffset = !CGPointEqualToPoint(self.offset, CGPointZero);
            CGPoint offset = hasOffset ? CGPointMake(-200, -160) : CGPointZero;
            W01[i].frame= CGRectOffset(frame, offset.x, offset.y);
            // 合併 rect
            bgFrame = CGRectUnion(bgFrame, W01[i].frame);

            needleRange = NSMakeRange(i,1);
            wcolor=eecolor[song[nowIdx]][i];
            oneWord=[eword[nowIdx] substringWithRange:needleRange];
            W00[i].image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
            W01[i].image=[[UIImage imageNamed: [self getWordPng:oneWord]] rotate:UIImageOrientationUp];
            wordinx[i]=[self getWordInx:oneWord];


            W00[i].hidden=!mark[nowIdx];
            I00[i].hidden=mark[nowIdx]; //NO;

            [dW00[i] erase];
            dW00[i].hidden=NO;
            dW00[i].alpha=0.6;
        }
    }
    // self.writeBackgroundView.frame = CGRectInset(bgFrame, -12, -12);
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.

}
-(void)timesUp:(NSTimer *)timer{
    timecnt++;
    musttime++;
    switch(timecnt) {

            // High school 0.8+0.5xN+0.8  Write 3
            // Mid         0.9+0.6*N+0.9  Write 6
            // Kid         0.9+0.7xN+0.9  Write 9 //
            // Test        1.0+0.7xN+1.0
        case 5: //0.5
            laTimes.text=[NSString stringWithFormat:@"%d",test01];
            lacWord.text=cword[nowIdx];
            laStatus.hidden=YES;
            if (self.bombViewModel != nil) {
                CGSize screen = self.view.bounds.size;
                CGFloat x = self.bombViewModel.isSrcFromRight ? -0.2 : 0.3;
                self.offset = CGPointMake(screen.width * x, 300);
            } else {
                self.offset = CGPointMake(0, 0);
            }
            [self UpdateFrame];
            [self UpdateWord];
            if(novoice) {
                laStatus.text=cword[nowIdx];
                laStatus.hidden=NO;
            }
            else laStatus.hidden=YES;
            SoundStep=0;
            songdelay=0;
            break;
        case 6:
            {

                NSString *base = DirSound;
                NSString *path = [NSString stringWithFormat:@"%@/%@", base,ss[song[nowIdx]][SoundStep]];
                NSURL *soundUrl = [NSURL fileURLWithPath:path];
                _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:soundUrl error:nil];
                [_audioPlayer setVolume:1.0];
                [_audioPlayer prepareToPlay];
                mp3time+=_audioPlayer.duration;
                NSLog(@"MP3 Length :[%d][%d] %.2f - %.2f",musttime,mp3cnt++,mp3time,_audioPlayer.duration);
                //if(wcount == 76) [_audioPlayer setVolume:1.0];
                //else [_audioPlayer setVolume:0.0];
                if(novoice==0) [_audioPlayer play];
            }
            break;
        case 7:
            //songdelay++;
            if(_audioPlayer.isPlaying) timecnt=6;

            else {
                SoundStep++;
                if(SoundStep < scnt[song[nowIdx]]) timecnt=5;

            }
            switch(dlytype) {
                case 1: songdelay=eword[nowIdx].length*5; break;
                case 2: songdelay=eword[nowIdx].length*6; break;
                case 3: songdelay=eword[nowIdx].length*7; break;
                case 4: songdelay=eword[nowIdx].length*7; break;
                default: songdelay=eword[nowIdx].length*7; break;
            }

            break;
        case 8:
            if(songdelay > 0) {
                songdelay--;
                timecnt=7;
            } else {
                switch(dlytype) {
                    case 1: timecnt+=4; break; //0.8
                    case 2: timecnt+=2; break; //0.9
                    case 3: timecnt+=2; break; //0.9
                }
            }
            break;
        case 25: //0.9 = 23 . 0.8 = 21 . 0.7=19
            {
//                if(WordsId >= 9999){
//                    WordsId = 0;
//                }//2024 1110 WordsId上傳 //vocabulary單字(就是eword)
                bool NoPass0 = 1;
                int len=eword[nowIdx].length;
                int lcnt=0;
                for(int i=0;i<len;i++) {
                    [dW00[i] isWorNumber:wordinx[i]];
                    if([dW00[i] isWord:W00[i].image] < 60){
                        NoPass0 = 0;
                    }//如單字中的其中字母有未通過,則布林值NoPass0(未通過)
                    lcnt+=[dW00[i] isWord:W00[i].image];
                    NSLog(@"CHK:%d,Len:%d",lcnt,wordinx[i]);
                    W01[i].hidden=NO;
                }
                NSLog(@"Total:%d,Len:%d",lcnt,len);
                lcnt/=len;
                NSLog(@"Score:%d", lcnt);
                static const int kPassedScore = 60;
                if( (lcnt >= kPassedScore) && NoPass0 ) {
                    [btSingal setImage:[UIImage imageNamed:@"O_Red.png"] forState:UIControlStateNormal];
                    btSingal.hidden=NO;
                    TotalPass++;
                    [self.resultSignal sendNext:@(ConfirmResultRight)];
                } else {
                    [btSingal setImage:[UIImage imageNamed:@"wait_red.png"] forState:UIControlStateNormal];
                    btSingal.hidden=NO;
                    TotalNG++;
                    [self.resultSignal sendNext:@(ConfirmResultWrong)];
                }
                laNG.text=[NSString stringWithFormat:@"%04d",TotalNG];
                laPass.text=[NSString stringWithFormat:@"%04d",TotalPass];
                laStatus.text=cword[nowIdx]; //eword[nowIdx];
                laStatus.hidden=NO;
                lacWord.hidden=NO;
            }
            waitcnt=SYSPARA4;
            timecnt=54;
            break;
        case 55:
            if(nowIdx==(wcount-1)) timecnt=99;
            else {
                if(waitcnt==0) {
                    nowIdx++;
                    [self resetRoundStartTime];
                    timecnt=4;
                } else {
                    timecnt=54;
                    waitcnt--;
                }
            }
            break;
        case 100:
        {
            /*
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:@"本單元測試結束" delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
            [alertView show];
             */
            [cTimer invalidate];
            cTimer = nil;
            [self publishCommandClassStop];
            NSLog(@"PLAY END :[%d][%d] %.2f",musttime,mp3cnt++,mp3time);
            [self onClose];
            [self dismissViewControllerAnimated:NO completion:nil];
        }
            break;
        case 102:
            timecnt=101;
            break;

    }
    //NSLog(@"TimeUp");
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/


- (IBAction)btTest:(id)sender {

    /*
    UIGraphicsBeginImageContext(self.drawSignView.bounds.size);
    [[self.drawSignView.layer presentationLayer] renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *viewImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    NSData *postData = UIImageJPEGRepresentation(viewImage, 1.0);
     */
}
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex{
    [cTimer invalidate];
    cTimer = nil;
    [self dismissViewControllerAnimated:NO completion:nil];

}
- (IBAction)btBack:(id)sender {
    /*
    [cTimer invalidate];
    cTimer = nil;
    [session publishData:[[[NSString alloc] initWithFormat:(@"$%@,CLASS_STOP,%@,end~"),uten_class,ID] dataUsingEncoding:NSUTF8StringEncoding] onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
     }];
     [self dismissViewControllerAnimated:NO completion:nil];
     */
}
- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // New message received in topic
    NSError *error;
    UTENCommand *command = [UTENCommand fromData:data error:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    // 收到中斷訊息
    if (command.isBye) {
        [cTimer invalidate];
        cTimer = nil;
        [self publishCommandClassStop];
        NSLog(@"PLAY END :[%d][%d] %.2f",musttime,mp3cnt++,mp3time);
        [self onClose];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

// publish class start
- (void)publishCommandClassStart {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStart;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

// publish class stop
- (void)publishCommandClassStop {
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = uten_class;
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error);
        return;
    }
    [session publishData:data onTopic:r_uten_class retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
        if (error) {
            NSLog(@"Error: %@", error);
        }
    }];
}

- (void)saveScoreToMMKV {
    NSString *groupId = [NSString stringWithFormat:@"%@", @(LocalTableScore)];
    MMKV *mmkv = [MMKV mmkvWithID:groupId];
    UTENScoreModel *scoreModel = [self createScoreModel];
    NSString *jsonString = [scoreModel toJSON:NSUTF8StringEncoding error:nil];
    [mmkv setString:jsonString forKey:@"score"];
    // [mmkv setString:jsonString forKey:recordingURL.lastPathComponent];
}

- (UTENScoreModel *)createScoreModel {
    UTENScoreModel *scoreModel = [[UTENScoreModel alloc] init];
    // scoreModel.id = @"1";
    // scoreModel.created_at = @"2019-01-01 00:00:00";
    // scoreModel.updated_at = @"2019-01-01 00:00:00";
    // scoreModel.user_id = @"1";
    // scoreModel.file = @"";
    // scoreModel.u_class = @"upsu1u4_s";
    // scoreModel.sound_step = @"5";
    // scoreModel.vocabulary_en = @"Julie";
    // scoreModel.vocabulary_zh = @"茱莉";
    return scoreModel;
}

@end
