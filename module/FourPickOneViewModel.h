//
//  FourPickOneViewModel.h
//  uten
//
//  Created by pht on 2025/5/5.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "UTENBasicTypes.h"

NS_ASSUME_NONNULL_BEGIN

@interface FourPickOneViewModel : NSObject

// 回調屬性
@property (nonatomic, copy, nullable) VoidCallback onBackPressed;
// 選項按鈕點擊回調，參數為是否選擇正確
@property (nonatomic, copy, nullable) void (^onOptionSelected)(BOOL isCorrect, NSString *selectedOption);
// 目前播放中的文字
@property (nonatomic, copy, nullable) NSString *currentPlayingText;

- (instancetype)initWithView:(UIView *)view;
- (void)setupLayout;
- (void)dispose;

@end

NS_ASSUME_NONNULL_END
