//
//  AppRoutes.m
//  uten
//
//  Created by pht on 2025/3/2.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import "AppRoutes.h"
#import <JLRoutes/JLRoutes.h>
#import "PickOneViewController.h"

@implementation AppRoutes

static UINavigationController *_navigationController;

+ (UINavigationController *)navigationController {
    return _navigationController;
}

+ (nullable UIViewController *)pop {
    return [_navigationController popViewControllerAnimated:YES];
}

+ (nullable NSArray<__kindof UIViewController *> *)popToRoot {
    return [_navigationController popToRootViewControllerAnimated:YES];
}

+ (NSString *)main {
    return @"/main";
}

+ (NSString *)wait {
    return @"/wait";
}

+ (NSString *)selector410 {
    return @"/selector410";
}

+ (NSString *)selector41 {
    return @"/selector41";
}

+ (NSString *)pickOne {
    return @"/pickOne";
}

+ (UIStoryboard *)mainStoryboard {
    return [UIStoryboard storyboardWithName:@"Main" bundle:nil];
}

+ (void)setupRoutes:(UINavigationController *)navigationController {
    _navigationController = navigationController;
    // 隐藏导航栏
    [navigationController setNavigationBarHidden:YES animated:NO];
    // 主頁面
    [JLRoutes.globalRoutes addRoute:self.main handler:^BOOL(NSDictionary<NSString *,id> * _Nonnull parameters) {
        DDLogInfoTag(@"JLRoutes", @"導航至 MainViewController 視圖: %@", parameters);
        UIViewController *vc = [self.mainStoryboard instantiateViewControllerWithIdentifier:@"MainViewController"];
        [navigationController setViewControllers:@[vc] animated:YES];
        return YES;
    }];
    // 等待頁面
    [JLRoutes.globalRoutes addRoute:self.wait handler:^BOOL(NSDictionary<NSString *,id> * _Nonnull parameters) {
        DDLogInfoTag(@"JLRoutes", @"導航至 WaitViewController 視圖: %@", parameters);
        UIViewController *vc = [self.mainStoryboard instantiateViewControllerWithIdentifier:@"WaitViewController"];
        [navigationController setViewControllers:@[vc] animated:YES];
        return YES;
    }];
    // 410 頁面
    [JLRoutes.globalRoutes addRoute:self.selector410 handler:^BOOL(NSDictionary<NSString *,id> * _Nonnull parameters) {
        DDLogInfoTag(@"JLRoutes", @"導航至 Selector410ViewController 視圖: %@", parameters);
        UIViewController *vc = [self.mainStoryboard instantiateViewControllerWithIdentifier:@"selector410ViewController"];
        [navigationController pushViewController:vc animated:YES];
        return YES;
    }];
    // 41 頁面
    [JLRoutes.globalRoutes addRoute:self.selector41 handler:^BOOL(NSDictionary<NSString *,id> * _Nonnull parameters) {
        DDLogInfoTag(@"JLRoutes", @"導航至 Selector41ViewController 視圖: %@", parameters);
        UIViewController *vc = [self.mainStoryboard instantiateViewControllerWithIdentifier:@"selector41ViewController"];
        [navigationController pushViewController:vc animated:YES];
        return YES;
    }];
    // PickOne 頁面
    [JLRoutes.globalRoutes addRoute:self.pickOne handler:^BOOL(NSDictionary<NSString *,id> * _Nonnull parameters) {
        // PickOneViewController is created programmatically, not from storyboard
        DDLogInfoTag(@"JLRoutes", @"導航至 PickOneViewController 視圖: %@", parameters);
        UIViewController *vc = [[PickOneViewController alloc] init];
        // DDLogInfoTag(@"JLRoutes", @"導航至 PickOneViewController 視圖: %@", parameters);
        // UIViewController *vc = [self.mainStoryboard instantiateViewControllerWithIdentifier:@"PickOneViewController"];
        [navigationController pushViewController:vc animated:YES];
        return YES;
    }];
}

+ (BOOL)push:(NSString *)path {
    @try {
        return [JLRoutes routeURL:[NSURL URLWithString:path]];
    } @catch (NSException *exception) {
        DDLogErrorTag(@"JLRoutes", @"路由异常: %@", exception);
        return NO;
    }
}

@end
