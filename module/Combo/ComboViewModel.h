#import <Foundation/Foundation.h>
#import <ReactiveObjC/ReactiveObjC.h>

@class UTENComboModel;
@class UTENFile;
@class AVAudioPlayer;

NS_ASSUME_NONNULL_BEGIN

@interface ComboViewModel : NSObject
// combo model
@property (nonatomic, strong, readonly) UTENComboModel *comboModel;
// wrong image file
@property (nonatomic, copy, readonly) NSString *wrongImageFile;
// right image file
@property (nonatomic, copy, readonly) NSString *rightImageFile;
// 連擊數
@property (nonatomic, assign) int comboCounter;

- (void)onResult:(NSNumber *)result;
- (UTENFile *)randomAttackImage;
- (UTENFile *)randomAttackImageWithComboNumber:(NSInteger)comboNumber;
- (UTENFile *)randomComboImage;
- (CGPoint)randomPoint;
- (void)setupLayout:(UIView *)view withPassLabel:(UILabel *)laPass andNGLabel:(UILabel *)laNG andTimerLabel:(UILabel **)laTimer;
- (void)showSpark:(UTENFile *)file withPoint:(CGPoint)point;
- (void)dispose;

@end

NS_ASSUME_NONNULL_END
