#import "ComboViewModel.h"
#import "UTENComboModel.h"
#import "UTENFile.h"
#import "UTENEnum.h"
#import "UTENUtility.h"
#import "UTENRect.h"
#import "UTENRect+X.h"
#import "UTENFile+X.h"
#import <AVFoundation/AVFoundation.h>
#import "UTENPlayerGroup.h"
#import <Masonry/Masonry.h>
#import "UIColor+X.h"

@interface ComboViewModel()
// player group property
@property (nonatomic, strong) UTENPlayerGroup *playerGroup;
// readonly AVAudioPlayer
@property (nonatomic, strong) AVAudioPlayer *bgmPlayer;
@end

@implementation ComboViewModel

- (instancetype)init {
    self = [super init];
    if (self) {
        NSString *path = [[NSBundle mainBundle] pathForResource:@"assets/combo" ofType:@"json"];
        NSData *jsonData = [NSData dataWithContentsOfFile:path];
        NSError *error;
        _comboModel = [UTENComboModel fromData:jsonData error:&error];
    }
    return self;
}

// getter right image file
- (NSString *)rightImageFile {
    return _comboModel.rightImage.name;
}

// getter wrong image file
- (NSString *)wrongImageFile {
    return _comboModel.wrongImage.name;
}

- (void)onResult:(NSNumber *)result {
    if (result.integerValue == ConfirmResultRight) {
        NSLog(@"Right");
        self.comboCounter += 1;
    } else {
        NSLog(@"Wrong");
        self.comboCounter = 0;
        // self.comboCounter += 1;
    }
}

// 取得以日期
- (NSInteger)currentDay {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"dd";
    NSString *dayString = [formatter stringFromDate:[NSDate date]];
    return dayString.integerValue;
}

// 取得以日期取 mod count 之後的值
- (NSArray<UTENFile *> *)backgroundImageByDay {
    NSInteger index = self.currentDay % self.comboModel.backgroundImages.count;
    return self.comboModel.backgroundImages[index];
}

- (NSArray<UTENFile *> *)randomBackgroundImage {
    int index = self.comboModel.backgroundImages.count * UTENUtility.randomValue;
    return self.comboModel.backgroundImages[index];
}

- (UTENFile *)randomAttackImage {
    int index = self.comboModel.attackImage.count * UTENUtility.randomValue;
    return self.comboModel.attackImage[index];
}

- (UTENFile *)randomAttackImageWithComboNumber:(NSInteger)comboNumber {
    // filter comboNumber in rect x
    NSArray *filtered = [self.comboModel.attackImage filteredArrayUsingPredicate:[NSPredicate predicateWithBlock:^BOOL(UTENFile * _Nullable attackImage, NSDictionary<NSString *,id> * _Nullable bindings) {
        if (attackImage.range != nil) {
            return [attackImage.range containsX:comboNumber];
        }
        return NO;
    }]];
    if (filtered.count > 0) {
        int index = filtered.count * UTENUtility.randomValue;
        return filtered[index];
    }
    return nil;
}

- (UTENFile *)randomComboImage {
    int index = self.comboModel.comboImage.count * UTENUtility.randomValue;
    return self.comboModel.comboImage[index];
}

- (CGPoint)randomPoint {
    CGFloat x = UTENUtility.randomValue;
    CGFloat y = UTENUtility.randomValue;
    CGPoint point = CGPointMake(x, y);
    if ([self isAvailablePoint:point]) {
        return point;
    }
    return [self randomPoint];
}

- (BOOL)isAvailablePoint:(CGPoint)point {
    for (UTENRect *area in self.comboModel.excludeArea) {
        CGRect rect = CGRectMake(area.x.floatValue, area.y.floatValue, area.width.floatValue, area.height.floatValue);
        if (CGRectContainsPoint(rect, point)) {
            return NO;
        }
    }
    return YES;
}

// 亂數播放其中一首背景音樂
- (void)playBGM {
    NSUInteger count = self.comboModel.backgroundMusics.count;
    if (count > 0) {
        int index = count * UTENUtility.randomValue;
        UTENFile *bgm = self.comboModel.backgroundMusics[index];
        NSError *error;
        _bgmPlayer = [[AVAudioPlayer alloc] initWithData:bgm.data error:&error];
        if (error != nil) {
            NSLog(@"Error: %@", error);
        } else {
            _bgmPlayer.numberOfLoops = -1; // 設置為 -1 以循環播放
            _bgmPlayer.volume = self.comboModel.backgroundMusicsVolume.floatValue;
            [_bgmPlayer play];
        }
    }
}

- (void)playBackground:(UIView *)view {
    self.playerGroup = [[UTENPlayerGroup alloc] init];
    self.playerGroup.view = view;
    self.playerGroup.list = self.backgroundImageByDay;
    [self.playerGroup play];
}

- (void)setupLayout:(UIView *)view withPassLabel:(UILabel *)laPass andNGLabel:(UILabel *)laNG andTimerLabel:(UILabel **)laTimer {
    // 設置背景圖片
    [self playBackground:view];
    // 設置背景音樂
    [self playBGM];
    // white view
    UIView *whiteView = [[UIView alloc] init];
    whiteView.backgroundColor = [UIColor whiteColor];
    whiteView.alpha = 0.5;
    [view insertSubview:whiteView atIndex:1];
    [whiteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@800);
        make.height.equalTo(@600);
        // center
        make.center.equalTo(view);
    }];
    // banner
    // 創建 UIStackView
    UIStackView *banner = [[UIStackView alloc] init];
    banner.axis = UILayoutConstraintAxisHorizontal; // 設置為水平排列
    banner.distribution = UIStackViewDistributionFillEqually; // 子視圖等寬分布
    banner.alignment = UIStackViewAlignmentFill; // 子視圖填滿容器
    banner.translatesAutoresizingMaskIntoConstraints = NO;
    banner.spacing = 20; // 子視圖之間的間距
    banner.backgroundColor = [UIColor whiteColor];
    banner.alpha = 0.9;
    [view addSubview:banner];
    [banner mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(whiteView);
        make.height.equalTo(@100);
        // center
        make.top.equalTo(whiteView.mas_top);
        // x equal to whiteView
        make.centerX.equalTo(whiteView.mas_centerX);
    }];
    // wrong
    {
        UIView *stackView = [[UIView alloc] init];
        [banner addArrangedSubview:stackView];
        // add lable
        // UILabel *label = [[UILabel alloc] init];
        UILabel *label = laNG;
        [label removeFromSuperview];
        label.text = @"0000";
        label.textColor = [UIColor comboPinkColor];
        label.font = [UIFont boldSystemFontOfSize:100];
        // label.textAlignment = NSTextAlignmentCenter;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        // [stackView addArrangedSubview:label];
        [stackView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            // fill y
            make.top.equalTo(stackView.mas_top);
            make.bottom.equalTo(stackView.mas_bottom);
            // fill x
            make.right.equalTo(stackView.mas_right);
        }];
        // add wrong image
        UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:self.wrongImageFile]];
        imageView.translatesAutoresizingMaskIntoConstraints = NO;
        imageView.contentMode = UIViewContentModeScaleAspectFit;
        // [stackView addArrangedSubview:imageView];
        [stackView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@80);
            make.height.equalTo(@80);
            // center
            make.centerY.equalTo(stackView.mas_centerY);
            // align right
            make.right.equalTo(label.mas_left);
        }];
    }
    // right
    {
        UIView *stackView = [[UIView alloc] init];
        [banner addArrangedSubview:stackView];
        // add right image
        UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:self.rightImageFile]];
        imageView.translatesAutoresizingMaskIntoConstraints = NO;
        [stackView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@80);
            make.height.equalTo(@80);
            // center y
            make.centerY.equalTo(stackView.mas_centerY);
            // align left
            make.left.equalTo(stackView.mas_left).offset(12);
        }];
        // add label
        // UILabel *label = [[UILabel alloc] init];
        UILabel *label = laPass;
        [laPass removeFromSuperview];
        label.text = @"0000";
        label.textColor = [UIColor comboBlueColor];
        label.font = [UIFont boldSystemFontOfSize:100];
        // label.textAlignment = NSTextAlignmentCenter;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        [stackView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            // fill y
            make.top.equalTo(stackView.mas_top);
            make.bottom.equalTo(stackView.mas_bottom);
            // fill x
            make.left.equalTo(imageView.mas_right);
            make.right.equalTo(stackView.mas_right);
        }];
    }
    // 秒數 label
    {
        UILabel *label = [[UILabel alloc] init];
        *laTimer = label;
        // label.text = [@(self.roundDuration) stringValue];
        label.textColor = [UIColor redColor];
        label.font = [UIFont boldSystemFontOfSize:100];
        label.textAlignment = NSTextAlignmentCenter;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        [view addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(whiteView.mas_centerY).offset(50);
            make.left.equalTo(whiteView.mas_left).offset(100);
        }];
    }
}

- (void)showSpark:(UTENFile *)file withPoint:(CGPoint)point {
    UIImage *image = [UIImage imageWithData:file.data];
    UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageWithData:file.data]];
    // imageView.frame = CGRectMake(0, 0, 100, 100);
    // imageView.center = CGPointMake(point.x * 800, point.y * 600);
    [self.playerGroup.view addSubview:imageView];
    CGFloat scale = [UTENUtility randomValueBetween:0.5 andMax:0.7];
    imageView.transform = CGAffineTransformMakeScale(scale, scale);
    imageView.center = point;
    // 1秒後淡出
    [UIView animateWithDuration:0.8 animations:^{
        imageView.alpha = 0;
    } completion:^(BOOL finished) {
        [imageView removeFromSuperview];
    }];
}

- (void)dispose {
    [self.bgmPlayer stop];
}

@end
