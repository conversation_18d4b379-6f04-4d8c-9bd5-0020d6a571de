//
//  BombViewModel.h
//  uten
//
//  Created by yuming on 2024/8/22.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "UTENEnum.h"

@class UTENBombModel;
@class BulletView;

NS_ASSUME_NONNULL_BEGIN

@interface BombViewModel : NSObject
// bomb model
@property (nonatomic, strong, readonly) UTENBombModel *bombModel;
// write target readonly property
@property (nonatomic, assign, readonly) CGPoint writeTarget;
@property (nonatomic, assign, readonly) BOOL isSrcFromRight;
// text property setter
@property (nonatomic, copy) NSString *text;

- (void)setupLayout:(UIView *)view withPassLabel:(UILabel *)laPass andNGLabel:(UILabel *)laNG andTimerLabel:(UILabel **)laTimer;
// - (void)emit:(UIView *)view;
- (void)createTankAndEmit:(UIView *)view withDuration:(NSTimeInterval)duration;
- (BulletView *)createBulletAt:(CGPoint)point;
- (void)createExplosion:(UIView *)view withResult:(ConfirmResult)confirmResult;
- (void)dispose;

@end

NS_ASSUME_NONNULL_END
