//
//  BombViewModel.m
//  uten
//
//  Created by yuming on 2024/8/22.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "BombViewModel.h"
#import "UTENBombModel.h"
#import "UTENPlayerGroup.h"
#import "UTENFile.h"
#import "UTENFile+X.h"
#import "UTENUtility.h"
#import <AVFoundation/AVFoundation.h>
#import <UIKit/UIKit.h>
#import "BulletView.h"
#import "NSArray+X.h"
#import "UTENAnimation.h"
#import "UTENAnimation+X.h"
#import <Masonry/Masonry.h>
#import "UIColor+X.h"

@interface BombViewModel()
// player group property
@property (nonatomic, strong) UTENPlayerGroup *playerGroup;
// readonly AVAudioPlayer
@property (nonatomic, strong) AVAudioPlayer *bgmPlayer;
// dest property setter
@property (nonatomic, assign) CGFloat dest;
@end

@implementation BombViewModel

- (instancetype)init {
    self = [super init];
    if (self) {
        NSString *path = [[NSBundle mainBundle] pathForResource:@"assets/bomb.json" ofType:nil];
        NSData *jsonData = [NSData dataWithContentsOfFile:path];
        NSError *error;
        _bombModel = [UTENBombModel fromData:jsonData error:&error];
    }
    return self;
}

// getter right image file
- (NSString *)rightImageFile {
    return _bombModel.rightImage.name;
}

// getter wrong image file
- (NSString *)wrongImageFile {
    return _bombModel.wrongImage.name;
}

- (void)setupLayout:(UIView *)view withPassLabel:(UILabel *)laPass andNGLabel:(UILabel *)laNG andTimerLabel:(UILabel **)laTimer {
    // 設置背景圖片
    [self playBackground:view];
    // 設置背景音樂
    [self playBGM];
    // 秒數 label
    {
        UILabel *label = [[UILabel alloc] init];
        *laTimer = label;
        // label.text = [@(self.roundDuration) stringValue];
        label.textColor = [UIColor redColor];
        label.font = [UIFont boldSystemFontOfSize:80];
        label.textAlignment = NSTextAlignmentCenter;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        [view addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(view.mas_top).offset(100);
            make.left.equalTo(view.mas_left).offset(100);
        }];
    }
    // banner
    // 創建 UIStackView
    UIStackView *banner = [[UIStackView alloc] init];
    banner.axis = UILayoutConstraintAxisHorizontal; // 設置為水平排列
    banner.distribution = UIStackViewDistributionFillEqually; // 子視圖等寬分布
    banner.alignment = UIStackViewAlignmentFill; // 子視圖填滿容器
    banner.translatesAutoresizingMaskIntoConstraints = NO;
    banner.spacing = 20; // 子視圖之間的間距
    banner.backgroundColor = [UIColor whiteColor];
    banner.alpha = 0.9;
    [view addSubview:banner];
    [banner mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@600);
        make.height.equalTo(@80);
        // center
        make.top.equalTo(view.mas_top).offset(40);
        // x equal to whiteView
        make.right.equalTo(view.mas_right).offset(-40);
    }];
    // wrong
    {
        UIView *stackView = [[UIView alloc] init];
        [banner addArrangedSubview:stackView];
        // add lable
        // UILabel *label = [[UILabel alloc] init];
        UILabel *label = laNG;
        [label removeFromSuperview];
        label.text = @"0000";
        label.textColor = [UIColor comboPinkColor];
        label.font = [UIFont boldSystemFontOfSize:80];
        // label.textAlignment = NSTextAlignmentCenter;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        // [stackView addArrangedSubview:label];
        [stackView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            // fill y
            make.top.equalTo(stackView.mas_top);
            make.bottom.equalTo(stackView.mas_bottom);
            // fill x
            make.right.equalTo(stackView.mas_right);
        }];
        // add wrong image
        UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:self.wrongImageFile]];
        imageView.translatesAutoresizingMaskIntoConstraints = NO;
        imageView.contentMode = UIViewContentModeScaleAspectFit;
        // [stackView addArrangedSubview:imageView];
        [stackView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@50);
            make.height.equalTo(@50);
            // center
            make.centerY.equalTo(stackView.mas_centerY);
            // align right
            make.right.equalTo(label.mas_left);
        }];
    }
    // right
    {
        UIView *stackView = [[UIView alloc] init];
        [banner addArrangedSubview:stackView];
        // add right image
        UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:self.rightImageFile]];
        imageView.translatesAutoresizingMaskIntoConstraints = NO;
        [stackView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@50);
            make.height.equalTo(@50);
            // center y
            make.centerY.equalTo(stackView.mas_centerY);
            // align left
            make.left.equalTo(stackView.mas_left).offset(12);
        }];
        // add label
        // UILabel *label = [[UILabel alloc] init];
        UILabel *label = laPass;
        [laPass removeFromSuperview];
        label.text = @"0000";
        label.textColor = [UIColor comboBlueColor];
        label.font = [UIFont boldSystemFontOfSize:80];
        // label.textAlignment = NSTextAlignmentCenter;
        label.translatesAutoresizingMaskIntoConstraints = NO;
        [stackView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            // fill y
            make.top.equalTo(stackView.mas_top);
            make.bottom.equalTo(stackView.mas_bottom);
            // fill x
            make.left.equalTo(imageView.mas_right);
            make.right.equalTo(stackView.mas_right);
        }];
    }
}

- (void)playBackground:(UIView *)view {
    self.playerGroup = [[UTENPlayerGroup alloc] init];
    self.playerGroup.view = view;
    self.playerGroup.list = self.backgroundImageByDay;
    [self.playerGroup play];
}

// 亂數播放其中一首背景音樂
- (void)playBGM {
    NSUInteger count = self.bombModel.backgroundMusics.count;
    if (count > 0) {
        int index = count * UTENUtility.randomValue;
        UTENFile *bgm = self.bombModel.backgroundMusics[index];
        NSError *error;
        _bgmPlayer = [[AVAudioPlayer alloc] initWithData:bgm.data error:&error];
        if (error != nil) {
            NSLog(@"Error: %@", error);
        } else {
            _bgmPlayer.numberOfLoops = -1; // 設置為 -1 以循環播放
            _bgmPlayer.volume = self.bombModel.backgroundMusicsVolume.floatValue;
            [_bgmPlayer play];
        }
    }
}

// 取得以日期
- (NSInteger)currentDay {
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"dd";
    NSString *dayString = [formatter stringFromDate:[NSDate date]];
    return dayString.integerValue;
}

// 取得以日期取 mod count 之後的值
- (NSArray<UTENFile *> *)backgroundImageByDay {
    NSInteger index = self.currentDay % self.bombModel.backgroundImages.count;
    return self.bombModel.backgroundImages[index];
}

- (void)createTankAndEmit:(UIView *)view withDuration:(NSTimeInterval)duration {
    // log duration
    NSLog(@"duration: %f", duration);
    CGPoint left = CGPointMake(0.2, 0.3); // left
    CGPoint right = CGPointMake(0.7, 0.8); // right
    CGFloat src = [UTENUtility randomValueBetween:left.x andMax:left.y];
    CGFloat dest = [UTENUtility randomValueBetween:right.x andMax:right.y];
    _isSrcFromRight = UTENUtility.randomValue >= 0.5;
    if (_isSrcFromRight == YES) {
        // swap src and dest
        CGFloat temp = src;
        src = dest;
        dest = temp;
    }
    self.dest = dest;
    CGSize screen = view.frame.size;
    // 創建坦克
    UIView *tank = [self createTank:screen withTarget:src];
    [view addSubview:tank];
    // 創建砲彈
    BulletView *bullet = [self createBulletAt:tank.center];
    [view addSubview:bullet];
    // 發射砲彈
    [bullet animateParabolicMotion:screen withTarget:dest andDuration:duration];
    // 重置縮放比例
    bullet.transform = CGAffineTransformIdentity;
    @weakify(self);
    [UIView animateWithDuration:duration animations:^{
        @strongify(self);
        bullet.transform = CGAffineTransformMakeScale(4, 4);
    } completion:^(BOOL finished) {
        @strongify(self);
        if (finished) {
            [bullet removeFromSuperview];
            [tank removeFromSuperview];
            // 顯示爆炸效果
            // CGPoint point = CGPointMake(dest * screen.width, 0.6 * screen.height);
            // UIImageView *explosion = [self createExplosionImage:ConfirmResultRight];
            // explosion.center = point;
            // [view addSubview:explosion];
            // [explosion startAnimating];
            // // remove after animation
            // [UIView animateWithDuration:explosion.animationDuration animations:^{
            //     explosion.alpha = 0;
            // } completion:^(BOOL finished) {
            //     if (finished) {
            //         [explosion removeFromSuperview];
            //     }
            // }];
        }
    }];
}

- (UIView *)createTank:(CGSize)screen withTarget:(CGFloat)x {
    NSLog(@"createTank");
    NSUInteger index = [UTENUtility randomIndexWithCount:self.bombModel.tanks.count];
    UTENFile *file = self.bombModel.tanks[index];
    UIImage *image = [UIImage imageWithData:file.data];
    UIImageView *tank = [[UIImageView alloc] initWithImage:image];
    // aspect fit
    tank.contentMode = UIViewContentModeScaleAspectFit;
    // center point
    CGPoint point = CGPointMake(x, 0.2);
    // log point
    NSLog(@"point: %@", NSStringFromCGPoint(point));
    CGSize size = CGSizeMake(200, 200);
    // log size
    NSLog(@"size: %@", NSStringFromCGSize(size));
    // size 200x200
    CGRect rect = CGRectMake(point.x * screen.width, point.y * screen.height, size.width, size.height);
    // log rect
    NSLog(@"rect: %@", NSStringFromCGRect(rect));
    tank.frame = rect;
    return tank;
}

- (BulletView *)createBulletAt:(CGPoint)point {
    BulletView *bullet = [[BulletView alloc] initWithFrame:CGRectMake(point.x, point.y, 100, 50)];
    bullet.imageFile = self.bombModel.cards.randomObject;
    bullet.text = self.text;
    return bullet;
}

// 產生爆炸效果
- (void)createExplosion:(UIView *)view withResult:(ConfirmResult)confirmResult {
    CGSize screen = view.frame.size;
    CGPoint point = CGPointMake(self.dest * screen.width, 0.6 * screen.height);
    UIImageView *explosion = [self createExplosionImage:confirmResult];
    explosion.center = point;
    [view addSubview:explosion];
    [explosion startAnimating];
    // remove after animation
    [UIView animateWithDuration:explosion.animationDuration animations:^{
        explosion.alpha = 0;
    } completion:^(BOOL finished) {
        if (finished) {
            [explosion removeFromSuperview];
        }
    }];
}

- (UIImageView *)createExplosionImage:(ConfirmResult)confirmResult {
    if (confirmResult == ConfirmResultRight) {
        // 正確爆炸
        return [self createCurrectExplotion];
    } else {
        // 錯誤爆炸
        return [self createIncurrectExplotion];
    }
}

- (UIImageView *)createCurrectExplotion {
    // NSUInteger index = [UTENUtility randomIndexWithCount:self.bombModel.explosions.count];
    // UTENFile *file = self.bombModel.explosions[index];
    UTENAnimation *ani = self.bombModel.explosions.randomObject;
    // 初始化 UIImageView
    UIImageView *spriteImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 300, 300)];
    // 設置動畫幀
    spriteImageView.animationImages = ani.images;
    // 設置動畫持續時間
    spriteImageView.animationDuration = ani.duration; // 所有影格播放完畢所需時間
    // 設置動畫重複次數
    spriteImageView.animationRepeatCount = 1; // 1次
    return spriteImageView;
}

// wrong image
- (UIImageView *)createIncurrectExplotion {
    UTENFile *file = self.bombModel.wrongImage;
    UIImage *image = [UIImage imageWithData:file.data];
    UIImageView *imageView = [[UIImageView alloc] initWithImage:image];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    imageView.animationDuration = 0.5;
    return imageView;
}

- (void)dispose {
    [self.bgmPlayer stop];
}

@end
