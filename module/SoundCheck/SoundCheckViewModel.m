//
//  SoundCheckViewModel.m
//  uten
//
//  Created by pht on 2024/6/3.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import "SoundCheckViewModel.h"
#import "UTENBasicModel.h"
#import "UTENMySql.h"
#import "UTENNhistoryModel.h"
#import "UTENNclassModel.h"
#import "UTENSoundCheckModel.h"

@interface SoundCheckViewModel () 
// {
//     NSArray<UTENQrcodeModel *> *_qrcodeList;
// }
// @property (nonatomic, strong) NSArray<UTENQrcodeModel *> *qrcodeList;
@end

@implementation SoundCheckViewModel

- (instancetype)init {
    self = [super init];
    if (self) {
        _nclass = @[];  // 初始化 nclass 為空陣列
        _nstudent = @[];  // 初始化 nstudent 為空陣列
        _selectedSoundCheckModelIndex = NSNotFound;  // 初始化 selectedSoundCheckModelIndex 為 NSNotFound
    }
    return self;
}

- (NSArray<UTENBasicModel *> *)column2 {
    return @[
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"金城武",
            @"desc": @"Uten2022Student001",
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"宋仲基",
            @"desc": @"Uten2022Student001",
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"金秀賢",
            @"desc": @"Uten2022Student001",
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"金城武",
            @"desc": @"Uten2022Student001",
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"宋大基",
            @"desc": @"Uten2022Student001",
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"銀秀賢",
            @"desc": @"Uten2022Student001",
        }],
    ];
}

// column3
- (NSArray<UTENBasicModel *> *)column3 {
    return @[
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"bat_at.mp3",
            @"result": @2,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"cat_cat.mp3",
            @"result": @0,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"duck_uck.mp3",
            @"result": @1,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"rat_uck.mp3",
            @"result": @2,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"bat_at.mp3",
            @"result": @1,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"duck_uck.mp3",
            @"result": @0,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"rat_uck.mp3",
            @"result": @2,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"bat_at.mp3",
            @"result": @1,
        }],
        [UTENBasicModel fromJSONDictionary:@{ 
            @"title": @"cat_cat.mp3",
            @"result": @2,
        }],
    ];
}

// column4 使用 column3 的 result 排序
- (NSArray<UTENBasicModel *> *)column4 {
    return [self.column3 sortedArrayUsingComparator:^NSComparisonResult(UTENBasicModel *obj1, UTENBasicModel *obj2) {
        return [obj1.result compare:obj2.result];
    }];
}

- (void)fetchQrCode {
    @weakify(self)
    [[UTENMySql.sharedInstance fetchQrCode]
        subscribeNext:^(NSArray<UTENQrcodeModel *> *list) {
            @strongify(self)
            self.qrcode = list;
        }
        error:^(NSError *error) {
            NSLog(@"%@", error);
        }
        completed:^{
            NSLog(@"completed");
        }
    ];
}

- (void)fetchNclass {
    @weakify(self)
    [[UTENMySql.sharedInstance fetchNclass]
        subscribeNext:^(NSArray<UTENNclassModel *> *list) {
            @strongify(self)
            self.nclass = list;
        }
        error:^(NSError *error) {
            NSLog(@"%@", error);
        }
        completed:^{
            NSLog(@"completed");
        }
    ];
}

- (void)fetchNstudent {
    @weakify(self)
    [[UTENMySql.sharedInstance fetchNstudent]
        subscribeNext:^(NSArray<UTENNstudentModel *> *list) {
            @strongify(self)
            self.nstudent = list;
        }
        error:^(NSError *error) {
            NSLog(@"%@", error);
        }
        completed:^{
            NSLog(@"completed");
        }
    ];
}

- (void)fetchNteacher {
    @weakify(self)
    [[UTENMySql.sharedInstance fetchNteacher]
        subscribeNext:^(NSArray<UTENNteacherModel *> *list) {
            @strongify(self)
            self.nteacher = list;
        }
        error:^(NSError *error) {
            NSLog(@"%@", error);
        }
        completed:^{
            NSLog(@"completed");
        }
    ];
}

// fetch nhistory data
- (void)fetchNhistory {
    @weakify(self)
    [[UTENMySql.sharedInstance fetchNhistory]
        subscribeNext:^(NSArray<UTENNhistoryModel *> *list) {
            @strongify(self)
            self.nhistory = list;
            // distinct nclass
            NSMutableSet<NSString *> *nclassSet = [NSMutableSet set];
            for (UTENNhistoryModel *history in list) {
                [nclassSet addObject:history.nclass];
            }
            // nclass array
            NSMutableArray<UTENNclassModel *> *nclassArray = @[].mutableCopy;
            for (NSString *nclass in nclassSet) {
                UTENNclassModel *element = [UTENNclassModel fromJSONDictionary:@{@"ename": nclass}];
                [nclassArray addObject:element];
            }
            self.nclass = nclassArray.copy;
        }
        error:^(NSError *error) {
            NSLog(@"%@", error);
        }
        completed:^{
            NSLog(@"completed");
        }
    ];
}

- (void)setSelectedNclass:(UTENNclassModel *)selectedNclass {
    // _selectedNclass = selectedNclass;
    // distinct nstudent
    NSMutableSet<NSString *> *nstudentSet = [NSMutableSet set];
    for (UTENNhistoryModel *history in self.nhistory) {
        if ([history.nclass isEqualToString:selectedNclass.ename]) {
            [nstudentSet addObject:history.cname];
        }
    }
    // nstudent array
    NSMutableArray<UTENNstudentModel *> *nstudentArray = @[].mutableCopy;
    for (NSString *nstudent in nstudentSet) {
        // search cname from nstudent
        UTENNstudentModel *element = [self.nstudent filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:@"cname == %@", nstudent]].firstObject;
        if (element != nil) {
            [nstudentArray addObject:element];
        }
    }
    self.filtedStudents = nstudentArray.copy;
}

- (UTENSoundCheckModel *)currentSoundCheckModel {
    if (self.selectedSoundCheckModelIndex >=0 && self.selectedSoundCheckModelIndex < self.mediaList.count) {
        return self.mediaList[self.selectedSoundCheckModelIndex];
    }
    return nil;
}

- (void)moveNext {
    return;
    if (self.selectedSoundCheckModelIndex < self.mediaList.count - 1) {
        self.selectedSoundCheckModelIndex++;
    }
}

- (void)movePrevious {
    return;
    if (self.selectedSoundCheckModelIndex > 0) {
        self.selectedSoundCheckModelIndex--;
    }
}

- (NSArray<NSString *> *)allClasses {
    // distinct sound check models by class name
    NSMutableSet<NSString *> *classSet = [NSMutableSet set];
    for (UTENSoundCheckModel *element in self.soundCheckModels) {
        [classSet addObject:element.className];
    }
    return classSet.allObjects;
}

- (NSArray<NSString *> *)allLessonsWithClass:(NSString *)className {
    // distinct sound check models by class name
    NSMutableSet<NSString *> *lessonSet = [NSMutableSet set];
    for (UTENSoundCheckModel *element in self.soundCheckModels) {
        if ([element.className isEqualToString:className]) {
            [lessonSet addObject:element.lesson];
        }
    }
    return lessonSet.allObjects;
}

- (NSArray<NSString *> *)allLessons {
    return [self allLessonsWithClass:self.selectedClass];
}

// list all sound check models with selected class and lesson
- (NSArray<UTENSoundCheckModel *> *)allSoundCheckModels {
    NSMutableArray<UTENSoundCheckModel *> *list = @[].mutableCopy;
    for (UTENSoundCheckModel *element in self.soundCheckModels) {
        if ([element.className isEqualToString:self.selectedClass] && 
            [element.lesson isEqualToString:self.selectedLesson] &&
            [element.speaker isEqualToString:self.selectedSpeaker]) {
            [list addObject:element];
        }
    }
    return list.copy;
}

// all speakers
- (NSArray<NSString *> *)allSpeakers {
    // select class and lesson
    NSString *selectedClass = self.selectedClass;
    NSString *selectedLesson = self.selectedLesson;
    return [self allSpeakersWithClass:self.selectedClass lesson:self.selectedLesson];
}

// all speakers with class and lesson
- (NSArray<NSString *> *)allSpeakersWithClass:(NSString *)className lesson:(NSString *)lesson {
    // distinct sound check models by speaker
    NSMutableSet<NSString *> *speakerSet = [NSMutableSet set];
    for (UTENSoundCheckModel *element in self.soundCheckModels) {
        if ([element.className isEqualToString:className] && [element.lesson isEqualToString:lesson]) {
            [speakerSet addObject:element.speaker];
        }
    }
    return speakerSet.allObjects;
}

- (void)fetchMediaDb {
    @weakify(self)
    [[UTENMySql.sharedInstance fetchMedia]
        subscribeNext:^(NSArray<UTENMediaModel *> *list) {
            @strongify(self)
            _mediaList = list;
            DDLogDebugTag(@"audio", @"list: %@", self.mediaList);
        }
        error:^(NSError *error) {
            DDLogErrorTag(@"audio", @"Error reading audio file: %@", error.localizedDescription);
        }
        completed:^{
            DDLogInfoTag(@"audio", @"completed");
        }
    ];
}

@end
