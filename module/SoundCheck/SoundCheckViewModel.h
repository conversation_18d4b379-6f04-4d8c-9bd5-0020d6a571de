//
//  SoundCheckViewModel.h
//  uten
//
//  Created by pht on 2024/6/3.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <ReactiveObjC/ReactiveObjC.h>

@class UTENBasicModel;
@class UTENQrcodeModel;
@class UTENNclassModel;
@class UTENNstudentModel;
@class UTENNteacherModel;
@class UTENNhistoryModel;
@class UTENSoundCheckModel;
@class UTENMediaModel;  

NS_ASSUME_NONNULL_BEGIN

@interface SoundCheckViewModel : NSObject
// column2
@property (nonatomic, readonly, copy) NSArray<UTENBasicModel *> *column2;
// column3
@property (nonatomic, readonly, copy) NSArray<UTENBasicModel *> *column3;
// column4
@property (nonatomic, readonly, copy) NSArray<UTENBasicModel *> *column4;
@property (nonatomic, copy) NSArray<UTENQrcodeModel *> *qrcode;
- (void)fetchQrCode;
// nclass
@property (nonatomic, copy) NSArray<UTENNclassModel *> *nclass;
// - (void)fetchNclass;
// nstudent
@property (nonatomic, copy) NSArray<UTENNstudentModel *> *nstudent;
- (void)fetchNstudent;
// nteacher
@property (nonatomic, copy) NSArray<UTENNteacherModel *> *nteacher;
- (void)fetchNteacher;
// nhistory
@property (nonatomic, copy) NSArray<UTENNhistoryModel *> *nhistory;
- (void)fetchNhistory;
// student with selected class
@property (nonatomic, copy) NSArray<UTENNstudentModel *> *filtedStudents;
// nclass property set only
@property (nonatomic, copy) UTENNclassModel *selectedNclass;
// current sound check model property readonly
@property (nonatomic, readonly, copy) UTENSoundCheckModel *currentSoundCheckModel;
// sound check model array property
@property (nonatomic, copy) NSArray<UTENSoundCheckModel *> *soundCheckModels;
@property (nonatomic, readonly, copy) NSArray<NSString *> *allClasses;
// selected class
@property (nonatomic, copy) NSString *selectedClass;
// list of lessons with selected class
@property (nonatomic, readonly, copy) NSArray<NSString *> *allLessons;
// select lesson
@property (nonatomic, copy) NSString *selectedLesson;
// list all sound check models with selected class and lesson
@property (nonatomic, readonly, copy) NSArray<UTENSoundCheckModel *> *allSoundCheckModels;
// selected sound check model
@property (nonatomic, copy) UTENSoundCheckModel *selectedSoundCheckModel;
// selected sound check model index
@property (nonatomic, assign) NSInteger selectedSoundCheckModelIndex;
- (void)moveNext;
- (void)movePrevious;
// all speakers
@property (nonatomic, readonly, copy) NSArray<NSString *> *allSpeakers;
// selected speaker
@property (nonatomic, copy) NSString *selectedSpeaker;
@property (nonatomic, readonly, copy) NSArray<UTENMediaModel *> *mediaList;
// fetch db
- (void)fetchMediaDb;
@end

NS_ASSUME_NONNULL_END
