//
//  FourPickOneViewModel.m
//  uten
//
//  Created by pht on 2025/5/5.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import "FourPickOneViewModel.h"
#import "Masonry.h"

@interface FourPickOneViewModel ()
@property (nonatomic, strong) UIView *rootView;
@property (nonatomic, strong) UIView *view;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *soundView;
@property (nonatomic, strong) UILabel *soundLabel;
@property (nonatomic, strong) UIImageView *soundImageView;
@property (nonatomic, strong) NSArray<UIButton *> *optionButtons;
@property (nonatomic, assign) NSInteger correctButtonIndex; // 儲存正確答案的按鈕索引
@end

@implementation FourPickOneViewModel

- (instancetype)initWithView:(UIView *)view {
    self = [super init];
    if (self) {
        self.rootView = view;
    }
    return self;
}

- (void)btBack:(id)sender {
    // 如果有設置回調，則調用回調
    if (self.onBackPressed) {
        self.onBackPressed();
    }
}

- (void)optionButtonTapped:(UIButton *)sender {
    NSString *selectedOption = [sender titleForState:UIControlStateNormal];
    NSLog(@"選擇了選項: %@", selectedOption);

    // 檢查是否為正確答案
    NSInteger selectedIndex = [self.optionButtons indexOfObject:sender];
    BOOL isCorrect = (selectedIndex == self.correctButtonIndex);

    if (isCorrect) {
        NSLog(@"答案正確！");
        // 這裡可以添加正確答案的處理邏輯，例如視覺反饋
    } else {
        NSLog(@"答案錯誤！正確答案是: %@", [self.optionButtons[self.correctButtonIndex] titleForState:UIControlStateNormal]);
        // 這裡可以添加錯誤答案的處理邏輯，例如視覺反饋
    }

    // 如果有設置回調，則調用回調
    if (self.onOptionSelected) {
        self.onOptionSelected(isCorrect, selectedOption);
    }
}

- (void)setSoundLabelText:(NSString *)text {
    if (self.soundLabel) {
        self.soundLabel.text = text;
    }
}

- (void)setCurrentPlayingText:(NSString *)currentPlayingText {
    _currentPlayingText = [currentPlayingText copy];
    // 當設置目前播放中的文字時，同時更新聲音標籤
    [self setSoundLabelText:currentPlayingText];

    // 如果有設置選項按鈕，則隨機選擇一個按鈕顯示正確答案
    if (self.optionButtons && self.optionButtons.count > 0) {
        // 隨機選擇一個按鈕索引作為正確答案
        self.correctButtonIndex = arc4random_uniform((u_int32_t)self.optionButtons.count);

        // 更新所有按鈕的文字
        for (NSInteger i = 0; i < self.optionButtons.count; i++) {
            UIButton *button = self.optionButtons[i];

            if (i == self.correctButtonIndex) {
                // 正確答案按鈕顯示 currentPlayingText
                [button setTitle:currentPlayingText forState:UIControlStateNormal];
            } else {
                // 其他按鈕顯示隨機字母
                [button setTitle:[self generateRandomLetter] forState:UIControlStateNormal];
            }
        }
    }
}

// 生成隨機字母，避免與正確答案相同
- (NSString *)generateRandomLetter {
    // 英文字母表
    NSString *alphabet = @"abcdefghijklmnopqrstuvwxyz";

    // 如果有正確答案且長度為1，則從字母表中排除正確答案
    if (self.currentPlayingText && self.currentPlayingText.length == 1) {
        NSString *letterToExclude = [self.currentPlayingText lowercaseString];
        NSMutableString *modifiedAlphabet = [NSMutableString stringWithString:alphabet];
        NSRange range = [modifiedAlphabet rangeOfString:letterToExclude];
        if (range.location != NSNotFound) {
            [modifiedAlphabet deleteCharactersInRange:range];
            alphabet = [modifiedAlphabet copy];
        }
    }

    // 生成隨機索引
    NSUInteger randomIndex = arc4random_uniform((u_int32_t)alphabet.length);

    // 取得該索引位置的字元
    unichar randomChar = [alphabet characterAtIndex:randomIndex];

    // 將字元轉換為字串並返回
    return [NSString stringWithFormat:@"%C", randomChar];
}

- (void)setupLayout {
    self.view = UIView.new;
    // Add back button programmatically
    UIButton *backButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [backButton setTitle:@"返回" forState:UIControlStateNormal];
    [backButton setTitleColor:[UIColor blueColor] forState:UIControlStateNormal];
    backButton.frame = CGRectMake(20, 40, 60, 40);
    [backButton addTarget:self action:@selector(btBack:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:backButton];
    // Set background color to light green
    self.view.backgroundColor = [UIColor colorWithRed:204/255.0 green:238/255.0 blue:153/255.0 alpha:1.0];
    [self.rootView addSubview:self.view];
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.rootView);
    }];
    // Title Label
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"發音三.第1課";
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.textColor = [UIColor darkGrayColor];
    self.titleLabel.font = [UIFont systemFontOfSize:24 weight:UIFontWeightMedium];
    [self.view addSubview:self.titleLabel];

    // Sound View (container for sound icon and letter)
    self.soundView = [[UIView alloc] init];
    self.soundView.backgroundColor = [UIColor whiteColor];
    self.soundView.layer.cornerRadius = 8.0;
    [self.view addSubview:self.soundView];

    // Sound Image
    self.soundImageView = [[UIImageView alloc] init];
    NSString *imagePath = [[NSBundle mainBundle] pathForResource:@"assets/images/volume_up" ofType:@"png"];
    self.soundImageView.image = [UIImage imageWithContentsOfFile:imagePath]; // Using volume_up.png with bundle approach
    self.soundImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.soundImageView.tintColor = [UIColor colorWithRed:180/255.0 green:180/255.0 blue:240/255.0 alpha:1.0]; // Light purple
    [self.soundView addSubview:self.soundImageView];

    // Sound Label
    self.soundLabel = [[UILabel alloc] init];
    self.soundLabel.text = @"";
    self.soundLabel.textAlignment = NSTextAlignmentCenter;
    self.soundLabel.textColor = [UIColor darkGrayColor];
    self.soundLabel.font = [UIFont systemFontOfSize:60 weight:UIFontWeightRegular];
    self.soundLabel.hidden = YES; // Hide the sound label
    [self.soundView addSubview:self.soundLabel];

    // Option Buttons
    NSMutableArray *buttons = [NSMutableArray arrayWithCapacity:4];

    // 創建4個選項按鈕
    for (int i = 0; i < 4; i++) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button setTitle:@"" forState:UIControlStateNormal]; // 初始化為空字串
        [button setTitleColor:[UIColor darkGrayColor] forState:UIControlStateNormal];
        button.titleLabel.font = [UIFont systemFontOfSize:40 weight:UIFontWeightRegular];
        button.backgroundColor = [UIColor whiteColor];
        button.layer.cornerRadius = 8.0;
        [button addTarget:self action:@selector(optionButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:button];
        [buttons addObject:button];
    }

    self.optionButtons = [buttons copy];

    // Title Label Layout
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop).offset(20);
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view).offset(-20);
        make.height.mas_equalTo(40);
    }];

    // Sound View Layout
    [self.soundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view).offset(-20);
        make.height.mas_equalTo(180);
    }];

    // Sound Image Layout
    [self.soundImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.soundView); // Center in both X and Y axes
        make.width.height.mas_equalTo(100);
    }];

    // Sound Label Layout
    [self.soundLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.soundView);
        make.width.height.mas_equalTo(100);
    }];

    // Option Buttons Layout (2x2 grid - 2 buttons per row)
    CGFloat buttonPadding = 10;
    CGFloat buttonHeight = 80;
    CGFloat verticalPadding = 10;

    // First row - Left button
    [self.optionButtons[0] mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.soundView.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view.mas_centerX).offset(-buttonPadding/2);
        make.height.mas_equalTo(buttonHeight);
    }];

    // First row - Right button
    [self.optionButtons[1] mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.soundView.mas_bottom).offset(20);
        make.left.equalTo(self.view.mas_centerX).offset(buttonPadding/2);
        make.right.equalTo(self.view).offset(-20);
        make.height.mas_equalTo(buttonHeight);
    }];

    // Second row - Left button
    [self.optionButtons[2] mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.optionButtons[0].mas_bottom).offset(verticalPadding);
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view.mas_centerX).offset(-buttonPadding/2);
        make.height.mas_equalTo(buttonHeight);
    }];

    // Second row - Right button
    [self.optionButtons[3] mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.optionButtons[1].mas_bottom).offset(verticalPadding);
        make.left.equalTo(self.view.mas_centerX).offset(buttonPadding/2);
        make.right.equalTo(self.view).offset(-20);
        make.height.mas_equalTo(buttonHeight);
    }];
}

- (void)dispose {
    // 移除視圖
    [self.view removeFromSuperview];
}

@end
