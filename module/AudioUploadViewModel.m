//
//  AudioUploadViewModel.m
//  uten
//
//  Created by pht on 2025/2/24.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import "AudioUploadViewModel.h"
#import <AVFoundation/AVFoundation.h>
#import <CocoaLumberjack/CocoaLumberjack.h>

@implementation AudioUploadViewModel

- (float)getAudioPeakLevel:(NSString *)audioPath {
    NSError *error = nil;
    AVAudioFile *audioFile = [[AVAudioFile alloc] initForReading:[NSURL fileURLWithPath:audioPath] error:&error];
    if (error) {
        DDLogErrorTag(@"audio", @"Error opening audio file: %@", error.localizedDescription);
        return 0.0f;
    }

    AVAudioFormat *format = audioFile.processingFormat;
    AVAudioPCMBuffer *buffer = [[AVAudioPCMBuffer alloc] initWithPCMFormat:format 
                                                           frameCapacity:(AVAudioFrameCount)audioFile.length];
    
    [audioFile readIntoBuffer:buffer error:&error];
    if (error) {
        DDLogErrorTag(@"audio", @"Error reading audio file: %@", error.localizedDescription);
        return 0.0f;
    }

    float peakLevel = 0.0f;
    float *samples = buffer.floatChannelData[0];
    NSUInteger frameLength = buffer.frameLength;

    // 計算音量最大值
    for (NSUInteger i = 0; i < frameLength; i++) {
        float absValue = fabs(samples[i]);
        if (absValue > peakLevel) {
            peakLevel = absValue;
        }
    }

    // 轉換為分貝值 (dB)
    float db = 20 * log10(peakLevel);
    DDLogInfoTag(@"audio", @"Audio peak level: %.2f dB", db);
    
    return db;
}

@end
