//
//  AppRoutes.h
//  uten
//
//  Created by pht on 2025/3/2.
//  Copyright © 2025 bekubee. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AppRoutes : NSObject

@property (nonatomic, weak, readonly, nullable) UINavigationController *navigationController;
+ (void)setupRoutes:(UINavigationController *)navigationController;
+ (BOOL)push:(NSString *)path;
+ (nullable UIViewController *)pop;
+ (nullable NSArray<__kindof UIViewController *> *)popToRoot;
+ (NSString *)main;
+ (NSString *)wait;
+ (NSString *)selector410;
+ (NSString *)selector41;
+ (NSString *)pickOne;

@end

NS_ASSUME_NONNULL_END
