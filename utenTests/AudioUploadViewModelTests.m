#import <XCTest/XCTest.h>
#import "AudioUploadViewModel.h"

@interface AudioUploadViewModelTests : XCTestCase
@property (nonatomic, strong) AudioUploadViewModel *viewModel;
@end

@implementation AudioUploadViewModelTests

- (void)setUp {
    [super setUp];
    self.viewModel = [[AudioUploadViewModel alloc] init];
}

- (void)tearDown {
    self.viewModel = nil;
    [super tearDown];
}

- (void)testGetAudioPeakLevel {
    // 準備測試音檔
    NSBundle *bundle = [NSBundle bundleForClass:[self class]];
    
    // 測試不同音量的音檔
    NSArray *testFiles = @[
        @{@"file": @"volume_silent.mp3", @"expectedDB": @-60.0},     // 靜音檔案
        @{@"file": @"volume_low.mp3", @"expectedDB": @-40.0},        // 低音量
        @{@"file": @"volume_normal.mp3", @"expectedDB": @-20.0},     // 正常音量
        @{@"file": @"volume_loud.mp3", @"expectedDB": @-10.0}        // 大音量
    ];
    
    for (NSDictionary *test in testFiles) {
        NSString *path = [bundle pathForResource:test[@"file"] ofType:nil];
        float peakDB = [self.viewModel getAudioPeakLevel:path];
        float expectedDB = [test[@"expectedDB"] floatValue];
        
        // 允許 5 分貝的誤差範圍
        XCTAssertEqualWithAccuracy(peakDB, expectedDB, 5.0,
                                 @"Audio file %@ peak level (%.2f) should be close to %.2f",
                                 test[@"file"], peakDB, expectedDB);
    }
}

- (void)testInvalidAudioFile {
    // 測試無效的音檔路徑
    float peakDB = [self.viewModel getAudioPeakLevel:@"/invalid/path.mp3"];
    XCTAssertEqual(peakDB, 0.0f, @"Invalid audio file should return 0.0");
}

@end
