//
//  NSStringTests.m
//  utenTests
//
//  Created by pht on 2024/7/4.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "NSString+X.h"

@interface NSStringTests : XCTestCase

@end

@implementation NSStringTests

- (void)setUp {
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

-(void)testIsEmptyWithNil {
    NSString *str = nil;
    BOOL actual = [NSString isEmpty:str];
    XCTAssertTrue(actual);
}

-(void)testIsEmptyWithEmptyString {
    NSString *str = @"";
    BOOL actual = str.isEmpty;
    XCTAssertTrue(actual);
}

-(void)testIsEmptyWithNotEmptyString {
    NSString *str = @"123";
    BOOL actual = str.isEmpty;
    XCTAssertFalse(actual);
}

-(void)testIsNotEmptyWithNil {
    NSString *str = nil;
    BOOL actual = [NSString isNotEmpty:str];
    XCTAssertFalse(actual);
}

-(void)testIsNotEmptyWithEmptyString {
    NSString *str = @"";
    BOOL actual = str.isNotEmpty;
    XCTAssertFalse(actual);
}

-(void)testIsNotEmptyWithNotEmptyString {
    NSString *str = @"123";
    BOOL actual = str.isNotEmpty;
    XCTAssertTrue(actual);
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
