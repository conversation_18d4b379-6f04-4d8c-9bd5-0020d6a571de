//
//  utenSoundCheckModelTests.m
//  utenTests
//
//  Created by yuming on 2024/6/16.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "UTENSoundCheckModel.h"
#import "UTENSoundCheckModel+X.h"

@interface UTENSoundCheckModel (Testing)

+ (NSRegularExpression *)rightRegex;
+ (NSRegularExpression *)wrongRegex;

@end

@interface utenSoundCheckModelTests : XCTestCase

@property (nonatomic, strong) UTENSoundCheckModel *model;

@end

@implementation utenSoundCheckModelTests

- (void)setUp {
    // Put setup code here. This method is called before the invocation of each test method in the class.
    self.model = [[UTENSoundCheckModel alloc] init];
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    self.model = nil;
}

// test right regex
- (void)testRightRegex {
    NSRegularExpression *regex = [UTENSoundCheckModel rightRegex];
    NSString *string = @"tr";
    NSUInteger numberOfMatches = [regex numberOfMatchesInString:string options:0 range:NSMakeRange(0, string.length)];
    XCTAssertEqual(numberOfMatches, 1);
}

// test wrong regex
- (void)testWrongRegex {
    NSRegularExpression *regex = [UTENSoundCheckModel wrongRegex];
    NSString *string = @"tw";
    NSUInteger numberOfMatches = [regex numberOfMatchesInString:string options:0 range:NSMakeRange(0, string.length)];
    XCTAssertEqual(numberOfMatches, 1);
}

- (void)testParse {
    const NSString *string = @"eat_dinner+E0AN1+05+s031+s056r+tr.mp3";
    UTENSoundCheckModel *model = [[UTENSoundCheckModel alloc] init];
    [model parse:string];
    
    XCTAssertEqualObjects(model.sound, @"eat_dinner");
    XCTAssertEqualObjects(model.className, @"E0AN1");
    XCTAssertEqualObjects(model.lesson, @"05");
    XCTAssertEqualObjects(model.speaker, @"s031");
    XCTAssertEqualObjects(model.checker, @"s056r");
    XCTAssertEqualObjects(model.teacher, @"tr");
    XCTAssertEqualObjects(model.pathExtension, @"mp3");
    XCTAssertEqualObjects(model.fullname, string);
}

- (void)testModelWithString {
    const NSString *string = @"eat_dinner+E0AN1+05+s031+s056r+tr.mp3";
    UTENSoundCheckModel *model = [UTENSoundCheckModel modelWithString:string];
    
    XCTAssertEqualObjects(model.sound, @"eat_dinner");
    XCTAssertEqualObjects(model.className, @"E0AN1");
    XCTAssertEqualObjects(model.lesson, @"05");
    XCTAssertEqualObjects(model.speaker, @"s031");
    XCTAssertEqualObjects(model.checker, @"s056r");
    XCTAssertEqualObjects(model.teacher, @"tr");
    XCTAssertEqualObjects(model.pathExtension, @"mp3");
    XCTAssertEqualObjects(model.fullname, string);
}

// test prefix
- (void)testPrefix {
    const NSString *string = @"eat_dinner+E0AN1+05+s031+s056r+tr.mp3";
    UTENSoundCheckModel *model = [UTENSoundCheckModel modelWithString:string];
    XCTAssertEqualObjects([model prefix], @"eat_dinner+E0AN1+05+s031");
}

// test origin
- (void)testOrigin {
    const NSString *string = @"eat_dinner+E0AN1+05+s031+s056r+tr.mp3";
    UTENSoundCheckModel *model = [UTENSoundCheckModel modelWithString:string];
    XCTAssertEqualObjects([model origin], @"eat_dinner+E0AN1+05+s031+s056r+tr.mp3");
}

- (void)testTeacherConfirmResult {
    self.model.teacher = @"t";
    XCTAssertEqual([self.model teacherConfirmResult], ConfirmResultUnknown);
    
    self.model.teacher = @"tr";
    XCTAssertEqual([self.model teacherConfirmResult], ConfirmResultRight);
    
    self.model.teacher = @"tw";
    XCTAssertEqual([self.model teacherConfirmResult], ConfirmResultWrong);
}

- (void)testCheckerConfirmResult {
    self.model.checker = @"s056r";
    XCTAssertEqual([self.model checkerConfirmResult], ConfirmResultRight);
    
    self.model.checker = @"s056w";
    XCTAssertEqual([self.model checkerConfirmResult], ConfirmResultWrong);
    
    self.model.checker = @"s056";
    XCTAssertEqual([self.model checkerConfirmResult], ConfirmResultUnknown);
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
