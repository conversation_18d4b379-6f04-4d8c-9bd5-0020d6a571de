//
//  utenConstantsTests.m
//  utenTests
//
//  Created by pht on 2024/6/16.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "UTENConstants.h"

@interface utenConstantsTests : XCTestCase

@end

@implementation utenConstantsTests

- (void)setUp {
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testConstantString {
    XCTAssertEqualObjects(KEY_ID, @"ID");
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
