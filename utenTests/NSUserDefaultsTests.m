//
//  NSUserDefaultsTests.m
//  utenTests
//
//  Created by yuming on 2024/6/16.
//  Copyright © 2024 bekubee. All rights reserved.
//

#import <XCTest/XCTest.h>
#import "NSUserDefaults+X.h"

@interface NSUserDefaultsTests : XCTestCase

@end

@implementation NSUserDefaultsTests

- (void)setUp {
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
}

- (void)testExample {
    // This is an example of a functional test case.
    // Use XCTAssert and related functions to verify your tests produce the correct results.
}

- (void)testIdentity {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.identifier = @"foo";
    XCTAssertEqualObjects(defaults.identifier, @"foo");
}

// test type
- (void)testType {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.type = @"foo";
    XCTAssertEqualObjects(defaults.type, @"foo");
}

// test cname
- (void)testCname {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.cname = @"foo";
    XCTAssertEqualObjects(defaults.cname, @"foo");
}

// test ename
- (void)testEname {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.ename = @"foo";
    XCTAssertEqualObjects(defaults.ename, @"foo");
}

// test role
- (void)testRole {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.type = @"1";
    XCTAssertEqual(defaults.role, RoleManager);
}

// test is student
- (void)testIsStudent {
     NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.type = [@(RoleStudent) stringValue];
    XCTAssertTrue([defaults isStudent]);
    // image path
    XCTAssertEqualObjects([defaults imagePath], @"SoundCheck 05.png");
}

// test is master
- (void)testIsMaster {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    defaults.type = [@(RoleProgrammer) stringValue];
    XCTAssertTrue([defaults isMaster]);
    XCTAssertEqualObjects([defaults imagePath], @"SoundCheck 06.png");

    defaults.type = [@(RoleManager) stringValue];
    XCTAssertTrue([defaults isMaster]);
    XCTAssertEqualObjects([defaults imagePath], @"SoundCheck 06.png");

    defaults.type = [@(RoleTeacher) stringValue];
    XCTAssertTrue([defaults isMaster]);
    XCTAssertEqualObjects([defaults imagePath], @"SoundCheck 06.png");
}

- (void)testPerformanceExample {
    // This is an example of a performance test case.
    [self measureBlock:^{
        // Put the code you want to measure the time of here.
    }];
}

@end
