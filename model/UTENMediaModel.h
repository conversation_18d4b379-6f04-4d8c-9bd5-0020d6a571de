// UTENMediaModel.h

// To parse this JSON:
//
//   NSError *error;
//   UTENMediaModel *mediaModel = [UTENMediaModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENMediaModel;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENMediaModel : NSObject
@property (nonatomic, nullable, strong) NSNumber *identifier;
@property (nonatomic, nullable, copy)   NSString *createdAt;
@property (nonatomic, nullable, copy)   NSString *updatedAt;
@property (nonatomic, nullable, strong) NSNumber *userID;
@property (nonatomic, nullable, copy)   NSString *file;
@property (nonatomic, nullable, copy)   NSString *uClass;
@property (nonatomic, nullable, strong) NSNumber *soundStep;
@property (nonatomic, nullable, copy)   NSString *vocabularyEn;
@property (nonatomic, nullable, copy)   NSString *vocabularyZh;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENMediaModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END