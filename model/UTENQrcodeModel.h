// To parse this JSON:
//
//   NSError *error;
//   UTENQrcodeModel *qrcodeModel = [UTENQrcodeModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENQrcodeModel;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENQrcodeModel : NSObject
@property (nonatomic, nullable, strong) NSNumber *identifier;
@property (nonatomic, nullable, strong) NSNumber *type;
@property (nonatomic, nullable, copy)   NSString *cname;
@property (nonatomic, nullable, copy)   NSString *ename;
@property (nonatomic, nullable, copy)   NSString *qrcode;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
