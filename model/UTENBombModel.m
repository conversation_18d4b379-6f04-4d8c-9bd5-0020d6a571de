#import "UTENBombModel.h"
#import "UTENFile.h"
#import "UTENAnimation.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

static id map(id collection, id (^f)(id value)) {
    id result = nil;
    if ([collection isKindOfClass:NSArray.class]) {
            result = [NSMutableArray arrayWithCapacity:[collection count]];
            for (id x in collection) [result addObject:f(x)];
    } else if ([collection isKindOfClass:NSDictionary.class]) {
            result = [NSMutableDictionary dictionaryWithCapacity:[collection count]];
            for (id key in collection) [result setObject:f([collection objectForKey:key]) forKey:key];
    }
    return result;
}

#pragma mark - JSON serialization

UTENBombModel *_Nullable UTENBombModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENBombModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENBombModel *_Nullable UTENBombModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENBombModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENBombModelToData(UTENBombModel *bombModel, NSError **error)
{
    @try {
        id json = [bombModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENBombModelToJSON(UTENBombModel *bombModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENBombModelToData(bombModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENBombModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"background_images": @"backgroundImages",
        @"background_musics": @"backgroundMusics",
        @"background_musics_volume": @"backgroundMusicsVolume",
        @"right_image": @"rightImage",
        @"wrong_image": @"wrongImage",
        @"tanks": @"tanks",
        @"cards": @"cards",
        @"explosions": @"explosions",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENBombModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENBombModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENBombModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
        _backgroundImages = map(_backgroundImages, λ(id x, map(x, λ(id x, [UTENFile fromJSONDictionary:x]))));
        _backgroundMusics = map(_backgroundMusics, λ(id x, [UTENFile fromJSONDictionary:x]));
        _rightImage = [UTENFile fromJSONDictionary:(id)_rightImage];
        _wrongImage = [UTENFile fromJSONDictionary:(id)_wrongImage];
        _tanks = map(_tanks, λ(id x, [UTENFile fromJSONDictionary:x]));
        _cards = map(_cards, λ(id x, [UTENFile fromJSONDictionary:x]));
        _explosions = map(_explosions, λ(id x, [UTENAnimation fromJSONDictionary:x]));
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENBombModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENBombModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    id dict = [[self dictionaryWithValuesForKeys:UTENBombModel.properties.allValues] mutableCopy];

    // Rewrite property names that differ in JSON
    for (id jsonName in UTENBombModel.properties) {
        id propertyName = UTENBombModel.properties[jsonName];
        if (![jsonName isEqualToString:propertyName]) {
            dict[jsonName] = dict[propertyName];
            [dict removeObjectForKey:propertyName];
        }
    }

    // Map values that need translation
    [dict addEntriesFromDictionary:@{
        @"background_images": NSNullify(map(_backgroundImages, λ(id x, map(x, λ(id x, [x JSONDictionary]))))),
        @"background_musics": NSNullify(map(_backgroundMusics, λ(id x, [x JSONDictionary]))),
        @"right_image": NSNullify([_rightImage JSONDictionary]),
        @"wrong_image": NSNullify([_wrongImage JSONDictionary]),
        @"tanks": NSNullify(map(_tanks, λ(id x, [x JSONDictionary]))),
        @"cards": NSNullify(map(_cards, λ(id x, [x JSONDictionary]))),
        @"explosions": NSNullify(map(_explosions, λ(id x, [x JSONDictionary]))),
    }];

    return dict;
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENBombModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENBombModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
