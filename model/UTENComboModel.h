// To parse this JSON:
//
//   NSError *error;
//   UTENComboModel *comboModel = [UTENComboModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENComboModel;
@class UTENFile;
@class UTENRect;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENComboModel : NSObject
@property (nonatomic, nullable, copy) NSArray<NSArray<UTENFile *> *> *backgroundImages;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *backgroundMusics;
@property (nonatomic, nullable, strong) NSNumber *backgroundMusicsVolume;
@property (nonatomic, nullable, strong) UTENFile *rightImage;
@property (nonatomic, nullable, strong) UTENFile *wrongImage;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *attackImage;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *comboImage;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *comboNumber;
@property (nonatomic, nullable, copy) NSArray<UTENRect *> *excludeArea;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

NS_ASSUME_NONNULL_END
