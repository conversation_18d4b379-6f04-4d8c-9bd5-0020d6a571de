// To parse this JSON:
//
//   NSError *error;
//   UTENNstudentModel *nstudentModel = [UTENNstudentModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENNstudentModel;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENNstudentModel : NSObject
@property (nonatomic, nullable, copy) NSString *cname;
@property (nonatomic, nullable, copy) NSString *ename;
@property (nonatomic, nullable, copy) NSString *school;
@property (nonatomic, nullable, copy) NSString *class;
@property (nonatomic, nullable, copy) NSString *tel01;
@property (nonatomic, nullable, copy) NSString *tel02;
@property (nonatomic, nullable, copy) NSString *tel03;
@property (nonatomic, nullable, copy) NSString *tel04;
@property (nonatomic, nullable, copy) NSString *tel05;
@property (nonatomic, nullable, copy) NSString *tel06;
@property (nonatomic, nullable, copy) NSString *tel07;
@property (nonatomic, nullable, copy) NSString *birthday;
@property (nonatomic, nullable, copy) NSString *address;
@property (nonatomic, nullable, copy) NSString *bodyclass;
@property (nonatomic, nullable, copy) NSString *remark1;
@property (nonatomic, nullable, copy) NSString *remark2;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENNstudentModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
