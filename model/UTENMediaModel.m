// UTENMediaModel.m

#import "UTENMediaModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - JSON serialization

UTENMediaModel *_Nullable UTENMediaModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENMediaModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENMediaModel *_Nullable UTENMediaModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENMediaModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENMediaModelToData(UTENMediaModel *mediaModel, NSError **error)
{
    @try {
        id json = [mediaModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENMediaModelToJSON(UTENMediaModel *mediaModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENMediaModelToData(mediaModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENMediaModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"id": @"identifier",
        @"created_at": @"createdAt",
        @"updated_at": @"updatedAt",
        @"user_id": @"userID",
        @"file": @"file",
        @"u_class": @"uClass",
        @"sound_step": @"soundStep",
        @"vocabulary_en": @"vocabularyEn",
        @"vocabulary_zh": @"vocabularyZh",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENMediaModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENMediaModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENMediaModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
        // nsdata 轉換成 nsstring
        self.file = [[NSString alloc] initWithData:self.file encoding:NSUTF8StringEncoding];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENMediaModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENMediaModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    id dict = [[self dictionaryWithValuesForKeys:UTENMediaModel.properties.allValues] mutableCopy];

    // Rewrite property names that differ in JSON
    for (id jsonName in UTENMediaModel.properties) {
        id propertyName = UTENMediaModel.properties[jsonName];
        if (![jsonName isEqualToString:propertyName]) {
            dict[jsonName] = dict[propertyName];
            [dict removeObjectForKey:propertyName];
        }
    }

    return dict;
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENMediaModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENMediaModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END