// To parse this JSON:
//
//   NSError *error;
//   UTENSoundCheckModel *soundCheckModel = [UTENSoundCheckModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENSoundCheckModel;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENSoundCheckModel : NSObject
@property (nonatomic, nullable, copy) NSString *origin;
@property (nonatomic, nullable, copy) NSString *sound;
@property (nonatomic, nullable, copy) NSString *className;
@property (nonatomic, nullable, copy) NSString *lesson;
@property (nonatomic, nullable, copy) NSString *speaker;
@property (nonatomic, nullable, copy) NSString *checker;
@property (nonatomic, nullable, copy) NSString *teacher;
@property (nonatomic, nullable, copy) NSString *pathExtension;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENSoundCheckModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
