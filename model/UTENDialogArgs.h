// To parse this JSON:
//
//   NSError *error;
//   UTENDialogArgs *dialogArgs = [UTENDialogArgs fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENDialogArgs;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENDialogArgs : NSObject
@property (nonatomic, nullable, copy) NSString *title;
@property (nonatomic, nullable, copy) NSString *message;
@property (nonatomic, nullable, copy) NSString *okButton;
@property (nonatomic, nullable, copy) NSString *cancelButton;
@property (nonatomic, nullable, copy) NSString *placeholder;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENDialogArgs (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END