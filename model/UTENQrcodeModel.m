
#import "UTENQrcodeModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Private model interfaces

@interface UTENQrcodeModel (JSONConversion)
// + (instancetype)fromJSONDictionary:(NSDictionary *)dict;
// - (NSDictionary *)JSONDictionary;
@end

#pragma mark - JSON serialization

UTENQrcodeModel *_Nullable UTENQrcodeModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENQrcodeModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENQrcodeModel *_Nullable UTENQrcodeModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENQrcodeModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENQrcodeModelToData(UTENQrcodeModel *qrcodeModel, NSError **error)
{
    @try {
        id json = [qrcodeModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENQrcodeModelToJSON(UTENQrcodeModel *qrcodeModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENQrcodeModelToData(qrcodeModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENQrcodeModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"id": @"identifier",
        @"type": @"type",
        @"cname": @"cname",
        @"ename": @"ename",
        @"qrcode": @"qrcode",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENQrcodeModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENQrcodeModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENQrcodeModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENQrcodeModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENQrcodeModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    id dict = [[self dictionaryWithValuesForKeys:UTENQrcodeModel.properties.allValues] mutableCopy];

    // Rewrite property names that differ in JSON
    for (id jsonName in UTENQrcodeModel.properties) {
        id propertyName = UTENQrcodeModel.properties[jsonName];
        if (![jsonName isEqualToString:propertyName]) {
            dict[jsonName] = dict[propertyName];
            [dict removeObjectForKey:propertyName];
        }
    }

    return dict;
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENQrcodeModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENQrcodeModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
