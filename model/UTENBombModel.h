// UTENBombModel.h

// To parse this JSON:
//
//   NSError *error;
//   UTENBombModel *bombModel = [UTENBombModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENBombModel;
@class UTENFile;
@class UTENAnimation;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENBombModel : NSObject
@property (nonatomic, nullable, copy) NSArray<NSArray<UTENFile *> *> *backgroundImages;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *backgroundMusics;
@property (nonatomic, nullable, strong) NSNumber *backgroundMusicsVolume;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *tanks;
@property (nonatomic, nullable, copy) NSArray<UTENFile *> *cards;
@property (nonatomic, nullable, copy) NSArray<UTENAnimation *> *explosions;
@property (nonatomic, nullable, strong) UTENFile *rightImage;
@property (nonatomic, nullable, strong) UTENFile *wrongImage;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENBombModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
