#import "UTENNteacherModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - JSON serialization

UTENNteacherModel *_Nullable UTENNteacherModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENNteacherModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENNteacherModel *_Nullable UTENNteacherModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENNteacherModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENNteacherModelToData(UTENNteacherModel *nteacherModel, NSError **error)
{
    @try {
        id json = [nteacherModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENNteacherModelToJSON(UTENNteacherModel *nteacherModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENNteacherModelToData(nteacherModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENNteacherModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"id": @"identifier",
        @"cname": @"cname",
        @"ename": @"ename",
        @"level": @"level",
        @"start": @"start",
        @"onjob": @"onjob",
        @"seniority": @"seniority",
        @"education": @"education",
        @"phone": @"phone",
        @"address": @"address",
        @"c01": @"c01",
        @"c02": @"c02",
        @"c03": @"c03",
        @"c04": @"c04",
        @"c05": @"c05",
        @"c06": @"c06",
        @"c07": @"c07",
        @"c08": @"c08",
        @"c09": @"c09",
        @"c10": @"c10",
        @"c11": @"c11",
        @"c12": @"c12",
        @"c13": @"c13",
        @"c14": @"c14",
        @"c15": @"c15",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENNteacherModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENNteacherModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENNteacherModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
        // nsdata 轉換成 nsstring
        self.cname = [[NSString alloc] initWithData:self.cname encoding:NSUTF8StringEncoding];
        self.ename = [[NSString alloc] initWithData:self.ename encoding:NSUTF8StringEncoding];
        self.level = [[NSString alloc] initWithData:self.level encoding:NSUTF8StringEncoding];
        self.start = [[NSString alloc] initWithData:self.start encoding:NSUTF8StringEncoding];
        self.onjob = [[NSString alloc] initWithData:self.onjob encoding:NSUTF8StringEncoding];
        self.seniority = [[NSString alloc] initWithData:self.seniority encoding:NSUTF8StringEncoding];
        self.education = [[NSString alloc] initWithData:self.education encoding:NSUTF8StringEncoding];
        self.phone = [[NSString alloc] initWithData:self.phone encoding:NSUTF8StringEncoding];
        self.address = [[NSString alloc] initWithData:self.address encoding:NSUTF8StringEncoding];
        self.c01 = [[NSString alloc] initWithData:self.c01 encoding:NSUTF8StringEncoding];
        self.c02 = [[NSString alloc] initWithData:self.c02 encoding:NSUTF8StringEncoding];
        self.c03 = [[NSString alloc] initWithData:self.c03 encoding:NSUTF8StringEncoding];
        self.c04 = [[NSString alloc] initWithData:self.c04 encoding:NSUTF8StringEncoding];
        self.c05 = [[NSString alloc] initWithData:self.c05 encoding:NSUTF8StringEncoding];
        self.c06 = [[NSString alloc] initWithData:self.c06 encoding:NSUTF8StringEncoding];
        self.c07 = [[NSString alloc] initWithData:self.c07 encoding:NSUTF8StringEncoding];
        self.c08 = [[NSString alloc] initWithData:self.c08 encoding:NSUTF8StringEncoding];
        self.c09 = [[NSString alloc] initWithData:self.c09 encoding:NSUTF8StringEncoding];
        self.c10 = [[NSString alloc] initWithData:self.c10 encoding:NSUTF8StringEncoding];
        self.c11 = [[NSString alloc] initWithData:self.c11 encoding:NSUTF8StringEncoding];
        self.c12 = [[NSString alloc] initWithData:self.c12 encoding:NSUTF8StringEncoding];
        self.c13 = [[NSString alloc] initWithData:self.c13 encoding:NSUTF8StringEncoding];
        self.c14 = [[NSString alloc] initWithData:self.c14 encoding:NSUTF8StringEncoding];
        self.c15 = [[NSString alloc] initWithData:self.c15 encoding:NSUTF8StringEncoding];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENNteacherModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENNteacherModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    id dict = [[self dictionaryWithValuesForKeys:UTENNteacherModel.properties.allValues] mutableCopy];

    // Rewrite property names that differ in JSON
    for (id jsonName in UTENNteacherModel.properties) {
        id propertyName = UTENNteacherModel.properties[jsonName];
        if (![jsonName isEqualToString:propertyName]) {
            dict[jsonName] = dict[propertyName];
            [dict removeObjectForKey:propertyName];
        }
    }

    return dict;
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENNteacherModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENNteacherModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
