// UTENRect.h

// To parse this JSON:
//
//   NSError *error;
//   UTENRect *range = [UTENRect fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENRect;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENRect : NSObject
@property (nonatomic, nullable, strong) NSNumber *x;
@property (nonatomic, nullable, strong) NSNumber *y;
@property (nonatomic, nullable, strong) NSNumber *width;
@property (nonatomic, nullable, strong) NSNumber *height;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENRect (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
