// To parse this JSON:
//
//   NSError *error;
//   UTENNhistoryModel *nhistoryModel = [UTENNhistoryModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENNhistoryModel;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENNhistoryModel : NSObject
@property (nonatomic, nullable, strong) NSNumber *identifier;
@property (nonatomic, nullable, copy)   NSString *cname;
@property (nonatomic, nullable, copy)   NSString *ateacher;
@property (nonatomic, nullable, copy)   NSString *nclass;
@property (nonatomic, nullable, copy)   NSString *abook;
@property (nonatomic, nullable, copy)   NSString *paydate;
@property (nonatomic, nullable, copy)   NSString *prestudydate;
@property (nonatomic, nullable, copy)   NSString *aclassdate;
@property (nonatomic, nullable, copy)   NSString *aallstudydate;
@property (nonatomic, nullable, copy)   NSString *hisstart;
@property (nonatomic, nullable, copy)   NSString *hidtory;
@property (nonatomic, nullable, copy)   NSString *remark01;
@property (nonatomic, nullable, copy)   NSString *remark02;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENNhistoryModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
