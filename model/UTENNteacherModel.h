// To parse this JSON:
//
//   NSError *error;
//   UTENNteacherModel *nteacherModel = [UTENNteacherModel fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENNteacherModel;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENNteacherModel : NSObject
@property (nonatomic, nullable, strong) NSNumber *identifier;
@property (nonatomic, nullable, copy)   NSString *cname;
@property (nonatomic, nullable, copy)   NSString *ename;
@property (nonatomic, nullable, copy)   NSString *level;
@property (nonatomic, nullable, copy)   NSString *start;
@property (nonatomic, nullable, copy)   NSString *onjob;
@property (nonatomic, nullable, copy)   NSString *seniority;
@property (nonatomic, nullable, copy)   NSString *education;
@property (nonatomic, nullable, copy)   NSString *phone;
@property (nonatomic, nullable, copy)   NSString *address;
@property (nonatomic, nullable, copy)   NSString *c01;
@property (nonatomic, nullable, copy)   NSString *c02;
@property (nonatomic, nullable, copy)   NSString *c03;
@property (nonatomic, nullable, copy)   NSString *c04;
@property (nonatomic, nullable, copy)   NSString *c05;
@property (nonatomic, nullable, copy)   NSString *c06;
@property (nonatomic, nullable, copy)   NSString *c07;
@property (nonatomic, nullable, copy)   NSString *c08;
@property (nonatomic, nullable, copy)   NSString *c09;
@property (nonatomic, nullable, copy)   NSString *c10;
@property (nonatomic, nullable, copy)   NSString *c11;
@property (nonatomic, nullable, copy)   NSString *c12;
@property (nonatomic, nullable, copy)   NSString *c13;
@property (nonatomic, nullable, copy)   NSString *c14;
@property (nonatomic, nullable, copy)   NSString *c15;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENNteacherModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
