#import "UTENBasicModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Private model interfaces

@interface UTENBasicModel (JSONConversion)
// + (instancetype)fromJSONDictionary:(NSDictionary *)dict;
// - (NSDictionary *)JSONDictionary;
@end

#pragma mark - JSON serialization

UTENBasicModel *_Nullable UTENBasicModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENBasicModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENBasicModel *_Nullable UTENBasicModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENBasicModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENBasicModelToData(UTENBasicModel *basicModel, NSError **error)
{
    @try {
        id json = [basicModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENBasicModelToJSON(UTENBasicModel *basicModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENBasicModelToData(basicModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENBasicModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"identifier": @"identifier",
        @"leading": @"leading",
        @"title": @"title",
        @"desc": @"desc",
        @"trailing": @"trailing",
        @"result": @"result",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENBasicModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENBasicModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENBasicModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENBasicModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENBasicModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    return [self dictionaryWithValuesForKeys:UTENBasicModel.properties.allValues];
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENBasicModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENBasicModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
