// To parse this JSON:
//
//   NSError *error;
//   UTENAnimation *animation = [UTENAnimation fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENAnimation;
@class UTENFrame;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENAnimation : NSObject
@property (nonatomic, nullable, copy) NSString *name;
@property (nonatomic, nullable, copy) NSArray<UTENFrame *> *frames;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENAnimation (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

@interface UTENFrame : NSObject
@property (nonatomic, nullable, copy)   NSString *name;
@property (nonatomic, nullable, strong) NSNumber *duration;
@end

@interface UTENFrame (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
