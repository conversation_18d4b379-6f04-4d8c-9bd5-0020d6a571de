#import "UTENSoundCheckModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - JSON serialization

UTENSoundCheckModel *_Nullable UTENSoundCheckModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENSoundCheckModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENSoundCheckModel *_Nullable UTENSoundCheckModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENSoundCheckModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENSoundCheckModelToData(UTENSoundCheckModel *soundCheckModel, NSError **error)
{
    @try {
        id json = [soundCheckModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENSoundCheckModelToJSON(UTENSoundCheckModel *soundCheckModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENSoundCheckModelToData(soundCheckModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENSoundCheckModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"origin": @"origin",
        @"sound": @"sound",
        @"class_name": @"className",
        @"lesson": @"lesson",
        @"speaker": @"speaker",
        @"checker": @"checker",
        @"teacher": @"teacher",
        @"path_extension": @"pathExtension",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENSoundCheckModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENSoundCheckModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENSoundCheckModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENSoundCheckModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENSoundCheckModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    return [self dictionaryWithValuesForKeys:UTENSoundCheckModel.properties.allValues];
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENSoundCheckModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENSoundCheckModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
