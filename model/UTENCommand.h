// UTENCommand.h

// To parse this JSON:
//
//   NSError *error;
//   UTENCommand *command = [UTENCommand fromJSON:json encoding:NSUTF8Encoding error:&error];

#import <Foundation/Foundation.h>

@class UTENCommand;

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Object interfaces

@interface UTENCommand : NSObject
@property (nonatomic, nullable, copy)   NSString *name;
@property (nonatomic, nullable, copy)   NSString *utenClass;
@property (nonatomic, nullable, copy)   NSString *rUtenClass;
@property (nonatomic, nullable, copy)   NSString *c1;
@property (nonatomic, nullable, copy)   NSString *c2;
@property (nonatomic, nullable, copy)   NSString *c3;
@property (nonatomic, nullable, copy)   NSString *selTag;
@property (nonatomic, nullable, strong) NSNumber *writeModule;
@property (nonatomic, nullable, copy)   NSString *loginID;
@property (nonatomic, nullable, copy)   NSString *cname;
@property (nonatomic, nullable, copy)   NSString *ename;
@property (nonatomic, nullable, strong) NSNumber *triggerAt;

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error;
- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error;
- (NSData *_Nullable)toData:(NSError *_Nullable *)error;
@end

#pragma mark - Private model interfaces

@interface UTENCommand (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

NS_ASSUME_NONNULL_END
