#import "UTENComboModel.h"
#import "UTENFile.h"
#import "UTENRect.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - Private model interfaces

@interface UTENComboModel (JSONConversion)
+ (instancetype)fromJSONDictionary:(NSDictionary *)dict;
- (NSDictionary *)JSONDictionary;
@end

static id map(id collection, id (^f)(id value)) {
    id result = nil;
    if ([collection isKindOfClass:NSArray.class]) {
            result = [NSMutableArray arrayWithCapacity:[collection count]];
            for (id x in collection) [result addObject:f(x)];
    } else if ([collection isKindOfClass:NSDictionary.class]) {
            result = [NSMutableDictionary dictionaryWithCapacity:[collection count]];
            for (id key in collection) [result setObject:f([collection objectForKey:key]) forKey:key];
    }
    return result;
}

#pragma mark - JSON serialization

UTENComboModel *_Nullable UTENComboModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENComboModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENComboModel *_Nullable UTENComboModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENComboModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENComboModelToData(UTENComboModel *comboModel, NSError **error)
{
    @try {
        id json = [comboModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENComboModelToJSON(UTENComboModel *comboModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENComboModelToData(comboModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENComboModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"background_images": @"backgroundImages",
        @"background_musics": @"backgroundMusics",
        @"background_musics_volume": @"backgroundMusicsVolume",
        @"right_image": @"rightImage",
        @"wrong_image": @"wrongImage",
        @"attack_image": @"attackImage",
        @"combo_image": @"comboImage",
        @"combo_number": @"comboNumber",
        @"exclude_area": @"excludeArea",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENComboModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENComboModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENComboModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
        _backgroundImages = map(_backgroundImages, λ(id x, map(x, λ(id x, [UTENFile fromJSONDictionary:x]))));
        _backgroundMusics = map(_backgroundMusics, λ(id x, [UTENFile fromJSONDictionary:x]));
        _rightImage = [UTENFile fromJSONDictionary:(id)_rightImage];
        _wrongImage = [UTENFile fromJSONDictionary:(id)_wrongImage];
        _attackImage = map(_attackImage, λ(id x, [UTENFile fromJSONDictionary:x]));
        _comboImage = map(_comboImage, λ(id x, [UTENFile fromJSONDictionary:x]));
        _comboNumber = map(_comboNumber, λ(id x, [UTENFile fromJSONDictionary:x]));
        _excludeArea = map(_excludeArea, λ(id x, [UTENRect fromJSONDictionary:x]));
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENComboModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENComboModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    id dict = [[self dictionaryWithValuesForKeys:UTENComboModel.properties.allValues] mutableCopy];

    // Rewrite property names that differ in JSON
    for (id jsonName in UTENComboModel.properties) {
        id propertyName = UTENComboModel.properties[jsonName];
        if (![jsonName isEqualToString:propertyName]) {
            dict[jsonName] = dict[propertyName];
            [dict removeObjectForKey:propertyName];
        }
    }

    // Map values that need translation
    [dict addEntriesFromDictionary:@{
        @"background_images": NSNullify(map(_backgroundImages, λ(id x, map(x, λ(id x, [x JSONDictionary]))))),
        @"background_musics": NSNullify(map(_backgroundMusics, λ(id x, [x JSONDictionary]))),
        @"right_image": NSNullify([_rightImage JSONDictionary]),
        @"wrong_image": NSNullify([_wrongImage JSONDictionary]),
        @"attack_image": NSNullify(map(_attackImage, λ(id x, [x JSONDictionary]))),
        @"combo_image": NSNullify(map(_comboImage, λ(id x, [x JSONDictionary]))),
        @"combo_number": NSNullify(map(_comboNumber, λ(id x, [x JSONDictionary]))),
        @"exclude_area": NSNullify(map(_excludeArea, λ(id x, [x JSONDictionary]))),
    }];

    return dict;
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENComboModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENComboModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
