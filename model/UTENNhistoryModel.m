
#import "UTENNhistoryModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - JSON serialization

UTENNhistoryModel *_Nullable UTENNhistoryModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENNhistoryModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENNhistoryModel *_Nullable UTENNhistoryModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENNhistoryModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENNhistoryModelToData(UTENNhistoryModel *nhistoryModel, NSError **error)
{
    @try {
        id json = [nhistoryModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENNhistoryModelToJSON(UTENNhistoryModel *nhistoryModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENNhistoryModelToData(nhistoryModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENNhistoryModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"id": @"identifier",
        @"cname": @"cname",
        @"ateacher": @"ateacher",
        @"nclass": @"nclass",
        @"abook": @"abook",
        @"paydate": @"paydate",
        @"prestudydate": @"prestudydate",
        @"aclassdate": @"aclassdate",
        @"aallstudydate": @"aallstudydate",
        @"hisstart": @"hisstart",
        @"hidtory": @"hidtory",
        @"remark01": @"remark01",
        @"remark02": @"remark02",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENNhistoryModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENNhistoryModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENNhistoryModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
        // nsdata 轉換成 nsstring
        self.cname = [[NSString alloc] initWithData:self.cname encoding:NSUTF8StringEncoding];
        self.ateacher = [[NSString alloc] initWithData:self.ateacher encoding:NSUTF8StringEncoding];
        self.nclass = [[NSString alloc] initWithData:self.nclass encoding:NSUTF8StringEncoding];
        self.abook = [[NSString alloc] initWithData:self.abook encoding:NSUTF8StringEncoding];
        // self.paydate = [[NSString alloc] initWithData:self.paydate encoding:NSUTF8StringEncoding];
        // self.prestudydate = [[NSString alloc] initWithData:self.prestudydate encoding:NSUTF8StringEncoding];
        self.aclassdate = [[NSString alloc] initWithData:self.aclassdate encoding:NSUTF8StringEncoding];
        self.aallstudydate = [[NSString alloc] initWithData:self.aallstudydate encoding:NSUTF8StringEncoding];
        // self.hisstart = [[NSString alloc] initWithData:self.hisstart encoding:NSUTF8StringEncoding];
        self.hidtory = [[NSString alloc] initWithData:self.hidtory encoding:NSUTF8StringEncoding];
        self.remark01 = [[NSString alloc] initWithData:self.remark01 encoding:NSUTF8StringEncoding];
        self.remark02 = [[NSString alloc] initWithData:self.remark02 encoding:NSUTF8StringEncoding];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENNhistoryModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENNhistoryModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    id dict = [[self dictionaryWithValuesForKeys:UTENNhistoryModel.properties.allValues] mutableCopy];

    // Rewrite property names that differ in JSON
    for (id jsonName in UTENNhistoryModel.properties) {
        id propertyName = UTENNhistoryModel.properties[jsonName];
        if (![jsonName isEqualToString:propertyName]) {
            dict[jsonName] = dict[propertyName];
            [dict removeObjectForKey:propertyName];
        }
    }

    return dict;
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENNhistoryModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENNhistoryModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
