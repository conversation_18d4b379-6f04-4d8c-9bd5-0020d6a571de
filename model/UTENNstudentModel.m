#import "UTENNstudentModel.h"

// Shorthand for simple blocks
#define λ(decl, expr) (^(decl) { return (expr); })

// nil → NSNull conversion for JSON dictionaries
static id NSNullify(id _Nullable x) {
    return (x == nil || x == NSNull.null) ? NSNull.null : x;
}

NS_ASSUME_NONNULL_BEGIN

#pragma mark - JSON serialization

UTENNstudentModel *_Nullable UTENNstudentModelFromData(NSData *data, NSError **error)
{
    @try {
        id json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingAllowFragments error:error];
        return *error ? nil : [UTENNstudentModel fromJSONDictionary:json];
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

UTENNstudentModel *_Nullable UTENNstudentModelFromJSON(NSString *json, NSStringEncoding encoding, NSError **error)
{
    return UTENNstudentModelFromData([json dataUsingEncoding:encoding], error);
}

NSData *_Nullable UTENNstudentModelToData(UTENNstudentModel *nstudentModel, NSError **error)
{
    @try {
        id json = [nstudentModel JSONDictionary];
        NSData *data = [NSJSONSerialization dataWithJSONObject:json options:kNilOptions error:error];
        return *error ? nil : data;
    } @catch (NSException *exception) {
        *error = [NSError errorWithDomain:@"JSONSerialization" code:-1 userInfo:@{ @"exception": exception }];
        return nil;
    }
}

NSString *_Nullable UTENNstudentModelToJSON(UTENNstudentModel *nstudentModel, NSStringEncoding encoding, NSError **error)
{
    NSData *data = UTENNstudentModelToData(nstudentModel, error);
    return data ? [[NSString alloc] initWithData:data encoding:encoding] : nil;
}

@implementation UTENNstudentModel
+ (NSDictionary<NSString *, NSString *> *)properties
{
    static NSDictionary<NSString *, NSString *> *properties;
    return properties = properties ? properties : @{
        @"cname": @"cname",
        @"ename": @"ename",
        @"school": @"school",
        @"class": @"class",
        @"tel01": @"tel01",
        @"tel02": @"tel02",
        @"tel03": @"tel03",
        @"tel04": @"tel04",
        @"tel05": @"tel05",
        @"tel06": @"tel06",
        @"tel07": @"tel07",
        @"birthday": @"birthday",
        @"address": @"address",
        @"bodyclass": @"bodyclass",
        @"remark1": @"remark1",
        @"remark2": @"remark2",
    };
}

+ (_Nullable instancetype)fromData:(NSData *)data error:(NSError *_Nullable *)error
{
    return UTENNstudentModelFromData(data, error);
}

+ (_Nullable instancetype)fromJSON:(NSString *)json encoding:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENNstudentModelFromJSON(json, encoding, error);
}

+ (instancetype)fromJSONDictionary:(NSDictionary *)dict
{
    return dict ? [[UTENNstudentModel alloc] initWithJSONDictionary:dict] : nil;
}

- (instancetype)initWithJSONDictionary:(NSDictionary *)dict
{
    if (self = [super init]) {
        [self setValuesForKeysWithDictionary:dict];
        // nsdata 轉換成 nsstring
        self.cname = [[NSString alloc] initWithData:self.cname encoding:NSUTF8StringEncoding];
        self.ename = [[NSString alloc] initWithData:self.ename encoding:NSUTF8StringEncoding];
        self.school = [[NSString alloc] initWithData:self.school encoding:NSUTF8StringEncoding];
        self.class = [[NSString alloc] initWithData:self.class encoding:NSUTF8StringEncoding];
        self.tel01 = [[NSString alloc] initWithData:self.tel01 encoding:NSUTF8StringEncoding];
        self.tel02 = [[NSString alloc] initWithData:self.tel02 encoding:NSUTF8StringEncoding];
        self.tel03 = [[NSString alloc] initWithData:self.tel03 encoding:NSUTF8StringEncoding];
        self.tel04 = [[NSString alloc] initWithData:self.tel04 encoding:NSUTF8StringEncoding];
        self.tel05 = [[NSString alloc] initWithData:self.tel05 encoding:NSUTF8StringEncoding];
        self.tel06 = [[NSString alloc] initWithData:self.tel06 encoding:NSUTF8StringEncoding];
        self.tel07 = [[NSString alloc] initWithData:self.tel07 encoding:NSUTF8StringEncoding];
        self.birthday = [[NSString alloc] initWithData:self.birthday encoding:NSUTF8StringEncoding];
        self.address = [[NSString alloc] initWithData:self.address encoding:NSUTF8StringEncoding];
        self.bodyclass = [[NSString alloc] initWithData:self.bodyclass encoding:NSUTF8StringEncoding];
        self.remark1 = [[NSString alloc] initWithData:self.remark1 encoding:NSUTF8StringEncoding];
        self.remark2 = [[NSString alloc] initWithData:self.remark2 encoding:NSUTF8StringEncoding];
    }
    return self;
}

- (void)setValue:(nullable id)value forKey:(NSString *)key
{
    id resolved = UTENNstudentModel.properties[key];
    if (resolved) [super setValue:value forKey:resolved];
}

- (void)setNilValueForKey:(NSString *)key
{
    id resolved = UTENNstudentModel.properties[key];
    if (resolved) [super setValue:@(0) forKey:resolved];
}

- (NSDictionary *)JSONDictionary
{
    return [self dictionaryWithValuesForKeys:UTENNstudentModel.properties.allValues];
}

- (NSData *_Nullable)toData:(NSError *_Nullable *)error
{
    return UTENNstudentModelToData(self, error);
}

- (NSString *_Nullable)toJSON:(NSStringEncoding)encoding error:(NSError *_Nullable *)error
{
    return UTENNstudentModelToJSON(self, encoding, error);
}
@end

NS_ASSUME_NONNULL_END
