//
//  ABCViewController.m
//  uten
//
//  Created by <PERSON> on 2019/11/5.
//  Copyright © 2019 bekubee. All rights reserved.
//

#import "ABCViewController.h"
#import "ViewController.h"
#import "UIImage+Color.h"
#import "UIImage+Rotate.h"
#import "UIImage+SubImage.h"
#import "UIImage+Gif.h"
#import "MQTTClient.h"
#import "SignatureDrawView.h"
#import "SignatureDrawView4H.h"
#import "UTENCommand.h"
#import "UTENCommand+X.h"
#import "UTENEnum.h"
#import <OHMySQL/OHMySQL.h>

@interface ABCViewController () <MQTTSessionDelegate> {
   MQTTSession *session;
   NSString *ID;
   IBOutlet UIButton *btS01;
   IBOutlet UIButton *btS02;
   IBOutlet UIButton *btS03;
   IBOutlet UIButton *btStartTime;
   IBOutlet UIButton *btTimes;
   IBOutlet UILabel *laTimes;
   IBOutlet UIButton *btSingal;
   IBOutlet UILabel *laStatus;
   IBOutlet UILabel *lacWord;
   IBOutlet UILabel *laPass;
   IBOutlet UILabel *laNG;
   IBOutlet UILabel *lasT;
   IBOutlet UILabel *lasTitle;
   int timecnt;
   int wtimer1;
   int wtimer2;
//   int JcountLMA3;     //Peter 0603
//   int JLMA3[400][4];  //Peter 0603
   NSString *ServerIP;
   NSTimer *cTimer;

    UIImageView *I00;
    UIImageView *W01;
    UIImageView *I01;
    SignatureDrawView4H *dW01;
    UIImageView *Wb0;
    UIImageView *Ib0;
    SignatureDrawView *dWb0;
    int wordsel;
    int pass;
    int onshow;
    int PCnt;
    int RCnt;
    int gpencount;
    int wordinx;
    
    //James_0123
    UILabel *hl[100];        //(分數/右上.每個計分點的分數/紅藍70透明.黑籃綠橘紅70透明)
    UILabel *hR,*hL;        //(hR/咖啡字/下/最差/看哪點被扣最多分)(hL/紅字/上/平均/有無通過)
    UIImageView *hs[30];    //(png點)標準計分點.紅藍Ｘ點
    UIImageView *hiv[1000]; //(png點)手寫點.黑籃綠橘紅80透明.紅藍50透明
    UIImageView *hivSQ[100];  //(png點)紅方框(補手寫點)
    UIImageView *hlSQ[100];   //(png點)紅方框(補分數)
    UIImageView *hivUD[100];  //(png點)紅方框(補手寫點)
    UIImageView *hlUD[100];   //(png點)紅方框(補分數)
    
    int badScore;          //本次成績.咖啡色 //0613 peter
    int AverageScore;      //平均分數.紅色   //0613 peter
    int TotalScore;        //總分數         //0613 peter
    int TotalWritePoints;  //總計分點數      //0613 peter
    int UDi;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UDj;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UDk;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UpDownLMA3[400][4];//全域變數.(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)
    int UDsi;   //手寫點上移或下移的指標(在btCheckUp.btCheckDwon使用)
    int UDLMAs[20][4];     //全域變數.(將標準點LMAs_a[~][~]放入,在btCheckUp.btCheckDwon使用)
//    int UpDownLMA3[400][4] = LMA3[~][~];
}

//==說明===============================================================
//宣告參數，導入a-z標準點
//拆分標準筆劃數                        //將標準點的筆劃數,拆分成數筆畫
//跨頁面導入
//    LMA0 [i][0] = LAM [i*4+0];
//    筆畫數超過9筆畫                   //超過，btSimOK直接跳出
//    5~8筆畫,合併成第5筆畫
//準備切5段
//    LMA0A [~][~] = LMA0  [~][~];  //LMA0 放到 LMA0A   // 準備切4段;
//    LMA0  [~][~] = LMA0A [~][~];  //只放第1段(第1筆劃的點數量).
//==<做1~5筆劃迴圈>==========
//  刪重複點
//      LMA1 [~][~] = LMA0 [~][~] ;
//      countLMA1     //計數器
//  做連續點
//      LMA2 [~][~] = LMAn [~][~] ;  //數次連續點暫放 LMAn [~][~] 中
//      countLMA2     //計數器
//      筆畫點數超過300點               //超過，btSimOK直接跳出
//  做轉折點
//    ekztmw.鈍銳角處理
//      LMA2 [~][~]                  //直接寫入 LMA2 [~][~] 中
//    將標準點拆分的筆劃數點,再依<筆劃ic迴圈>倒回去LMAs[][]中
//    補足5點                         //不足5個計分點,補足5點，<ij專則>
//    計分點數超過17點                  //超過，btSimOK直接跳出
//    算各點分數                       //超過標準點的點，設-99分
//  完成導入                           //分段完成，放入完成段中
//      LMA3 [~][~] = LMA2 [~][~];
//==<做1~5筆劃迴圈>==========
//  手寫點缺少點，缺點補點,補-98分
//  4點筆順，NESW.8方位                           //檢查軌跡方向是否順向
//    NESW_way.8方位.內部函式
//  UpDownLMA3[~][~]                 //(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)
//
//
//印出(標準)計分點，(紅叉)(藍叉)點
//印出(手寫)計分點，有紅.藍點+淡黑.橘.綠.藍.紅
//印出分數(紅.藍字)
//印.98.99(紅方框.紅長框)
//印出總分(紅.咖非字)
//==說明===============================================================


//====================================================================
@property (nonatomic, strong)UIImageView*imageView;  //設定底圖//穎作圖(1.)自取名字imageView
@property (nonatomic, strong) UILabel *helloLabel;   //新增文字//穎作字(1.)自取名字helloLabel
//===================================================================
@property (nonatomic, assign) BOOL btSimOKhasExecuted; // 偵測btSimOK不重複執行//peter0617


@end

@implementation ABCViewController



- (IBAction)btSimClear:(id)sender {
    self.btSimOKhasExecuted = NO; // 偵測btSimOK不重複執行//peter0617
    [dWb0 erase];
   //James_0123
    UDi=-3; //清零 peter 0628
    UDj=-1;  //btCheckUp.Down的計數參數.清零
    UDk=-1;  //btCheckUp.Down的計數參數.清零
    UDsi=-1;  //btCheckUp.Down標準點的計數參數.清零
    
    for(int i=0;i<100;i++) { hl[i].text=@""; hl[i].alpha=0.0f; }//0613 peter
    for(int i=0;i<30;i++)   [hs[i] removeFromSuperview];
    for(int i=0;i<1000;i++) [hiv[i] removeFromSuperview];
    for(int i=0;i<100;i++) {[hlSQ[i] removeFromSuperview];  hlSQ[i] = 0;}  //紅方框(補分數)
    for(int i=0;i<100;i++) {[hivSQ[i] removeFromSuperview]; hivSQ[i] = 0;}  //紅方框(補手寫點)
    for(int i=0;i<100;i++) {[hlUD[i] removeFromSuperview];  hlUD[i] = 0;}  //紅方框(向上向下)
    for(int i=0;i<100;i++) {[hivUD[i] removeFromSuperview]; hivUD[i] = 0;}  //紅方框(向上向下)
    hR.text=@"";hR.alpha=0.0f; hL.text=@"";hL.alpha=0.0f;//0613 peter
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//先清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=0;//清零
}

- (IBAction)btSimOK:(id)sender {
    if (self.btSimOKhasExecuted) {
        NSLog(@"ABC:按钮已经被按下过，操作不会再次执行。");
        return; // 直接返回，不执行下面的代码
    }
    self.btSimOKhasExecuted = YES;    // 设置hasExecuted为YES，表示按钮已经被按下执行过
    int mm;
    int weight = 10;
    
    //=====(Peter手寫轉折點辨識 turningPoint)======================================================
    
    int x0,y0,x1,y1,cx,cy,ds,n;  //sx.sy(sx,sy)totalPoints，cx,cyis counter，ds，n
    int  countLMA1=0, countLMA2=0, k=0,K=0,countLMAs=0 ;       //countLMA=0,(countLMA2,count LMA2[~][] )(m.counter is lastnumber)(countLMAs為計數標準點比對數量)
    //(big K is firstPointNumber about[3/for circle].K=0)(small k is firstPointNumber's number.k=44)
    int countK=0;                //Peter 0603.計數.有幾筆劃.//count stroke Number
    int countkk[6]={};           //countkk為count1k數完,加上count2k.數完,再加上count3k.數完,再加上count4k.數完,再加上count5k.數完的值
    int countkkk[5]={};          //countkkk[0]=count1k,countkkk[1]=count2k,countkkk[2]=count3k,countkkk[3]=count4k,countkkk[4]=count5k
    int count1k=0, count2k=0;    //Peter 0603.計數.LMA2[~][3]1000的有幾個;2000的有幾個
    int count3k=0, count4k=0;    //Peter 0603.計數.LMA2[~][3]3000的有幾個;4000的有幾個
    int count5k=0;               //Peter 0617.計數.LMA2[~][3]5000的有幾個;
    int LMA2i=0;                 //(LMA2i.counter is turningPoint for LMA2)
    int LMA0[10000][4] = {};     //James    //*LAM解碼後放入.LMA0[][]
    int LMA0A[10000][4] = {};    //         //暫時運算存放用，用來複製LMA0[~][~]拆成4筆劃
    int LMA1[2000][4] = {};      //         //刪去重複的點後,放入LMA1[][]中
    int LMAn[2000][4] = {}; //new Array     //暫時運算存放用
    int LMA2[400][4] = {};  //correctPoints //做完連續放入LMA2中+完成轉折點.計分點.全部完成//LineMustArray -> LMA2
    int LMA3[400][4] ;  //LMA2->LMA3.數次的LMA2合併進入LMA3    //danny0924 //把這一行移動到76行,變成全域變數
    
    int countLMA3=0 ;                       //計數.LMA0做完每筆畫後到多少.接下筆劃續存
    int countLMA3before=0 ;                 //計數.countLMA3before為countLMA3的上一筆
    int *LAM= [dWb0 getLAMx];       //James //跨頁面傳送
    int c=[dWb0 getLAMc];           //James //跨頁面傳送.c=getLAMc,可以知道數量,導入多少筆資料數量的計數器
    
    
    
    //碰到{08,25,0,0}和{09,25,0,0}出問題，需要消除0變成{8,25,0,0}{9,25,0,0}
    int LMAs[20][4] = {{19,29,0,1001},{5,30,700,1002},{5,45,560,1003},{19,44,0,0},{19,29,230,1005},{19,34,0,0},{19,39,0,0},{19,44,0,0},{22,48,530,1009}};
    //依序將LMAs[i][j]=LMAs_a[i][j]代入用。 LineMustArray Standard for "a"
    //標準點.的第2筆畫 -> {-,-,<1>,-}<1>為第2筆劃標示   LMAs_x[10][4]={{5,25,<0>,0}, ~ ,{-,-,<1>,-}, ~ };
    //4點筆順
    
        int LMAs_a[9][4] = {{19,29,0,1001},{5,30,700,1002},{5,45,560,1003},{19,44,0,0},{19,29,230,1005},{19,34,0,0},{19,39,0,0},{19,44,0,0},{22,48,530,1009}};//Standard-"a"  9
        int LMAs_b[9][4] = {{5,5,0,1001},{5,16,0,0},{5,27,0,0},{5,38,0,0},{5,49,500,1005},{6,28,0,0},{19,31,240,1007},{18,46,530,1008},{5,43,760,1009}};//Standard-"b"  9
        int LMAs_c[5][4] = {{19,33,0,1001},{10,25,700,1002},{5,37,0,0},{10,49,560,1004},{19,41,340,1005}};//Standard-"c" 5
        int LMAs_d[9][4] = {{19,5,0,1001},{19,16,0,0},{19,27,0,0},{19,38,0,0},{19,49,500,1005},{19,32,0,0},{6,28,860,1007},{5,45,570,1008},{19,43,340,1009}};//Standard-"d"   9
        int LMAs_e[5][4] = {{5,37,0,1001},{19,33,300,1002},{6,28,710,1003},{6,46,560,1004},{19,41,340,1005}};//Standard-"e" 5
        int LMAs_f[10][4] = {{20,9,0,1001},{12,10,700,1002},{12,23,0,0},{12,36,0,0},{12,49,560,1005},{4,25,861,2001},{8,25,1,0},{12,25,1,0},{16,25,1,0},{20,25,321,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"f"  10
        int LMAs_g[9][4] = {{18,28,0,1001},{5,30,700,1002},{6,46,560,1003},{19,44,0,0},{19,28,0,0},{19,42,0,0},{19,55,340,1007},{15,68,0,0},{5,62,750,1009}};//Standard-"g" 9
        int LMAs_h[9][4] = {{5,5,0,1001},{5,16,0,0},{5,27,0,0},{5,38,0,0},{5,49,500,1005},{5,35,0,0},{12,25,240,1007},{19,35,0,0},{19,49,430,1009}};//Standard-"h"  9
        int LMAs_i[10][4] = {{12,25,0,1001},{12,31,0,0},{12,37,0,0},{12,43,0,0},{12,49,500,1005},{12,10,1,0},{12,11,1,0},{12,12,1,0},{12,13,1,0},{12,14,111,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"i" 10
        int LMAs_j[10][4] = {{12,25,0,1001},{12,38,0,0},{12,50,0,0},{12,63,500,1004},{5,66,750,1005},{12,10,1,0},{12,11,1,0},{12,12,1,0},{12,13,1,0},{12,14,111,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"j" 10
        int LMAs_k[14][4] = {{5,5,0,1001},{5,16,0,0},{5,27,0,0},{5,38,0,0},{5,49,500,1005},{19,25,241,2001},{15,28,1,0},{12,31,1,0},{8,34,1,0},{5,37,611,2005},{8,40,1,0},{12,43,1,0},{15,46,1,0},{19,49,451,2009}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"k"  14
        int LMAs_l[5][4] = {{12,05,0,1001},{12,16,0,0},{12,27,500,1003},{12,38,0,0},{12,49,550,1005}};//Standard-"l"  5
        int LMAs_m[13][4] = {{5,25,0,1001},{5,31,0,0},{5,37,0,0},{5,43,0,0},{5,49,500,1005},{5,36,0,0},{9,25,130,1007},{12,37,0,0},{12,49,530,1009},{12,36,0,0},{16,25,130,1011},{19,37,0,0},{19,49,530,1013}};//Standard-"m"13
        int LMAs_n[9][4] = {{5,25,0,1001},{5,31,0,0},{5,37,0,0},{5,43,0,0},{5,49,500,1005},{5,35,0,0},{12,25,230,1007},{19,36,0,0},{19,49,430,1009}};//Standard-"n"  9
        int LMAs_o[5][4] = {{12,25,0,1001},{5,37,700,1002},{12,49,550,1003},{19,37,330,1004},{12,25,110,1005}};//Standard-"o"  5
        int LMAs_p[10][4] = {{5,25,0,1001},{5,36,0,0},{5,47,0,0},{5,58,0,0},{5,69,500,1005},{5,31,101,2001},{16,26,1,0},{19,37,421,2003},{15,49,1,0},{5,44,651,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"p"  10
        int LMAs_q[10][4] = {{18,28,0,1001},{7,27,0,0},{5,38,600,1003},{9,49,0,0},{19,44,350,1005},{19,28,121,2001},{19,39,1,0},{19,49,1,0},{19,59,1,0},{19,69,551,2005}};//{-,-,<1>,-}<1>為第2筆劃標示 Standard-"q"  10
        int LMAs_r[9][4] = {{8,25,0,1001},{8,31,0,0},{8,37,0,0},{8,43,0,0},{8,49,500,1005},{8,42,0,0},{8,35,0,0},{10,28,0,0},{17,25,230,1009}};//Standard-"r"  9
        int LMAs_s[5][4] = {{19,31,0,1001},{7,26,700,1002},{12,37,550,1003},{16,48,540,1004},{5,43,700,1005}};//Standard-"s"  5
        int LMAs_t[14][4] = {{12,5,0,1001},{12,18,0,0},{12,31,0,0},{12,44,500,1004},{19,45,350,1005},
            {5,25,811,2001},{9,25,1,0},{12,25,1,0},{16,25,1,0},{19,25,311,2005}};   //{-,-,<1>,-}<1>為第2筆劃標示 Standard-"t" 14
        int LMAs_u[9][4] = {{5,25,0,1001},{5,39,0,0},{12,49,500,1003},{19,38,0,0},{19,25,130,1005},{19,31,0,0},{19,37,0,0},{19,43,0,0},{22,48,530,1009}};  //Standard-"u"    9
        int LMAs_v[9][4] = {{5,25,0,1001},{7,31,0,0},{8,37,0,0},{10,43,0,0},{12,49,400,1005},{14,43,0,0},{16,37,0,0},{17,31,0,0},{19,25,230,1009}};//Standard-"v"    9
        int LMAs_w[17][4] = {{5,25,0,1001},{6,31,0,0},{7,37,0,0},{8,43,0,0},{8,49,400,1005},{9,43,0,0},{10,37,0,0},{11,31,0,0},{12,25,230,1009},{13,31,0,0},{14,37,0,0},{15,43,0,0},{15,49,430,1013},{16,43,0,0},{17,37,0,0},{18,31,0,0},{19,25,230,1017}};//Standard-"w"17
        int LMAs_x[10][4] = {{5,25,0,1001},{9,31,0,0},{12,37,0,0},{15,43,0,0},{19,49,400,1005},{19,25,131,2001},{15,31,1,0},{12,37,1,0},{9,43,1,0},{5,49,671,2005}};//Standard-"x"  10
        int LMAs_y[10][4] = {{5,25,0,1001},{7,31,0,0},{8,37,0,0},{10,43,0,0},{11,49,400,1005},{19,25,231,2001},{15,36,1,0},{12,47,1,0},{8,58,1,0},{5,69,661,2005}};//Standard-"y"  10
        int LMAs_z[13][4] = {{5,25,0,1001},{9,25,0,0},{12,25,0,0},{16,25,0,0},{19,25,300,1005},{15,31,0,0},{12,37,0,0},{9,43,0,0},{5,49,650,1009},{9,49,0,0},{12,49,0,0},{16,49,0,0},{19,49,350,1013}};//Standard-"z" 13
    
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) LMA3[i][j]=0;//清零
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) LMAs[i][j]=0;//清零
    
    switch(wordinx) {
        case 0: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_a[i][j];  break;
        case 1: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_b[i][j];  break;
        case 2: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_c[i][j];  break;
        case 3: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_d[i][j];  break;
        case 4: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_e[i][j];  break;
        case 5: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_f[i][j];  break;
        case 6: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_g[i][j];  break;
        case 7: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_h[i][j];  break;
        case 8: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_i[i][j];  break;
        case 9: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_j[i][j];  break;
        case 10: countLMAs=14; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_k[i][j];  break;
        case 11: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_l[i][j];  break;
        case 12: countLMAs=13; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_m[i][j];  break;
        case 13: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_n[i][j];  break;
        case 14: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_o[i][j];  break;
        case 15: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_p[i][j];  break;
        case 16: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_q[i][j];  break;
        case 17: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_r[i][j];  break;
        case 18: countLMAs=5; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_s[i][j];  break;
        case 19: countLMAs=14; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_t[i][j];  break;
        case 20: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_u[i][j];  break;
        case 21: countLMAs=9; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_v[i][j];  break;
        case 22: countLMAs=17; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_w[i][j];  break;
        case 23: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_x[i][j];  break;
        case 24: countLMAs=10; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_y[i][j];  break;
        case 25: countLMAs=13; for(int i=0;i<countLMAs;i++) for(int j=0;j<4;j++) LMAs[i][j]=LMAs_z[i][j];  break;
    }
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=LMAs[i][j];
    
    //--------(設定參數,將標準點的筆劃數,拆分成數筆畫)------------------
    int LMAs1[20][4]={};//將標準點StandardPoint拆成多筆劃
    int LMAs2[20][4]={};//準備放入第2筆
    int LMAs3[20][4]={};//準備放入第3筆
    int LMAs4[20][4]={};//準備放入第4筆
    for(int i=0,j0=0,j1=0,j2=0,j3=0; i<countLMAs; i++){   //LMAs[i][2]
        //        NSLog(@"標準點=LMAs[%d][]=(%d,%d,<%d>,%d)", i, LMAs[i][0], LMAs[i][1], LMAs[i][2], LMAs[i][3]);   //原始
        switch ( LMAs[i][2] % 10 ){
            case 0:LMAs1[j0][0]=LMAs[i][0]; LMAs1[j0][1]=LMAs[i][1]; j0++;
                NSLog(@"ABC:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs1[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j0-1,LMAs1[j0-1][0],LMAs1[j0-1][1],LMAs1[j0-1][2],LMAs1[j0-1][3]);//小寫最多2筆
                break;
            case 1:LMAs2[j1][0]=LMAs[i][0]; LMAs2[j1][1]=LMAs[i][1]; j1++;
                NSLog(@"ABC:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs2[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j1-1,LMAs2[j1-1][0],LMAs2[j1-1][1],LMAs2[j1-1][2],LMAs2[j1-1][3]);//小寫最多2筆
                break;
            case 2:LMAs3[j2][0]=LMAs[i][0]; LMAs3[j2][1]=LMAs[i][1]; j2++;
                NSLog(@"ABC:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs3[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j2-1,LMAs3[j2-1][0],LMAs3[j2-1][1],LMAs3[j2-1][2],LMAs3[j2-1][3]);//大寫最多3筆，只有Ｅ4筆
                break;
            case 3:LMAs4[j3][0]=LMAs[i][0]; LMAs4[j3][1]=LMAs[i][1]; j3++;
                NSLog(@"ABC:標準點=LMAs[%d][](%d,%d,%d,%d)LMAs4[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j3-1,LMAs4[j3-1][0],LMAs4[j3-1][1],LMAs4[j3-1][2],LMAs4[j3-1][3]);//大寫最多3筆，只有Ｅ4筆
                break;
        }
    }
    //--------(設定參數,將標準點的筆劃數,拆分成數筆畫)------------------
    
    //--------(跨頁面導入/解碼.跨頁面傳輸資料 *LAM=[dWb0 getLAMx])------------------
    for(int i=0; i<10000; i++){
        for(int j=0; j<4 ; j++){
            LMA0[i][j] =0;
        }
    }//LMA0[i][j]，一開始進來，先清零
    //--------(筆畫數超過9筆畫，btSimOK直接跳出)------------------
    for(int i=0; (i<10000)&&(LAM[i*4+0]+LAM[i*4+1]>0) ; i++){
        NSLog(@"ABC:<i=%d>< LAM[%d],[%d],[%d],[%d] >(%d,%d,%d,%d)",i,(i*4+0),(i*4+1),(i*4+2),(i*4+3),LAM[i*4+0],LAM[i*4+1],LAM[i*4+2],LAM[i*4+3]);
        if(LAM[i*4+3] >= 9000 ){
            NSLog(@"ABC:筆畫數超過9筆畫，btSimOK直接跳出。");
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"不要亂畫，老師會生氣！" message:
                                      [NSString stringWithFormat:@"筆畫數超過9筆畫！"] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
            [alertView show];
            
            for(int i=0; i< 40000*4 ; i++){
                LAM[i] =0;
            }              //清除 LAM[~]值
            return;        //直接結束 btSimOK
        }
        //--------(筆畫數超過9筆畫，btSimOK直接跳出)------------------
        
    }//印出LMA[i][j]
    NSLog(@"ABC:<<<*LAMc>>>(%d)",c);
    //--------(5~8筆畫,合併成第5筆畫)------------------
    for(int i=0,j=0;i<c;i++) {            //James
        LMA0[i][0]=LAM[i*4+0];            //James//導入外部LAM[i*4+0]資料.併解碼,為x值
        LMA0[i][1]=LAM[i*4+1];            //James//導入外部LAM[i*4+1]資料.併解碼,為y值
        LMA0[i][2]=LAM[i*4+2];            //James
        
        if( (LAM[i*4+3])/1000>=1 ){       //Peter 0603.導入筆劃1-1000,2-2000,3-3000,4-4000
            if(LAM[i*4+3]<4001){          //控制(1000~4000以內)
                LMA0[i][3]=LAM[i*4+3];    //將LAM第1筆1000,2000,3000,4000放入第1筆LMA0中
                j = LAM[i*4+3];           //將LAM第1筆1000,2000,3000,4000放入加權數j=1000,2000,3000
            }else{
                LMA0[i][3]=5000;          //否則(超過4000以上)都歸入(LMA0[i][3]=5000)第5.6.7...筆劃都只在(LMA0[i][3]=5000)
                j = 5000;                 //否則(超過4000以上)都歸入(j=5000)         第5.6.7...筆劃都只在(j=5000)
            }
        }else{
            LMA0[i][3]=LAM[i*4+3]+j ;
        }                                 //Peter 0603.導入筆劃1-1000,2-2000,3-3000,4-4000
        countK = j/1000;                  //Peter 0603.總筆畫數(countK)
        NSLog(@"ABC:=<i=%d>LMA0(%d,%d,%d,%d)<countK=%d>",i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
        //        NSLog(@"==== LMA0=(%d,%d,%d,%d)<countK=%d>",LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
    }
    //--------(5~8筆畫,合併成第5筆畫)------------------
    
    for(int i=0; i<c; i++){
        NSLog(@"ABC:1K.LMA0<i=%d>LMA0(%d,%d,%d,%d)<countK=%d>",i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
        switch( (LMA0[i][3])/1000 ){
            case 1: count1k += 1; break;  //Peter 0603.計算器count1k第1筆劃有幾點,做幾次.
            case 2: count2k += 1; break;  //Peter 0603.計算器count2k第2筆劃有幾點,做幾次.
            case 3: count3k += 1; break;  //Peter 0603.計算器count3k第3筆劃有幾點,做幾次.
            case 4: count4k += 1; break;  //Peter 0617.計算器count4k第4筆劃有幾點,做幾次.
            default:count5k += 1; break;  //Peter 0617.計算器count5k第5-n筆劃有幾點(全部放到最後筆)
        }
    }
    countkk[0]=0;
    countkk[1]=countkk[0]+count1k;
    countkk[2]=countkk[1]+count2k;
    countkk[3]=countkk[2]+count3k;
    countkk[4]=countkk[3]+count4k;
    countkk[5]=countkk[4]+count5k;
    countkkk[0]=count1k;
    countkkk[1]=count2k;
    countkkk[2]=count3k;
    countkkk[3]=count4k;
    countkkk[4]=count5k;
    NSLog(@"ABC:countkk[0]--(kk[0]=%d,kk[1]=%d,kk[2]=%d,kk[3]=%d,kk[4]=%d,kk[5]=%d,(countK=%d)",countkk[0],countkk[1],countkk[2],countkk[3],countkk[4],countkk[5],countK);
    NSLog(@"ABC:countkkk[0]--[kkk0]=%d,kkk[1]=%d,kkk[2]=%d,kkk[3]=%d,kkk[4]=%d",countkkk[0],countkkk[1],countkkk[2],countkkk[3],countkkk[4]);
    //--------(跨頁面導入/解碼.跨頁面傳輸資料 *LAM=[dWb0 getLAMx])------------------
    //--------( 準備切5段/LMA0A[~][~]=LMA0[~][~];LMA0[~][~]=LMA0A[~][~];)----------
    for (int i=0;i<(count1k+count2k+count3k+count4k+count5k); i++){  //i<(總筆畫數)
        for(int j=0;j<4; j++){
            LMA0A[i][j] = LMA0[i][j];  //LMA0 放到 LMA0A
            LMA0[i][j] = 0;            //放完，LMA0立刻清零，第一次的清零
        }
        NSLog(@"ABC:LMA0A<%d>(%d,%d,%d,%d)<%d>",i,LMA0A[i][0],LMA0A[i][1],LMA0A[i][2],LMA0A[i][3],countK);
        NSLog(@"ABC:LMA0<%d>(%d,%d,%d,%d)<%d>",i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3],countK);
    }
    //--------( 準備切5段/LMA0A[~][~]=LMA0[~][~];LMA0[~][~]=LMA0A[~][~];)----------
    
    //========<做1~5筆劃迴圈>(4+1筆劃/1~4+1數次判斷4+1.times/ic)======================================================
    
    //--------(LMA0[i][j]清零.LMA2[i][j]清零/將上次筆劃殘留的，再次清零)------------------
    for(int ic=0,countLMA1=0,countLMA2=0; ic<countK; ic++){
        countLMA1=0;     //每執行１筆畫,要清零
        countLMA2=0;     //每執行１筆畫,要清零
        
        for(int i=0; (ic>0)&&(i<countkkk[ic-1]); i++){
            for(int j=0; j<4 ; j++){
                LMA0[i][j] =0;
            }
        }//LMA0[i][j]，上次筆畫殘留的，再次清零
        
        for(int i=0; i<400; i++){
            for(int j=0; j<4 ; j++){
                LMA2[i][j] =0;
            }
        }//LMA2[~][~]，上次筆畫殘留的，再次清零 //LMA2[400][4]
        //--------(LMA0[i][j]清零.LMA2[i][j]清零/將上次筆劃殘留的，再次清零)------------------
        
        //--------(LMA0倒入LMA1中/LMA1[~][~]=LMA0[~][~])------------------
        for(int i=0; i< countkkk[ic]; i++ ){
            for(int j=0; j<4 ; j++){
                LMA0[i][j] = LMA0A[(i+countkk[ic])][j];  //只放第1段(第1筆劃的點數量).
            }
            NSLog(@"ABC:=LMA0A->(LMA0)，第%d+1筆.(countkkk[%d]=%d)，LMA0[i=%d](%d,%d,%d,%d)",ic,ic,countkkk[ic],i,LMA0[i][0],LMA0[i][1],LMA0[i][2],LMA0[i][3]);
        }//將LMA0A[i][j]放入LMA0[i][j]中，依序每筆畫執行一次，放在同一位置
        //--------(LMA0倒入LMA1中/LMA1[~][~]=LMA0[~][~])------------------
        //--------(刪重複的點/Delete the same points)----------------------
        LMA1[0][0]=LMA0[0][0];        //put first point
        LMA1[0][1]=LMA0[0][1];        //put first point
        LMA1[0][2]=LMA0[0][2];        //put first point,Peter 0603
        LMA1[0][3]=LMA0[0][3];        //put first point,Peter 0603 數字已改成1000,要導入
        
        for(int i=0 ; i< countkkk[ic]; i++ ){
            if (!(LMA0[i][0]==LMA0[i+1][0] && LMA0[i][1]==LMA0[i+1][1]) ){
                LMA1[(countLMA1)][0] = LMA0[i][0];
                LMA1[(countLMA1)][1] = LMA0[i][1];
                LMA1[(countLMA1)][2] = LMA0[i][2];     //Peter 0603.這行數字沒有變化,可以不用導入
                LMA1[(countLMA1)][3] = LMA0[i][3];     //Peter 0603.這行數字已經變成1000-2000-3000...要導入。
                NSLog(@"ABC:=LMA0-><LMA1>，第%d+1筆.(countkkk[%d]=%d)，LMA1[countLMA1=%d](%d,%d,%d,%d)",ic,ic,countkkk[ic],countLMA1,LMA1[(countLMA1)][0],LMA1[(countLMA1)][1],LMA1[(countLMA1)][2],LMA1[(countLMA1)][3]);
                NSLog(@"ABC:刪重複點(countkkk[ic=%d]=%d)，LMA1[(countLMA1=%d)][](%d,%d,%d,%d)",ic,countkkk[ic],countLMA1,LMA1[(countLMA1)][0],LMA1[(countLMA1)][1],LMA1[(countLMA1)][2],LMA1[(countLMA1)][3]);
                countLMA1++;
            }
        }
        NSLog(@"ABC:<<<*LAMc>>><ic=%d>(countLMA1-1)=%d",ic,(countLMA1-1));
        //--------(刪重複的點/Delete the same points)----------------------
        //--------(put (-1) to LMA2[~][3])--------
        for (int i=0; i< 400 ; i++){
            LMA2[i][3] = -1;
        }
        //--------(put (-1) to LMA2[~][3])--------
        LMA2[0][0] = LMA1[0][0];    //put first point
        LMA2[0][1] = LMA1[0][1];
        LMA2[0][3] = 0;
        LMAn[0][0] = LMA1[0][0];
        LMAn[0][1] = LMA1[0][1];
        //--------(做連續點/做<數次>連續點 make continuousPoint/P/times)------------
        for (int p=0; p<(countLMA1-1) ;p++){
            x0 = LMA1[p][0];
            y0 = LMA1[p][1];
            x1 = LMA1[p+1][0];
            y1 = LMA1[p+1][1];
            ds = 0;
            //--------(做連續點/單次)------------
            if( (abs(x0-x1)-1) >= (abs(y0-y1)-1) ){      //(put biger side into ds，x-side or y-side)
                ds = (abs(x0-x1)-1);
            }else{
                ds = (abs(y0-y1)-1);
            }                                           //(put biger side into ds，x-side or y-side)
            cx=0;
            cy=0;                                      //firstPoint cx+1,cy+1 if x==y don't do anything
            if( (abs(x0-x1)-1) > (abs(y0-y1)-1) ){
                cy=1 ;
            }else if( (abs(x0-x1)-1) < (abs(y0-y1)-1) ){
                cx=1 ;                                   //if (abs(x0-x1)-1)=(abs(y0-y1)-1) don't do anything
            }
            int pmx = 1,pmy = 1;  //(pmx 1 or -1)(pmy 1 or -1)
            pmx = pmx*(abs(x1-x0)/(x1-x0));
            pmy = pmy*(abs(y1-y0)/(y1-y0));
            
            for ( n=0; n < ds ; n++){
                LMAn[n][0] = x0+(cx+(abs(x0-x1)-1)*(n+1)/ds)*pmx ;
                LMAn[n][1] = y0+(cy+(abs(y0-y1)-1)*(n+1)/ds)*pmy ; //(put firstPoint ->.x)(put firstPoint ->.y)
            }//--------(做連續點)
            for(int n=0; n< ds ; n++){               //check don't repeat the same
                LMA2[countLMA2+1][0] = LMAn[n][0];
                LMA2[countLMA2+1][1] = LMAn[n][1];
                countLMA2++;
            }
            LMA2[countLMA2+1][0] = LMA1[p+1][0];//(make continuousPoint last one/the endPoint)
            LMA2[countLMA2+1][1] = LMA1[p+1][1];//(make continuousPoint last one/the endPoint)
            countLMA2++;
            //--------(做連續點/單次)------------
        }
        //--------(做連續點/做<數次>連續點 make continuousPoint/P/times)------------
        
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 ; i++){
            NSLog(@"ABC:做連續點完.第%d+1筆(countLMA2=%d) LMA2(i=%d)(%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        //--------(筆畫點數超過300點，btSimOK直接跳出)------------------
        NSLog(@"ABC:不要亂畫，筆畫點數超過300點 countLMA2= %d點",countLMA2);
        if ( countLMA2 > 300 ){
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"不要亂畫，老師會生氣！" message:
                                      [NSString stringWithFormat:@"筆畫點數超過300點，共%d個！",countLMA2 ] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
            [alertView show];
            return;
        }
        //--------(筆畫點數超過300點，btSimOK直接跳出)------------------
        //--------(做轉折點/make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way)------------
        //--------(ekztmw鈍銳角處理)------------------
        int turnYway_X=4, turnYway_Y=1; //Y軸轉折尖鈍,含abc~大部分(原設定Y_X=4,Y_Y=1)   //上限<=1，上限<=( 4 ~ 8 )
        int turnXway_X=1, turnXway_Y=4; //X軸轉折尖鈍,只有(e.k.m)(原設定X_X=1,X_Y=4)  e k z t m w
        NSLog(@"ABC:turnYway(wordinx=%d)",wordinx);
        switch(wordinx) {
            case 4:  turnYway_X=4;turnYway_Y=1;turnXway_X=1;turnXway_Y=1;  break;//e,4降1變尖.避免有轉折<ekz改turnXway_Y>  //4114
            case 10: turnYway_X=4;turnYway_Y=1;turnXway_X=5;turnXway_Y=8;  break;//k,4升8變鈍。<ekz改turnXway_Y>
            case 12: turnYway_X=2;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;  break;//m,4降2變尖.避免有轉折
            case 13: turnYway_X=2;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;  break;//  n,4降2變尖.避免有轉折
            case 19: turnYway_X=1;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;  break;//t,4降1變尖.避免有轉折
            case 21: turnYway_X=8;turnYway_Y=3;turnXway_X=1;turnXway_Y=4;  break;//  v,4升8變鈍，盡量有轉折
            case 22: turnYway_X=8;turnYway_Y=3;turnXway_X=1;turnXway_Y=4;  break;//w,4升8變鈍，盡量有轉折
            case 25: turnYway_X=4;turnYway_Y=1;turnXway_X=5;turnXway_Y=8;  break;//z,4升8變鈍，90度1升5。<ekz改turnXway_Y>
            default: turnYway_X=4;turnYway_Y=1;turnXway_X=1;turnXway_Y=4;
        }
        //--------(ekztmw鈍銳角處理)------------------
        K=0;//先歸零 //peter 0621
        k=0;//先歸零 //peter 1021
        for(LMA2i=5; LMA2i< (countLMA2+8) ;LMA2i++){        //LMA2i是避免手抖，直接從第5點(LMA2i=5)開始計算。
            //(LMA2i.counter is turningPoint for LMA2)(m.counter is lastnumber for LMA2)
            bool isYnoZero=(LMA2[(LMA2i+4)][1] - LMA2[(LMA2i+3)][1] ) != 0;
            //y軸2數相隔壁，取第1個，第2個不用。(y2-y1)=(33-33)=0;(y2=33,y1=33,y2 is wrong,y1 is turningPoint)
            bool isYChange=(LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+4)][1] ) * ( LMA2[(LMA2i+4)][1] - LMA2[LMA2i+0][1] ) < 0 ;
            //y軸轉方向，(y8-y4)*(y4-y0)(差2.90度)(差1變弧120度,2-3個轉折)(差3變60度,不夠尖無轉折)
            bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= turnYway_Y ;
            //要形成適當的角度，(y8-y0)必須要(0或1)，(y8-y0)=1or=0;(y8-y0<=1)
            //(y8-y4/轉折點)(y4/轉折點-y0)需間隔(3-4)，間隔太近會形成弧非尖角
            bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= turnYway_X;
            //要形成適當的角度，(x8-x0)必須要(0,1,2,3,4)，//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
            //以下相對於x軸。
//            bool isYdistanceNoBig2=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 1;
//            //要形成適當的角度，(y8-y0)必須要(0或1)，(y8-y0)=1or=0;(y8-y0<=1)
//            bool isXdistanceNoBig5=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 4;
//            //要形成適當的角度，(x8-x0)必須要(0,1,2,3,4)，//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
//            //以下相對於x軸。
            
            bool isXnoZero=(LMA2[(LMA2i+4)][0] - LMA2[(LMA2i+3)][0] ) != 0;
            bool isXChange=(LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+4)][0] ) * ( LMA2[(LMA2i+4)][0] - LMA2[LMA2i+0][0] ) < 0 ;//change x way.(x8-x4)*(x4-x0)
            bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= turnXway_X ;//(x8-x0)=1or=0;(x8-x0<=1)
            bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= turnXway_Y;//(y8-y0)=4or3,2,1,0;(y8-y0<=4)
//            bool isXdistanceNoBig2=abs( LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0] ) <= 1;//(x8-x0)=1or=0;(x8-x0<=1)
//            bool isYdistanceNoBig5=abs( LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1] ) <= 4;//(x8-x0)=4or3,2,1,0;(x8-x0<=4)
            
            bool islastPointBig10 = k+10 < (LMA2i+4) && (LMA2i+4) < countLMA2-10;
            //上個轉折點需超過10個計數距離以上，避免手抖產生轉折點 // 最後10點手抖，不做轉折點
            
            NSLog(@"ABC:轉折點前.第%d+1筆 LMA2(LMA2i=%d)(%d,%d,%d,%d)(k=%d +6 =< (LMA2i+4)=%d)",ic,LMA2i,LMA2[LMA2i][0],LMA2[LMA2i][1],LMA2[LMA2i][2],LMA2[LMA2i][3],k,(LMA2i+4));
            if( (isYnoZero && isYChange && isYdistanceNoBig2 && isXdistanceNoBig5 && islastPointBig10)|| (isXnoZero && isXChange && isXdistanceNoBig2 && isYdistanceNoBig5 && islastPointBig10) ){
                
    /*
                NSLog(@"ABC:turnYwayY方向(isYdistanceNoBig2=%d)(y8-y0=%d-%d=%d)(<=%d)",isYdistanceNoBig2,LMA2[(LMA2i+8)][1],LMA2[(LMA2i+0)][1],LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1],turnYway_X);
                NSLog(@"ABC:turnYwayY方向(isXdistanceNoBig5=%d)(x8-x0=%d-%d=%d)(<=%d)",isXdistanceNoBig5,LMA2[(LMA2i+8)][0],LMA2[(LMA2i+0)][0],LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0],turnYway_Y);
                NSLog(@"ABC:turnXwayX方向(isXdistanceNoBig2=%d)(x8-x0=%d-%d=%d)(<=%d)",isXdistanceNoBig2,LMA2[(LMA2i+8)][0],LMA2[(LMA2i+0)][0],LMA2[(LMA2i+8)][0] - LMA2[(LMA2i+0)][0],turnXway_X);
                NSLog(@"ABC:turnXwayX方向(isYdistanceNoBig5=%d)(y8-y0=%d-%d=%d)(<=%d)",isYdistanceNoBig5,LMA2[(LMA2i+8)][1],LMA2[(LMA2i+0)][1],LMA2[(LMA2i+8)][1] - LMA2[(LMA2i+0)][1],turnXway_Y);
     
                NSLog(@"ABC:turn轉折點.轉(i+4)=%d(%d,%d,%d,%d)",LMA2i+4,LMA2[(LMA2i+4)][0],LMA2[(LMA2i+4)][1],LMA2[(LMA2i+4)][2],LMA2[(LMA2i+4)][3] );
                NSLog(@"ABC:turn轉折點.x=1(i+0)=%d(%d,%d,%d,%d)",LMA2i+0,LMA2[(LMA2i+0)][0],LMA2[(LMA2i+0)][1],LMA2[(LMA2i+0)][2],LMA2[(LMA2i+0)][3] );
                NSLog(@"ABC:turn轉折點.y=4(i+8)=%d(%d,%d,%d,%d)",LMA2i+8,LMA2[(LMA2i+8)][0],LMA2[(LMA2i+8)][1],LMA2[(LMA2i+8)][2],LMA2[(LMA2i+8)][3] );
*/
                LMA2[(k+(((LMA2i+4)-k)*1/4))][3] = 4*(K+1)-3 ; //(firstPoint ~ turningPoint)
                LMA2[(k+(((LMA2i+4)-k)*2/4))][3] = 4*(K+1)-2 ; //假如上述條件成立(LMA2i+4)則是轉折點，is turningPoint
                LMA2[(k+(((LMA2i+4)-k)*3/4))][3] = 4*(K+1)-1 ; //[( 上一個轉折點 + (新轉折點-上一個轉折點)*1/4 )] // <做1/4點>
                LMA2[(k+(((LMA2i+4)-k)*4/4))][3] = 4*(K+1)-0 ; //這是轉折點
                
                NSLog(@"ABC:轉折點後1/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*1/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*1/4),LMA2[k+(((LMA2i+4)-k)*1/4)][0],LMA2[k+(((LMA2i+4)-k)*1/4)][1],LMA2[k+(((LMA2i+4)-k)*1/4)][2],LMA2[k+(((LMA2i+4)-k)*1/4)][3]);
                NSLog(@"ABC:轉折點後2/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*2/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*2/4),LMA2[k+(((LMA2i+4)-k)*2/4)][0],LMA2[k+(((LMA2i+4)-k)*2/4)][1],LMA2[k+(((LMA2i+4)-k)*2/4)][2],LMA2[k+(((LMA2i+4)-k)*2/4)][3]);
                NSLog(@"ABC:轉折點後3/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*3/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*3/4),LMA2[k+(((LMA2i+4)-k)*3/4)][0],LMA2[k+(((LMA2i+4)-k)*3/4)][1],LMA2[k+(((LMA2i+4)-k)*3/4)][2],LMA2[k+(((LMA2i+4)-k)*3/4)][3]);
                NSLog(@"ABC:轉折點後4/4.第%d+1筆 LMA2[[k+(((LMA2i+4)-k)*4/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+(((LMA2i+4)-k)*4/4),LMA2[k+(((LMA2i+4)-k)*4/4)][0],LMA2[k+(((LMA2i+4)-k)*4/4)][1],LMA2[k+(((LMA2i+4)-k)*4/4)][2],LMA2[k+(((LMA2i+4)-k)*4/4)][3]);
                
                k = (LMA2i+4); //小k是轉折點，是記錄上一次(LMA2i+4)的值 //small-k is LMA2i counter for Previous turningPoint
                K++; // big-K is counter-turningPoint，K=new turningPoint,1005,1009,1013
            }//if( (isYnoZero && isYChange &&...~
        }//for(LMA2i=5; LMA2i< (countLMA2+8)...~
        
        NSLog(@"ABC:結束點.k=%d,countLMA2=%d,(countLMA2-k)=%d,((countLMA2-k)*1/4)=%d,[k+((countLMA2-k)*1/4)]=%d",k,countLMA2,(countLMA2-k),(countLMA2-k)/4,k+((countLMA2-k)/4) );
        
        LMA2[(k+((countLMA2-k)*1/4))][3] = 4*(K+1)-3 ; //(turningPoint - endPoint)
        LMA2[(k+((countLMA2-k)*2/4))][3] = 4*(K+1)-2 ;
        LMA2[(k+((countLMA2-k)*3/4))][3] = 4*(K+1)-1 ;
        LMA2[(k+((countLMA2-k)*4/4))][3] = 4*(K+1)-0 ;
        
        NSLog(@"ABC:結束點.1/4.第%d+1筆 LMA2[[k+((countLMA2-k)*1/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*1/4),LMA2[k+((countLMA2-k)*1/4)][0],LMA2[k+((countLMA2-k)*1/4)][1],LMA2[k+((countLMA2-k)*1/4)][2],LMA2[k+((countLMA2-k)*1/4)][3]);
        NSLog(@"ABC:結束點.2/4.第%d+1筆 LMA2[[k+((countLMA2-k)*2/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*2/4),LMA2[k+((countLMA2-k)*2/4)][0],LMA2[k+((countLMA2-k)*2/4)][1],LMA2[k+((countLMA2-k)*2/4)][2],LMA2[k+((countLMA2-k)*2/4)][3]);
        NSLog(@"ABC:結束點.3/4.第%d+1筆 LMA2[[k+((countLMA2-k)*3/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*3/4),LMA2[k+((countLMA2-k)*3/4)][0],LMA2[k+((countLMA2-k)*3/4)][1],LMA2[k+((countLMA2-k)*3/4)][2],LMA2[k+((countLMA2-k)*3/4)][3]);
        NSLog(@"ABC:結束點.4/4.第%d+1筆 LMA2[[k+((countLMA2-k)*4/4)]=%d][3]=(%d,%d,%d,%d)",ic,k+((countLMA2-k)*4/4),LMA2[k+((countLMA2-k)*4/4)][0],LMA2[k+((countLMA2-k)*4/4)][1],LMA2[k+((countLMA2-k)*4/4)][2],LMA2[k+((countLMA2-k)*4/4)][3]);
        //--------(做轉折點/make turningPoint )(idea-2:use distance is 4) (y8-y4)*(y4-y0)<=0;change y way)------------
        
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 ; i++){
            NSLog(@"ABC:做轉折點完.第%d+1筆(countLMA2=%d) LMA2(i=%d)(%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        
        //--------(將標準點拆分的筆劃數點,再依<筆劃ic迴圈>倒回去LMAs[][]中)------------
        
        int LMAsC[20][4]={};
        
        for(int i=0; i<countLMAs; i++){   //LMAs[i][2]
            switch (ic){
                case 0:LMAsC[i][0]=LMAs1[i][0]; LMAsC[i][1]=LMAs1[i][1];  break;
                case 1:LMAsC[i][0]=LMAs2[i][0]; LMAsC[i][1]=LMAs2[i][1];  break;
                case 2:LMAsC[i][0]=LMAs3[i][0]; LMAsC[i][1]=LMAs3[i][1];  break;
                case 3:LMAsC[i][0]=LMAs4[i][0]; LMAsC[i][1]=LMAs4[i][1];  break;
                default:LMAsC[i][0]=LMAs4[i][0]; LMAsC[i][1]=LMAs4[i][1];  break;
            }
            NSLog(@"ABC:分段.標準點 LMAsC[i][0].第%d+1筆(i=%d)(%d,%d,%d,%d)",ic ,i,LMAsC[i][0],LMAsC[i][1],LMAsC[i][2],LMAsC[i][3]);
        }
        //--------(將標準點拆分的筆劃數點,再依<筆劃ic迴圈>倒回去LMAs[][]中)------------
        
        //--------(不足5個計分點,補足5點，<ij專則>)------------
        //        NSLog(@"<countLMA2=%d>",countLMA2);
        
        //------印出LMA2[][]內容
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 ; i++){
            NSLog(@"ABC:全印.未補不足5點.第%d+1筆<countLMA2=%d>LMA2[i=%d](%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        
        for(int i=0, countLMA22=countLMA2 ; i<(4-countLMA22)&&(countLMA2<5); i++){
            for(int j=0; j<4; j++){
                if(j<3){
                    LMA2[countLMA22+i+1][j] = LMA2[countLMA22+i][j];
                }else{
                    LMA2[countLMA22+i+1][j] = LMA2[countLMA22+i][j]+1 ;
                }
            }    //將不足5點的最後1點，複製到下1點，(1點複製4次，2點複製3次，3點複製2次，4點複製1次)
            countLMA2++;
            NSLog(@"ABC:<補加第%d點><countLMA2=%d>",(i+1),countLMA2);
            NSLog(@"ABC:now<補點><i=%d>LMA2[countLMA2=%d](%d,%d,%d,%d)",i,countLMA2,LMA2[countLMA2][0],LMA2[countLMA2][1],LMA2[countLMA2][2],LMA2[countLMA2][3]);
        }
        //--------(不足5個計分點,補足5點，<ij專則>)------------
        
        //------再印一次，印出LMA2[][]內容
        for(int i=0; (i<countLMA2+8)&&(LMA2[i][0]+LMA2[i][1])>0 && countLMA2<5 ; i++){
            LMA2[i][3] = i;
            NSLog(@"ABC:全印.已補不足5點.第%d+1筆<countLMA2=%d>LMA2[i=%d](%d,%d,%d,%d)",ic,countLMA2,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        
        //   LMA2[countLMA2][3]<17----1     @"分數 %d",mm
        int LMAsCount=0 ;
        switch (ic){
            case 0: LMAsCount = 17+5 ; break; //第1筆劃上限17防亂畫，加5預防手誤。
            case 1: LMAsCount = 13+5 ; break; //第2筆劃上限13防亂畫，加5預防手誤。
            case 2: LMAsCount = 5+5  ; break; //第3筆劃上限 5防亂畫，加5預防手誤。
            case 3: LMAsCount = 5+5  ; break; //第4筆劃上限 5防亂畫，加5預防手誤。
        }
        //--------(計分點數超過17點，btSimOK直接跳出)------------------
        NSLog(@"ABC:不要亂畫 LMAsCount=%d LMA2[%d][3]= %d ",LMAsCount,countLMA2,LMA2[countLMA2][3]);
        if ( (LMA2[countLMA2][3] > LMAsCount)&& ic<4 ){
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"不要亂畫，老師會生氣！" message:
                                      [NSString stringWithFormat:@"第%d+1筆，計分點超過%d個！",(ic+1),(LMAsCount-5)] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
            [alertView show];
            return;
        }
        //--------(計分點數超過17點，btSimOK直接跳出)------------------
        
        //        --------(算各點分數)------------
        for (int i=0, j=0; i<(countLMA2+1); i++){               //counter-m.last one
            if( LMA2[i][3] > -1 ){
                if( LMAsC[j][0]+LMAsC[j][1]>0  ){
                    int x = LMAsC[ (LMA2[i][3]) ][0]-LMA2[i][0];//(x0-x1)*(x0-x1) = x^2
                    int y = LMAsC[ (LMA2[i][3]) ][1]-LMA2[i][1];//(y0-y1)*(y0-y1) = y^2
                    LMA2[i][2] = round(sqrt( x*x + y*y ));     //count circleScore (√z^2 = √(x^2+y^2))， round(3.5)=4
                    NSLog(@"ABC:(計分)LMA2[i][2].第%d+1筆 i=(%d)(j=%d)(LMA2[%d][3]=%d)(%d,%d,<%d>,%d)",ic,i,j,i,LMA2[i][3],LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
                    j++;
                }else{
                    LMA2[i][2] = 99;
                    NSLog(@"ABC:(扣99)LMA2[i][2].第%d+1筆 i=(%d)(j=%d)(LMA2[%d][3]=%d)(%d,%d,<%d>,%d)",ic,i,j,i,LMA2[i][3],LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
                    j++;
                }
            }
            NSLog(@"ABC:全LMA2[i][2].<第%d+1筆><i=%d><j=%d><LMA2[%d][3]=%d>(%d,%d,<%d>,%d))",ic,i,j,i,LMA2[i][3],LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }
        //        --------(算各點分數)------------
        
        for (int i=0; i<(countLMA2+1) ;i++){     //counter-m.last one
            NSLog(@"ABC:=*第%d+1筆，LMA2<%d>(%d,%d,%d,%d)",ic,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3]);
        }//印出來看看
        
        countLMA3 = countLMA3 + countLMA2; //依序第1次/筆的countLMA2加入countLMA3，第2筆依序加入...
        
        
        
        //--------(將已完成的LMA2放入LMA3中/將每段(依每筆劃),依序將LMA3[~][~]=LMA2[~][~]+(ic+1)*1000+1)放入)------------
        for (int i=0;i< (countLMA2+1); i++){
            for(int j=0; j<4; j++){
                if(j==3){
                    LMA3[(i+countLMA3before)][j] = (LMA2[i][j]+(ic+1)*1000+1);//第1筆畫1000.2000.3000.4000
                    NSLog(@"ABC:第%d+1筆，[3]加權*1000+1 LMA3[(i=%d+countLMA3before=%d)][j=%d] = (LMA2[i=%d][j=%d]+(ic=%d+1)*1000+1)",ic,i,countLMA3before,j,i,j,ic);
                }else{
                    LMA3[(i+countLMA3before)][j] = LMA2[i][j];
                }
            }
            NSLog(@"ABC:第%d+1筆=LMA2[%d](%d,%d,%d,%d)改後,放入 LMA3[%d](%d,%d,%d,%d)",ic,i,LMA2[i][0],LMA2[i][1],LMA2[i][2],LMA2[i][3],i, LMA3[(i+countLMA3before)][0], LMA3[(i+countLMA3before)][1], LMA3[(i+countLMA3before)][2], LMA3[(i+countLMA3before)][3]);
        }
        NSLog(@"ABC:=第%d+1筆<LMA3> (countLMA2=%d)+1 (countLMA3before=%d),(countLMA3=%d)",ic,countLMA2,countLMA3before,countLMA3);
        countLMA3before = countLMA3before+countLMA2+1; //countLMA3A原為0,這樣使他為countLMA3的之前一筆資料做使用
        //--------(將已完成的LMA2放入LMA3中/將每段(依每筆劃),依序將LMA3[~][~]=LMA2[~][~]+(ic+1)*1000+1)放入)------------
    }
    //========<做1~5筆劃迴圈>(4+1筆劃/1~4+1數次判斷4+1.times/ic)======================================================
    
    //印出檢查LMA3全部內容//可刪除
    for(int i=0 ;(i<1000)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0);i++){
        NSLog(@"ABC:=LMA3.全<%d>(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
    }
    //NSLog(@"標準點=LMAs[%d][](%d,%d,%d,%d)LMAs1[%d][](%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3],j0-1,LMAs1[j0-1][0],LMAs1[j0-1][1],LMAs1[j0-1][2],LMAs1[j0-1][3]);//小寫最多2筆
    //--------(手寫點缺少點，補點,補-98分)------------
    int countLMA3Point = 0;   //計數LMA3有計分點的數量
    int countLMA3LastOne = 0; //計數LMA3最後的位置
    for(int i=0 ;(i<1000)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0);i++){
        countLMA3LastOne++;
        if( LMA3[i][3]%1000!=0 ){
            countLMA3Point++;
            NSLog(@"ABC:=LMA3.全(countLMA3Point=%d)(countLMA3LastOne=%d)<%d>(%d,%d,%d,%d)",countLMA3Point,countLMA3LastOne,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        }
    }
    //印出檢查LMAs全部內容//可刪除
    for(int i=0 ; (LMAs[i][0]+LMAs[i][1])>0 ; i++){
        NSLog(@"ABC:=LMAs.標準點.全<%d>(%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3]);
        countLMAs = i;
    }
    for(int i=0; i<( countLMAs - countLMA3Point ); i++){      //假如標準點多於手寫點
        NSLog(@"ABC:=標準點>手寫點.(countLMAs=%d)-(countLMA3Point=%d) > (i=%d)",countLMAs,countLMA3Point,i );
        LMA3[countLMA3LastOne][0] = LMAs[countLMA3Point+i][0]; //將標準點LMAs[][0]的x值.放入手寫LMA3[][0]中
        LMA3[countLMA3LastOne][1] = LMAs[countLMA3Point+i][1]; //將標準點LMAs[][1]的y值.放入手寫LMA3[][1]中
        LMA3[countLMA3LastOne][2] = 98;                        //將 98 放入手寫LMA3[2]中
        LMA3[countLMA3LastOne][3] = (2000+1+i);  //放入2001,2002,2003...但是如果<第3.4筆>會錯誤！
        NSLog(@"ABC:=   標準點       <第%d個>(%d,%d,%d,%d)",i+1,LMAs[countLMA3Point+i][0],LMAs[countLMA3Point+i][1],LMAs[countLMA3Point+i][2],(2000+1+i) );
        NSLog(@"ABC:=補標準點到 LMA3中<第%d個>(countLMA3LastOne=%d)(%d,%d,%d,%d)",i+1,countLMA3LastOne,LMA3[countLMA3LastOne][0],LMA3[countLMA3LastOne][1],LMA3[countLMA3LastOne][2],LMA3[countLMA3LastOne][3]);
        countLMA3LastOne++;
    }
    for(int i=0 ;(i<1000)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0);i++){
        NSLog(@"ABC:=再印LMA3.全<%d>(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
    }
    //--------(手寫點缺少點，補點,補-98分)------------
    
    
    //--------(4點筆順，<NESW.8方位>，檢查軌跡方向是否正確)------------

    /*
    //--------(<NESW_way.8方位>內部函式)------------
    //(內部函式用法)(透過 block(代碼塊)來實現類似內部函式的功能) Peter 2024 1014
    //原始 int NESW_way( int X0, int Y0, int X1, int Y1, int Way ){~}
    //原始 int (^NESW_way)(int,int,int,int,int)= ^( int X0, int Y0, int X1, int Y1, int Way){}
    
    int (^NESW_way)(int,int,int,int,int,int,int)= ^( int X00, int Y00, int X0, int Y0, int X1, int Y1, int Way){
        switch( Way/100 ){
            case 1: if( (Y0-Y1)<0 ){ NSLog(@"往北^^ (O)"); return 1;} else { NSLog(@"往北^^ (X)"); return 11;} break;//往北N^^.是回傳1.否回傳0
            case 2: if( (Y0-Y1)<0 && (X0-X1)<0 ){ NSLog(@"往東北 >^ (O)"); return 1;} else { NSLog(@"往東北 >^ (X)"); return 12;} break;//往東北EN>^.是回傳1.否回傳0
            case 3: if( (X0-X1)<0 ){ NSLog(@"往東>> (O)"); return 1;} else { NSLog(@"往東>> (X)"); return 13;} break;//往東E>>.是回傳1.否回傳0
            case 4: if( (X0-X1)<0 && (Y0-Y1)>0 ){ NSLog(@"往東南 >v (O)"); return 1;} else { NSLog(@"往東南 >v (X)"); return 14;} break;//往東南ES>v.是回傳1.否回傳0
            case 5: if( (Y0-Y1)>0 ){ NSLog(@"往南vv (O)"); return 1;} else { NSLog(@"往南vv (X)"); return 15;} break;//往南Svv.是回傳1.否回傳0
            case 6: if( (Y0-Y1)>0 && (X0-X1)>0 ){ NSLog(@"往西南 <v (O)"); return 1;} else { NSLog(@"往西南 <v (X)"); return 16;} break;//往西南WS<v.是回傳1.否回傳0
            case 7: if( (X0-X1)>0 ){ NSLog(@"往西<< (O)"); return 1;} else { NSLog(@"往西<< (X)"); return 17;} break;//往西W<<.是回傳1.否回傳0
            case 8: if( (X0-X1)>0 && (Y0-Y1)<0 ){ NSLog(@"往西北 <^ (O)"); return 1;} else { NSLog(@"往西北 <^ (X)"); return 18;} break;//往西北NW<^.是回傳1.否回傳0
            default: return -1;
        }
        switch( (Way/10)%10 ){
            case 1: if( (Y00-Y1)<0 ){ NSLog(@"上上點.往北^^ (O)"); return 1;} else { NSLog(@"上上點.往北^^ (X)"); return 21;} break;//往北N^^.是回傳1.否回傳0
            case 2: if( (Y00-Y1)<0 && (X00-X1)<0 ){ NSLog(@"上上點.往東北 >^ (O)"); return 1;} else { NSLog(@"上上點.往東北 >^ (X)"); return 22;} break;//往東北EN>^.是回傳1.否回傳0
            case 3: if( (X00-X1)<0 ){ NSLog(@"上上點.往東>> (O)"); return 1;} else { NSLog(@"上上點.往東>> (X)"); return 23;} break;//往東E>>.是回傳1.否回傳0
            case 4: if( (X00-X1)<0 && (Y00-Y1)>0 ){ NSLog(@"上上點.往東南 >v (O)"); return 1;} else { NSLog(@"上上點.往東南 >v (X)"); return 24;} break;//往東南ES>v.是回傳1.否回傳0
            case 5: if( (Y00-Y1)>0 ){ NSLog(@"上上點.往南vv (O)"); return 1;} else { NSLog(@"上上點.往南vv (X)"); return 25;} break;//往南Svv.是回傳1.否回傳0
            case 6: if( (Y00-Y1)>0 && (X00-X1)>0 ){ NSLog(@"上上點.往西南 <v (O)"); return 1;} else { NSLog(@"上上點.往西南 <v (X)"); return 26;} break;//往西南WS<v.是回傳1.否回傳0
            case 7: if( (X00-X1)>0 ){ NSLog(@"上上點.往西<< (O)"); return 1;} else { NSLog(@"上上點.往西<< (X)"); return 27;} break;//往西W<<.是回傳1.否回傳0
            case 8: if( (X00-X1)>0 && (Y00-Y1)<0 ){ NSLog(@"上上點.往西北 <^ (O)"); return 1;} else { NSLog(@"上上點.往西北 <^ (X)"); return 28;} break;//往西北NW<^.是回傳1.否回傳0
            default: return -1;
        }
        
        return 99;
    };
    //--------(<NESW_way.8方位>內部函式)------------
    */
     
    int NESW_n1 = 1001; int NESW_n0 = 1001; int NESW_n00 = 1001; int NESW_2W = 0;//n判斷點,n0上一點,n00為上上點  //(已將LMAs[i][j]=LMAs_a[i][j]代入使用)，1001做完.查找LMAs[~][~]下一個為1003或1005，1005放入NESW_n中。
    int NESW_i1=0, NESW_i0=0, NESW_i00=0;  //n->i,n0->i0,n00->i00;(n判斷點在LMA3[][]中的第i位置)
    int LMA3_X1=0;int LMA3_Y1=0;  int LMA3_X0=0;int LMA3_Y0=0;  int LMA3_X00=0;int LMA3_Y00=0;//為上個點的上一點，即上上點
    
    //--------(取判斷點.NESW_n，即LMA3[~][3]的1001的下一點，即1005)------------
    for(int m=1; (m<20)&&(LMAs[m][0]+LMAs[m][1])>0; m++){
        if(NESW_n1 < LMAs[m][3]){
            NESW_n1 = LMAs[m][3];
            NESW_2W = LMAs[m][2];
            NSLog(@"標準s(NESW_n1=%d)(LMAs[%d][3]=%d)(NESW_2W=%d)",NESW_n1,m,LMAs[m][3],NESW_2W);
        }else{
            continue;
        }
        
    //--------(取手寫LMA3[~][3]的(X1,Y1)(NESW_i1))------------
    for(int i=0; (LMA3[i][3]>=1000)&&(i<400) ; i++){
        if(NESW_n1 == LMA3[i][3]){
            LMA3_X1 = LMA3[i][0];//此為1002，即1001的下一個
            LMA3_Y1 = LMA3[i][1];
            NESW_i1 = i;
            NSLog(@"手寫3(X1,Y1)(NESW_n1=%d)LMA3[%d][3]=(%d,%d,~,%d)",NESW_n1,i,LMA3[i][0],LMA3[i][1],LMA3[i][3]);
            //--------(取手寫LMA3[~][3]的(X0,Y0)(NESW_i0)------------
            for(int j=i; j>=0; j--){
                if(NESW_n0 == LMA3[j][3]){
                    LMA3_X0 = LMA3[j][0];
                    LMA3_Y0 = LMA3[j][1];
                    NESW_i0 = j;
                    NSLog(@"手寫3(X0,Y0)(NESW_n0=%d)LMA3[%d][3]=(%d,%d,~,%d)",NESW_n0,j,LMA3[j][0],LMA3[j][1],LMA3[j][3]);
                    //--------(取手寫LMA3[~][3]的(X00,Y00)(NESW_i00)------------
                    for(int k=j; k>=0; k--){
                        if(NESW_n00 == LMA3[k][3]){
                            LMA3_X00 = LMA3[k][0];
                            LMA3_Y00 = LMA3[k][1];
                            NESW_i00 = k;
                            NSLog(@"手寫3(X00,Y00)(NESW_n00=%d)LMA3[%d][3]=(%d,%d,~,%d)",NESW_n00,k,LMA3[k][0],LMA3[k][1],LMA3[k][3]);
                            break;
                        }//由1002倒退回去找1001的點。
                    }
                    break;
                }//由1002倒退回去找1001的點。
            }
            break;
        }
    }
    
    //--------(<NESW_way.8方位>函式)------------
    
        switch( NESW_2W/100 ){
            case 1: if( (LMA3_Y0-LMA3_Y1)>0 ){ NSLog(@"(Y0-Y1)往北1 ^^ (O)");} else { NSLog(@"(Y0-Y1)往北1 ^^ (X)"); LMA3[NESW_i1][2]=97;} break;//往北N^^.是(O).否改97
            case 2: if( (LMA3_Y0-LMA3_Y1)>0 && (LMA3_X0-LMA3_X1)<0 ){ NSLog(@"(XY0-XY1)往東北2 ^> (O)");} else { NSLog(@"(XY0-XY1)往東北2 ^> (X)"); LMA3[NESW_i1][2]=97;} break;//往東北EN>^.是(O).否改97
            case 3: if( (LMA3_X0-LMA3_X1)<0 ){ NSLog(@"(X0-X1)往東3 >> (O)");} else { NSLog(@"(X0-X1)往東3 >> (X)"); LMA3[NESW_i1][2]=97;} break;//往東E>>.是(O).否改97
            case 4: if( (LMA3_X0-LMA3_X1)<0 && (LMA3_Y0-LMA3_Y1)<0 ){ NSLog(@"(XY0-XY1)往東南4 v> (O)");} else { NSLog(@"(XY0-XY1)往東南4 v> (X)"); LMA3[NESW_i1][2]=97;} break;//往東南ES>v.是(O).否改97
            case 5: if( (LMA3_Y0-LMA3_Y1)<0 ){ NSLog(@"(Y0-Y1)往南5 vv (O)");} else { NSLog(@"(Y0-Y1)往南5 vv (X)"); LMA3[NESW_i1][2]=97;} break;//往南Svv.是(O).否改97
            case 6: if( (LMA3_Y0-LMA3_Y1)<0 && (LMA3_X0-LMA3_X1)>0 ){ NSLog(@"(XY0-XY1)往西南6 <v (O)");} else { NSLog(@"(XY0-XY1)往西南6 <v (X)"); LMA3[NESW_i1][2]=97;} break;//往西南WS<v.是(O).否改97
            case 7: if( (LMA3_X0-LMA3_X1)>0 ){ NSLog(@"(X0-X1)往西7 << (O)"); } else { NSLog(@"(X0-X1)往西7 << (X)"); LMA3[NESW_i1][2]=97;} break;//往西W<<.是(O).否改97
            case 8: if( (LMA3_X0-LMA3_X1)>0 && (LMA3_Y0-LMA3_Y1)>0 ){ NSLog(@"(XY0-XY1)往西北8 <^ (O)");} else { NSLog(@"(XY0-XY1)往西北8 <^ (X)"); LMA3[NESW_i1][2]=97;} break;//往西北NW<^.是(O).否改97
        }
        switch( (NESW_2W/10)%10 ){
            case 1: if( (LMA3_Y00-LMA3_Y1)>0 ){ NSLog(@"(Y00-Y1)往北1 ^^ (O)");} else { NSLog(@"(Y00-Y1)往北1 ^^ (X)"); LMA3[NESW_i1][2]=96;} break;//往北N^^.是(O).否改96
            case 2: if( (LMA3_Y00-LMA3_Y1)>0 && (LMA3_X00-LMA3_X1)<0 ){ NSLog(@"(XY00-XY1)往東北2 ^> (O)");} else { NSLog(@"(XY00-XY1)往東北2 ^> (X)"); LMA3[NESW_i1][2]=96;} break;//往東北EN>^.是(O).否改96
            case 3: if( (LMA3_X00-LMA3_X1)<0 ){ NSLog(@"(X00-X1)往東3 >> (O)");} else { NSLog(@"(X00-X1)往東3 >> (X)"); LMA3[NESW_i1][2]=96;} break;//往東E>>.是(O).否改96
            case 4: if( (LMA3_X00-LMA3_X1)<0 && (LMA3_Y00-LMA3_Y1)<0 ){ NSLog(@"(XY00-XY1)往東南4 v> (O)");} else { NSLog(@"(XY00-XY1)往東南4 v> (X)"); LMA3[NESW_i1][2]=96;} break;//往東南ES>v.是(O).否改96
            case 5: if( (LMA3_Y00-LMA3_Y1)<0 ){ NSLog(@"(Y00-Y1)往南5 vv (O)");} else { NSLog(@"(Y00-Y1)往南5 vv (X)"); LMA3[NESW_i1][2]=96;} break;//往南Svv.是(O).否改96
            case 6: if( (LMA3_Y00-LMA3_Y1)<0 && (LMA3_X00-LMA3_X1)>0 ){ NSLog(@"(XY00-XY1)往西南6 <v (O)");} else { NSLog(@"(XY00-XY1)往西南6 <v (X)"); LMA3[NESW_i1][2]=96;} break;//往西南WS<v.是(O).否改96
            case 7: if( (LMA3_X00-LMA3_X1)>0 ){ NSLog(@"(X00-X1)往西7 << (O)"); } else { NSLog(@"(X00-X1)往西7 << (X)"); LMA3[NESW_i1][2]=96;} break;//往西W<<.是(O).否改96
            case 8: if( (LMA3_X00-LMA3_X1)>0 && (LMA3_Y00-LMA3_Y1)>0 ){ NSLog(@"(XY00-XY1)往西北8 <^ (O)");} else { NSLog(@"(XY00-XY1)往西北8 <^ (X)"); LMA3[NESW_i1][2]=96;} break;//往西北NW<^.是(O).否改96
    }
    
    NSLog(@"LMA3的(i00=%d<%d,%d>)(i0=%d<%d,%d>)(i1=%d<%d,%d>)(NESW_n1=%d,NESW_2W=%d)(LMA3[NESW_i1][2]=%d)", NESW_i00,LMA3_X00,LMA3_Y00, NESW_i0,LMA3_X0,LMA3_Y0, NESW_i1,LMA3_X1,LMA3_Y1,NESW_n1,NESW_2W,LMA3[NESW_i1][2] );

    NESW_n00 = NESW_n0;//將(X0,Y0)1001.進入下一點為.(X0,Y0)1002
    NESW_n0 = NESW_n1;//將(X0,Y0)1001.進入下一點為.(X0,Y0)1002
        
    }//把 標準點的LMAs[m][3].從1檢查到最後，標準點不超過20點，第1點跳過
    //--------(4點筆順，<NESW.8方位>，檢查軌跡方向是否正確)------------
    
    //--------(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)------------

    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//先清零
    for(int i=0; (i<400)&&(LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3]>0); i++){
        for(int j=0; j<4; j++){
            UpDownLMA3[i][j] = LMA3[i][j];
        }
    }
    UDi = -1;  //預設值為起點-1(不存在)
    UDj = -1;  //預設值為起點-1(不存在)
    UDk = -1;  //預設值為起點-1(不存在)
    //--------(將LMA3[~][~]放入,在btCheckUp.btCheckDwon使用)------------
//=====(Peter手寫轉折點辨識 turningPoint)======================================================

    
    
    
    
    
    
    //==danny.做圖1 =============================================================================
        
        //--------(印.標準計分點 print StandardPoint)------------------
    for (int i=0; (i<20)&&(LMAs[i][0]+LMAs[i][1])!=0 ;i++){
        NSLog(@"<redP1>.LMAs<i=%d>(%d,%d,%d,%d)",i,LMAs[i][0],LMAs[i][1],LMAs[i][2],LMAs[i][3]);
    }
    
        CGRect frame;                           //James
        for(int i = 0, j=0; i < countLMAs ; i++){            //j用來計算又增加1筆劃，將redP1多印1次
            if( ((LMAs[i][2]%10-LMAs[i-1][2]%10)!=0)&&(j!=0) ){
                hs[i]= [[UIImageView alloc] initWithFrame:CGRectMake(180+10*LMAs[i][0],12+10*LMAs[i][1] , weight, weight)];
                hs[i].image=[UIImage imageNamed:@"redP1"];//標準計分點.(紅叉)點
                [self.view addSubview:hs[i]];
                NSLog(@"ABC:<redP1.標準點>(i=%d,j=%d)(LMAs[%d][2]除10餘=%d)-(LMAs[%d-1][2]=%d)!=0 ",i,j,i, LMAs[i][2]%10,i, LMAs[i-1][2]%10 );
                continue;
            }
            if( j %4 == 0 ){
                hs[i]= [[UIImageView alloc] initWithFrame:CGRectMake(180+10*LMAs[i][0],12+10*LMAs[i][1] , weight, weight)];
                hs[i].image=[UIImage imageNamed:@"redP1"];//標準計分點.(紅叉)點
                [self.view addSubview:hs[i]];
                NSLog(@"ABC:<redP1.標準點.紅叉>(if) hs<i=%d>.<j=%d>", i, j);
                j++;
            }else {
                hs[i]= [[UIImageView alloc] initWithFrame:CGRectMake(180+10*LMAs[i][0],12+10*LMAs[i][1] , weight, weight)];
                hs[i].image=[UIImage imageNamed:@"blueP2"];//標準計分點.(藍叉)點
                [self.view addSubview:hs[i]];
                NSLog(@"ABC:<redP1.標準點.藍叉>(else) hs<i=%d>.<j=%d>", i, j);
                j++;
            }
        }
        //--------(印.標準計分點 print StandardPoint)------------------
        
        //計算LMA3[][]有多少數量放入countLMA3中
        //    int LMA3a1=0, LMA3a2=0, LMA3a3=0, LMA3a4=0; //設LMA3每筆畫數的結尾數LMA3a1...
        for(int i=0,countLMA3=-1; i<2000 && (LMA3[i][0]+LMA3[i][1]+LMA3[i][2]+LMA3[i][3])>0; i++){
            countLMA3 +=1 ;
            NSLog(@"ABC:<countLMA3/%d><%d>(%d/%d/%d/%d)",countLMA3,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        }
        //計算LMA3[][]有多少數量放入countLMA3中
        
        //--------(印.手寫點 print StandardPoint)------------------
        for (int i = 0; i<(countLMA3LastOne) && LMA3[i][3]>=1000 ; i++) { //peter 0621 改
            NSString *imageName;  //imageName
            NSLog(@"ABC:=<black80><LMA3[i][3]/1000=第%d筆<%d>(%d,%d,%d,%d)",LMA3[i][3]/1000,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
            switch (LMA3[i][3]/1000) {     //a=(LMA3[~][3]/1000)，第1筆畫數1001/1000=1需要換成1，第2筆畫數2008/1000=2需要換成2
                case 1: imageName = @"black80";  break;  //淡黑.透明度80.第1筆
                case 2: imageName = @"blue80";   break;  //淡藍.透明度80.第2筆
                case 3: imageName = @"green80";  break;  //淡綠.透明度80.第3筆
                case 4: imageName = @"orange80"; break;  //淡橘.透明度80.第4筆
                default:imageName = @"red80";    break;  //淡紅.透明度80.Ｘ超過第5筆
            }
//i依序跑完直到最後筆(conutLMA2+1)(原有-1.0.1.2.3.4值，-1直接濾除)(現為1001.1000~1000.1002.1003.20001.2000~2000.2002.~)
            switch ((LMA3[i][3]%1000-1) % 4) {  //先(LMA2[i][3]/1000-1)變回原值;
                case 0:                 imageName = @"red50";  break;
                case 1: case 2: case 3: imageName = @"blue50"; break;
            }
            hiv[i] = [[UIImageView alloc] initWithFrame:CGRectMake(180 + 10 * LMA3[i][0], 12 + 10 * LMA3[i][1], weight, weight)];
            hiv[i].image = [UIImage imageNamed:imageName];
            hiv[i].contentMode = UIViewContentModeScaleAspectFit;
            [self.view addSubview:hiv[i]];
        }
        //--------(印.手寫點 print StandardPoint)------------------
    
        //--------(印.分數紅藍字.落起轉(紅)計分點(藍) print )----------
        badScore = -1;       //本次成績.咖啡色    //0613 peter
        AverageScore=0;      //平均分數.紅色      //0613 peter
        TotalScore=0;        //總分數            //0613 peter
        TotalWritePoints=0;  //總計分點數         //0613 peter
        //peter 0614 先產生實例，再設定屬性
        //Danny 0603.依筆畫數切換位置(落.1/4.2/4.3/4.轉.1/4.2/4.3/4.起)的位置
        //所有點的分數    //i依序播放上述的點的位置.
        //i依序跑完hl[i]直到最後筆.(LMA2[k][2]已經放入hl[i]).
        //(原有-1.0.1.2.3.4值，-1直接濾除)(現為1001.1000~1000.1002..20001.2000~2000.2002.~)

        for(int j=0,i=0,k=0; ( j<(countLMA3LastOne) ) && (LMA3[j][3]>=1000) ; j++,i++,k++ ) { //peter 0621 改
            while ( LMA3[j][3]%1000 == 0 ){        //跳過非計分點,只挑(0,1,2~最後筆/例8)跳過()
                j++;
            }
            //----------------
            if(LMA3[j][3]%1000==1){
                k=0;                                                 //(k)點用來位移到(下一個字)
                NSLog(@"ABC:=開頭為1,數字左移1行,k=0歸零(LMA3[%d][3]除餘1000)=(%d) <i=%d><j=%d>",j,LMA3[j][3]%1000,i,j);
            }
            switch (LMA3[j][3]/1000) {                               //(case:)用來切換(跳行)印出
                case 1: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(550, 50+38*k, 60, 60)]; break;//分數紅.黑字(第1排/筆)//原始
                case 2: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(610, 50+38*k, 60, 60)]; break;//分數紅.藍字(第2排/筆)//原始
                case 3: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(670, 50+38*k, 60, 60)]; break;//分數紅.綠字(第3排/筆)//原始
                case 4: hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(730, 50+38*k, 60, 60)]; break;//分數紅.橘字(第4排/筆)//原始
                default:hl[i]=[[UILabel alloc] initWithFrame:CGRectMake(790, 50+38*k, 60, 60)]; break;//分數紅.紅字(第5排/筆)//0613 peter
            }                                                       //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
            //peter 0614
            NSLog(@"ABC:=hl[i=%d],<k=%d>LMA3[%d](%d,%d,%d,%d)",i,k,j,LMA3[j][0],LMA3[j][1],LMA3[j][2],LMA3[j][3]);
            hl[i].font = [UIFont systemFontOfSize:30];
            [self.view addSubview:hl[i]];
            }
//            for(int i=0, k=0 , minusLMA3=0; (i<=countLMA3+4)&&(k<=countLMA3+4) ; i++){ //原始peter 0625
            NSLog(@"ABC:countLMA3LastOne=%d",countLMA3LastOne);
            for(int i=0, k=0 , minusLMA3=0; (i<=countLMA3LastOne-1)&&(k<=countLMA3LastOne-1) ; i++){
                //      次數=>LMA3a1為第1筆的[3]是第1筆的計分點最後數量，上面為4個筆畫所有的計分點數量
                while ( LMA3[k][3]%1000 == 0 ){        //只挑(0,1,2~最後筆/例8)
                    k++;
                }
                TotalWritePoints++;
                NSLog(@"ABC:<TotalWritePoints>=<%d>",TotalWritePoints);
                minusLMA3 = LMA3[k][2]*(-1) ;     //將圈數分數加上負號,(5)圈.轉為(-5)
                NSLog(@"<TotalScore.before>=<%d>，LMA3[%d][2]=%d，",TotalScore,k,LMA3[k][2]*(-1));
                TotalScore = TotalScore + LMA3[k][2]*(-1);
                NSLog(@"ABC:<TotalScore.after>=<%d>",TotalScore);
                hl[i].text = [NSString stringWithFormat:@"%d", minusLMA3];//穎作字(4.)放入文字內容
                if( ( (LMA3[k][3]%1000-1)%4 == 0) ){
                    hl[i].textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.5 alpha:0.7];
                    //分數紅藍字.依.hl[i].0/4/8 順序決定.(紅字)
                }else{
                    //分數紅藍字.依.hl[i].123/567順序決定.(藍字換以下字)
                    switch (LMA3[k][3]/1000) {             //(case:)用來切換(跳行)印出
                        case 1: hl[i].textColor = [UIColor colorWithRed:0.0 green:0.0 blue:0.0 alpha:0.5]; break;//分數淡黑字(第1排/筆)
                        case 2: hl[i].textColor = [UIColor colorWithRed:0.5 green:0.6 blue:0.8 alpha:0.7]; break;//分數淡藍字(第2排/筆)
                        case 3: hl[i].textColor = [UIColor colorWithRed:0.7 green:0.8 blue:0.1 alpha:0.7]; break;//分數淡綠字(第3排/筆)
                        case 4: hl[i].textColor = [UIColor colorWithRed:0.9 green:0.5 blue:0.1 alpha:0.7]; break;//分數淡橘字(第4排/筆)
                        default:hl[i].textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.5 alpha:0.5]; break;//分數紅字(第4排/筆)//0613 peter
                    }
                }//(1.0不透明.alpha:0.7)(黑0/0/0.藍0.5/0.6/0.8綠0.7/0.8/0.1橘0.9/0.5/0.1紅0.9/0/0.5白1/1/1 //測試OK
                if ( minusLMA3 < badScore ){
                    badScore = minusLMA3;        //放最差的成績，-4<-5把-4放入badScore記錄下來
                }//-------------(get score)------------
                k++;
    //            [self.view addSubview:hl[i]];//0612
            }

        //--------(印.分數紅藍字.落起轉(紅)計分點(藍) print )----------
    
    
    //--------(印.98.99紅方框/圖)------------------
    for (int i = 0; i<(countLMA3LastOne) && LMA3[i][3]>=1000 ; i++) { //peter 0621 改
        if ( LMA3[i][2]==98 ){
            NSString *imageName01 = @"square03";
//            NSLog(@"印方框 imageName=%@,(%d)(%d,%d,%d,%d)",imageName01,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
            hivSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*LMA3[i][0],2+10*LMA3[i][1],30,30)];
            hivSQ[i].image = [UIImage imageNamed:imageName01];
        }else if( LMA3[i][2]==99 ){
            NSString *imageName02 = @"square05";
//            NSLog(@"印方框 imageName=%@,(%d)(%d,%d,%d,%d)",imageName02,i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
            hivSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*LMA3[i][0],2+10*LMA3[i][1],30,30)];
            hivSQ[i].image = [UIImage imageNamed:imageName02];
        }
            hivSQ[i].contentMode = UIViewContentModeScaleAspectFit;
            [self.view addSubview:hivSQ[i]];
    }
    //--------(印.98.99紅方框/圖)------------------
    //--------(印.98.99紅長框/數字)------------------
    //Danny 0603.依筆畫數切換位置(落.1/4.2/4.3/4.轉.1/4.2/4.3/4.起)的位置
    //i依序跑完hl[i]直到最後筆.(LMA2[k][2]已經放入hl[i]).
    //(原有-1.0.1.2.3.4值，-1直接濾除)(現為1001.1000~1000.1002..20001.2000~2000.2002.~)
    
    for(int j=0,i=0,k=0; ( j<(countLMA3LastOne) ) && (LMA3[j][3]>=1000) ; j++,k++) { //peter 0621 改
        while ( LMA3[j][3]%1000 == 0 ){  j++;  } //跳過非計分點,只挑(0,1,2~最後筆/例8)跳過()
        if(LMA3[j][3]%1000==1){
            k=0;                                                 //(k)點用來位移到(下一個字)
//            NSLog(@"=開頭為1,數字左移1行,k=0歸零(LMA3[%d][3]除餘1000)=(%d) <i=%d><j=%d>",j,LMA3[j][3]%1000,i,j);
        }
//        NSLog(@"長方形99(%d)(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
        if( LMA3[j][2]==98 ){
            NSString *imageName03 = @"rectangle03";
//            NSLog(@"(有.長方形99)(%d)(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
            switch (LMA3[j][3]/1000) {                               //(case:)用來切換(跳行)印出
                case 1: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*k, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
                case 2: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*k, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
                case 3: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*k, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
                case 4: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*k, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
                default:hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*k, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
            }    //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
            hlSQ[i].image = [UIImage imageNamed:imageName03];
        }else if( LMA3[j][2]==99 ){
            NSString *imageName04 = @"rectangle05";
//            NSLog(@"(有.長方形99)(%d)(%d,%d,%d,%d)",i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
            switch (LMA3[j][3]/1000) {                               //(case:)用來切換(跳行)印出
                case 1: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*k, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
                case 2: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*k, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
                case 3: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*k, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
                case 4: hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*k, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
                default:hlSQ[i] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*k, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
            }                                                       //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
            hlSQ[i].image = [UIImage imageNamed:imageName04];
        }
        hlSQ[i].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hlSQ[i]];
        i++;
    }
    //--------(印.98.99紅長框/數字)------------------
    
    //--------(印.總分紅咖啡字.(紅)通過(咖啡)最差 print verageScore/red badScore/brown)----------
    AverageScore = round(TotalScore/(TotalWritePoints-1));//四捨五入//(get score/get Average score)
    NSLog(@"ABC:AverageScore %d,TotalScore %d,TotalWritePoints %d)",AverageScore,TotalScore,TotalWritePoints);
    //James_0123
    //peter 0614 先產生實例，再設定屬性
    //----------------
    hL= [[UILabel alloc] initWithFrame:CGRectMake(850, 360, 200, 100)]; //分數紅/咖啡字.(紅字 /上)
    hL.font=[UIFont systemFontOfSize:70];
    hL.text=[NSString stringWithFormat:@"%d", AverageScore];
    hL.textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.0 alpha:1.0];                                
    //分數紅/咖啡字.(紅字 /上)(平均/有無通過)
    hR= [[UILabel alloc] initWithFrame:CGRectMake(850, 500, 150, 100)]; //分數紅/咖啡字.(咖啡字/下)
    hR.font=[UIFont systemFontOfSize:70];
    hR.text=[NSString stringWithFormat:@"%d", badScore];
    hR.textColor=[UIColor colorWithRed:0.5 green:0.6 blue:0.8 alpha:1.0];
    //分數紅/咖啡字.(咖啡字/下)(最差/看哪點被扣最多分)
    [self.view addSubview:hL];
    [self.view addSubview:hR];
    //--------(印.總分紅咖啡字.(紅)通過(咖啡)最差 print verageScore/red badScore/brown)----------

    
    //==danny.做圖1 =============================================================================
//    for(int i=0;i<400;i++)
//        for(int j=0;j<4;j++) JLMA3[i][j]=LMA3[i][j];
//    JcountLMA3=countLMA3;
    [dWb0 isWorNumber:wordinx];// 告訴他什麼字
    mm=[dWb0 isWord:Wb0.image];// 看709行
    NSLog(@"ABC AverageScore %d",AverageScore);

    //==上傳手寫字母所有資料含LMA3==================
    // 計數LMA3的數量=======
    int Json_n=0;
    for ( int i=0 ; (i<400) && (LMA3[i][0]+LMA3[i][1]) >0 ; Json_n++, i++){
        NSLog(@"<i=%d>(%d,%d,%d,%d)", i,LMA3[i][0],LMA3[i][1],LMA3[i][2],LMA3[i][3]);
    }//Json_n計算LMA3長度計數
    // 計數LMA3的數量=======
    // 以雙重迴圈將LMA3壓製成字串型態jsonString=======
    NSMutableArray *array = [[NSMutableArray alloc] init];
    for (int i = 0; i < Json_n; i++) {
        NSMutableArray *subArray = [[NSMutableArray alloc] init];
        for (int j = 0; j < 4; j++) {
            [subArray addObject:@(LMA3[i][j])];
        }
        [array addObject:subArray];
    }

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:array options:0 error:&error];
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

    NSLog(@" JSON 格式的 jsonData : %@", jsonData);
    NSLog(@" JSON 格式的 jsonString : %@", jsonString);
    // 以雙重迴圈將LMA3壓製成字串型態jsonString=======
    
    NSString *GetCNAME = [[NSUserDefaults standardUserDefaults] objectForKey:@"CNAME2"];
    
    
    
    
    //設定上傳連線=======
  OHMySQLUser *user;
    user = [[OHMySQLUser alloc] initWithUserName:@"uten"
                                         password:@"1qazXSW@3edcVFR$"
                                       serverName:@"uten.synology.me"
                                           dbName:@"uten"
                                             port:3307
                                           socket:@"/run/mysqld/mysqld10.sock"];
    
    OHMySQLStoreCoordinator *coordinator = [[OHMySQLStoreCoordinator alloc] initWithUser:user];
    [coordinator connect];
    [coordinator setEncoding:CharsetEncodingUTF8MB4];
    
    OHMySQLQueryContext *queryContext = [OHMySQLQueryContext new];
    queryContext.storeCoordinator = coordinator;
    //設定上傳連線=======
    //取得字母abc.待罰=======
    // wordinx
    char Getabc = 'a';
    for (int i=0; i<=25; i++){
        if (i == wordinx){
            Getabc = Getabc + i;
            NSLog(@"Getabc = %c",Getabc);
        }
    }
    NSString *GetabcString = [NSString stringWithFormat:@"%c", Getabc];
    int NoPass = 5;
    if ( mm >= 60 ){
        NoPass = 0;
    }
    
    //取得字母abc.待罰=======
    //設定上傳的資料=======
    // 假設只插入不包含自動填充的日期欄位
    NSDictionary *insertData = @{
        @"CNAME": GetCNAME,            // 倒入CNAME
        @"字母": GetabcString,          // 字母 未  @"test_word",
        @"字母軌跡": jsonString,         // 導入字母軌跡
        @"通過分數": @(mm),              // 導入 mm 是整數變數
        @"待罰"   : @(NoPass),          // 導入 NoPass (及格0,不及格5次)
    };
  
    OHMySQLQueryRequest *query = [OHMySQLQueryRequestFactory INSERT:@"AllStudentABC" set:insertData];
    //設定上傳的資料=======
    //上傳完成=======
    NSError *error1 = nil;
    [queryContext executeQueryRequest:query error:&error];
    
    if (error1) {
        NSLog(@"Error1: %@", error.localizedDescription);
    } else {
        NSLog(@"Data inserted successfully into dannytest.");
    }
    [coordinator disconnect];
    //上傳完成=======
    NSLog(@"ABC讀Signatrue分數mm %d",mm);
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:
                              [NSString stringWithFormat:@"你的分數 %d 已上傳分數",mm] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
    [alertView show];
   
    //==上傳手寫字母所有資料含LMA3==================
        
}//(IBAction)btSimOK:(id)sender(結束)==========================================================================

- (IBAction)btAdd:(id)sender {

    self.btSimOKhasExecuted = NO; // 偵測btSimOK不重複執行//peter0617
    [dWb0 erase];
    
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=0;//清零
    
    for(int i=0;i<100;i++) { hl[i].text=@""; hl[i].alpha=0.0f; }
    for(int i=0;i<30;i++) [hs[i] removeFromSuperview];
    for(int i=0;i<1000;i++) [hiv[i] removeFromSuperview];
    for(int i=0;i<100;i++) {  [hlSQ[i] removeFromSuperview];  hlSQ[i] = 0;}  //紅方框(補分數)
    for(int i=0;i<100;i++) { [hivSQ[i] removeFromSuperview]; hivSQ[i] = 0;}  //紅方框(補手寫點)
    for(int i=0;i<100;i++) {  [hlUD[i] removeFromSuperview];  hlUD[i] = 0;}  //紅方框(向上向下)
    for(int i=0;i<100;i++) { [hivUD[i] removeFromSuperview]; hivUD[i] = 0;}  //紅方框(向上向下)
//    hR.text=@""; hL.text=@"";
    hR.text=@"";hR.alpha=0.0f;hL.text=@"";hL.alpha=0.0f;//0613
    if(wordinx < 25) wordinx++;
   // self.imageView.image = [UIImage imageNamed:@"Wa"];               //代入名稱
    switch(wordinx) {
        case 0: Wb0.image=[UIImage imageNamed:@"a1.png"]; self.imageView.image = [UIImage imageNamed:@"Wa"]; break;
        case 1: Wb0.image=[UIImage imageNamed:@"b1.png"]; self.imageView.image = [UIImage imageNamed:@"Wb"]; break;
        case 2: Wb0.image=[UIImage imageNamed:@"c1.png"]; self.imageView.image = [UIImage imageNamed:@"Wc"]; break;
        case 3: Wb0.image=[UIImage imageNamed:@"d1.png"]; self.imageView.image = [UIImage imageNamed:@"Wd"]; break;
        case 4: Wb0.image=[UIImage imageNamed:@"e1.png"]; self.imageView.image = [UIImage imageNamed:@"We"]; break;
        case 5: Wb0.image=[UIImage imageNamed:@"f1.png"]; self.imageView.image = [UIImage imageNamed:@"Wf"]; break;
        case 6: Wb0.image=[UIImage imageNamed:@"g1.png"]; self.imageView.image = [UIImage imageNamed:@"Wg"]; break;
        case 7: Wb0.image=[UIImage imageNamed:@"h1.png"]; self.imageView.image = [UIImage imageNamed:@"Wh"]; break;
        case 8: Wb0.image=[UIImage imageNamed:@"i1.png"]; self.imageView.image = [UIImage imageNamed:@"Wi"]; break;
        case 9: Wb0.image=[UIImage imageNamed:@"j1.png"]; self.imageView.image = [UIImage imageNamed:@"Wj"]; break;
        case 10: Wb0.image=[UIImage imageNamed:@"k1.png"]; self.imageView.image = [UIImage imageNamed:@"Wk"]; break;
        case 11: Wb0.image=[UIImage imageNamed:@"l1.png"]; self.imageView.image = [UIImage imageNamed:@"Wl"]; break;
        case 12: Wb0.image=[UIImage imageNamed:@"m1.png"]; self.imageView.image = [UIImage imageNamed:@"Wm"]; break;
        case 13: Wb0.image=[UIImage imageNamed:@"n1.png"]; self.imageView.image = [UIImage imageNamed:@"Wn"]; break;
        case 14: Wb0.image=[UIImage imageNamed:@"o1.png"]; self.imageView.image = [UIImage imageNamed:@"Wo"]; break;
        case 15: Wb0.image=[UIImage imageNamed:@"p1.png"]; self.imageView.image = [UIImage imageNamed:@"Wp"]; break;
        case 16: Wb0.image=[UIImage imageNamed:@"q1.png"]; self.imageView.image = [UIImage imageNamed:@"Wq"]; break;
        case 17: Wb0.image=[UIImage imageNamed:@"r1.png"]; self.imageView.image = [UIImage imageNamed:@"Wr"]; break;
        case 18: Wb0.image=[UIImage imageNamed:@"s1.png"]; self.imageView.image = [UIImage imageNamed:@"Ws"]; break;
        case 19: Wb0.image=[UIImage imageNamed:@"t1.png"]; self.imageView.image = [UIImage imageNamed:@"Wt"]; break;
        case 20: Wb0.image=[UIImage imageNamed:@"u1.png"]; self.imageView.image = [UIImage imageNamed:@"Wu"]; break;
        case 21: Wb0.image=[UIImage imageNamed:@"v1.png"]; self.imageView.image = [UIImage imageNamed:@"Wv"]; break;
        case 22: Wb0.image=[UIImage imageNamed:@"w1.png"]; self.imageView.image = [UIImage imageNamed:@"Ww"]; break;
        case 23: Wb0.image=[UIImage imageNamed:@"x1.png"]; self.imageView.image = [UIImage imageNamed:@"Wx"]; break;
        case 24: Wb0.image=[UIImage imageNamed:@"y1.png"]; self.imageView.image = [UIImage imageNamed:@"Wy"]; break;
        case 25: Wb0.image=[UIImage imageNamed:@"z1.png"]; self.imageView.image = [UIImage imageNamed:@"Wz"]; break;
        default: wordinx=0;
    }
}
- (IBAction)btSub:(id)sender {
    
    self.btSimOKhasExecuted = NO; // 偵測btSimOK不重複執行//peter0617
    [dWb0 erase];
    
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=0;//清零
    
    for(int i=0;i<100;i++) { hl[i].text=@""; hl[i].alpha=0.0f; }
    for(int i=0;i<30;i++)   [hs[i] removeFromSuperview];
    for(int i=0;i<1000;i++) [hiv[i] removeFromSuperview];
    for(int i=0;i<100;i++) {[hlSQ[i] removeFromSuperview];  hlSQ[i] = 0;}  //紅方框(補分數)
    for(int i=0;i<100;i++) {[hivSQ[i] removeFromSuperview]; hivSQ[i] = 0;}  //紅方框(補手寫點)
    for(int i=0;i<100;i++) {[hlUD[i] removeFromSuperview];  hlUD[i] = 0;}  //紅方框(向上向下)
    for(int i=0;i<100;i++) {[hivUD[i] removeFromSuperview]; hivUD[i] = 0;}  //紅方框(向上向下)
//    hR.text=@""; hL.text=@"";
    hR.text=@"";hR.alpha=0.0f;hL.text=@"";hL.alpha=0.0f;//0613
    if(wordinx > 0) wordinx--;
    switch(wordinx) {
        case 0: Wb0.image=[UIImage imageNamed:@"a1.png"]; self.imageView.image = [UIImage imageNamed:@"Wa"]; break;
        case 1: Wb0.image=[UIImage imageNamed:@"b1.png"]; self.imageView.image = [UIImage imageNamed:@"Wb"]; break;
        case 2: Wb0.image=[UIImage imageNamed:@"c1.png"]; self.imageView.image = [UIImage imageNamed:@"Wc"]; break;
        case 3: Wb0.image=[UIImage imageNamed:@"d1.png"]; self.imageView.image = [UIImage imageNamed:@"Wd"]; break;
        case 4: Wb0.image=[UIImage imageNamed:@"e1.png"]; self.imageView.image = [UIImage imageNamed:@"We"]; break;
        case 5: Wb0.image=[UIImage imageNamed:@"f1.png"]; self.imageView.image = [UIImage imageNamed:@"Wf"]; break;
        case 6: Wb0.image=[UIImage imageNamed:@"g1.png"]; self.imageView.image = [UIImage imageNamed:@"Wg"]; break;
        case 7: Wb0.image=[UIImage imageNamed:@"h1.png"]; self.imageView.image = [UIImage imageNamed:@"Wh"]; break;
        case 8: Wb0.image=[UIImage imageNamed:@"i1.png"]; self.imageView.image = [UIImage imageNamed:@"Wi"]; break;
        case 9: Wb0.image=[UIImage imageNamed:@"j1.png"]; self.imageView.image = [UIImage imageNamed:@"Wj"]; break;
        case 10: Wb0.image=[UIImage imageNamed:@"k1.png"]; self.imageView.image = [UIImage imageNamed:@"Wk"]; break;
        case 11: Wb0.image=[UIImage imageNamed:@"l1.png"]; self.imageView.image = [UIImage imageNamed:@"Wl"]; break;
        case 12: Wb0.image=[UIImage imageNamed:@"m1.png"]; self.imageView.image = [UIImage imageNamed:@"Wm"]; break;
        case 13: Wb0.image=[UIImage imageNamed:@"n1.png"]; self.imageView.image = [UIImage imageNamed:@"Wn"]; break;
        case 14: Wb0.image=[UIImage imageNamed:@"o1.png"]; self.imageView.image = [UIImage imageNamed:@"Wo"]; break;
        case 15: Wb0.image=[UIImage imageNamed:@"p1.png"]; self.imageView.image = [UIImage imageNamed:@"Wp"]; break;
        case 16: Wb0.image=[UIImage imageNamed:@"q1.png"]; self.imageView.image = [UIImage imageNamed:@"Wq"]; break;
        case 17: Wb0.image=[UIImage imageNamed:@"r1.png"]; self.imageView.image = [UIImage imageNamed:@"Wr"]; break;
        case 18: Wb0.image=[UIImage imageNamed:@"s1.png"]; self.imageView.image = [UIImage imageNamed:@"Ws"]; break;
        case 19: Wb0.image=[UIImage imageNamed:@"t1.png"]; self.imageView.image = [UIImage imageNamed:@"Wt"]; break;
        case 20: Wb0.image=[UIImage imageNamed:@"u1.png"]; self.imageView.image = [UIImage imageNamed:@"Wu"]; break;
        case 21: Wb0.image=[UIImage imageNamed:@"v1.png"]; self.imageView.image = [UIImage imageNamed:@"Wv"]; break;
        case 22: Wb0.image=[UIImage imageNamed:@"w1.png"]; self.imageView.image = [UIImage imageNamed:@"Ww"]; break;
        case 23: Wb0.image=[UIImage imageNamed:@"x1.png"]; self.imageView.image = [UIImage imageNamed:@"Wx"]; break;
        case 24: Wb0.image=[UIImage imageNamed:@"y1.png"]; self.imageView.image = [UIImage imageNamed:@"Wy"]; break;
        case 25: Wb0.image=[UIImage imageNamed:@"z1.png"]; self.imageView.image = [UIImage imageNamed:@"Wz"]; break;
        default: wordinx=0;
    }
}


- (IBAction)btCheckDown:(id)sender {
    if( UpDownLMA3[0][3]+UpDownLMA3[1][3]+UpDownLMA3[2][3]+UpDownLMA3[3][3] == 0 ){
        NSLog(@"ABC:請先按計分！");
        UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"請先寫，再按計分！" message:
                                  [NSString stringWithFormat:@"再按上一點 或 下一點"] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
        [alertView show];
        return;        //直接結束 btCheckUp
    }
    //--------(印.左字母圖方框)------------------
    NSLog(@"(Down開始清除.前)(UDi=%d,UDsi=%d,UDj=%d)",UDi,UDsi,UDj);
    [hivUD[0] removeFromSuperview]; //清除上次顯示的方框(Up轉Down)
    [hivSQ[0] removeFromSuperview]; //清除上次顯示的方框.標準點(Up轉Down)
    [hivUD[UDi] removeFromSuperview]; //清除上次顯示的方框
    [hivSQ[UDsi] removeFromSuperview]; //清除上次顯示的方框.標準點
    [hlSQ[UDj] removeFromSuperview];  //清除上次顯示的長框

    UDi++ ;
    UDj++ ;
    UDk++ ;
    UDsi++ ;

//    NSLog(@"印方框1,(UDi=%d)(UDj=%d)(UDk=%d)",UDi,UDj,UDk);

    if(UpDownLMA3[UDi][3]>=1000){
        NSLog(@"(Down.while前 UDi=%d)(UpDownLMA3[UDi][3]=%d)",UDi,UpDownLMA3[UDi][3]);
        while(UpDownLMA3[UDi][3]%1000==0){ UDi++; } //跳過非計分點,只挑(0,1,2~最後筆/例8)跳過()
        NSLog(@"(Down.while後 UDi=%d)",UDi);
//            NSLog(@"(UDi=%d)(UpDownLMA3[UDi][3]=%d)(UpDownLMA3[UDi][3]%1000=%d)",UDi,UpDownLMA3[UDi][3],UpDownLMA3[UDi][3]%1000);
//            NSLog(@"印方框2,(UDi=%d)(UDi=%d)(%d,%d,%d,%d)(UDi+1=%d)(UpDownLMA3[UDi+1][3]=%d)",UDi,UDi,UpDownLMA3[UDi][0],UpDownLMA3[UDi][1],UpDownLMA3[UDi][2],UpDownLMA3[UDi][3],(UDi+1),(UpDownLMA3[UDi+1][3]));
            NSString *imageName05 = @"square04";
            hivUD[UDi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UpDownLMA3[UDi][0],2+10*UpDownLMA3[UDi][1],30,30)];
            hivUD[UDi].image = [UIImage imageNamed:imageName05];
            //        }
            hivUD[UDi].contentMode = UIViewContentModeScaleAspectFit;
            [self.view addSubview:hivUD[UDi]];
        
        //---(印.左字母圖方框.標準點)-------------
        NSLog(@"Down雙框if(UDi=%d)(UDsi=%d)(%d,%d)",UDi,UDsi,170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1]);
        hivSQ[UDsi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1],30,30)];
        hivSQ[UDsi].image = [UIImage imageNamed:imageName05];
        //        }
        hivSQ[UDsi].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hivSQ[UDsi]];
        //---(印.左字母圖方框.標準點)-------------
        
    }else{
        NSLog(@"Down-else.未減１(UDi=%d)",UDi);
        UDi--;//倒退一格，停在原來的位置點
        NSLog(@"Down-else.減１後(UDi=%d)",UDi);
//        NSLog(@"(UDi=%d)(UpDownLMA3[UDi][3]=%d)(UpDownLMA3[UDi][3]%1000=%d)",UDi,UpDownLMA3[UDi][3],UpDownLMA3[UDi][3]%1000);
//        NSLog(@"印方框3,(UDi=%d)(UDi=%d)(%d,%d,%d,%d)(UDi+1=%d)(UpDownLMA3[UDi+1][3]=%d)",UDi,UDi,UpDownLMA3[UDi][0],UpDownLMA3[UDi][1],UpDownLMA3[UDi][2],UpDownLMA3[UDi][3],(UDi+1),(UpDownLMA3[UDi+1][3]));
        NSString *imageName05 = @"square01";
        NSLog(@"Down雙框else(UDi=%d)(UDsi=%d)(%d,%d)",UDi,UDsi,170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1]);
        hivUD[UDi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UpDownLMA3[UDi][0],2+10*UpDownLMA3[UDi][1],30,30)];
        hivUD[UDi].image = [UIImage imageNamed:imageName05];
        //        }
        hivUD[UDi].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hivUD[UDi]];
        UDi++;//倒退一格，停在原來的位置點
        NSLog(@"Down(UDi=%d)(UDsi=%d)",UDi,UDsi);
        
        //---(印.左字母圖方框.標準點)-------------

        UDsi--;//倒退一格，停在原來的位置點
        hivSQ[UDsi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1],30,30)];
        hivSQ[UDsi].image = [UIImage imageNamed:imageName05];
        //        }
        hivSQ[UDsi].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hivSQ[UDsi]];
        NSLog(@"Down雙框else(UDi=%d)(UDsi=%d)(UDj=%d)(%d,%d)",UDi,UDsi,UDj,170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1]);
//        UDsi++;//倒退一格，停在原來的位置點(不用加１)
        NSLog(@"Down加１後(UDsi=%d)",UDsi);
        //---(印.左字母圖方框.標準點)-------------
    }
    //--------(印.左字母圖方框)------------------
    
    //--------(印.右計分數字長框)------------------
    NSLog(@"(Down印長框.前)(UDi=%d,UDsi=%d,UDj=%d,UDk=%d)(UpDownLMA3[UDi][3]=%d)",UDi,UDsi,UDj,UDk,UpDownLMA3[UDi][3]);
    if(UpDownLMA3[UDi][3]>=1000){
        if(UpDownLMA3[UDi][3]%1000==1){ UDk=0; }//(UDk)點用來位移(下一個字)
        NSLog(@"(Down印長框.前2)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
        NSString *imageName06 = @"rectangle04";
        switch (UpDownLMA3[UDi][3]/1000) {                               //(case:)用來切換(跳行)印出
            case 1: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*UDk, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
            case 2: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*UDk, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
            case 3: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*UDk, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
            case 4: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*UDk, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
            default:hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*UDk, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        }    //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
        hlSQ[UDj].image = [UIImage imageNamed:imageName06];
        hlSQ[UDj].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hlSQ[UDj]];
    }else{
        NSLog(@"(Down右.倒退一格.前)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
        UDi-- ;//倒退一格，停在原來的位置點
        UDj-- ;//倒退一格，停在原來的位置點
        UDk-- ;//倒退一格，停在原來的位置點
        //不用 UDsi--; 前面已經減過了，不重複！
        NSLog(@"(Down右.倒退一格.後.印出前)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
        NSString *imageName06 = @"rectangle01";
        switch (UpDownLMA3[UDi][3]/1000) {                               //(case:)用來切換(跳行)印出
            case 1: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*UDk, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
            case 2: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*UDk, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
            case 3: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*UDk, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
            case 4: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*UDk, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
            default:hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*UDk, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        }    //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
        hlSQ[UDj].image = [UIImage imageNamed:imageName06];
        hlSQ[UDj].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hlSQ[UDj]];
//        UDi++ ;//退回原來的位置點
    }
    NSLog(@"(Down右.結束.後)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
    //--------(印.右計分數字長框)------------------


}//(IBAction)btCheckDown:



- (IBAction)btCheckUp:(id)sender {
    if( UpDownLMA3[0][3]+UpDownLMA3[1][3]+UpDownLMA3[2][3]+UpDownLMA3[3][3] == 0 ){
        NSLog(@"ABC:請先按計分！");
        UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"請先寫，再按計分！" message:
                                  [NSString stringWithFormat:@"再按上一點 或 下一點"] delegate:self cancelButtonTitle:@"OK" otherButtonTitles:nil];
        [alertView show];
        return;        //直接結束 btCheckUp
    }

    //--------(印.左字母圖方框)------------------
    NSLog(@"(Up開始清除.前)(UDi=%d,UDsi=%d,UDj=%d)",UDi,UDsi,UDj);
    [hivUD[0] removeFromSuperview]; //清除上次顯示的方框(Down轉Up)
    [hivSQ[0] removeFromSuperview]; //清除上次顯示的方框.標準點(Down轉Up)
    [hivUD[UDi] removeFromSuperview]; //刪除上次顯示的方框
    [hivSQ[UDsi] removeFromSuperview]; //清除上次顯示的方框.標準點
    [hlSQ[UDj] removeFromSuperview];  //刪除上次顯示的長框
    
    UDi-- ;
    UDj-- ;
    UDk-- ;
    UDsi-- ;
//    NSLog(@"印方框11,(UDi=%d)(UDj=%d)(UDk=%d)",UDi,UDj,UDk);

    if( UDi >=0 ){
        NSLog(@"(Up.while前 UDi=%d)(UpDownLMA3[UDi][3]=%d)",UDi,UpDownLMA3[UDi][3]);
        while(UpDownLMA3[UDi][3]%1000==0){ UDi--; } //跳過非計分點,只挑(0,1,2~最後筆/例8)跳過()
        NSLog(@"(Up.while後 UDi=%d)",UDi);
//            NSLog(@"(UDi=%d)(UpDownLMA3[UDi][3]=%d)(UpDownLMA3[UDi][3]%1000=%d)",UDi,UpDownLMA3[UDi][3],UpDownLMA3[UDi][3]%1000);
//            NSLog(@"印方框12,(UDi=%d)(UDi=%d)(%d,%d,%d,%d)(UDi+1=%d)(UpDownLMA3[UDi+1][3]=%d)",UDi,UDi,UpDownLMA3[UDi][0],UpDownLMA3[UDi][1],UpDownLMA3[UDi][2],UpDownLMA3[UDi][3],(UDi+1),(UpDownLMA3[UDi+1][3]));
            NSString *imageName05 = @"square04";
            hivUD[UDi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UpDownLMA3[UDi][0],2+10*UpDownLMA3[UDi][1],30,30)];
            hivUD[UDi].image = [UIImage imageNamed:imageName05];
            //        }
            hivUD[UDi].contentMode = UIViewContentModeScaleAspectFit;
            [self.view addSubview:hivUD[UDi]];
        
        //---(印.左字母圖方框.標準點)-------------
        NSLog(@"Up雙框if(UDi=%d)(UDsi=%d)(%d,%d)",UDi,UDsi,170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1]);
        hivSQ[UDsi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1],30,30)];
        hivSQ[UDsi].image = [UIImage imageNamed:imageName05];
        //        }
        hivSQ[UDsi].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hivSQ[UDsi]];
        //---(印.左字母圖方框.標準點)-------------
        
    }else{
        NSLog(@"Up-else.未減１(UDi=%d)",UDi);
        UDi=0;//倒退一格，停在原來的位置點
        NSLog(@"Up-else.減１後(UDi=%d)",UDi);
//        NSLog(@"(UDi=%d)(UpDownLMA3[UDi][3]=%d)(UpDownLMA3[UDi][3]%1000=%d)",UDi,UpDownLMA3[UDi][3],UpDownLMA3[UDi][3]%1000);
//        NSLog(@"印方框13,(UDi=%d)(UDi=%d)(%d,%d,%d,%d)(UDi+1=%d)(UpDownLMA3[UDi+1][3]=%d)",UDi,UDi,UpDownLMA3[UDi][0],UpDownLMA3[UDi][1],UpDownLMA3[UDi][2],UpDownLMA3[UDi][3],(UDi+1),(UpDownLMA3[UDi+1][3]));
        NSString *imageName05 = @"square01";
        NSLog(@"Up雙框else(UDi=%d)(UDsi=%d)(%d,%d)",UDi,UDsi,170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1]);
        hivUD[UDi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UpDownLMA3[UDi][0],2+10*UpDownLMA3[UDi][1],30,30)];
        hivUD[UDi].image = [UIImage imageNamed:imageName05];
        //        }
        hivUD[UDi].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hivUD[UDi]];
        UDi=-1;//倒退一格，停在原來的位置點
        NSLog(@"Up(UDi=%d)(UDsi=%d)",UDi,UDsi);
        
        //---(印.左字母圖方框.標準點)-------------

        UDsi=0;//倒退一格，停在原來的位置點
        hivSQ[UDsi] = [[UIImageView alloc] initWithFrame:CGRectMake(170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1],30,30)];
        hivSQ[UDsi].image = [UIImage imageNamed:imageName05];
        //        }
        hivSQ[UDsi].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hivSQ[UDsi]];
        NSLog(@"Up雙框else(UDi=%d)(UDsi=%d)(UDj=%d)(%d,%d)",UDi,UDsi,UDj,170+10*UDLMAs[UDsi][0],2+10*UDLMAs[UDsi][1]);
        UDsi=-1;//倒退一格，停在原來的位置點
        NSLog(@"Up減１後(UDsi=%d)",UDsi);
        //---(印.左字母圖方框.標準點)-------------
    }
    //--------(印.左字母圖方框)------------------
    
    //--------(印.右計分數字長框)------------------
    NSLog(@"(Up印長框.前)(UDi=%d,UDsi=%d,UDj=%d,UDk=%d)(UpDownLMA3[UDi][3]=%d)",UDi,UDsi,UDj,UDk,UpDownLMA3[UDi][3]);
    if( UDi >=0 ){
        if( UpDownLMA3[UDi+1][3]%1000-1==0 ){
            UDk=UpDownLMA3[UDi][3]%1000-1;
        }//(UDk)點用來位移(下一個字)
        //說明2001上移到1005.if(2-1==0)將5-1放入UDk中
        NSLog(@"(Up印長框.前2)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);

        NSString *imageName06 = @"rectangle04";
        switch (UpDownLMA3[UDi][3]/1000) {                               //(case:)用來切換(跳行)印出
            case 1: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*UDk, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
            case 2: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*UDk, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
            case 3: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*UDk, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
            case 4: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*UDk, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
            default:hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*UDk, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        }    //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
        hlSQ[UDj].image = [UIImage imageNamed:imageName06];
        hlSQ[UDj].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hlSQ[UDj]];
    }else{
        NSLog(@"(Up右.倒退一格.前)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
        UDi=0 ;//倒退一格，停在原來的位置點
        UDj=0 ;//倒退一格，停在原來的位置點
        UDk=0 ;//倒退一格，停在原來的位置點
        NSLog(@"(Up右.倒退一格.後.印出前)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
        NSString *imageName06 = @"rectangle01";
        switch (UpDownLMA3[UDi][3]/1000) {                               //(case:)用來切換(跳行)印出
            case 1: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(545, 35+38*UDk, 60, 88)]; break;//分數紅.黑字(第1排/筆)//原始
            case 2: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(605, 35+38*UDk, 60, 88)]; break;//分數紅.藍字(第2排/筆)//原始
            case 3: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(665, 35+38*UDk, 60, 88)]; break;//分數紅.綠字(第3排/筆)//原始
            case 4: hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(725, 35+38*UDk, 60, 88)]; break;//分數紅.橘字(第4排/筆)//原始
            default:hlSQ[UDj] = [[UIImageView alloc] initWithFrame:CGRectMake(785, 35+38*UDk, 60, 88)]; break;//分數紅.紅字(第5排/筆)//0613 peter
        }    //hl[i]在前面已經放入待印的計分點(負.分數)，查前面(minusLMA3)
        hlSQ[UDj].image = [UIImage imageNamed:imageName06];
        hlSQ[UDj].contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:hlSQ[UDj]];
        UDi=-1 ;//退回原來的位置點
    }
    NSLog(@"(Up右.結束.後)(UDi=%d,UDsi=%d,UDj=%d)(UDk=%d)",UDi,UDsi,UDj,UDk);
    //--------(印.右計分數字長框)------------------
    
}//(IBAction)btCheckUp:




- (void)viewDidLoad {
    [super viewDidLoad];
    self.btSimOKhasExecuted = NO; // 偵測btSimOK不重複執行//peter0617
//    hL.text=[NSString stringWithFormat:@"%d", AverageScore];  //0613 peter
//    hL.textColor = [UIColor colorWithRed:0.9 green:0.0 blue:0.0 alpha:0.0];
//    hL = [[UILabel alloc] initWithFrame:CGRectMake(850, 360, 200, 100)]; //分數紅/咖啡字.(紅字 /上)
//    hL.font=[UIFont systemFontOfSize:70];
//    hR.text=[NSString stringWithFormat:@"%d", badScore];
//    hR.textColor=[UIColor colorWithRed:0.5 green:0.6 blue:0.8 alpha:0.0];
//    hR = [[UILabel alloc] initWithFrame:CGRectMake(850, 500, 150, 100)]; //分數紅/咖啡字.(咖啡字/下)
//    hR.font=[UIFont systemFontOfSize:70];
//    [self.view addSubview:hL];
//    [self.view addSubview:hR];                               //0613 peter

//有搬移(danny.做圖1)
    //===============================================================================
    //----(紫色.正字底圖)-----------------------------
        // 创建 UIImageView
    //    UIImageView *backgroundImageView = [[UIImageView alloc] initWithFrame:self.view.bounds];//穎作圖(2.)
    //    UIImage *backgroundImage = [UIImage imageNamed:@"WRWallpaper"]; // 替换为你的图像文件名      //穎作圖(3.)
    //    backgroundImageView.image = backgroundImage;                  //  代入名稱               //穎作圖(4.)
    //          // 设置UIImageView的contentMode，确保图像适应屏幕
    //    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;                    //穎作圖(5.)必寫
    //    [self.view addSubview:backgroundImageView];// 将 UIImageView 添加到视图中                 //穎作圖(6.)必寫
    //----(紫色.正字底圖)-----------------------------

    //----(字母底圖alphabet白底)-----------------------------
        
        self.imageView = [[UIImageView alloc] initWithFrame:CGRectMake(180,12,250,750)];
        self.imageView.image = [UIImage imageNamed:@"Wa"];                            //穎作圖(4.)
                // 可以设置 UIImageView 的其他属性，比如 contentMode，来调整图像的显示方式
        self.imageView.contentMode = UIViewContentModeScaleAspectFit;                         //穎作圖(5.)必寫
        [self.view addSubview:self.imageView];// 将 UIImageView 添加到视图中                     //穎作圖(6.)必寫

    //----(字母底圖alphabet)--------------------------------
    //===============================================================================
//有搬移(danny.做圖1)
    
    UDi=-1; //清零 peter 0628
    UDj=-1;  //btCheckUp.Down的計數參數.清零
    UDk=-1;  //btCheckUp.Down的計數參數.清零
    UDsi=-1;  //btCheckUp.Down標準點的計數參數.清零
    for(int i=0;i<400;i++) for(int j=0;j<4;j++) UpDownLMA3[i][j]=0;//清零
    for(int i=0;i<20;i++) for(int j=0;j<4;j++) UDLMAs[i][j]=0;//清零
    
    wordinx=0;
    Wb0 =[[UIImageView alloc] initWithFrame:CGRectMake(500,250,25,75)];
    Wb0.image=[UIImage imageNamed:@"a1.png"];
    Wb0.alpha=1.0f;
    [self.view addSubview:Wb0];
    //Wb0.hidden=NO;
    Ib0 =[[UIImageView alloc] initWithFrame:CGRectMake(500,350,25,75)];
    Ib0.image=[UIImage imageNamed:@"bk.png"];
    Ib0.alpha=0.9f;
    [self.view addSubview:Ib0];
    //Ib0.hidden=NO;
    dWb0 =[[SignatureDrawView alloc] initWithFrame:CGRectMake(500,350,25,75)]; //dWb0 在這裡宣告成 SignatureDrawView 裡面的東西
    [self.view addSubview:dWb0];
    //dWb0.hidden=NO;
    
    [dWb0 erase];
   // dWb0.hidden=NO;
    dWb0.alpha=0.6;
    
    
    //wordsel=0;
    /*
    onshow=0;
    I00 =[[UIImageView alloc] initWithFrame:CGRectMake(450,250,100,300)];
    I00.image=[UIImage imageNamed:@"ispeak_a1_01.png"];
    [self.view addSubview:I00];
    
    W01 =[[UIImageView alloc] initWithFrame:CGRectMake(450,250,100,300)];
    W01.image=[UIImage imageNamed:@"a1.png"];
    W01.alpha=0.0f;
    [self.view addSubview:W01];
    W01.hidden=NO;
    I01 =[[UIImageView alloc] initWithFrame:CGRectMake(450,250,100,300)];
    I01.image=[UIImage imageNamed:@"bk.png"];
    I01.alpha=0.5f;
    [self.view addSubview:I01];
    I01.hidden=NO;
    dW01 =[[SignatureDrawView4H alloc] initWithFrame:CGRectMake(450,250,100,300)];
    [self.view addSubview:dW01];
    dW01.hidden=NO;
    dW01.alpha=0.3f;
     */
  //  cTimer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(timesUp:) userInfo:nil repeats:YES];
    
    lasT.alpha=0.0f;
    lasTitle.alpha=0.0f;
    pass=0;
    PCnt=0;
    RCnt=0;
    btS01.alpha=0.0f;
    btS02.alpha=0.0f;
    btS03.alpha=0.0f;
    // Do any additional setup after loading the view.
    /*
    MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    ServerIP = [defaults objectForKey:@"ServerIP"];
    ID = [defaults objectForKey:@"ID"];
     transport.host = ServerIP;
     transport.port = 1883;
     
     session = [[MQTTSession alloc] init];
     session.transport = transport;
     session.delegate=self;
     [session connectWithConnectHandler:^(NSError *error) {
         // Do some work
         
         [session publishData:[[[NSString alloc] initWithFormat:(@"$uten_class01,CLASSSTART,%@,end~"),ID] dataUsingEncoding:NSUTF8StringEncoding] onTopic:@"r_uten_class01" retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
         }];
         
         NSLog(@"Subscription uten_class01");
         [session subscribeToTopic:@"uten_class01" atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
             if (error) {
                 NSLog(@"Subscription failed %@", error.localizedDescription);
             } else {
                 NSLog(@"Subscription sucessfull! Granted Qos: %@", gQoss);
             }
         }];

     }];
     */
    
}


-(void)timesUp:(NSTimer *)timer{
    /*
    NSString *png[26][13]={{
        @"ispeak_a1_01.png",@"ispeak_a1_02.png",@"ispeak_a1_03.png",@"ispeak_a1_04.png",
        @"ispeak_a1_05.png",@"ispeak_a1_06.png",@"ispeak_a1_07.png",@"ispeak_a1_08.png",
        @"ispeak_a1_09.png",@"ispeak_a1_10.png",@"ispeak_a1_11.png",@"ispeak_a1_12.png",
    },{
        @"ispeak_b1_01.png",@"ispeak_b1_02.png",@"ispeak_b1_03.png",@"ispeak_b1_04.png",
        @"ispeak_b1_05.png",@"ispeak_b1_06.png",@"ispeak_b1_07.png",@"ispeak_b1_08.png",
        @"ispeak_b1_09.png",@"ispeak_b1_10.png",@"ispeak_b1_10.png",@"ispeak_b1_10.png",
    },{
        @"ispeak_c1_01.png",@"ispeak_c1_02.png",@"ispeak_c1_03.png",@"ispeak_c1_04.png",
        @"ispeak_c1_05.png",@"ispeak_c1_06.png",@"ispeak_c1_07.png",@"ispeak_c1_08.png",
        @"ispeak_c1_09.png",@"ispeak_c1_09.png",@"ispeak_c1_09.png",@"ispeak_c1_09.png",
    },{
        @"ispeak_d1_01.png",@"ispeak_d1_02.png",@"ispeak_d1_03.png",@"ispeak_d1_04.png",
        @"ispeak_d1_05.png",@"ispeak_d1_06.png",@"ispeak_d1_07.png",@"ispeak_d1_08.png",
        @"ispeak_d1_09.png",@"ispeak_d1_10.png",@"ispeak_d1_10.png",@"ispeak_d1_10.png",
    },{
        @"ispeak_e1_01.png",@"ispeak_e1_02.png",@"ispeak_e1_03.png",@"ispeak_e1_04.png",
        @"ispeak_e1_05.png",@"ispeak_e1_06.png",@"ispeak_e1_07.png",@"ispeak_e1_08.png",
        @"ispeak_e1_09.png",@"ispeak_e1_09.png",@"ispeak_e1_09.png",@"ispeak_e1_09.png",
    },{
        @"ispeak_f1_01.png",@"ispeak_f1_02.png",@"ispeak_f1_03.png",@"ispeak_f1_04.png",
        @"ispeak_f1_05.png",@"ispeak_f1_06.png",@"ispeak_f1_07.png",@"ispeak_f1_08.png",
        @"ispeak_f1_09.png",@"ispeak_f1_10.png",@"ispeak_f1_11.png",@"ispeak_f1_12.png",
    },{
        @"ispeak_g1_01.png",@"ispeak_g1_02.png",@"ispeak_g1_03.png",@"ispeak_g1_04.png",
        @"ispeak_g1_05.png",@"ispeak_g1_06.png",@"ispeak_g1_07.png",@"ispeak_g1_08.png",
        @"ispeak_g1_09.png",@"ispeak_g1_10.png",@"ispeak_g1_10.png",@"ispeak_g1_10.png",
    },{
        @"ispeak_h1_01.png",@"ispeak_h1_02.png",@"ispeak_h1_03.png",@"ispeak_h1_04.png",
        @"ispeak_h1_05.png",@"ispeak_h1_06.png",@"ispeak_h1_07.png",@"ispeak_h1_08.png",
        @"ispeak_h1_09.png",@"ispeak_h1_09.png",@"ispeak_h1_09.png",@"ispeak_h1_09.png",
    },{
        @"ispeak_i1_01.png",@"ispeak_i1_02.png",@"ispeak_i1_03.png",@"ispeak_i1_04.png",
        @"ispeak_i1_05.png",@"ispeak_i1_06.png",@"ispeak_i1_07.png",@"ispeak_i1_08.png",
        @"ispeak_i1_08.png",@"ispeak_i1_08.png",@"ispeak_i1_08.png",@"ispeak_i1_08.png",
    },{
        @"ispeak_j1_01.png",@"ispeak_j1_02.png",@"ispeak_j1_03.png",@"ispeak_j1_04.png",
        @"ispeak_j1_05.png",@"ispeak_j1_06.png",@"ispeak_j1_07.png",@"ispeak_j1_08.png",
        @"ispeak_j1_09.png",@"ispeak_j1_09.png",@"ispeak_j1_09.png",@"ispeak_j1_09.png",
    },{
        @"ispeak_k1_01.png",@"ispeak_k1_02.png",@"ispeak_k1_03.png",@"ispeak_k1_04.png",
        @"ispeak_k1_05.png",@"ispeak_k1_06.png",@"ispeak_k1_07.png",@"ispeak_k1_08.png",
        @"ispeak_k1_09.png",@"ispeak_k1_10.png",@"ispeak_k1_11.png",@"ispeak_k1_11.png",
    },{
        @"ispeak_l1_01.png",@"ispeak_l1_02.png",@"ispeak_l1_03.png",@"ispeak_l1_04.png",
        @"ispeak_l1_05.png",@"ispeak_l1_06.png",@"ispeak_l1_06.png",@"ispeak_l1_06.png",
        @"ispeak_l1_06.png",@"ispeak_l1_06.png",@"ispeak_l1_06.png",@"ispeak_l1_06.png",
    },{
        @"ispeak_m1_01.png",@"ispeak_m1_02.png",@"ispeak_m1_03.png",@"ispeak_m1_04.png",
        @"ispeak_m1_05.png",@"ispeak_m1_06.png",@"ispeak_m1_07.png",@"ispeak_m1_08.png",
        @"ispeak_m1_09.png",@"ispeak_m1_09.png",@"ispeak_m1_09.png",@"ispeak_m1_09.png",
    },{
        @"ispeak_n1_01.png",@"ispeak_n1_02.png",@"ispeak_n1_03.png",@"ispeak_n1_04.png",
        @"ispeak_n1_05.png",@"ispeak_n1_06.png",@"ispeak_n1_07.png",@"ispeak_n1_08.png",
        @"ispeak_n1_08.png",@"ispeak_n1_08.png",@"ispeak_n1_08.png",@"ispeak_n1_08.png",
    },{
        @"ispeak_o1_01.png",@"ispeak_o1_02.png",@"ispeak_o1_03.png",@"ispeak_o1_04.png",
        @"ispeak_o1_05.png",@"ispeak_o1_06.png",@"ispeak_o1_07.png",@"ispeak_o1_08.png",
        @"ispeak_o1_08.png",@"ispeak_o1_08.png",@"ispeak_o1_08.png",@"ispeak_o1_08.png",
    },{
        @"ispeak_p1_01.png",@"ispeak_p1_02.png",@"ispeak_p1_03.png",@"ispeak_p1_04.png",
        @"ispeak_p1_05.png",@"ispeak_p1_06.png",@"ispeak_p1_07.png",@"ispeak_p1_08.png",
        @"ispeak_p1_09.png",@"ispeak_p1_10.png",@"ispeak_p1_11.png",@"ispeak_p1_12.png",
        @"ispeak_p1_13.png"
    },{
        @"ispeak_q1_01.png",@"ispeak_q1_02.png",@"ispeak_q1_03.png",@"ispeak_q1_04.png",
        @"ispeak_q1_05.png",@"ispeak_q1_06.png",@"ispeak_q1_07.png",@"ispeak_q1_08.png",
        @"ispeak_q1_09.png",@"ispeak_q1_10.png",@"ispeak_q1_11.png",@"ispeak_q1_12.png",
        @"ispeak_q1_13.png"
    },{
        @"ispeak_r1_01.png",@"ispeak_r1_02.png",@"ispeak_r1_03.png",@"ispeak_r1_04.png",
        @"ispeak_r1_05.png",@"ispeak_r1_06.png",@"ispeak_r1_07.png",@"ispeak_r1_07.png",
        @"ispeak_r1_07.png",@"ispeak_r1_07.png",@"ispeak_r1_07.png",@"ispeak_r1_07.png",
    },{
        @"ispeak_s1_01.png",@"ispeak_s1_02.png",@"ispeak_s1_03.png",@"ispeak_s1_04.png",
        @"ispeak_s1_05.png",@"ispeak_s1_06.png",@"ispeak_s1_07.png",@"ispeak_s1_08.png",
        @"ispeak_s1_08.png",@"ispeak_s1_08.png",@"ispeak_s1_08.png",@"ispeak_s1_08.png",
    },{
        @"ispeak_t1_01.png",@"ispeak_t1_02.png",@"ispeak_t1_03.png",@"ispeak_t1_04.png",
        @"ispeak_t1_05.png",@"ispeak_t1_06.png",@"ispeak_t1_07.png",@"ispeak_t1_08.png",
        @"ispeak_t1_09.png",@"ispeak_t1_10.png",@"ispeak_t1_10.png",@"ispeak_t1_10.png",
    },{
        @"ispeak_u1_01.png",@"ispeak_u1_02.png",@"ispeak_u1_03.png",@"ispeak_u1_04.png",
        @"ispeak_u1_05.png",@"ispeak_u1_06.png",@"ispeak_u1_07.png",@"ispeak_u1_07.png",
        @"ispeak_u1_07.png",@"ispeak_u1_07.png",@"ispeak_u1_07.png",@"ispeak_u1_07.png",
    },{
        @"ispeak_v1_01.png",@"ispeak_v1_02.png",@"ispeak_v1_03.png",@"ispeak_v1_04.png",
        @"ispeak_v1_05.png",@"ispeak_v1_06.png",@"ispeak_v1_06.png",@"ispeak_v1_06.png",
        @"ispeak_v1_06.png",@"ispeak_v1_06.png",@"ispeak_v1_06.png",@"ispeak_v1_06.png",
    },{
        @"ispeak_w1_01.png",@"ispeak_w1_02.png",@"ispeak_w1_03.png",@"ispeak_w1_04.png",
        @"ispeak_w1_05.png",@"ispeak_w1_06.png",@"ispeak_w1_07.png",@"ispeak_w1_08.png",
        @"ispeak_w1_08.png",@"ispeak_w1_08.png",@"ispeak_w1_08.png",@"ispeak_w1_08.png",
    },{
        @"ispeak_x1_01.png",@"ispeak_x1_02.png",@"ispeak_x1_03.png",@"ispeak_x1_04.png",
        @"ispeak_x1_05.png",@"ispeak_x1_06.png",@"ispeak_x1_07.png",@"ispeak_x1_08.png",
        @"ispeak_x1_09.png",@"ispeak_x1_10.png",@"ispeak_x1_10.png",@"ispeak_x1_10.png",
    },{
        @"ispeak_y1_01.png",@"ispeak_y1_02.png",@"ispeak_y1_03.png",@"ispeak_y1_04.png",
        @"ispeak_y1_05.png",@"ispeak_y1_06.png",@"ispeak_y1_07.png",@"ispeak_y1_08.png",
        @"ispeak_y1_09.png",@"ispeak_y1_10.png",@"ispeak_y1_10.png",@"ispeak_y1_10.png",
    },{
        @"ispeak_z1_01.png",@"ispeak_z1_02.png",@"ispeak_z1_03.png",@"ispeak_z1_04.png",
        @"ispeak_z1_05.png",@"ispeak_z1_06.png",@"ispeak_z1_07.png",@"ispeak_z1_07.png",
        @"ispeak_z1_07.png",@"ispeak_z1_07.png",@"ispeak_z1_07.png",@"ispeak_z1_07.png",
    }};
    NSString *wordpng[26]={@"a1",@"b1.png",@"c1.png",@"d1.png",@"e1.png",@"f1.png",
        @"g1.png",@"h1.png",@"i1.png",@"j1.png",@"k1.png",@"l1.png",
        @"m1.png",@"n1.png",@"o1.png",@"p1.png",@"q1.png",@"r1.png",
        @"s1.png",@"t1.png",@"u1.png",@"v1.png",@"w1.png",@"x1.png",
        @"y1.png",@"z1.png"
    };
     */
    NSString *png[26][14]={{
        @"ispeak_a1_01",@"ispeak_a1_02",@"ispeak_a1_03",@"ispeak_a1_04",
        @"ispeak_a1_05",@"ispeak_a1_06",@"ispeak_a1_07",@"ispeak_a1_08",
        @"ispeak_a1_09",@"ispeak_a1_10",@"ispeak_a1_11",@"ispeak_a1_12",
        @"ispeak_a1_13",@"ispeak_a1_14",
    },{
        @"ispeak_b1_01",@"ispeak_b1_02",@"ispeak_b1_03",@"ispeak_b1_04",
        @"ispeak_b1_05",@"ispeak_b1_06",@"ispeak_b1_07",@"ispeak_b1_08",
        @"ispeak_b1_09",@"ispeak_b1_10",@"ispeak_b1_10",@"ispeak_b1_10",
        @"ispeak_b1_13",@"ispeak_b1_14",
    },{
        @"ispeak_c1_01",@"ispeak_c1_02",@"ispeak_c1_03",@"ispeak_c1_04",
        @"ispeak_c1_05",@"ispeak_c1_06",@"ispeak_c1_07",@"ispeak_c1_08",
        @"ispeak_c1_09",@"ispeak_c1_10",@"ispeak_c1_11",@"ispeak_c1_12",
        @"ispeak_c1_13",@"ispeak_c1_14",
    },{
        @"ispeak_d1_01",@"ispeak_d1_02",@"ispeak_d1_03",@"ispeak_d1_04",
        @"ispeak_d1_05",@"ispeak_d1_06",@"ispeak_d1_07",@"ispeak_d1_08",
        @"ispeak_d1_09",@"ispeak_d1_10",@"ispeak_d1_11",@"ispeak_d1_12",
        @"ispeak_d1_13",@"ispeak_d1_14",
    },{
        @"ispeak_e1_01",@"ispeak_e1_02",@"ispeak_e1_03",@"ispeak_e1_04",
        @"ispeak_e1_05",@"ispeak_e1_06",@"ispeak_e1_07",@"ispeak_e1_08",
        @"ispeak_e1_09",@"ispeak_e1_10",@"ispeak_e1_11",@"ispeak_e1_12",
        @"ispeak_e1_13",@"ispeak_e1_14",
    },{
        @"ispeak_f1_01",@"ispeak_f1_02",@"ispeak_f1_03",@"ispeak_f1_04",
        @"ispeak_f1_05",@"ispeak_f1_06",@"ispeak_f1_07",@"ispeak_f1_08",
        @"ispeak_f1_09",@"ispeak_f1_10",@"ispeak_f1_11",@"ispeak_f1_12",
        @"ispeak_f1_13",@"ispeak_f1_14",
    },{
        @"ispeak_g1_01",@"ispeak_g1_02",@"ispeak_g1_03",@"ispeak_g1_04",
        @"ispeak_g1_05",@"ispeak_g1_06",@"ispeak_g1_07",@"ispeak_g1_08",
        @"ispeak_g1_09",@"ispeak_g1_10",@"ispeak_g1_11",@"ispeak_g1_12",
        @"ispeak_g1_13",@"ispeak_g1_14",
    },{
        @"ispeak_h1_01",@"ispeak_h1_02",@"ispeak_h1_03",@"ispeak_h1_04",
        @"ispeak_h1_05",@"ispeak_h1_06",@"ispeak_h1_07",@"ispeak_h1_08",
        @"ispeak_h1_09",@"ispeak_h1_10",@"ispeak_h1_11",@"ispeak_h1_12",
        @"ispeak_h1_13",@"ispeak_h1_14",
    },{
        @"ispeak_i1_01",@"ispeak_i1_02",@"ispeak_i1_03",@"ispeak_i1_04",
        @"ispeak_i1_05",@"ispeak_i1_06",@"ispeak_i1_07",@"ispeak_i1_08",
        @"ispeak_i1_09",@"ispeak_i1_10",@"ispeak_i1_11",@"ispeak_i1_12",
        @"ispeak_i1_13",@"ispeak_i1_14",
    },{
        @"ispeak_j1_01",@"ispeak_j1_02",@"ispeak_j1_03",@"ispeak_j1_04",
        @"ispeak_j1_05",@"ispeak_j1_06",@"ispeak_j1_07",@"ispeak_j1_08",
        @"ispeak_j1_09",@"ispeak_j1_10",@"ispeak_j1_11",@"ispeak_j1_12",
        @"ispeak_j1_13",@"ispeak_j1_14",
    },{
        @"ispeak_k1_01",@"ispeak_k1_02",@"ispeak_k1_03",@"ispeak_k1_04",
        @"ispeak_k1_05",@"ispeak_k1_06",@"ispeak_k1_07",@"ispeak_k1_08",
        @"ispeak_k1_09",@"ispeak_k1_10",@"ispeak_k1_11",@"ispeak_k1_12",
        @"ispeak_k1_13",@"ispeak_k1_14",
    },{
        @"ispeak_l1_01",@"ispeak_l1_02",@"ispeak_l1_03",@"ispeak_l1_04",
        @"ispeak_l1_05",@"ispeak_l1_06",@"ispeak_l1_07",@"ispeak_l1_08",
        @"ispeak_l1_09",@"ispeak_l1_10",@"ispeak_l1_11",@"ispeak_l1_12",
        @"ispeak_l1_13",@"ispeak_l1_14",
    },{
        @"ispeak_m1_01",@"ispeak_m1_02",@"ispeak_m1_03",@"ispeak_m1_04",
        @"ispeak_m1_05",@"ispeak_m1_06",@"ispeak_m1_07",@"ispeak_m1_08",
        @"ispeak_m1_09",@"ispeak_m1_10",@"ispeak_m1_11",@"ispeak_m1_12",
        @"ispeak_m1_13",@"ispeak_m1_14",
    },{
        @"ispeak_n1_01",@"ispeak_n1_02",@"ispeak_n1_03",@"ispeak_n1_04",
        @"ispeak_n1_05",@"ispeak_n1_06",@"ispeak_n1_07",@"ispeak_n1_08",
        @"ispeak_n1_09",@"ispeak_n1_10",@"ispeak_n1_11",@"ispeak_n1_12",
        @"ispeak_n1_13",@"ispeak_n1_14",
    },{
        @"ispeak_o1_01",@"ispeak_o1_02",@"ispeak_o1_03",@"ispeak_o1_04",
        @"ispeak_o1_05",@"ispeak_o1_06",@"ispeak_o1_07",@"ispeak_o1_08",
        @"ispeak_o1_09",@"ispeak_o1_10",@"ispeak_o1_11",@"ispeak_o1_12",
        @"ispeak_o1_13",@"ispeak_o1_14",
    },{
        @"ispeak_p1_01",@"ispeak_p1_02",@"ispeak_p1_03",@"ispeak_p1_04",
        @"ispeak_p1_05",@"ispeak_p1_06",@"ispeak_p1_07",@"ispeak_p1_08",
        @"ispeak_p1_09",@"ispeak_p1_10",@"ispeak_p1_11",@"ispeak_p1_12",
        @"ispeak_p1_13",@"ispeak_p1_14",
    },{
        @"ispeak_q1_01",@"ispeak_q1_02",@"ispeak_q1_03",@"ispeak_q1_04",
        @"ispeak_q1_05",@"ispeak_q1_06",@"ispeak_q1_07",@"ispeak_q1_08",
        @"ispeak_q1_09",@"ispeak_q1_10",@"ispeak_q1_11",@"ispeak_q1_12",
        @"ispeak_q1_13",@"ispeak_q1_14",
    },{
        @"ispeak_r1_01",@"ispeak_r1_02",@"ispeak_r1_03",@"ispeak_r1_04",
        @"ispeak_r1_05",@"ispeak_r1_06",@"ispeak_r1_07",@"ispeak_r1_08",
        @"ispeak_r1_09",@"ispeak_r1_10",@"ispeak_r1_11",@"ispeak_r1_12",
        @"ispeak_r1_13",@"ispeak_r1_14",
    },{
        @"ispeak_s1_01",@"ispeak_s1_02",@"ispeak_s1_03",@"ispeak_s1_04",
        @"ispeak_s1_05",@"ispeak_s1_06",@"ispeak_s1_07",@"ispeak_s1_08",
        @"ispeak_s1_09",@"ispeak_s1_10",@"ispeak_s1_11",@"ispeak_s1_12",
        @"ispeak_s1_13",@"ispeak_s1_14",
    },{
        @"ispeak_t1_01",@"ispeak_t1_02",@"ispeak_t1_03",@"ispeak_t1_04",
        @"ispeak_t1_05",@"ispeak_t1_06",@"ispeak_t1_07",@"ispeak_t1_08",
        @"ispeak_t1_09",@"ispeak_t1_10",@"ispeak_t1_11",@"ispeak_t1_12",
        @"ispeak_t1_13",@"ispeak_t1_14",
    },{
        @"ispeak_u1_01",@"ispeak_u1_02",@"ispeak_u1_03",@"ispeak_u1_04",
        @"ispeak_u1_05",@"ispeak_u1_06",@"ispeak_u1_07",@"ispeak_u1_08",
        @"ispeak_u1_09",@"ispeak_u1_10",@"ispeak_u1_11",@"ispeak_u1_12",
        @"ispeak_u1_13",@"ispeak_u1_14",
    },{
        @"ispeak_v1_01",@"ispeak_v1_02",@"ispeak_v1_03",@"ispeak_v1_04",
        @"ispeak_v1_05",@"ispeak_v1_06",@"ispeak_v1_07",@"ispeak_v1_08",
        @"ispeak_v1_09",@"ispeak_v1_10",@"ispeak_v1_11",@"ispeak_v1_12",
        @"ispeak_v1_13",@"ispeak_v1_14",
    },{
        @"ispeak_w1_01",@"ispeak_w1_02",@"ispeak_w1_03",@"ispeak_w1_04",
        @"ispeak_w1_05",@"ispeak_w1_06",@"ispeak_w1_07",@"ispeak_w1_08",
        @"ispeak_w1_09",@"ispeak_w1_10",@"ispeak_w1_11",@"ispeak_w1_12",
        @"ispeak_w1_13",@"ispeak_w1_14",
    },{
        @"ispeak_x1_01",@"ispeak_x1_02",@"ispeak_x1_03",@"ispeak_x1_04",
        @"ispeak_x1_05",@"ispeak_x1_06",@"ispeak_x1_07",@"ispeak_x1_08",
        @"ispeak_x1_09",@"ispeak_x1_10",@"ispeak_x1_11",@"ispeak_x1_12",
        @"ispeak_x1_13",@"ispeak_x1_14",
    },{
        @"ispeak_y1_01",@"ispeak_y1_02",@"ispeak_y1_03",@"ispeak_y1_04",
        @"ispeak_y1_05",@"ispeak_y1_06",@"ispeak_y1_07",@"ispeak_y1_08",
        @"ispeak_y1_09",@"ispeak_y1_10",@"ispeak_y1_11",@"ispeak_y1_12",
        @"ispeak_y1_13",@"ispeak_y1_14",
    },{
        @"ispeak_z1_01",@"ispeak_z1_02",@"ispeak_z1_03",@"ispeak_z1_04",
        @"ispeak_z1_05",@"ispeak_z1_06",@"ispeak_z1_07",@"ispeak_z1_08",
        @"ispeak_z1_09",@"ispeak_z1_10",@"ispeak_z1_11",@"ispeak_z1_12",
        @"ispeak_z1_13",@"ispeak_z1_14",
    }};
    NSString *wordpng[26]={@"a1",@"b1",@"c1",@"d1",@"e1",@"f1",
        @"g1",@"h1",@"i1",@"j1",@"k1",@"l1",
        @"m1",@"n1",@"o1",@"p1",@"q1",@"r1",
        @"s1",@"t1",@"u1",@"v1",@"w1",@"x1",
        @"y1",@"z1"
    };
    int max[26]={14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14};
    int isp[52]={1,1,//a
                 1,1,//b
                 1,1,//c
                 1,1,//d
                 1,1,//e
                 2,2,//f
                 1,1,//g
                 1,1,//h
                 2,2,//i
                 2,2,//j
                 2,2,//k
                 1,1,//l
                 1,1,//m
                 1,1,//n
                 1,1,//o
                 2,2,//p
                 2,2,//q
                 1,1,//r
                 1,1,//s
                 2,2,//t
                 1,1,//u
                 1,1,//v
                 1,1,//w
                 2,2,//x
                 2,2,//y
                 1,1 //z
    };
       
    NSError *error;
    NSData *data = [NSData dataWithContentsOfFile:[[NSBundle mainBundle] pathForResource: png[wordsel/2][timecnt] ofType:@"png"]
                                          options:NSDataReadingUncached error:&error];
    I00.image= [UIImage imageWithData:data]; timecnt++;
    if(onshow==1) {
        I00.alpha=1.0f;
        onshow=0;
    }
    //I00.image=[UIImage imageNamed:png[wordsel/2][timecnt]];
    NSLog(@"wordsel:%d-%d",wordsel,timecnt);
    if(timecnt==max[wordsel/2]) {
        timecnt--;
        if(wtimer1==0) {
            I00.alpha=0.0f;
            //W01.image=[UIImage imageNamed:wordpng[wordsel/2]];
            NSError *error;
            NSData *data = [NSData dataWithContentsOfFile:[[NSBundle mainBundle] pathForResource: wordpng[wordsel/2] ofType:@"png"] options:NSDataReadingUncached error:&error];
             
            W01.image = [UIImage imageWithData:data];
            if((wordsel%2)==0) W01.alpha=0.4f;
            else W01.alpha=0.05f;
            lasT.alpha=1.0f;
        }
        wtimer1++;
        if(((10-wtimer1)/2)>=0) {
            lasT.text=[NSString stringWithFormat: @"%d",(10-wtimer1)/2];
        } else lasT.text=@"回放中";
        if((wtimer1 > 10) & (wtimer1 <= 20)) {
            if(wtimer1==11) {
                gpencount=[dW01 isWord:W01.image pens:isp[wordsel]];
                [dW01 draw_clear];
                PCnt=[dW01 draw_getcount];
                RCnt=0;
            }
            for(int i=0;i<PCnt/10;i++) {
                [dW01 draw_replay:RCnt];
                RCnt++;
            }
        }
        if(wtimer1 > 20) {
            lasT.alpha=0.0f;
            if(wordsel <= 25*2) {
                int lcnt=gpencount; //[dW01 isWord:W01.image pens:isp[wordsel]];
                NSString *s;
                if(lcnt==-1) {
                    s=  [NSString stringWithFormat:@"落筆次數不正確-%d",isp[wordsel]];
                    btS01.alpha=0.0f;
                    btS02.alpha=0.0f;
                    btS03.alpha=0.0f;
                } else {
                    if(lcnt > 75) {
                        s=  [NSString stringWithFormat:@"寫的非常好請保持[%d%%]",lcnt];
                        btS01.alpha=1.0f;
                        btS02.alpha=1.0f;
                        btS03.alpha=1.0f;
                        pass+=2;
                    } else {
                        if(lcnt > 60) {
                            s=  [NSString stringWithFormat:@"還差一點,請再接再厲[%d%%]",lcnt];
                            btS01.alpha=1.0f;
                            btS02.alpha=1.0f;
                            btS03.alpha=1.0f;
                            pass++;
                        } else {
                            s=  [NSString stringWithFormat:@"寫的糟透了,請再多練習[%d%%]",lcnt];
                            btS01.alpha=1.0f;
                            btS02.alpha=1.0f;
                            btS03.alpha=1.0f;
                        }
                    }
                }
                laPass.text=[NSString stringWithFormat: @"%04d",pass];
                lasTitle.text=s;
                lasTitle.alpha=1.0f;
                [dW01 erase];
                wordsel++;
                
                W01.alpha=0.0f;
                timecnt=0;
                wtimer1=0;
                onshow=1;
            } else {
                
                [cTimer invalidate];
                cTimer = nil;
                UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:
                                          @"練習完成!!!" delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
                    [alertView show];
            }
        }
    }

}
- (void)alertView:(UIAlertView *)alertView didDismissWithButtonIndex:(NSInteger)buttonIndex
{
   // [self dismissViewControllerAnimated:NO completion:nil];
}

- (IBAction)btBack:(id)sender {
    [cTimer invalidate];
    cTimer = nil;
    UTENCommand *command = [[UTENCommand alloc] init];
    command.name = CmdClassStop;
    command.utenClass = @"uten_class01";
    command.loginID = ID;
    NSError *error;
    NSData *data = [command toData:&error];
    if (error) {
        NSLog(@"Error: %@", error.localizedDescription);
        return;
    }
    [session publishData:data onTopic:@"r_uten_class01" retain:NO qos:MQTTQosLevelAtMostOnce publishHandler:^(NSError *error) {
    }];
    [self dismissViewControllerAnimated:NO completion:nil];
}
- (IBAction)btNext:(id)sender {
    if(wordsel < 5) wordsel++;
    W01.alpha=0.0f;
    timecnt=0;
    wtimer1=0;
}
- (IBAction)btPrev:(id)sender {
    if(wordsel > 0) wordsel--;
    W01.alpha=0.0f;
    timecnt=0;
    wtimer1=0;
}
- (IBAction)btClear:(id)sender {
    [dW01 erase];
}
- (IBAction)btRun:(id)sender {
    NSString *s;
    int lcnt=[dW01 isWord:W01.image pens:1];
    if(lcnt > 75) {
        s=  [NSString stringWithFormat:@"寫的非常好請保持[%d%%]",lcnt];
    } else {
        if(lcnt > 60) {
            s=  [NSString stringWithFormat:@"還差一點,請再接再厲[%d%%]",lcnt];
        } else {
            s=  [NSString stringWithFormat:@"寫的糟透了,請再多練習[%d%%]",lcnt];
        }
    }
    UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:@"訊息提示" message:
                              [NSString stringWithFormat:s, lcnt] delegate:self cancelButtonTitle:@"關閉" otherButtonTitles:nil];
        [alertView show];
    [dW01 erase];
    
}
- (IBAction)btPlay:(id)sender {

}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end













