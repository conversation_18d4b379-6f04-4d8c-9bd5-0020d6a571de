# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane
OUTPUT_DIRECTORY = "./build/ios"
KEY_CONFIG_NAME = "config_name"
KEY_OUTPUT_NAME = "output_name"

default_platform(:ios)

platform :ios do
  desc "Description of what the lane does"
  lane :custom_lane do
    # add actions here: https://docs.fastlane.tools/actions
  end

  desc "Export ipa file for AdHoc"
  lane :export_adhoc do
    cocoapods
    EXPORT_METHOD = "ad-hoc"
    ENV[KEY_OUTPUT_NAME] = EXPORT_METHOD
    build_app(
      clean: true,
      # scheme: SCHEME,
      # workspace: WORKSPACE,
      include_bitcode: false,
      export_method: EXPORT_METHOD,
      configuration: "AnyRelease",
      output_directory: OUTPUT_DIRECTORY,
      output_name: "#{EXPORT_METHOD}.ipa",
      # export_options: {
      #   provisioningProfiles: { 
      #     "com.shangching.com.shangching.efshop" => "EFShop ADHOC",
      #     "com.shangching.com.shangching.efshop.efshopapp-service-extension" => "Ad Hoc-Service Extension",
      #     "com.shangching.com.shangching.efshop.efshopapp-content-extension" => "Ad Hoc Content-Extension"
      #   }
      # },
      # export_xcargs: "-allowProvisioningUpdates",
      # xcargs: "-allowProvisioningUpdates",
    )
  end
end
