name: uten
options:
  bundleIdPrefix: com.bekubee
  deploymentTarget:
    iOS: 12.1
  createIntermediateGroups: true
  usesTabs: false
  indentWidth: 2
  tabWidth: 2
  xcodeVersion: "13.0"

settings:
  base:
    ALWAYS_SEARCH_USER_PATHS: NO
    CLANG_ANALYZER_NONNULL: YES
    CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION: YES_AGGRESSIVE
    CLANG_CXX_LANGUAGE_STANDARD: "gnu++14"
    CLANG_CXX_LIBRARY: "libc++"
    CLANG_ENABLE_MODULES: YES
    CLANG_ENABLE_OBJC_ARC: YES
    CLANG_ENABLE_OBJC_WEAK: YES
    CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING: YES
    CLANG_WARN_BOOL_CONVERSION: YES
    CLANG_WARN_COMMA: YES
    CLANG_WARN_CONSTANT_CONVERSION: YES
    CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS: YES
    CLANG_WARN_DIRECT_OBJC_ISA_USAGE: YES_ERROR
    CLANG_WARN_DOCUMENTATION_COMMENTS: YES
    CLANG_WARN_EMPTY_BODY: YES
    CLANG_WARN_ENUM_CONVERSION: YES
    CLANG_WARN_INFINITE_RECURSION: YES
    CLANG_WARN_INT_CONVERSION: YES
    CLANG_WARN_NON_LITERAL_NULL_CONVERSION: YES
    CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF: YES
    CLANG_WARN_OBJC_LITERAL_CONVERSION: YES
    CLANG_WARN_OBJC_ROOT_CLASS: YES_ERROR
    CLANG_WARN_RANGE_LOOP_ANALYSIS: YES
    CLANG_WARN_STRICT_PROTOTYPES: YES
    CLANG_WARN_SUSPICIOUS_MOVE: YES
    CLANG_WARN_UNGUARDED_AVAILABILITY: YES_AGGRESSIVE
    CLANG_WARN_UNREACHABLE_CODE: YES
    CLANG_WARN__DUPLICATE_METHOD_MATCH: YES
    COPY_PHASE_STRIP: NO
    DEBUG_INFORMATION_FORMAT: dwarf
    ENABLE_STRICT_OBJC_MSGSEND: YES
    GCC_C_LANGUAGE_STANDARD: gnu11
    GCC_NO_COMMON_BLOCKS: YES
    GCC_WARN_64_TO_32_BIT_CONVERSION: YES
    GCC_WARN_ABOUT_RETURN_TYPE: YES_ERROR
    GCC_WARN_UNDECLARED_SELECTOR: YES
    GCC_WARN_UNINITIALIZED_AUTOS: YES_AGGRESSIVE
    GCC_WARN_UNUSED_FUNCTION: YES
    GCC_WARN_UNUSED_VARIABLE: YES
    IPHONEOS_DEPLOYMENT_TARGET: 13.1
    SDKROOT: iphoneos
  
  debug:
    DEBUG_INFORMATION_FORMAT: dwarf
    ENABLE_TESTABILITY: YES
    GCC_DYNAMIC_NO_PIC: NO
    GCC_OPTIMIZATION_LEVEL: 0
    GCC_PREPROCESSOR_DEFINITIONS: 
      - "DEBUG=1"
      - "$(inherited)"
    MTL_ENABLE_DEBUG_INFO: YES
    ONLY_ACTIVE_ARCH: YES
  
  release:
    DEBUG_INFORMATION_FORMAT: "dwarf-with-dsym"
    ENABLE_NS_ASSERTIONS: NO
    COPY_PHASE_STRIP: NO
    VALIDATE_PRODUCT: YES
    MTL_ENABLE_DEBUG_INFO: NO

targets:
  uten:
    type: application
    platform: iOS
    deploymentTarget: 12.1
    sources:
      - path: uten
        excludes:
          - "Framework"
          - "image/game"
          - "selector41"
          - "sound/0.sound411/ph4"
          - "sound/0.sound411/ph5"
          - "sound/0.sound411/ph6"
      - path: assets
        type: folder
      - path: class
        type: folder
      - path: utility
      - path: module
      - path: model
      - path: extension
      - path: component
      - path: ABCViewController.h
        type: file
      - path: ABCViewController.m
        type: file
    settings:
      base:
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        CODE_SIGN_STYLE: Manual
        CURRENT_PROJECT_VERSION: 53
        DEVELOPMENT_TEAM: SKBW5U8PP6
        FRAMEWORK_SEARCH_PATHS:
          - "$(inherited)"
          - "$(PROJECT_DIR)/uten/Framework"
        GCC_PRECOMPILE_PREFIX_HEADER: YES
        GCC_PREFIX_HEADER: "uten/uten-Prefix.pch"
        INFOPLIST_FILE: uten/Info.plist
        LD_RUNPATH_SEARCH_PATHS:
          - "$(inherited)"
          - "@executable_path/Frameworks"
        MARKETING_VERSION: 6.32
        PRODUCT_BUNDLE_IDENTIFIER: com.bekubee.uten
        PRODUCT_NAME: "$(TARGET_NAME)"
        TARGETED_DEVICE_FAMILY: 2
      configs:
        debug:
          EXCLUDED_ARCHS[sdk=iphonesimulator*]: arm64
          PROVISIONING_PROFILE_SPECIFIER: EFAnyDevelopment
          CODE_SIGN_IDENTITY: "iPhone Developer"
        release:
          CODE_SIGN_IDENTITY: "iPhone Distribution"
          PROVISIONING_PROFILE_SPECIFIER: EFAnyAdhoc
    dependencies:
      - sdk: AVFoundation.framework
      - sdk: AudioToolbox.framework
      - sdk: CoreGraphics.framework
      - sdk: UIKit.framework
      - sdk: Foundation.framework
      - framework: uten/Framework/OpenEars.framework
        embed: false
        # sign: true  # 防止重新簽名
      - framework: uten/Framework/Slt.framework
        embed: false
        # sign: true  # 防止重新簽名
      - sdk: CFNetwork.framework
      # - framework: libSSZipArchive.a
      #   embed: false
        
    scheme:
      testTargets:
        - utenTests
      environmentVariables:
        - variable: OS_ACTIVITY_MODE
          value: disable
          isEnabled: true
    
  utenTests:
    type: bundle.unit-test
    platform: iOS
    deploymentTarget: 12.1
    sources:
      - path: utenTests
    settings:
      base:
        BUNDLE_LOADER: "$(TEST_HOST)"
        CODE_SIGN_STYLE: Automatic
        DEVELOPMENT_TEAM: 44T8449567
        INFOPLIST_FILE: utenTests/Info.plist
        LD_RUNPATH_SEARCH_PATHS:
          - "$(inherited)"
          - "@executable_path/Frameworks"
          - "@loader_path/Frameworks"
        PRODUCT_BUNDLE_IDENTIFIER: com.bekubee.utenTests
        PRODUCT_NAME: "$(TARGET_NAME)"
        TARGETED_DEVICE_FAMILY: "1,2"
        TEST_HOST: "$(BUILT_PRODUCTS_DIR)/uten.app/uten"
      debug:
        "EXCLUDED_ARCHS[sdk=iphonesimulator*]": arm64
    dependencies:
      - target: uten
    
  utenUITests:
    type: bundle.ui-testing
    platform: iOS
    deploymentTarget: 12.1
    sources:
      - path: utenUITests
    settings:
      base:
        BUNDLE_LOADER: "$(TEST_HOST)"
        CODE_SIGN_STYLE: Automatic
        DEVELOPMENT_TEAM: 44T8449567
        INFOPLIST_FILE: utenUITests/Info.plist
        LD_RUNPATH_SEARCH_PATHS:
          - "$(inherited)"
          - "@executable_path/Frameworks"
          - "@loader_path/Frameworks"
        PRODUCT_BUNDLE_IDENTIFIER: com.bekubee.utenUITests
        PRODUCT_NAME: "$(TARGET_NAME)"
        TARGETED_DEVICE_FAMILY: "1,2"
      debug:
        "EXCLUDED_ARCHS[sdk=iphonesimulator*]": arm64
    dependencies:
      - target: uten

attributes:
  LastUpgradeCheck: "1340"
  ORGANIZATIONNAME: "BekuBee Inc."
