[`MQTTSessionDelegate`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A83%2C%22character%22%3A10%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 是一個協議，用於接收來自 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 的各種事件通知。以下是每個方法的說明及其用途：

### 方法說明

1. **newMessage:data:onTopic:qos:retained:mid:**
   - **說明**: 當接收到新消息時調用。
   - **參數**:
     - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 發送消息的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
     - [`data`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A89%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 接收到的數據，可能為零長度。
     - [`topic`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A90%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 數據發布的主題。
     - [`qos`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A91%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 消息的 QoS 等級。
     - [`retained`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A92%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 指示數據是否從服務器存儲中重新傳輸。
     - [`mid`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A93%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 消息的標識符，如果 QoS 為 1 或 2，否則為零。

2. **newMessageWithFeedback:data:onTopic:qos:retained:mid:**
   - **說明**: 當接收到新消息時調用，並允許反饋。
   - **參數**: 與 [`newMessage`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A95%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同。
   - **返回值**: [`true`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A109%2C%22character%22%3A9%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 表示消息已處理或將被處理，[`false`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A109%2C%22character%22%3A55%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 表示消息不應被確認。

3. **session:newMessage:onTopic:**
   - **說明**: 與 [`newMessage`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A95%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 方法相同，用於向後兼容。
   - **參數**: 與 [`newMessage`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A95%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同。

4. **handleEvent:event:error:**
   - **說明**: 當連接建立、關閉或發生問題時調用。
   - **參數**:
     - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告事件的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
     - [`eventCode`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A127%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 事件代碼。
     - [`error`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A55%2C%22character%22%3A5%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 可選的錯誤對象，包含額外信息。

5. **session:handleEvent:**
   - **說明**: 與 [`handleEvent`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A130%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 方法相同，用於向後兼容。
   - **參數**: 與 [`handleEvent`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A130%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同。

6. **connected:**
   - **說明**: 當連接成功建立時調用。
   - **參數**:
     - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告連接的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。

7. **connected:sessionPresent:**
   - **說明**: 當連接成功建立時調用，並報告會話存在標誌。
   - **參數**: 與 [`connected`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A142%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同，外加 [`sessionPresent`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A146%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。

8. **connectionRefused:error:**
   - **說明**: 當連接被拒絕時調用。
   - **參數**:
     - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告拒絕的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
     - [`error`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A55%2C%22character%22%3A5%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 可選的錯誤對象，包含額外信息。

9. **connectionClosed:**
   - **說明**: 當連接關閉時調用。
   - **參數**:
     - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告關閉的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。

10. **connectionError:error:**
    - **說明**: 當連接發生錯誤時調用。
    - **參數**:
      - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告錯誤的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
      - [`error`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A55%2C%22character%22%3A5%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 可選的錯誤對象，包含額外信息。

11. **protocolError:error:**
    - **說明**: 當發生 MQTT 協議錯誤時調用。
    - **參數**: 與 [`connectionError`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A167%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同。

12. **messageDelivered:msgID:**
    - **說明**: 當消息成功傳遞時調用。
    - **參數**:
      - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告傳遞的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
      - [`msgID`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A177%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 傳遞消息的標識符。

13. **messageDelivered:msgID:topic:data:qos:retainFlag:**
    - **說明**: 當消息成功傳遞時調用，並提供更多信息。
    - **參數**: 與 [`messageDelivered`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A180%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同，外加 [`topic`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A90%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")、[`data`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A89%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")、[`qos`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A91%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 和 [`retainFlag`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A188%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。

14. **subAckReceived:msgID:grantedQoss:**
    - **說明**: 當訂閱被 MQTT broker 確認時調用。
    - **參數**:
      - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告確認的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
      - [`msgID`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A177%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 訂閱消息的標識符。
      - [`grantedQoss`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A204%2C%22character%22%3A66%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 包含授予的 QoS 的數組。

15. **unsubAckReceived:msgID:**
    - **說明**: 當取消訂閱被 MQTT broker 確認時調用。
    - **參數**:
      - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告確認的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
      - [`msgID`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A177%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 取消訂閱消息的標識符。

16. **sending:type:qos:retained:duped:mid:data:**
    - **說明**: 當命令發送到 MQTT broker 時調用，用於低級別監控。
    - **參數**:
      - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告發送命令的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
      - [`type`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A215%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): MQTT 命令類型。
      - [`qos`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A91%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 命令的 QoS 等級。
      - [`retained`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A92%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 命令的保留狀態。
      - [`duped`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A218%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 命令的重複狀態。
      - [`mid`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A93%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 命令的標識符。
      - [`data`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A89%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 命令的有效負載數據。

17. **received:type:qos:retained:duped:mid:data:**
    - **說明**: 當從 MQTT broker 接收到命令時調用，用於低級別監控。
    - **參數**: 與 [`sending`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A63%2C%22character%22%3A96%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同。

18. **ignoreReceived:type:qos:retained:duped:mid:data:**
    - **說明**: 當從 MQTT broker 接收到命令時調用，用於低級別控制。
    - **參數**: 與 [`sending`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A63%2C%22character%22%3A96%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 相同。
    - **返回值**: [`true`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A109%2C%22character%22%3A9%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 表示應忽略接收到的消息。

19. **buffered:queued:flowingIn:flowingOut:**
    - **說明**: 當 MQTTClient 內部緩衝區的內容發生變化時調用，用於監控消息的完成情況。
    - **參數**:
      - [`session`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A88%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 報告變化的 [`MQTTSession`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A1%2C%22character%22%3A3%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition")。
      - [`queued`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A252%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 向後兼容，MQTTClient 不再排隊消息。
      - [`flowingIn`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A253%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 尚未被 MQTTClient 確認的傳入消息數量。
      - [`flowingOut`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A254%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition"): 尚未被 MQTT broker 確認的傳出消息數量。

20. **buffered:flowingIn:flowingOut:**
    - **說明**: 與 `buffered:queued:flowingIn:flowingOut:` 方法相同，但不包含 [`queued`](command:_github.copilot.openSymbolFromReferences?%5B%22%22%2C%5B%7B%22uri%22%3A%7B%22scheme%22%3A%22file%22%2C%22authority%22%3A%22%22%2C%22path%22%3A%22%2FUsers%2Fpht%2Fworkspace%2Futen%2FPods%2FMQTTClient%2FMQTTClient%2FMQTTClient%2FMQTTSession.h%22%2C%22query%22%3A%22%22%2C%22fragment%22%3A%22%22%7D%2C%22pos%22%3A%7B%22line%22%3A252%2C%22character%22%3A8%7D%7D%5D%2C%2200e80d5f-50ab-4c05-be7d-946299834703%22%5D "Go to definition") 參數。
    - **參數**: 與 `buffered:queued:flowingIn:flowingOut:` 相同。

這些方法提供了豐富的回調機制，允許應用程序監控和控制 MQTT 連接的各個方面。

---

要確認兩個接收方都已處理消息，可以使用 MQTT 的 QoS 2 等級，這樣可以確保消息被每個接收方僅一次且成功地處理。此外，接收方需要回傳確認消息，發送方可以通過訂閱一個回應主題來接收這些確認消息。

以下是一個簡單的範例，展示如何在 Objective-C 中實現這個功能：

1. 發送方發送消息到一個主題，並訂閱一個回應主題來接收確認消息。
2. 接收方處理消息後，發送確認消息到回應主題。

### 發送方代碼

```objective-c
#import "MQTTClient.h"

// 初始化 MQTTSession
MQTTSession *session = [[MQTTSession alloc] init];
session.clientId = @"yourClientId";
session.userName = @"yourUsername";
session.password = @"yourPassword";
session.host = @"yourBrokerAddress";
session.port = 1883;
session.keepAliveInterval = 60;
session.cleanSessionFlag = YES;

// 設定連接成功的回呼
session.connectionHandler = ^(MQTTSessionEvent event) {
    if (event == MQTTSessionEventConnected) {
        NSLog(@"Connected to MQTT broker");

        // 訂閱回應主題
        [session subscribeToTopic:@"response/topic" atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                NSLog(@"Failed to subscribe to response topic: %@", error.localizedDescription);
            } else {
                NSLog(@"Subscribed to response topic");
            }
        }];

        // 發送消息
        NSString *message = @"Hello, MQTT!";
        NSData *data = [message dataUsingEncoding:NSUTF8StringEncoding];
        [session publishData:data
                     onTopic:@"your/topic"
                      retain:NO
                         qos:MQTTQosLevelExactlyOnce // 使用 QoS 2
             publishHandler:^(NSError *error) {
            if (error) {
                NSLog(@"Failed to publish message: %@", error.localizedDescription);
            } else {
                NSLog(@"Message published successfully");
            }
        }];
    }
};

// 設定消息確認的回呼
__block int responseCount = 0;
session.messageHandler = ^(NSData *data, NSString *topic) {
    if ([topic isEqualToString:@"response/topic"]) {
        NSString *response = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        NSLog(@"Received response: %@", response);
        responseCount++;
        if (responseCount == 2) {
            NSLog(@"Both receivers have processed the message");
        }
    }
};

// 連接到 MQTT broker
[session connect];
```

### 接收方代碼

```objective-c
#import "MQTTClient.h"

//

 初始化

 MQTTSession
MQTTSession *session = [[MQTTSession alloc] init];
session.clientId = @"receiverClientId";
session.userName = @"yourUsername";
session.password = @"yourPassword";
session.host = @"yourBrokerAddress";
session.port = 1883;
session.keepAliveInterval = 60;
session.cleanSessionFlag = YES;

// 設定連接成功的回呼
session.connectionHandler = ^(MQTTSessionEvent event) {
    if (event == MQTTSessionEventConnected) {
        NSLog(@"Connected to MQTT broker");

        // 訂閱消息主題
        [session subscribeToTopic:@"your/topic" atLevel:MQTTQosLevelExactlyOnce subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
            if (error) {
                NSLog(@"Failed to subscribe to topic: %@", error.localizedDescription);
            } else {
                NSLog(@"Subscribed to topic");
            }
        }];
    }
};

// 設定消息處理的回呼
session.messageHandler = ^(NSData *data, NSString *topic) {
    if ([topic isEqualToString:@"your/topic"]) {
        NSString *message = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        NSLog(@"Received message: %@", message);

        // 發送確認消息
        NSString *response = @"Message processed";
        NSData *responseData = [response dataUsingEncoding:NSUTF8StringEncoding];
        [session publishData:responseData
                     onTopic:@"response/topic"
                      retain:NO
                         qos:MQTTQosLevelExactlyOnce // 使用 QoS 2
             publishHandler:^(NSError *error) {
            if (error) {
                NSLog(@"Failed to publish response: %@", error.localizedDescription);
            } else {
                NSLog(@"Response published successfully");
            }
        }];
    }
};

// 連接到 MQTT broker
[session connect];
```

這樣，發送方會在 `response/topic` 上接收到兩個確認消息，並在 `responseCount` 達到 2 時確認兩個接收方都已處理消息。接收方在處理完消息後，會發送確認消息到 `response/topic`。
